<?php

namespace App\Enums;

use BenSampo\Enum\Enum;
use BenSampo\Enum\Contracts\LocalizedEnum;
use Lang;
use App\Enums\Traits\Tools;

/**
 * @method static Sunday
 * @method static Monday
 * @method static Tuesday
 * @method static Wednesday
 * @method static Thursday
 * @method static Friday
 * @method static Saturday
 * @method static PublicHoliday
 */
final class DayType extends Enum implements LocalizedEnum
{
    use Tools;

    const Monday = 'MONDAY';
    const Tuesday = 'TUESDAY';
    const Wednesday = 'WEDNESDAY';
    const Thursday = 'THURSDAY';
    const Friday = 'FRIDAY';
    const Saturday = 'SATURDAY';
    const Sunday = 'SUNDAY';
    const PublicHoliday = 'PUBLIC_HOLIDAY';

    // 通过语言值获取语言文件
    public static function getLocalDescription($value, $local = null): ?string
    {
        if (parent::isLocalizable()) {
            $localizedStringKey = parent::getLocalizationKey() . '.' . $value;

            if (Lang::has($localizedStringKey)) {
                return __($localizedStringKey, [], $local);
            }
        }

        return parent::getFriendlyKeyName(parent::getKey($value));
    }

    // 通过语言值获取该语言下的枚举数组
    public static function asLocalSelectArray($local = null): array
    {
        $array = parent::asArray();
        $selectArray = [];

        foreach ($array as $value) {
            $selectArray[$value] = self::getLocalDescription($value, $local);
        }

        return $selectArray;
    }
}

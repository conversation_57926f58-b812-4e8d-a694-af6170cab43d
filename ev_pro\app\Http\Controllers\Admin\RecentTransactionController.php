<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Foundation\Application;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Enums\{
    IdentityType,
    ChargeTariffScheme,
    ChargeValueType,
    LmsMode,
    TariffTableType,
    RemoteStopChargeReasonEnum,
};
use App\Models\Modules\ChargeRecord;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class RecentTransactionController extends CommonController
{
    protected static string $module_name = 'recentTransaction'; // 模块名称

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'connector_name_search' => $request->get('connector_name_search'),
            'charge_tariff_scheme_search' => $request->get('charge_tariff_scheme_search'),
            'charge_value_type_search' => $request->get('charge_value_type_search'),
            'charge_start_time_search' => $request->get('charge_start_time_search'),
        );

        $data['charge_tariff_scheme_list'] = array();
        foreach (ChargeTariffScheme::asSelectArray() as $value => $name) {
            $data['charge_tariff_scheme_list'][] = array(
                'name' => $name,
                'value' => $value,
            );
        }

        $data['charge_value_type_list'] = array();
        foreach (ChargeValueType::asSelectArray() as $value => $name) {
            $data['charge_value_type_list'][] = array(
                'name' => $name,
                'value' => $value,
            );
        }

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {

        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $length = (int)$request->input('length', 10);
        $search = $this->_searchList($request);
        $data_list = $search->paginate($length, ['*'], 'start');

        $result = array();

        foreach ($data_list as $data) {
            $charge_value_interval = $data->charge_value_interval ?? 0;
            $charge_value_interval = match ($data->charge_value_type) {
                ChargeValueType::Time => round($charge_value_interval / 60) . __('common.unit_mins'),
                ChargeValueType::Energy => match ($data->tariff_table_type) {
                    TariffTableType::ComplexEnergyTariffTable => '—/—',
                    default => (float)bcdiv($charge_value_interval, 1000, 3) . __('common.unit_kwh'),
                },
                default => $charge_value_interval
            };
            $site_name = $this->getValueFromLanguageArray($data->site_name) ?? '—/—';
            $zone_name = $this->getValueFromLanguageArray($data->zone_name) ?? '—/—';
            $merchant_name = $this->getValueFromLanguageArray($data->merchant_name) ?? '—/—';
            $member_card_group_name = $this->getValueFromLanguageArray($data->member_card_group_name) ?? '—/—';
            $result[] = array(
                'charge_record_id' => $data->charge_record_id, // 充电记录ID
                'charge_record_number' => $data->charge_record_number, // 充电记录编号
                'gmt_start' => $data->gmt_power_on ?? $data->gmt_start, // 开始充电时间
                'gmt_stop' => $data->gmt_stop ?? '—/—', // 停止充电时间
                'charged_time' => round($data->charged_time / 60) . __('common.unit_mins'), // 已充时间
                'charged_energy' => (float)(bcdiv($data->charged_energy, 1000, 3)) . __('common.unit_kwh'), // 已充电量
                'merchant_name' => $merchant_name, // 商户名称
                'user_email' => $data->user_email ?? '—/—', // 用户邮箱
                'member_name' => $data->member_name ?? '—/—', // 会员名称
                'member_card_group_name' => $member_card_group_name, // 会员卡组名称
                'site_name' => $site_name, // 场地名称
                'zone_name' => $zone_name, // 区域名称
                'charge_point_name' => $data->charge_point_name ?? '—/—', // 充电机名称
                'connector_name' => $data->connector_name ?? '—/—', // 充电枪名称
                'lms_mode' => LmsMode::getDescription($data->lms_mode), // LMS模式
                'charge_tariff_scheme' => ChargeTariffScheme::getDescription($data->charge_tariff_scheme), // 充电收费方案
                'charge_value_type' => ChargeValueType::getDescription($data->charge_value_type), // 充电量类型
                'charge_value_interval' => $charge_value_interval, // 充电量间隔 - 选择/收费
                'charge_value_amount' => __('common.unit_hk') . (float)(bcdiv($data->charge_value_amount, 100, 1)), // 充电量金额
                'idling_penalty_amount' => __('common.unit_hk') . (float)(bcdiv($data->idling_penalty_amount, 100, 1)), // 闲置罚款金额
                'total_amount' => __('common.unit_hk') . (float)(bcdiv($data->total_amount, 100, 1)), // 合共金额
                'pre_paid_charge_value' => ChargeTariffScheme::PrePaid()->is($data->charge_tariff_scheme) ? (ChargeValueType::Time()->is($data->charge_value_type) ? round($data->pre_paid_charge_value / 60) . __('common.unit_mins') : (float)(bcdiv($data->pre_paid_charge_value, 1000, 3)) . __('common.unit_kwh')) : '—/—', // 预付充电量
                'pre_paid_purchase_charge_value' => ChargeTariffScheme::PrePaid()->is($data->charge_tariff_scheme) ? (ChargeValueType::Time()->is($data->charge_value_type) ? round($data->pre_paid_purchase_charge_value / 60) . __('common.unit_mins') : (float)(bcdiv($data->pre_paid_purchase_charge_value, 1000, 3)) . __('common.unit_kwh')) : '—/—', // 预付购买充电量
                'pre_paid_use_remain_charge_value' => ChargeTariffScheme::PrePaid()->is($data->charge_tariff_scheme) ? (ChargeValueType::Time()->is($data->charge_value_type) ? round($data->pre_paid_use_remain_charge_value / 60) . __('common.unit_mins') : (float)(bcdiv($data->pre_paid_use_remain_charge_value, 1000, 3)) . __('common.unit_kwh')) : '—/—', // 预付使用剩余充电量
                'pre_paid_charge_value_maximum_selection' => ChargeTariffScheme::PrePaid()->is($data->charge_tariff_scheme) ? (ChargeValueType::Time()->is($data->charge_value_type) ? round($data->pre_paid_charge_value_maximum_selection / 60) . __('common.unit_mins') : (float)(bcdiv($data->pre_paid_charge_value_maximum_selection, 1000, 3)) . __('common.unit_kwh')) : '—/—', // 预付充电量最大选择量
                'post_paid_purchase_charge_value' => ChargeTariffScheme::PostPaid()->is($data->charge_tariff_scheme) ? (ChargeValueType::Time()->is($data->charge_value_type) ? round($data->post_paid_purchase_charge_value / 60) . __('common.unit_mins') : (float)(bcdiv($data->post_paid_purchase_charge_value, 1000, 3)) . __('common.unit_kwh')) : '—/—', // 后付购买充电量
                'post_paid_identity_type' => ChargeTariffScheme::PostPaid()->is($data->charge_tariff_scheme) ? IdentityType::getDescription($data->post_paid_identity_type) : '—/—', // 后付身份类型
                'post_paid_maximum_charge_time' => ChargeTariffScheme::PostPaid()->is($data->charge_tariff_scheme) ? round($data->post_paid_maximum_charge_time / 60) . __('common.unit_mins') : '—/—', // 后付最大充电时间
                'is_enable_member_card_group_tariff_table' => $data->is_enable_member_card_group_tariff_table, // 是否启用会员卡组收费表
                'tariff_table_type' => TariffTableType::getDescription($data->tariff_table_type), // 收费表类型
                'trial_charge_timeout' => filled($data->trial_charge_timeout) ? $data->trial_charge_timeout . __('common.unit_s') : '—/—', // 试充超时时间
                'gmt_trial_charge_reached' => $data->gmt_trial_charge_reached ?? '—/—', // 试充达标时间
                'is_enable_admin_octopus_card_free_deduct' => $data->is_enable_admin_octopus_card_free_deduct, // 是否开启管理员八达通卡免费扣款
                'is_enable_top_up' => $data->is_enable_top_up, // 是否启用续充
                'top_up_buffer_limit' => filled($data->top_up_buffer_limit) ? $data->top_up_buffer_limit . __('common.unit_s') : '—/—', // 续充缓冲限制 - 剩余充电量需大于此数才可续充
                'is_top_up_need_confirm_identity' => $data->is_top_up_need_confirm_identity, // 是否续充需要确认身份
                'remain_charge_value_validity_period' => filled($data->remain_charge_value_validity_period) ? $data->remain_charge_value_validity_period . __('common.unit_s') : '—/—', // 剩余充电量有效期
                'remain_charge_value_minimum_limit' => $data->remain_charge_value_minimum_limit ? match ($data->charge_value_type) {
                    ChargeValueType::Time => round($data->remain_charge_value_minimum_limit / 60) . __('common.unit_mins'),
                    ChargeValueType::Energy => (float)(bcdiv($data->remain_charge_value_minimum_limit, 1000, 3)) . __('common.unit_kwh'),
                    default => $data->remain_charge_value_minimum_limit
                }
                    : '—/—', // 剩余充电量最小限制
                'is_enable_use_remain_charge_value' => $data->is_enable_use_remain_charge_value, // 是否允许使用剩余充电量
                'is_enable_round_up_tail_charge_value_calculation' => $data->is_enable_round_up_tail_charge_value_calculation, // 是否启用向上取整尾部充电量计算
                'start_id_tag' => $data->start_id_tag ?? '—/—', // 开始ID标识
                'meter_start' => $data->meter_start, // 开始充电仪表值
                'gmt_power_on' => $data->gmt_power_on ?? '—/—', // 上电时间
                'reminder_email' => $data->reminder_email ?? '—/—', // 提醒邮箱
                'reminder_telephone' => $data->reminder_telephone ?? '—/—', // 提醒电话
                'off_peak_charged_time' => round($data->off_peak_charged_time / 60) . __('common.unit_mins'), // 非高峰已充时间
                'on_peak_charged_time' => round($data->on_peak_charged_time / 60) . __('common.unit_mins'), // 高峰已充时间
                'off_peak_charged_energy' => (float)(bcdiv($data->off_peak_charged_energy, 1000, 3)) . __('common.unit_kwh'), // 非高峰已充电量
                'on_peak_charged_energy' => (float)(bcdiv($data->on_peak_charged_energy, 1000, 3)) . __('common.unit_kwh'), // 高峰已充电量
                'identity_type' => IdentityType::getDescription($data->identity_type), // 身份类型
                'identity_number' => $data->identity_number ?? '—/—', // 身份号码
                'meter_value_voltage' => $data->meter_value_voltage ?? '—/—', // 仪表值-电压
                'meter_value_current' => $data->meter_value_current ?? '—/—', // 仪表值-电流
                'meter_value_power' => $data->meter_value_power ?? '—/—', // 仪表值-功率
                'meter_value_power_factor' => $data->meter_value_power_factor ?? '—/—', // 仪表值-功率因数
                'meter_value_power_offered' => $data->meter_value_power_offered ?? '—/—', // 仪表值-最大功率
                'meter_value_start_vehicle_soc' => $data->meter_value_start_vehicle_soc ?? '—/—', // 仪表值-开始车辆电量
                'meter_value_vehicle_soc' => $data->meter_value_vehicle_soc ?? '—/—', // 仪表值-车辆电量
                'meter_value_temperature' => $data->meter_value_temperature ?? '—/—', // 仪表值-温度
                'meter_value_fan_speed' => $data->meter_value_fan_speed ?? '—/—', // 仪表值-风扇速度
                'remote_stop_operator_type' => $data->remote_stop_operator_type ?? '—/—', // 远程停止操作者类型
                'remote_stop_operator_number' => $data->remote_stop_operator_number ?? '—/—', // 远程停止操作者编号
                'remote_stop_reason' => RemoteStopChargeReasonEnum::getDescription($data->remote_stop_reason), // 远程停止原因
                'remote_stop_detail' => $data->remote_stop_detail ?? '—/—', // 远程停止详情
                'gmt_remote_stop' => $data->gmt_remote_stop ?? '—/—', // 远程停止时间
                'stop_id_tag' => $data->stop_id_tag ?? '—/—', // 停止ID标识
                'meter_stop' => $data->meter_stop ?? '—/—', // 结束充电仪表值
                'stop_reason' => $data->stop_reason ?? '—/—', // 停止充电原因
                'remote_unlock_operator_type' => $data->remote_unlock_operator_type ?? '—/—', // 远程解锁操作者类型
                'remote_unlock_operator_number' => $data->remote_unlock_operator_number ?? '—/—', // 远程解锁操作者编号
                'gmt_remote_unlock' => $data->gmt_remote_unlock ?? '—/—', // 远程解锁时间
                'gmt_unlocked' => $data->gmt_unlocked ?? '—/—', // 解锁时间
                'idling_penalty_time' => round($data->idling_penalty_time / 60) . __('common.unit_mins'), // 闲置罚款时间
                'off_peak_idling_penalty_time' => round($data->off_peak_idling_penalty_time / 60) . __('common.unit_mins'), // 非高峰闲置罚款时间
                'on_peak_idling_penalty_time' => round($data->on_peak_idling_penalty_time / 60) . __('common.unit_mins'), // 高峰闲置罚款时间
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString() // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );

        return response()->json($json);
    }

    /**
     * 返回地址栏参数
     *
     * @param Request $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    static function getUrlParams(Request $request): array
    {
        return array(
            'is_page' => 'true',
            'connector_name_search' => $request->get('connector_name_search'),
            'charge_tariff_scheme_search' => $request->get('charge_tariff_scheme_search'),
            'charge_value_type_search' => $request->get('charge_value_type_search'),
            'charge_start_time_search' => $request->get('charge_start_time_search'),
        );
    }

    protected function _searchList(Request $request)
    {
        // dataTable字段
        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $connector_name_search = $request->input('connector_name_search');
        $charge_tariff_scheme_search = $request->input('charge_tariff_scheme_search');
        $charge_value_type_search = $request->input('charge_value_type_search');
        $charge_start_time_search = $request->input('charge_start_time_search');

        $where = array();

        if (filled($connector_name_search)) {
            $where[] = ["connector_name like '%$connector_name_search%'"];
        }
        if (filled($charge_tariff_scheme_search)) {
            $where[] = ["charge_tariff_scheme = '$charge_tariff_scheme_search'"];
        }
        if (filled($charge_value_type_search)) {
            $where[] = ["charge_value_type = '$charge_value_type_search'"];
        }
        if (
            filled($charge_start_time_search) &&
            filled($charge_start_time_search_list = self::getRangeDateTimeArray($charge_start_time_search))
        ) {
            $where[] = ["IFNULL(gmt_power_on,gmt_start) >= '$charge_start_time_search_list[0]'"];
            $where[] = ["IFNULL(gmt_power_on,gmt_start) <= '$charge_start_time_search_list[1]'"];
        }

        $condition_list = null;
        foreach ($where as $condition) { // 拼接查询条件，用于在whereRaw执行，因为上面查询条件需要用到IFNULL函数
            if (blank($condition_list)) {
                $condition_list = $condition[0];
            } else {
                $condition_list = $condition_list . " AND " . $condition[0];
            }
        }

        $charge_record = ChargeRecord::with('connector');
        if (filled($condition_list)) {
            $charge_record = $charge_record->whereRaw("$condition_list");
        }

        if ($order == 'gmt_start') {
            $sort_order = "CASE WHEN gmt_power_on IS NULL THEN gmt_start ELSE gmt_power_on END $sort";
        } else {
            $sort_order = "$order $sort";
        }

        return $charge_record->select('charge_record.*', 'user.email as user_email') // 选择所需字段
            ->whereRaw('date(charge_record.gmt_create) > DATE_SUB(CURDATE(),INTERVAL 3 DAY)')
            ->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('charge_record.site_number', auth()->user()->site_number_list))
            ->leftJoin('user', 'charge_record.user_id', '=', 'user.user_id') // 关联 user 表
            ->orderByRaw($sort_order) // 再根据其他字段排序
            ->latest();
    }

    public function export(Request $request): void
    {
        // 获取数据
        $search = $this->_searchList($request);
        $data_list = $search->get();
        $module_name = self::$module_name;

        $result = array();
        foreach ($data_list as $data) {
            $site_name = $this->getValueFromLanguageArray($data->site_name) ?? '—/—';
            $zone_name = $this->getValueFromLanguageArray($data->zone_name) ?? '—/—';
            $merchant_name = $this->getValueFromLanguageArray($data->merchant_name) ?? '—/—';
            $member_card_group_name = $this->getValueFromLanguageArray($data->member_card_group_name) ?? '—/—';
            $is_pre_paid = $data->charge_tariff_scheme === ChargeTariffScheme::PrePaid; // 预付
            $is_post_paid = $data->charge_tariff_scheme === ChargeTariffScheme::PostPaid; // 后付
            $is_time = $data->charge_value_type === ChargeValueType::Time; // 时间
            $is_energy = $data->charge_value_type === ChargeValueType::Energy; // 电量
            $is_pre_paid_time = $is_pre_paid && $is_time; //预付时间
            $is_pre_paid_energy = $is_pre_paid && $is_energy; //预付电量
            $is_post_paid_time = $is_post_paid && $is_time; // 后付时间
            $is_post_paid_energy = $is_post_paid && $is_energy; // 后付电量

            $result[] = array(
                'charge_record_id' => $data->charge_record_id, // 充电记录ID
                'charge_record_number' => $data->charge_record_number, // 充电记录编号
                'connector_number' => $data->connector_number, // 充电枪编号
                'gmt_start' => $data->gmt_power_on ?? $data->gmt_start, // 开始充电时间
                'gmt_stop' => $data->gmt_stop ?? '—/—', // 停止充电时间
                'charged_time' => (string)round($data->charged_time / 60), // 已充时间
                'charged_energy' => bcdiv($data->charged_energy, 1000, 3), // 已充电量
                'total_amount' => bcdiv($data->total_amount, 100, 1), // 合共金额
                'merchant_name' => $merchant_name, // 商户名称
                'user_email' => $data->user?->email ?? '—/—', // 用户邮箱
                'member_name' => $data->member_name ?? '—/—', // 会员名称
                'member_card_group_name' => $member_card_group_name, // 会员卡组名称
                'site_name' => $site_name, // 场地名称
                'zone_name' => $zone_name, // 区域名称
                'charge_point_name' => $data->charge_point_name ?? '—/—', // 充电机名称
                'connector_name' => $data->connector_name ?? '—/—', // 充电枪名称
                'lms_mode' => LmsMode::getDescription($data->lms_mode), // LMS模式
                'charge_tariff_scheme' => ChargeTariffScheme::getDescription($data->charge_tariff_scheme), // 充电收费方案
                'charge_value_type' => ChargeValueType::getDescription($data->charge_value_type), // 充电量类型
                'charge_value_interval_time' => $is_time ? (string)round($data->charge_value_interval / 60) : '', // 充电量间隔 - 选择/收费
                'charge_value_interval_energy' => $is_energy ? bcdiv($data->charge_value_interval, 1000, 3) : '', // 充电量间隔 - 选择/收费
                'charge_value_amount' => bcdiv($data->charge_value_amount, 100, 1), // 充电量金额
                'idling_penalty_amount' => bcdiv($data->idling_penalty_amount, 100, 1), // 闲置罚款金额
                'pre_paid_charge_value_time' => $is_pre_paid_time ? (string)round($data->pre_paid_charge_value / 60) : '', // 预付充电量
                'pre_paid_charge_value_energy' => $is_pre_paid_energy ? bcdiv($data->pre_paid_charge_value, 1000, 3) : '', // 预付充电量
                'pre_paid_purchase_charge_value_time' => $is_pre_paid_time ? (string)round($data->pre_paid_purchase_charge_value / 60) : '', // 预付购买充电量
                'pre_paid_purchase_charge_value_energy' => $is_pre_paid_energy ? bcdiv($data->pre_paid_purchase_charge_value, 1000, 3) : '', // 预付购买充电量
                'pre_paid_use_remain_charge_value_time' => $is_pre_paid_time ? (string)round($data->pre_paid_use_remain_charge_value / 60) : '', // 预付使用剩余充电量
                'pre_paid_use_remain_charge_value_energy' => $is_pre_paid_energy ? bcdiv($data->pre_paid_use_remain_charge_value, 1000, 3) : '', // 预付使用剩余充电量
                'pre_paid_charge_value_maximum_selection_time' => $is_pre_paid_time ? (string)round($data->pre_paid_charge_value_maximum_selection / 60) : '', // 预付充电量最大选择量
                'pre_paid_charge_value_maximum_selection_energy' => $is_pre_paid_energy ? bcdiv($data->pre_paid_charge_value_maximum_selection, 1000, 3) : '', // 预付充电量最大选择量
                'post_paid_purchase_charge_value_time' => $is_post_paid_time ? (string)round($data->post_paid_purchase_charge_value / 60) : '', // 后付购买充电量
                'post_paid_purchase_charge_value_energy' => $is_post_paid_energy ? bcdiv($data->post_paid_purchase_charge_value, 1000, 3) : '', // 后付购买充电量
                'post_paid_identity_type' => $is_post_paid ? (IdentityType::getDescription($data->post_paid_identity_type)) : '—/—', // 后付身份类型
                'post_paid_maximum_charge_time' => $is_post_paid ? (string)round($data->post_paid_maximum_charge_time / 60) : '', // 后付最大充电时间
                'is_enable_member_card_group_tariff_table' => $data->is_enable_member_card_group_tariff_table, // 是否启用会员卡组收费表
                'tariff_table_type' => TariffTableType::getDescription($data->tariff_table_type), // 收费表类型
                'trial_charge_timeout' => filled($data->trial_charge_timeout) ? $data->trial_charge_timeout : '—/—', // 试充超时时间
                'gmt_trial_charge_reached' => $data->gmt_trial_charge_reached ?? '—/—', // 试充达标时间
                'is_enable_admin_octopus_card_free_deduct' => $data->is_enable_admin_octopus_card_free_deduct, // 是否开启管理员八达通卡免费扣款
                'is_enable_top_up' => $data->is_enable_top_up, // 是否启用续充
                'top_up_buffer_limit' => filled($data->top_up_buffer_limit) ? $data->top_up_buffer_limit : '', // 续充缓冲限制 - 剩余充电量需大于此数才可续充
                'is_top_up_need_confirm_identity' => $data->is_top_up_need_confirm_identity, // 是否续充需要确认身份
                'remain_charge_value_validity_period' => filled($data->remain_charge_value_validity_period) ? $data->remain_charge_value_validity_period : '', // 剩余充电量有效期
                'remain_charge_value_minimum_limit_time' => $is_time ? (string)round($data->remain_charge_value_minimum_limit / 60) : '', // 剩余充电量最小限制
                'remain_charge_value_minimum_limit_energy' => $is_energy ? bcdiv($data->remain_charge_value_minimum_limit, 1000, 3) : '', // 剩余充电量最小限制
                'is_enable_use_remain_charge_value' => $data->is_enable_use_remain_charge_value, // 是否允许使用剩余充电量
                'is_enable_round_up_tail_charge_value_calculation' => $data->is_enable_round_up_tail_charge_value_calculation, // 是否启用向上取整尾部充电量计算
                'start_id_tag' => $data->start_id_tag ?? '—/—', // 开始ID标识
                'meter_start' => $data->meter_start, // 开始充电仪表值
                'gmt_power_on' => $data->gmt_power_on ?? '—/—', // 上电时间
                'reminder_email' => $data->reminder_email ?? '—/—', // 提醒邮箱
                'reminder_telephone' => $data->reminder_telephone ?? '—/—', // 提醒电话
                'off_peak_charged_time' => (string)round($data->off_peak_charged_time / 60), // 非高峰已充时间
                'on_peak_charged_time' => (string)round($data->on_peak_charged_time / 60), // 高峰已充时间
                'off_peak_charged_energy' => bcdiv($data->off_peak_charged_energy, 1000, 3), // 非高峰已充电量
                'on_peak_charged_energy' => bcdiv($data->on_peak_charged_energy, 1000, 3), // 高峰已充电量
                'identity_type' => IdentityType::getDescription($data->identity_type), // 身份类型
                'identity_number' => $data->identity_number ?? '—/—', // 身份号码
                'meter_value_voltage' => $data->meter_value_voltage ?? '—/—', // 仪表值-电压
                'meter_value_current' => $data->meter_value_current ?? '—/—', // 仪表值-电流
                'meter_value_power' => $data->meter_value_power ?? '—/—', // 仪表值-功率
                'meter_value_power_factor' => $data->meter_value_power_factor ?? '—/—', // 仪表值-功率因数
                'meter_value_power_offered' => $data->meter_value_power_offered ?? '—/—', // 仪表值-最大功率
                'meter_value_start_vehicle_soc' => $data->meter_value_start_vehicle_soc ?? '—/—', // 仪表值-开始车辆电量
                'meter_value_vehicle_soc' => $data->meter_value_vehicle_soc ?? '—/—', // 仪表值-车辆电量
                'meter_value_temperature' => $data->meter_value_temperature ?? '—/—', // 仪表值-温度
                'meter_value_fan_speed' => $data->meter_value_fan_speed ?? '—/—', // 仪表值-风扇速度
                'remote_stop_operator_type' => $data->remote_stop_operator_type ?? '—/—', // 远程停止操作者类型
                'remote_stop_operator_number' => $data->remote_stop_operator_number ?? '—/—', // 远程停止操作者编号
                'remote_stop_reason' => RemoteStopChargeReasonEnum::getDescription($data->remote_stop_reason), // 远程停止原因
                'remote_stop_detail' => $data->remote_stop_detail ?? '—/—', // 远程停止详情
                'gmt_remote_stop' => $data->gmt_remote_stop ?? '—/—', // 远程停止时间
                'stop_id_tag' => $data->stop_id_tag ?? '—/—', // 停止ID标识
                'meter_stop' => $data->meter_stop ?? '—/—', // 结束充电仪表值
                'stop_reason' => $data->stop_reason ?? '—/—', // 停止充电原因
                'remote_unlock_operator_type' => $data->remote_unlock_operator_type ?? '—/—', // 远程解锁操作者类型
                'remote_unlock_operator_number' => $data->remote_unlock_operator_number ?? '—/—', // 远程解锁操作者编号
                'gmt_remote_unlock' => $data->gmt_remote_unlock ?? '—/—', // 远程解锁时间
                'gmt_unlocked' => $data->gmt_unlocked ?? '—/—', // 解锁时间
                'idling_penalty_time' => (string)round($data->idling_penalty_time / 60), // 闲置罚款时间
                'off_peak_idling_penalty_time' => (string)round($data->off_peak_idling_penalty_time / 60), // 非高峰闲置罚款时间
                'on_peak_idling_penalty_time' => (string)round($data->on_peak_idling_penalty_time / 60), // 高峰闲置罚款时间
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        // 先把语言赋值才可以在后面array出来表头，不然会输出未编译前的字符串
        $charge_record_number = __("$module_name.charge_record_number");
        $connector_name = __("$module_name.connector_name");
        $gmt_start = __("$module_name.gmt_start");
        $gmt_stop = __("$module_name.gmt_stop");
        $charged_time = __("$module_name.charged_time") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $charged_energy = __("$module_name.charged_energy") . ' ( ' . __('common.unit_kwh_blank') . ' )';
        $total_amount = __("$module_name.total_amount") . ' ( ' . __('common.unit_hk_blank') . ' )';
        $merchant_name = __("$module_name.merchant_name");
        $user = __("$module_name.user");
        $member_name = __("$module_name.member_name");
        $member_card_group_name = __("$module_name.member_card_group_name");
        $site_name = __("$module_name.site_name");
        $zone_name = __("$module_name.zone_name");
        $charge_point_name = __("$module_name.charge_point_name");
        $lms_mode = __("$module_name.lms_mode");
        $charge_tariff_scheme = __("$module_name.charge_tariff_scheme");
        $charge_value_type = __("$module_name.charge_value_type");
        $charge_value_interval_time = __("$module_name.charge_value_interval") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $charge_value_interval_energy = __("$module_name.charge_value_interval") . ' ( ' . __('common.unit_kwh_blank') . ' )';
        $charge_value_amount = __("$module_name.charge_value_amount") . ' ( ' . __('common.unit_hk_blank') . ' )';
        $idling_penalty_amount = __("$module_name.idling_penalty_amount") . ' ( ' . __('common.unit_hk_blank') . ' )';
        $pre_paid_charge_value_time = __("$module_name.pre_paid_charge_value") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $pre_paid_charge_value_energy = __("$module_name.pre_paid_charge_value") . ' ( ' . __('common.unit_kwh_blank') . ' )';
        $pre_paid_purchase_charge_value_time = __("$module_name.pre_paid_purchase_charge_value") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $pre_paid_purchase_charge_value_energy = __("$module_name.pre_paid_purchase_charge_value") . ' ( ' . __('common.unit_kwh_blank') . ' )';
        $pre_paid_use_remain_charge_value_time = __("$module_name.pre_paid_use_remain_charge_value") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $pre_paid_use_remain_charge_value_energy = __("$module_name.pre_paid_use_remain_charge_value") . ' ( ' . __('common.unit_kwh_blank') . ' )';
        $pre_paid_charge_value_maximum_selection_time = __("$module_name.pre_paid_charge_value_maximum_selection") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $pre_paid_charge_value_maximum_selection_energy = __("$module_name.pre_paid_charge_value_maximum_selection") . ' ( ' . __('common.unit_kwh_blank') . ' )';
        $post_paid_identity_type = __("$module_name.post_paid_identity_type");
        $post_paid_purchase_charge_value_time = __("$module_name.post_paid_purchase_charge_value") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $post_paid_purchase_charge_value_energy = __("$module_name.post_paid_purchase_charge_value") . ' ( ' . __('common.unit_kwh_blank') . ' )';
        $post_paid_maximum_charge_time = __("$module_name.post_paid_maximum_charge_time") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $is_enable_member_card_group_tariff_table = __("$module_name.is_enable_member_card_group_tariff_table");
        $tariff_table_type = __("$module_name.tariff_table_type");
        $trial_charge_timeout = __("$module_name.trial_charge_timeout");
        $gmt_trial_charge_reached = __("$module_name.gmt_trial_charge_reached");
        $is_enable_admin_octopus_card_free_deduct = __("$module_name.is_enable_admin_octopus_card_free_deduct");
        $is_enable_top_up = __("$module_name.is_enable_top_up");
        $top_up_buffer_limit = __("$module_name.top_up_buffer_limit") . ' ( ' . __('common.unit_s_blank') . ' )';
        $is_top_up_need_confirm_identity = __("$module_name.is_top_up_need_confirm_identity");
        $remain_charge_value_validity_period = __("$module_name.remain_charge_value_validity_period") . ' ( ' . __('common.unit_s_blank') . ' )';
        $remain_charge_value_minimum_limit_time = __("$module_name.remain_charge_value_minimum_limit") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $remain_charge_value_minimum_limit_energy = __("$module_name.remain_charge_value_minimum_limit") . ' ( ' . __('common.unit_kwh_blank') . ' )';
        $is_enable_use_remain_charge_value = __("$module_name.is_enable_use_remain_charge_value");
        $is_enable_round_up_tail_charge_value_calculation = __("$module_name.is_enable_round_up_tail_charge_value_calculation");
        $start_id_tag = __("$module_name.start_id_tag");
        $meter_start = __("$module_name.meter_start");
        $gmt_power_on = __("$module_name.gmt_power_on");
        $reminder_email = __("$module_name.reminder_email");
        $reminder_telephone = __("$module_name.reminder_telephone");
        $off_peak_charged_time = __("$module_name.off_peak_charged_time") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $on_peak_charged_time = __("$module_name.on_peak_charged_time") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $off_peak_charged_energy = __("$module_name.off_peak_charged_energy") . ' ( ' . __('common.unit_kwh_blank') . ' )';
        $on_peak_charged_energy = __("$module_name.on_peak_charged_energy") . ' ( ' . __('common.unit_kwh_blank') . ' )';
        $identity_type = __("$module_name.identity_type");
        $identity_number = __("$module_name.identity_number");
        $meter_value_voltage = __("$module_name.meter_value_voltage");
        $meter_value_current = __("$module_name.meter_value_current");
        $meter_value_power = __("$module_name.meter_value_power");
        $meter_value_power_factor = __("$module_name.meter_value_power_factor");
        $meter_value_power_offered = __("$module_name.meter_value_power_offered");
        $meter_value_start_vehicle_soc = __("$module_name.meter_value_start_vehicle_soc");
        $meter_value_vehicle_soc = __("$module_name.meter_value_vehicle_soc");
        $meter_value_temperature = __("$module_name.meter_value_temperature");
        $meter_value_fan_speed = __("$module_name.meter_value_fan_speed");
        $remote_stop_operator_type = __("$module_name.remote_stop_operator_type");
        $remote_stop_operator_number = __("$module_name.remote_stop_operator_number");
        $remote_stop_reason = __("$module_name.remote_stop_reason");
        $remote_stop_detail = __("$module_name.remote_stop_detail");
        $gmt_remote_stop = __("$module_name.gmt_remote_stop");
        $stop_id_tag = __("$module_name.stop_id_tag");
        $meter_stop = __("$module_name.meter_stop");
        $stop_reason = __("$module_name.stop_reason");
        $remote_unlock_operator_type = __("$module_name.remote_unlock_operator_type");
        $remote_unlock_operator_number = __("$module_name.remote_unlock_operator_number");
        $gmt_remote_unlock = __("$module_name.gmt_remote_unlock");
        $gmt_unlocked = __("$module_name.gmt_unlocked");
        $idling_penalty_time = __("$module_name.idling_penalty_time") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $off_peak_idling_penalty_time = __("$module_name.off_peak_idling_penalty_time") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $on_peak_idling_penalty_time = __("$module_name.on_peak_idling_penalty_time") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $gmt_create = __("$module_name.gmt_create");
        $web_title = __("$module_name.web_title");

        $spreadsheet = new Spreadsheet(); //实例化 Spreadsheet 对象
        $worksheet = $spreadsheet->getActiveSheet();

        // 工作簿名称为 "Usage Report"
        $worksheet->setTitle($web_title);

        $styleArray = [
            'font' => [
                'bold' => false
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
            ],
        ];

        // 计算数组长度用于动态补充表格样式
        $len = count($result);
        // 因为前面两格表头样式占了一行，所以要加1用于设置单元格样式
        $total_rows = $len + 1;

        //设置单元格样式
        $worksheet->getStyle("A1:BT$total_rows")->applyFromArray($styleArray)->getFont()->setName('Times New Roman')->setSize(11);
        $worksheet->getStyle('A1:BT1')->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setARGB('c0c0c0');

        $styleArrayBody = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['argb' => '666666'],
                ],
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
            ],
        ];
        $worksheet->getStyle("A1:BT$total_rows")->applyFromArray($styleArrayBody);

        $excel_data = array(
            array(
                $charge_record_number,
                $connector_name,
                $gmt_start,
                $gmt_stop,
                $charged_time,
                $charged_energy,
                $total_amount,
                $merchant_name,
                $user,
                $member_name,
                $member_card_group_name,
                $site_name,
                $zone_name,
                $charge_point_name,
                $lms_mode,
                $charge_tariff_scheme,
                $charge_value_type,
                $charge_value_interval_time,
                $charge_value_interval_energy,
                $charge_value_amount,
                $idling_penalty_amount,
                $pre_paid_charge_value_time,
                $pre_paid_charge_value_energy,
                $pre_paid_purchase_charge_value_time,
                $pre_paid_purchase_charge_value_energy,
                $pre_paid_use_remain_charge_value_time,
                $pre_paid_use_remain_charge_value_energy,
                $pre_paid_charge_value_maximum_selection_time,
                $pre_paid_charge_value_maximum_selection_energy,
                $post_paid_purchase_charge_value_time,
                $post_paid_purchase_charge_value_energy,
                $post_paid_identity_type,
                $post_paid_maximum_charge_time,
                $is_enable_member_card_group_tariff_table,
                $tariff_table_type,
                $trial_charge_timeout,
                $gmt_trial_charge_reached,
                $is_enable_admin_octopus_card_free_deduct,
                $is_enable_top_up,
                $top_up_buffer_limit,
                $is_top_up_need_confirm_identity,
                $remain_charge_value_validity_period,
                $remain_charge_value_minimum_limit_time,
                $remain_charge_value_minimum_limit_energy,
                $is_enable_use_remain_charge_value,
                $is_enable_round_up_tail_charge_value_calculation,
                $start_id_tag,
                $meter_start,
                $gmt_power_on,
                $reminder_email,
                $reminder_telephone,
                $off_peak_charged_time,
                $on_peak_charged_time,
                $off_peak_charged_energy,
                $on_peak_charged_energy,
                $identity_type,
                $identity_number,
                $meter_value_voltage,
                $meter_value_current,
                $meter_value_power,
                $meter_value_power_factor,
                $meter_value_power_offered,
                $meter_value_start_vehicle_soc,
                $meter_value_vehicle_soc,
                $meter_value_temperature,
                $meter_value_fan_speed,
                $remote_stop_operator_type,
                $remote_stop_operator_number,
                $remote_stop_reason,
                $remote_stop_detail,
                $gmt_remote_stop,
                $stop_id_tag,
                $meter_stop,
                $stop_reason,
                $remote_unlock_operator_type,
                $remote_unlock_operator_number,
                $gmt_remote_unlock,
                $gmt_unlocked,
                $idling_penalty_time,
                $off_peak_idling_penalty_time,
                $on_peak_idling_penalty_time,
                $gmt_create,
            )
        );

        //设置单元格样式
        // 将数组长度转换为 Excel 列名
        $last_column = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex(count($excel_data[0]));
        $worksheet->getStyle("A1:{$last_column}$total_rows")->applyFromArray($styleArray)->getFont()->setName('Times New Roman')->setSize(11);
        $worksheet->getStyle("A1:{$last_column}1")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('c0c0c0');
        $worksheet->getStyle("A1:{$last_column}$total_rows")->applyFromArray($styleArrayBody);

        foreach ($result as $report) {
            $excel_data[] = array(
                $charge_record_number => $report['charge_record_number'],
                $connector_name => $report['connector_name'],
                $gmt_start => $report['gmt_start'],
                $gmt_stop => $report['gmt_stop'],
                $charged_time => $report['charged_time'],
                $charged_energy => $report['charged_energy'],
                $total_amount => $report['total_amount'],
                $merchant_name => $report['merchant_name'],
                $user => $report['user_email'],
                $member_name => $report['member_name'],
                $member_card_group_name => $report['member_card_group_name'],
                $site_name => $report['site_name'],
                $zone_name => $report['zone_name'],
                $charge_point_name => $report['charge_point_name'],
                $lms_mode => $report['lms_mode'],
                $charge_tariff_scheme => $report['charge_tariff_scheme'],
                $charge_value_type => $report['charge_value_type'],
                $charge_value_interval_time => $report['charge_value_interval_time'],
                $charge_value_interval_energy => $report['charge_value_interval_energy'],
                $charge_value_amount => $report['charge_value_amount'],
                $idling_penalty_amount => $report['idling_penalty_amount'],
                $pre_paid_charge_value_time => $report['pre_paid_charge_value_time'],
                $pre_paid_charge_value_energy => $report['pre_paid_charge_value_energy'],
                $pre_paid_purchase_charge_value_time => $report['pre_paid_purchase_charge_value_time'],
                $pre_paid_purchase_charge_value_energy => $report['pre_paid_purchase_charge_value_energy'],
                $pre_paid_use_remain_charge_value_time => $report['pre_paid_use_remain_charge_value_time'],
                $pre_paid_use_remain_charge_value_energy => $report['pre_paid_use_remain_charge_value_energy'],
                $pre_paid_charge_value_maximum_selection_time => $report['pre_paid_charge_value_maximum_selection_time'],
                $pre_paid_charge_value_maximum_selection_energy => $report['pre_paid_charge_value_maximum_selection_energy'],
                $post_paid_purchase_charge_value_time => $report['post_paid_purchase_charge_value_time'],
                $post_paid_purchase_charge_value_energy => $report['post_paid_purchase_charge_value_energy'],
                $post_paid_identity_type => $report['post_paid_identity_type'],
                $post_paid_maximum_charge_time => $report['post_paid_maximum_charge_time'],
                $is_enable_member_card_group_tariff_table => ($report['is_enable_member_card_group_tariff_table']) ? '✔' : '✘',
                $tariff_table_type => $report['tariff_table_type'],
                $trial_charge_timeout => $report['trial_charge_timeout'],
                $gmt_trial_charge_reached => $report['gmt_trial_charge_reached'],
                $is_enable_admin_octopus_card_free_deduct => ($report['is_enable_admin_octopus_card_free_deduct']) ? '✔' : '✘',
                $is_enable_top_up => ($report['is_enable_top_up']) ? '✔' : '✘',
                $top_up_buffer_limit => $report['top_up_buffer_limit'],
                $is_top_up_need_confirm_identity => ($report['is_top_up_need_confirm_identity']) ? '✔' : '✘',
                $remain_charge_value_validity_period => $report['remain_charge_value_validity_period'],
                $remain_charge_value_minimum_limit_time => $report['remain_charge_value_minimum_limit_time'],
                $remain_charge_value_minimum_limit_energy => $report['remain_charge_value_minimum_limit_energy'],
                $is_enable_use_remain_charge_value => ($report['is_enable_use_remain_charge_value']) ? '✔' : '✘',
                $is_enable_round_up_tail_charge_value_calculation => ($report['is_enable_round_up_tail_charge_value_calculation']) ? '✔' : '✘',
                $start_id_tag => $report['start_id_tag'],
                $meter_start => (string)$report['meter_start'],
                $gmt_power_on => $report['gmt_power_on'],
                $reminder_email => (string)$report['reminder_email'],
                $reminder_telephone => (string)$report['reminder_telephone'],
                $off_peak_charged_time => $report['off_peak_charged_time'],
                $on_peak_charged_time => $report['on_peak_charged_time'],
                $off_peak_charged_energy => $report['off_peak_charged_energy'],
                $on_peak_charged_energy => $report['on_peak_charged_energy'],
                $identity_type => $report['identity_type'],
                $identity_number => $report['identity_number'],
                $meter_value_voltage => $report['meter_value_voltage'],
                $meter_value_current => $report['meter_value_current'],
                $meter_value_power => $report['meter_value_power'],
                $meter_value_power_factor => $report['meter_value_power_factor'],
                $meter_value_power_offered => $report['meter_value_power_offered'],
                $meter_value_start_vehicle_soc => $report['meter_value_start_vehicle_soc'],
                $meter_value_vehicle_soc => $report['meter_value_vehicle_soc'],
                $meter_value_temperature => $report['meter_value_temperature'],
                $meter_value_fan_speed => $report['meter_value_fan_speed'],
                $remote_stop_operator_type => $report['remote_stop_operator_type'],
                $remote_stop_operator_number => $report['remote_stop_operator_number'],
                $remote_stop_reason => (string)$report['remote_stop_reason'],
                $remote_stop_detail => (string)$report['remote_stop_detail'],
                $gmt_remote_stop => $report['gmt_remote_stop'],
                $stop_id_tag => $report['stop_id_tag'],
                $meter_stop => (string)$report['meter_stop'],
                $stop_reason => $report['stop_reason'],
                $remote_unlock_operator_type => $report['remote_unlock_operator_type'],
                $remote_unlock_operator_number => $report['remote_unlock_operator_number'],
                $gmt_remote_unlock => $report['gmt_remote_unlock'],
                $gmt_unlocked => $report['gmt_unlocked'],
                $idling_penalty_time => $report['idling_penalty_time'],
                $off_peak_idling_penalty_time => $report['off_peak_idling_penalty_time'],
                $on_peak_idling_penalty_time => $report['on_peak_idling_penalty_time'],
                $gmt_create => $report['gmt_create'],
            );
        }

        $worksheet->fromArray($excel_data);
        $writer = new Xlsx($spreadsheet);
        $file_name = $web_title . ' ' . date('Y-m-d H:i:s') . '.xlsx';
        //设置单元格的宽度
        foreach ($spreadsheet->getActiveSheet()->getColumnIterator() as $column) {
            $spreadsheet->getActiveSheet()->getColumnDimension($column->getColumnIndex())->setAutoSize(true);
        }

        // 客户端文件下载
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $file_name . '"');
        header('Cache-Control: max-age=0');
        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
        exit;
    }
}

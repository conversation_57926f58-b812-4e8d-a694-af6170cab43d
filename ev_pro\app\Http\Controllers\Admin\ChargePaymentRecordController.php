<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Auth;
use Illuminate\Http\{
    JsonResponse,
    Request,
};
use Illuminate\Foundation\Application;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use App\Enums\{
    PaymentMethod,
    ChargeTariffScheme,
    ChargeValueType,
    PaymentDeviceEnum,
    PaymentStatusEnum,
    ChargePreAuthorizationStatus,
    TransactionType,
    TransactionCategory,
    IdentityType,
};
use App\Models\Modules\{
    ChargePaymentRecord,
    ChargePaymentRecordCalculation,
};


class ChargePaymentRecordController extends CommonController
{
    protected static string $module_name = 'chargePaymentRecord'; // 模块名称

    /**
     * 初始页
     *
     * @param Request $request
     * @return View|Application|Factory
     */
    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'charge_tariff_scheme_search' => $request->get('charge_tariff_scheme_search'),
            'charge_value_type_search' => $request->get('charge_value_type_search'),
            'charge_start_time_search' => $request->get('charge_start_time_search'),
        );

        $data['payment_method_search'] = filled($request->get('payment_method_search')) ?
            explode(',', $request->get('payment_method_search'))
            : array();

        $data['payment_method_list'] = config('paymentMethod.payment_method');

        $data['charge_tariff_scheme_list'] = array();
        foreach (ChargeTariffScheme::asSelectArray() as $value => $name) {
            $data['charge_tariff_scheme_list'][] = array(
                'name' => $name,
                'value' => $value,
            );
        }

        $data['charge_value_type_list'] = array();
        foreach (ChargeValueType::asSelectArray() as $value => $name) {
            $data['charge_value_type_list'][] = array(
                'name' => $name,
                'value' => $value,
            );
        }

        return view("pages.{$data['module_name']}.list", $data);
    }

    /**
     * 列表接口
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $length = (int)$request->input('length', 10);
        $search = $this->_searchList($request);
        $data_list = $search->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $charge_value = $data->charge_value ?? 0;
            $charge_value = match ($data->charge_value_type) {
                ChargeValueType::Time => round($charge_value / 60) . __('common.unit_mins'),
                ChargeValueType::Energy => (float)bcdiv($charge_value, 1000, 3) . __('common.unit_kwh'),
                default => $charge_value,
            };
            $payment_method = match ($data->payment_device) {
                PaymentDeviceEnum::Pos => $data->pos_payment_method_name ?: ($data->payment_method ?: '—/—'),
                default => $data->payment_method ?: '—/—',
            };
            $card_number = match ($data->payment_device) {
                PaymentDeviceEnum::Pos => $data->pos_card_number ?? '—/—',
                PaymentDeviceEnum::Octopus => $data->octopus_card_number ?? '—/—',
                default => '—/—',
            };

            $result[] = array(
                'charge_payment_record_id' => $data->charge_payment_record_id, // 充电支付记录ID
                'charge_payment_record_number' => $data->charge_payment_record_number, // 充电支付记录编号
                'charge_record_number' => $data->charge_record_number, // 充电记录编号
                'connector_name' => $data->connector_name, // 充电枪名称
                'receipt_number' => ($data->octopus_receipt_number ?? $data->pos_receipt_number) ?? '—/—', // 收据编号
                'payment_method' => $payment_method, // 支付方式
                'card_number' => $card_number, // 卡号
                'total_amount' => __('common.unit_hk') . (float)bcdiv($data->total_amount, 100, 1), // 合共金额
                'gmt_start' => $data->gmt_power_on ?? $data->gmt_start, // 开始时间
                'gmt_stop' => $data->gmt_stop ?? '—/—', // 停止时间
                'charge_value_type' => ChargeValueType::getDescription($data->charge_value_type), // 充电量类型
                'charge_value' => $charge_value, // 充电量
                'charge_value_amount' => __('common.unit_hk') . (float)bcdiv($data->charge_value_amount, 100, 1), // 充电量金额
                'charge_tariff_scheme' => ChargeTariffScheme::getDescription($data->charge_tariff_scheme), // 充电收费方案
                'use_remain_charge_value' => $data->charge_value_type === ChargeValueType::Time ? round($data->use_remain_charge_value / 60) . __('common.unit_mins') : (float)bcdiv($data->use_remain_charge_value, 1000, 3) . __('common.unit_kwh'), // 使用剩余充电量
                'idling_penalty_time' => round($data->idling_penalty_time / 60) . __('common.unit_mins'), // 闲置罚款时间
                'idling_penalty_amount' => __('common.unit_hk') . (float)bcdiv($data->idling_penalty_amount, 100, 1), // 闲置金额
                'gmt_idling_penalty_start' => $data->gmt_idling_penalty_start ?? '—/—', // 闲置罚款开始时间
                'gmt_idling_penalty_stop' => $data->gmt_idling_penalty_stop ?? '—/—', // 闲置罚款结束时间
                'actual_payment_amount' => __('common.unit_hk') . (float)bcdiv($data->actual_payment_amount, 100, 1), // 实际付款金额
                'user_email' => $data->user?->email ?? ($data->user?->telephone ?? '—/—'), // 用户邮箱/电话
                'use_points' => (float)bcdiv($data->use_points, 100, 1) . __('common.unit_points'), // 使用积分
                'points_transaction_id' => $data->points_transaction_id ?? '—/—', // 积分交易ID
                'merchant_handling_fee' => __('common.unit_hk') . (float)bcdiv($data->merchant_handling_fee, 100, 2), // 商户手续费
                'merchant_receivable' => __('common.unit_hk') . (float)bcdiv($data->merchant_receivable, 100, 2), // 商户应收款
                'gmt_octopus_deduct' => $data->gmt_octopus_deduct ?? '—/—', // 八达通扣款时间
                'gmt_receipt_start' => $data->gmt_receipt_start ?? '—/—', // 收据开始时间
                'gmt_receipt_stop' => $data->gmt_receipt_stop ?? '—/—', // 收据结束时间
                'octopus_device_number' => $data->octopus_device_number ?? '—/—', // 八达通设备编号
                'octopus_card_number' => $data->octopus_card_number ?? '—/—', // 八达通卡号
                'octopus_raw_card_number' => $data->octopus_raw_card_number ?? '—/—', // 八达通原始卡号
                'octopus_card_type' => $data->octopus_card_type ?? '—/—', // 八达通卡类型
                'octopus_balance' => $data->octopus_balance ? __('common.unit_hk') . (float)bcdiv($data->octopus_balance, 100, 1) : '—/—', // 八达通余额
                'is_admin_octopus_card' => $data->is_admin_octopus_card, // 是否为管理员八达通
                'is_free_octopus_card' => $data->is_free_octopus_card, // 是否为免费八达通卡
                'octopus_last_added_value_type' => $data->octopus_last_added_value_type ?? '—/—', // 八达通上次充值类型
                'octopus_last_added_value_date' => $data->octopus_last_added_value_date ?? '—/—', // 八达通上次充值日期
                'gmt_pos_deduct' => $data->gmt_pos_deduct ?? '—/—', // POS扣款时间
                'pos_transaction_id' => $data->pos_transaction_id ?? '—/—', // POS交易ID
                'pos_payment_method_name' => $data->pos_payment_method_name ?? '—/—', // POS支付方式名称
                'pos_card_number' => $data->pos_card_number ?? '—/—', // POS卡号
                'pos_trace_no' => $data->pos_trace_no ?? '—/—', // POS追踪编号
                'pos_reference_id' => $data->pos_reference_id ?? '—/—', // POS参考编号
                'charge_pre_authorization_record_number' => $data->charge_pre_authorization_record_number ?? '—/—', // 充电预授权记录编号
                'print_receipt_count' => $data->print_receipt_count ?? '—/—', // 打印收据次数
                'gmt_last_print_receipt' => $data->gmt_last_print_receipt ?? '—/—', // 最后打印收据时间
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result
        );

        return response()->json($json);
    }

    /**
     * 视图页
     *
     * @param Request $request
     * @param $charge_payment_record_number
     * @return View|Application|Factory
     */
    public function view(Request $request, $charge_payment_record_number): View|Application|Factory
    {
        // dataTable字段
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'],
            self::getUrlParams($request)
        );

        // 获取支付记录
        $charge_payment_record_result = ChargePaymentRecord::with(['chargeRecord', 'chargePreAuthorizationRecord'])
            ->where('charge_payment_record_number', $charge_payment_record_number)
            ->firstOrFail();

        if (filled($charge_payment_record_result)) {
            switch ($charge_payment_record_result->payment_device) {
                case PaymentDeviceEnum::Pos:
                    $payment_method = $charge_payment_record_result->pos_payment_method_name ?: PaymentMethod::getDescription($charge_payment_record_result->payment_method);
                    $payment_method_image = existsImage('icon', 'payment_methods/' . strtolower($charge_payment_record_result->pos_payment_method_name ?: $charge_payment_record_result->payment_method) . '.png');
                    break;
                case PaymentDeviceEnum::OnlinePayment:
                    $payment_method = PaymentDeviceEnum::getDescription($charge_payment_record_result->payment_device);
                    $payment_method_image = existsImage('icon', 'payment_methods/' . strtolower($charge_payment_record_result->payment_method) . '.png');
                    break;
                default:
                    $payment_method = PaymentMethod::getDescription($charge_payment_record_result->payment_method);
                    $payment_method_image = existsImage('icon', 'payment_methods/' . strtolower($charge_payment_record_result->payment_method) . '.png');
                    break;
            }

            // 获取支付记录
            $charge_payment_record_info = array(
                'charge_payment_record_id' => $charge_payment_record_result->charge_payment_record_id,
                'charge_payment_record_number' => $charge_payment_record_result->charge_payment_record_number,
                'charge_record_number' => $charge_payment_record_result->charge_record_number, // 充电记录编号
                'payment_device' => $charge_payment_record_result->payment_device, // 付款设备
                'payment_method' => $payment_method, // 支付方式
                'payment_method_image' => ($payment_method_image ?: existsImage('icon', 'payment_methods/payment_method_placeholder.png')) ?: '', // 支付方式icon
                'charge_value_amount' => (float)bcdiv($charge_payment_record_result->charge_value_amount, 100, 1), // 充电量金额
                'idling_penalty_amount' => (float)bcdiv($charge_payment_record_result->idling_penalty_amount, 100, 1), // 闲置罚款金额
                'total_amount' => (float)bcdiv($charge_payment_record_result->total_amount, 100, 1), // 总金额
                'actual_payment_amount' => (float)bcdiv($charge_payment_record_result->actual_payment_amount, 100, 1), // 实际付款金额
                'use_points' => (float)bcdiv($charge_payment_record_result->use_points, 100, 1), // 使用积分
                'is_total_amount_equal_actual_payment_amount' => $charge_payment_record_result->total_amount === $charge_payment_record_result->actual_payment_amount, // 总金额是否等于实际付款金额
                'merchant_handling_fee_rate' => filled($charge_payment_record_result->chargeRecord?->merchant_handling_fee_rate) ? ($charge_payment_record_result->chargeRecord?->merchant_handling_fee_rate . __('common.unit_percent_blank')) : null, // 商户手续费率
                'merchant_handling_fee' => (float)bcdiv($charge_payment_record_result->merchant_handling_fee, 100, 2), // 商户手续费
                'merchant_receivable' => (float)bcdiv($charge_payment_record_result->merchant_receivable, 100, 2), // 商户应收款
                'is_has_merchant_handling_fee' => $charge_payment_record_result->merchant_handling_fee > 0, // 是否存在商户手续费
                'octopus_device_number' => $charge_payment_record_result->octopus_device_number ?? '—/—', // 八达通设备编号
                'octopus_card_number' => $charge_payment_record_result->octopus_card_number ?? '—/—', // 八达通卡号
                'octopus_raw_card_number' => $charge_payment_record_result->octopus_raw_card_number ?? '—/—', // 八达通原始卡号
                'octopus_card_type' => $charge_payment_record_result->octopus_card_type ?? '—/—', // 八达通卡类型
                'octopus_balance' => __('common.unit_hk') . (float)bcdiv($charge_payment_record_result->octopus_balance, 100, 1) ?? '—/—', // 八达通余额
                'octopus_last_added_value_type' => $charge_payment_record_result->octopus_last_added_value_type ?? '—/—', // 八达通上次充值类型
                'octopus_last_added_value_date' => $charge_payment_record_result->octopus_last_added_value_date ?? '—/—', // 八达通上次充值日期
                'pos_receipt_number' => $charge_payment_record_result->pos_receipt_number ?? '—/—', // POS收据编号
                'gmt_pos_deduct' => $charge_payment_record_result->gmt_pos_deduct ?? '—/—', // POS扣款时间
                'pos_transaction_id' => $charge_payment_record_result->pos_transaction_id ?? '—/—', // POS交易ID
                'pos_payment_method_name' => $charge_payment_record_result->pos_payment_method_name ?? '—/—', // POS支付方式名称
                'pos_card_number' => $charge_payment_record_result->pos_card_number ?? '—/—', // POS卡号
                'pos_trace_no' => $charge_payment_record_result->pos_trace_no ?? '—/—', // POS追踪编号
                'pos_reference_id' => $charge_payment_record_result->pos_reference_id ?? '—/—', // POS参考编号
                'charge_pre_authorization_record_number' => $charge_payment_record_result->charge_pre_authorization_record_number ?? '—/—', // 充电预授权记录编号
                'charge_value_type' => ChargeValueType::getDescription($charge_payment_record_result->chargeRecord?->charge_value_type), // 充电量类型描述
                'charge_value_type_value' => $charge_payment_record_result->chargeRecord?->charge_value_type, // 充电量类型值
                'tariff_table_type' => $charge_payment_record_result->chargeRecord?->tariff_table_type, // 收费表类型值
                'charge_tariff_scheme' => $charge_payment_record_result->chargeRecord?->charge_tariff_scheme, // 充电收费方案
                'is_idling_penalty_only' => $charge_payment_record_result->is_idling_penalty_only, // 是否仅闲置罚款
                'is_charge_arrears' => $charge_payment_record_result->is_charge_arrears, // 是否充电欠款
                'print_receipt_count' => $charge_payment_record_result->print_receipt_count ?? '—/—', // 打印收据次数
                'gmt_last_print_receipt' => $charge_payment_record_result->gmt_last_print_receipt ?? '—/—', // 最后打印收据时间
                'gmt_create' => $charge_payment_record_result->gmt_create->toDateTimeString(), // 创建时间
            );
            switch ($charge_payment_record_result->chargeRecord?->charge_value_type) {
                case ChargeValueType::Time:
                    $charge_payment_record_info['charged_time'] = floor($charge_payment_record_result->charge_value / 60) . __('common.unit_mins'); // 已充时间
                    if ($charge_payment_record_result->use_remain_charge_value > 0) $charge_payment_record_info['use_remain_charge_value'] = floor($charge_payment_record_result->use_remain_charge_value / 60) . __('common.unit_mins'); // 使用剩余充电量
                    break;
                case ChargeValueType::Energy:
                    $charge_payment_record_info['charged_energy'] = (float)bcdiv($charge_payment_record_result->charge_value, 1000, 3) . __('common.unit_kwh'); // 已充电量
                    if ($charge_payment_record_result->use_remain_charge_value > 0) $charge_payment_record_info['use_remain_charge_value'] = (float)bcdiv($charge_payment_record_result->use_remain_charge_value, 1000, 3) . __('common.unit_kwh'); // 使用剩余充电量
                    break;
                default:
                    break;
            }

            // 卡片显示不同数据，PaymentMethod为OCTOPUS就只显示OCTOPUS相关的，如果是其他的就显示Pos Card
            if ($charge_payment_record_result->payment_device === PaymentDeviceEnum::Octopus) {
                $charge_payment_record_info['octopus_data'] = array(
                    'octopus_device_number' => $charge_payment_record_result->octopus_device_number ?? '—/—', // 八达通设备编号
                    'octopus_card_number' => $charge_payment_record_result->octopus_card_number ?? '—/—', // 八达通卡号
                    'octopus_raw_card_number' => $charge_payment_record_result->octopus_raw_card_number ?? '—/—', // 八达通原始卡号
                    'octopus_card_type' => $charge_payment_record_result->octopus_card_type ?? '—/—', // 八达通卡类型
                    'octopus_balance' => filled($charge_payment_record_result->octopus_balance) ? __('common.unit_hk') . (float)bcdiv($charge_payment_record_result->octopus_balance, 100, 1) : '—/—', // 八达通余额
                    'is_admin_octopus_card' => $charge_payment_record_result->is_admin_octopus_card, // 是否为管理员八达通
                    'is_free_octopus_card' => $charge_payment_record_result->is_free_octopus_card, // 是否为免费八达通卡
                    'octopus_last_added_value_type' => $charge_payment_record_result->octopus_last_added_value_type ?? '—/—', // 八达通上次充值类型
                    'octopus_last_added_value_date' => filled($charge_payment_record_result->octopus_last_added_value_date) ? date('Y-m-d', strtotime($charge_payment_record_result->octopus_last_added_value_date)) : '—/—', // 八达通上次充值日期
                );
            } else if ($charge_payment_record_result->payment_device === PaymentDeviceEnum::Pos) {
                $charge_payment_record_info['pos_data'] = array(
                    'pos_receipt_number' => $charge_payment_record_result->pos_receipt_number ?? '—/—', // POS收据编号
                    'gmt_pos_deduct' => $charge_payment_record_result->gmt_pos_deduct ?? '—/—', // POS扣款时间
                    'pos_transaction_id' => $charge_payment_record_result->pos_transaction_id ?? '—/—', // POS交易ID
                    'pos_payment_method_name' => $charge_payment_record_result->pos_payment_method_name ?? '—/—', // POS支付方式名称
                    'pos_card_number' => $charge_payment_record_result->pos_card_number ?? '—/—', // POS卡号
                    'pos_trace_no' => $charge_payment_record_result->pos_trace_no ?? '—/—', // POS追踪编号
                    'pos_reference_id' => $charge_payment_record_result->pos_reference_id ?? '—/—', // POS参考编号
                    'charge_pre_authorization_record_number' => $charge_payment_record_result->charge_pre_authorization_record_number ?? '—/—', // 充电预授权记录编号
                );
            } else if ($charge_payment_record_result->payment_device === PaymentDeviceEnum::OnlinePayment) {
                $charge_payment_record_info['online_payment_data'] = array(
                    'actual_payment_amount' => __('common.unit_hk') . (filled($charge_payment_record_result->actual_payment_amount) ? (float)bcdiv($charge_payment_record_result->actual_payment_amount, 100, 1) : 0), // 实际付款金额
                    'use_points' => (filled($charge_payment_record_result->use_points) ? (float)bcdiv($charge_payment_record_result->use_points, 100, 1) : 0) . __('common.unit_points'), // 使用积分
                    'points_transaction_id' => $charge_payment_record_result->points_transaction_id ?? '—/—', // 积分交易ID
                );
            }

            // 获取充电预授权记录
            $charge_pre_authorization_record_result = $charge_payment_record_result->chargePreAuthorizationRecord;
            $charge_pre_authorization_record_info = null;
            if (filled($charge_pre_authorization_record_result)) {
                switch ($charge_pre_authorization_record_result->payment_device) {
                    case PaymentDeviceEnum::Pos:
                        $payment_method = $charge_pre_authorization_record_result->pos_payment_method_name;
                        $payment_method_image = existsImage('icon', 'payment_methods/' . strtolower($charge_pre_authorization_record_result->pos_payment_method_name) . '.png');
                        break;
                    case PaymentDeviceEnum::Octopus:
                        $payment_method = $charge_pre_authorization_record_result->octopus_payment_method_name;
                        $payment_method_image = existsImage('icon', 'payment_methods/octopus.png');
                        break;
                    default:
                        // 目前只有POS
                        $payment_method = $charge_pre_authorization_record_result->pos_payment_method_name;
                        $payment_method_image = existsImage('icon', 'payment_methods/' . strtolower($charge_pre_authorization_record_result->pos_payment_method_name) . '.png');
                        break;
                }

                // 获取充电预授权记录
                $charge_pre_authorization_record_info = array(
                    'charge_pre_authorization_record_id' => $charge_pre_authorization_record_result->charge_pre_authorization_record_id,
                    'charge_pre_authorization_record_number' => $charge_pre_authorization_record_result->charge_pre_authorization_record_number,
                    'payment_device' => $charge_pre_authorization_record_result->payment_device, // 支付设备
                    'charge_pre_authorization_status' => ChargePreAuthorizationStatus::getDescription($charge_pre_authorization_record_result->charge_pre_authorization_status), // 充电预授权状态
                    'charge_pre_authorization_status_enum' => $charge_pre_authorization_record_result->charge_pre_authorization_status, // 充电预授权状态枚举
                    'gmt_charge_pre_authorization_status' => $charge_pre_authorization_record_result->gmt_charge_pre_authorization_status, // 充电预授权状态时间
                    'pre_authorization_amount' => (float)bcdiv($charge_pre_authorization_record_result->pre_authorization_amount, 100, 1), // 预授权金额
                    'deduct_amount' => (float)bcdiv($charge_pre_authorization_record_result->deduct_amount, 100, 1), // 扣款金额
                    'refund_amount' => filled($charge_pre_authorization_record_result->refund_amount) ? ((float)bcdiv($charge_pre_authorization_record_result->refund_amount, 100, 1)) : '—/—', // 退款金额
                    'actual_refund_amount' => filled($charge_pre_authorization_record_result->actual_refund_amount) ? ((float)bcdiv($charge_pre_authorization_record_result->actual_refund_amount, 100, 1)) : '—/—', // 实际退款金额
                    'identity_type' => IdentityType::getDescription($charge_pre_authorization_record_result->identity_type), // 身份类型
                    'is_has_refund_amount' => filled($charge_pre_authorization_record_result->refund_amount) && $charge_pre_authorization_record_result->refund_amount > 0, // 是否有退款金额
                    'is_has_actual_refund_amount' => filled($charge_pre_authorization_record_result->actual_refund_amount), // 是否有实际退款金额
                    'is_refund_amount_equal_actual_refund_amount' => $charge_pre_authorization_record_result->refund_amount === $charge_pre_authorization_record_result->actual_refund_amount, // 退款金额是否等于实际退款金额
                    'identity_number' => $charge_pre_authorization_record_result->identity_number, // 身份号码
                    'connector_number' => $charge_pre_authorization_record_result->connector_number, // 充电枪编号
                    'connector_name' => $charge_pre_authorization_record_result->connector_name, // 充电枪名称
                    'kiosk_number' => $charge_pre_authorization_record_result->kiosk_number, // Kiosk编号
                    'kiosk_name' => $charge_pre_authorization_record_result->kiosk_name, // Kiosk名称
                    'octopus_transaction_id' => $charge_pre_authorization_record_result->octopus_transaction_id, // 八达通交易ID
                    'octopus_receipt_number' => $charge_pre_authorization_record_result->octopus_receipt_number, // 八达通收据编号
                    'gmt_octopus_deduct' => $charge_pre_authorization_record_result->gmt_octopus_deduct, // 八达通扣款时间
                    'octopus_device_number' => $charge_pre_authorization_record_result->octopus_device_number, // 八达通设备编号
                    'octopus_card_type' => $charge_pre_authorization_record_result->octopus_card_type, // 八达通卡类型
                    'octopus_card_number' => $charge_pre_authorization_record_result->octopus_card_number, // 八达通卡号
                    'octopus_raw_card_number' => $charge_pre_authorization_record_result->octopus_raw_card_number, // 八达通原始卡号
                    'octopus_balance' => __('common.unit_hk') . (float)bcdiv($charge_pre_authorization_record_result->octopus_balance, 100, 1), // 八达通余额
                    'octopus_last_added_value_type' => $charge_pre_authorization_record_result->octopus_last_added_value_type, // 八达通上次充值类型
                    'octopus_last_added_value_date' => $charge_pre_authorization_record_result->octopus_last_added_value_date, // 八达通上次充值日期
                    'is_admin_octopus_card' => $charge_pre_authorization_record_result->is_admin_octopus_card, // 是否为管理员八达通
                    'is_free_octopus_card' => $charge_pre_authorization_record_result->is_free_octopus_card, // 是否为免费八达通
                    'pos_vendor' => $charge_pre_authorization_record_result->pos_vendor, // POS供应商
                    'pos_company_id' => $charge_pre_authorization_record_result->pos_company_id, // POS公司ID
                    'pos_pre_authorization_id' => $charge_pre_authorization_record_result->pos_pre_authorization_id, // POS预授权ID
                    'pos_transaction_id' => $charge_pre_authorization_record_result->pos_transaction_id, // POS交易ID
                    'payment_method' => $payment_method, // 支付方式
                    'payment_method_image' => ($payment_method_image ?: existsImage('icon', 'payment_methods/payment_method_placeholder.png')) ?: '', // 支付方式icon
                    'pos_card_number' => $charge_pre_authorization_record_result->pos_card_number, // POS卡号
                    'pos_trace_no' => $charge_pre_authorization_record_result->pos_trace_no, // POS追踪编号
                    'pos_reference_id' => $charge_pre_authorization_record_result->pos_reference_id, // POS参考编号
                    'gmt_pos_create' => $charge_pre_authorization_record_result->gmt_pos_create, // POS创建时间
                    'gmt_pos_deduct' => $charge_pre_authorization_record_result->gmt_pos_deduct, // POS扣款时间
                    'gmt_pos_cancel' => $charge_pre_authorization_record_result->gmt_pos_cancel, // POS取消时间
                    'status_log' => $charge_pre_authorization_record_result->status_log, // 状态日志
                    'remark' => $charge_pre_authorization_record_result->remark, // 备注
                    'gmt_create' => $charge_pre_authorization_record_result->gmt_create->toDateTimeString(), // 创建时间
                    'charge_pre_authorization_record_view_url' => action(
                        [ChargePreAuthorizationRecordController::class, 'view'],
                        [
                            'id' => $charge_pre_authorization_record_result->charge_pre_authorization_record_number,
                        ]
                    ),
                );

                // 卡片显示不同数据，PaymentMethod为POS就只显示POS相关的
                if ($charge_pre_authorization_record_result->payment_device === PaymentDeviceEnum::Pos) {
                    $charge_pre_authorization_record_info['pos_data'] = array(
                        'pos_vendor' => $charge_pre_authorization_record_result->pos_vendor ?? '—/—', // POS供应商
                        'pos_company_id' => $charge_pre_authorization_record_result->pos_company_id ?? '—/—', // POS公司ID
                        'pos_pre_authorization_id' => $charge_pre_authorization_record_result->pos_pre_authorization_id ?? '—/—', // POS预授权ID
                        'pos_transaction_id' => $charge_pre_authorization_record_result->pos_transaction_id ?? '—/—', // POS交易ID
                        'pos_payment_method_name' => $charge_pre_authorization_record_result->pos_payment_method_name ?? '—/—', // POS支付方式名称
                        'pos_card_number' => $charge_pre_authorization_record_result->pos_card_number ?? '—/—', // POS卡号
                        'pos_trace_no' => $charge_pre_authorization_record_result->pos_trace_no ?? '—/—', // POS追踪编号
                        'pos_reference_id' => $charge_pre_authorization_record_result->pos_reference_id ?? '—/—', // POS参考编号
                        'gmt_pos_create' => $charge_pre_authorization_record_result->gmt_pos_create ?? '—/—', // POS创建时间
                        'gmt_pos_deduct' => $charge_pre_authorization_record_result->gmt_pos_deduct ?? '—/—', // POS扣款时间
                        'gmt_pos_cancel' => $charge_pre_authorization_record_result->gmt_pos_cancel ?? '—/—', // POS取消时间
                    );
                } elseif ($charge_pre_authorization_record_result->payment_device === PaymentDeviceEnum::Octopus) {
                    $charge_pre_authorization_record_info['octopus_data'] = array(
                        'octopus_transaction_id' => $charge_pre_authorization_record_result->octopus_transaction_id ?? '—/—', // 八达通交易ID
                        'octopus_receipt_number' => $charge_pre_authorization_record_result->octopus_receipt_number ?? '—/—', // 八达通收据编号
                        'gmt_octopus_deduct' => $charge_pre_authorization_record_result->gmt_octopus_deduct ?? '—/—', // 八达通扣款时间
                        'octopus_device_number' => $charge_pre_authorization_record_result->octopus_device_number ?? '—/—', // 八达通设备编号
                        'octopus_card_type' => $charge_pre_authorization_record_result->octopus_card_type ?? '—/—', // 八达通卡类型
                        'octopus_card_number' => $charge_pre_authorization_record_result->octopus_card_number ?? '—/—', // 八达通卡号
                        'octopus_raw_card_number' => $charge_pre_authorization_record_result->octopus_raw_card_number ?? '—/—', // 八达通原始卡号
                        'octopus_balance' => __('common.unit_hk') . (float)bcdiv($charge_pre_authorization_record_result->octopus_balance, 100, 1) ?? '—/—', // 八达通余额
                        'octopus_last_added_value_type' => $charge_pre_authorization_record_result->octopus_last_added_value_type ?? '—/—', // 八达通上次充值类型
                        'octopus_last_added_value_date' => $charge_pre_authorization_record_result->octopus_last_added_value_date ?? '—/—', // 八达通上次充值日期
                        'is_admin_octopus_card' => $charge_pre_authorization_record_result->is_admin_octopus_card ?? '—/—', // 是否为管理员八达通
                        'is_free_octopus_card' => $charge_pre_authorization_record_result->is_free_octopus_card ?? '—/—', // 是否为免费八达通
                    );
                }

                // 获取充电预授权记录编号
                $charge_pre_authorization_record_number = $charge_pre_authorization_record_result->charge_pre_authorization_record_number;
                // 拆分充电预授权记录编号
                $charge_pre_authorization_record_number_list = explode('-', $charge_pre_authorization_record_number);
                $charge_pre_authorization_record_number = end($charge_pre_authorization_record_number_list);
                $charge_pre_authorization_record_info['charge_pre_authorization_record_number'] = $charge_pre_authorization_record_number;
            }

            $charge_payment_record_info['charge_pre_authorization_record_info'] = $charge_pre_authorization_record_info;


            // 获取积分交易记录
            $points_transaction_record_result = $charge_payment_record_result->pointsTransaction;
            $points_transaction_record_info = null;
            if (filled($points_transaction_record_result)) {
                $points_transaction_record_info = array(
                    'points_transaction_id' => $points_transaction_record_result->points_transaction_id, // 积分交易ID
                    'user_id' => $points_transaction_record_result->user_id, // 用户ID
                    'nickname' => $points_transaction_record_result->appUser?->nickname ?? $points_transaction_record_result->user_id, // 用户昵称
                    'avatar' => existsImage('avatar', $points_transaction_record_result->appUser?->avatar_url) ?: existsImage('icon', 'not_select_image.png'), // 用户头像
                    'transaction_number' => $points_transaction_record_result->transaction_number, // 交易编号
                    'transaction_type' => TransactionType::getDescription($points_transaction_record_result->transaction_type), // 交易类型
                    'transaction_category' => TransactionCategory::getDescription($points_transaction_record_result->transaction_category), // 交易类别
                    'amount' => (float)bcdiv($points_transaction_record_result->amount, 100, 1), // 金额
                    'points_balance' => (float)bcdiv($points_transaction_record_result->points_balance, 100, 1), // 积分余额
                    'gmt_create' => $points_transaction_record_result->gmt_create->toDateTimeString(), // 创建时间
                    'gmt_modified' => $points_transaction_record_result->gmt_modified->toDateTimeString(), // 修改时间
                );
            }

            $charge_payment_record_info['points_transaction_record_info'] = $points_transaction_record_info;

            $data['data'] = $charge_payment_record_info;
        }

        return view("pages.{$data['module_name']}.view", $data);
    }

    /**
     * 获取充电支付记录计算
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getChargePaymentRecordCalculation(Request $request): JsonResponse
    {
        $charge_payment_record_number = $request->input('charge_payment_record_number');
        $site_number_list = auth()->user()->site_number_list;
        $charge_payment_record_calculation_model = ChargePaymentRecord::where('charge_payment_record_number', $charge_payment_record_number)->when(!isSuperAdministrator(), function ($query) use ($site_number_list) {
            return $query->whereHas('chargeRecord', function ($charge_record_query) use ($site_number_list) {
                return $charge_record_query->whereIn('site_number', $site_number_list);
            });
        })->first()?->chargePaymentRecordCalculation;

        if (
            empty($charge_payment_record_number) ||
            !filled($charge_payment_record_calculation_model)
        ) {
            $this->notFoundData('Payment Record Calculation');
            return $this->returnJson();
        }

        $charge_payment_record_calculation_model->charge_value_amount_calculation_json = is_string($charge_payment_record_calculation_model->charge_value_amount_calculation_json) ? json_decode($charge_payment_record_calculation_model->charge_value_amount_calculation_json, true) : $charge_payment_record_calculation_model->charge_value_amount_calculation_json;
        $charge_payment_record_calculation_model->charge_value_amount_calculation_json = is_string($charge_payment_record_calculation_model->charge_value_amount_calculation_json) ? json_decode($charge_payment_record_calculation_model->charge_value_amount_calculation_json, true) : $charge_payment_record_calculation_model->charge_value_amount_calculation_json;
        $charge_payment_record_calculation_model->idling_penalty_amount_calculation_json = is_string($charge_payment_record_calculation_model->idling_penalty_amount_calculation_json) ? json_decode($charge_payment_record_calculation_model->idling_penalty_amount_calculation_json, true) : $charge_payment_record_calculation_model->idling_penalty_amount_calculation_json;
        $charge_payment_record_calculation_model->idling_penalty_amount_calculation_json = is_string($charge_payment_record_calculation_model->idling_penalty_amount_calculation_json) ? json_decode($charge_payment_record_calculation_model->idling_penalty_amount_calculation_json, true) : $charge_payment_record_calculation_model->idling_penalty_amount_calculation_json;

        $this->data = array(
            'charge_payment_record_calculation_id' => $charge_payment_record_calculation_model->charge_payment_record_calculation_id,
            'charge_payment_record_number' => $charge_payment_record_calculation_model->charge_payment_record_number, // 充电记录编号
            'charge_value_amount_calculation_json' => $charge_payment_record_calculation_model->charge_value_amount_calculation_json, // 充电量计算json
            'idling_penalty_amount_calculation_json' => $charge_payment_record_calculation_model->idling_penalty_amount_calculation_json, // 闲时罚款计算json
        );

        return $this->returnJson();
    }

    /**
     * 导出
     *
     * @param Request $request
     * @return void
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function excelExport(Request $request): void
    {
        ini_set('memory_limit', '-1');
        // 获取数据
        $search = $this->_searchList($request);
        $data_list = $search->get();
        $module_name = self::$module_name;

        $result = array();
        foreach ($data_list as $data) {
            $is_time = $data->charge_value_type === ChargeValueType::Time; // 时间
            $is_energy = $data->charge_value_type === ChargeValueType::Energy; // 电量
            $payment_method = match ($data->payment_device) {
                PaymentDeviceEnum::Pos => $data->pos_payment_method_name ?: ($data->payment_method ?: '—/—'),
                default => $data->payment_method ?: '—/—',
            };

            $result[] = array(
                'charge_payment_record_id' => $data->charge_payment_record_id, // 充电支付记录ID
                'receipt_number' => ($data->octopus_receipt_number ?? $data->pos_receipt_number) ?? '—/—', // 收据编号
                'connector_name' => $data->connector_name, // 充电枪名称
                'payment_method' => $payment_method, // 支付方式
                'total_amount' => bcdiv($data->total_amount, 100, 1), // 合共金额
                'gmt_start' => $data->gmt_power_on ?? $data->gmt_start, // 开始时间
                'gmt_stop' => $data->gmt_stop ?? '—/—', // 停止时间
                'charge_value_time' => $is_time ? (string)round($data->charge_value / 60) : 0, // 充电时间
                'charge_value_energy' => $is_energy ? bcdiv($data->charge_value, 1000, 3) : 0, // 充电电量
                'charge_record_number' => $data->charge_record_number, // 充电记录编号
                'charge_tariff_scheme' => ChargeTariffScheme::getDescription($data->charge_tariff_scheme), // 充电收费方案
                'charge_value_type' => ChargeValueType::getDescription($data->charge_value_type), // 充电量类型
                'charge_value_amount' => bcdiv($data->charge_value_amount, 100, 1), // 充电量金额
                'use_remain_charge_value_time' => $is_time ? (string)round($data->use_remain_charge_value / 60) : 0, // 使用剩余充电量
                'use_remain_charge_value_energy' => $is_energy ? bcdiv($data->use_remain_charge_value, 1000, 3) : 0, // 使用剩余充电量
                'idling_penalty_time' => (string)round($data->idling_penalty_time / 60), // 闲置罚款时间
                'idling_penalty_amount' => bcdiv($data->idling_penalty_amount, 100, 1), // 闲置金额
                'gmt_idling_penalty_start' => $data->gmt_idling_penalty_start ?? '—/—', // 闲置罚款开始时间
                'gmt_idling_penalty_stop' => $data->gmt_idling_penalty_stop ?? '—/—', // 闲置罚款结束时间
                'actual_payment_amount' => bcdiv($data->actual_payment_amount, 100, 1), // 实际付款金额
                'user_email' => $data->user?->email ?? ($data->user?->telephone ?? '—/—'), // 用户邮箱/电话
                'use_points' => bcdiv($data->use_points, 100, 1), // 使用积分
                'points_transaction_id' => $data->points_transaction_id ?? '—/—', // 积分交易ID
                'merchant_handling_fee' => bcdiv($data->merchant_handling_fee, 100, 2), // 商户手续费
                'merchant_receivable' => bcdiv($data->merchant_receivable, 100, 2), // 商户应收款
                'gmt_octopus_deduct' => $data->gmt_octopus_deduct ?? '—/—', // 八达通扣款时间
                'gmt_receipt_start' => $data->gmt_receipt_start ?? '—/—', // 收据开始时间
                'gmt_receipt_stop' => $data->gmt_receipt_stop ?? '—/—', // 收据结束时间
                'octopus_device_number' => $data->octopus_device_number ?? '—/—', // 八达通设备编号
                'octopus_card_number' => $data->octopus_card_number ?? '—/—', // 八达通卡号
                'octopus_raw_card_number' => $data->octopus_raw_card_number ?? '—/—', // 八达通原始卡号
                'octopus_card_type' => $data->octopus_card_type ?? '—/—', // 八达通卡类型
                'octopus_balance' => $data->octopus_balance ? bcdiv($data->octopus_balance, 100, 1) : 0, // 八达通余额
                'is_admin_octopus_card' => $data->is_admin_octopus_card, // 是否为管理员八达通
                'is_free_octopus_card' => $data->is_free_octopus_card, // 是否为免费八达通卡
                'octopus_last_added_value_type' => $data->octopus_last_added_value_type ?? '—/—', // 八达通上次充值类型
                'octopus_last_added_value_date' => filled($data->octopus_last_added_value_date) ? date('Y-m-d', strtotime($data->octopus_last_added_value_date)) : '—/—', // 八达通上次充值日期
                'gmt_pos_deduct' => $data->gmt_pos_deduct ?? '—/—', // POS扣款时间
                'pos_transaction_id' => $data->pos_transaction_id ?? '—/—', // POS交易ID
                'pos_payment_method_name' => $data->pos_payment_method_name ?? '—/—', // POS支付方式名称
                'pos_card_number' => $data->pos_card_number ?? '—/—', // POS卡号
                'pos_trace_no' => $data->pos_trace_no ?? '—/—', // POS追踪编号
                'pos_reference_id' => $data->pos_reference_id ?? '—/—', // POS参考编号
                'charge_pre_authorization_record_number' => $data->charge_pre_authorization_record_number ?? '—/—', // 充电预授权记录编号
                'print_receipt_count' => $data->print_receipt_count ?? '—/—', // 打印收据次数
                'gmt_last_print_receipt' => $data->gmt_last_print_receipt ?? '—/—', // 最后打印收据时间
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        // 先把语言赋值才可以在后面array出来表头，不然会输出未编译前的字符串
        $receipt_number = __("$module_name.receipt_number");
        $connector_name = __("$module_name.connector_name");
        $payment_method = __("$module_name.payment_method");
        $total_amount = __("$module_name.total_amount") . ' ( ' . __('common.unit_hk_blank') . ' )';
        $gmt_start = __("$module_name.gmt_start");
        $gmt_stop = __("$module_name.gmt_stop");
        $charge_value_time = __("$module_name.charge_value") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $charge_value_energy = __("$module_name.charge_value") . ' ( ' . __('common.unit_kwh_blank') . ' )';
        $charge_record_number = __("$module_name.charge_record_number");
        $charge_tariff_scheme = __("$module_name.charge_tariff_scheme");
        $charge_value_type = __("$module_name.charge_value_type");
        $charge_value_amount = __("$module_name.charge_value_amount") . ' ( ' . __('common.unit_hk_blank') . ' )';
        $use_remain_charge_value_time = __("$module_name.use_remain_charge_value") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $use_remain_charge_value_energy = __("$module_name.use_remain_charge_value") . ' ( ' . __('common.unit_kwh_blank') . ' )';
        $idling_penalty_time = __("$module_name.idling_penalty_time") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $idling_penalty_amount = __("$module_name.idling_penalty_amount") . ' ( ' . __('common.unit_hk_blank') . ' )';
        $gmt_idling_penalty_start = __("$module_name.gmt_idling_penalty_start");
        $gmt_idling_penalty_stop = __("$module_name.gmt_idling_penalty_stop");
        $actual_payment_amount = __("$module_name.actual_payment_amount") . ' ( ' . __('common.unit_hk_blank') . ' )';
        $user_email = __("$module_name.user");
        $use_points = __("$module_name.use_points") . ' ( ' . __('common.unit_points_blank') . ' )';
        $points_transaction_id = __("$module_name.points_transaction_id");
        $merchant_handling_fee = __("$module_name.merchant_handling_fee") . ' ( ' . __('common.unit_hk_blank') . ' )';
        $merchant_receivable = __("$module_name.merchant_receivable") . ' ( ' . __('common.unit_hk_blank') . ' )';
        $gmt_octopus_deduct = __("$module_name.gmt_octopus_deduct");
        $gmt_receipt_start = __("$module_name.gmt_receipt_start");
        $gmt_receipt_stop = __("$module_name.gmt_receipt_stop");
        $octopus_device_number = __("$module_name.octopus_device_number");
        $octopus_card_number = __("$module_name.octopus_card_number");
        $octopus_raw_card_number = __("$module_name.octopus_raw_card_number");
        $octopus_card_type = __("$module_name.octopus_card_type");
        $octopus_balance = __("$module_name.octopus_balance") . ' ( ' . __('common.unit_hk_blank') . ' )';
        $is_admin_octopus_card = __("$module_name.is_admin_octopus_card");
        $is_free_octopus_card = __("$module_name.is_free_octopus_card");
        $octopus_last_added_value_type = __("$module_name.octopus_last_added_value_type");
        $octopus_last_added_value_date = __("$module_name.octopus_last_added_value_date");
        $gmt_pos_deduct = __("$module_name.gmt_pos_deduct");
        $pos_transaction_id = __("$module_name.pos_transaction_id");
        $pos_payment_method_name = __("$module_name.pos_payment_method_name");
        $pos_card_number = __("$module_name.pos_card_number");
        $pos_trace_no = __("$module_name.pos_trace_no");
        $pos_reference_id = __("$module_name.pos_reference_id");
        $charge_pre_authorization_record_number = __("$module_name.charge_pre_authorization_record_number");
        $print_receipt_count = __("$module_name.print_receipt_count");
        $gmt_last_print_receipt = __("$module_name.gmt_last_print_receipt");
        $gmt_create = __("$module_name.gmt_create");
        $web_title = __("$module_name.web_title");

        $remove_column_list = [];
        switch (env('CURRENT_PROJECT_NAME')) {
            default:
                // 需要移除的列
                $remove_column_list = [
                    $charge_value_energy,
                    $use_remain_charge_value_energy,
                ];
                break;
        }

        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet(); //实例化 Spreadsheet 对象
        $worksheet = $spreadsheet->getActiveSheet();

        // 工作簿名称为 "Usage Report"
        $worksheet->setTitle($web_title);

        $styleArray = [
            'font' => [
                'bold' => false
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
        ];

        // 计算数组长度用于动态补充表格样式
        $len = count($result);
        // 因为前面两格表头样式占了一行，所以要加1用于设置单元格样式
        $total_rows = $len + 1;
        $styleArrayBody = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['argb' => '666666'],
                ],
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
        ];

        // 指定表头字段
        $excel_data = array(
            array(
                $receipt_number,
                $connector_name,
                $payment_method,
                $total_amount,
                $gmt_start,
                $gmt_stop,
                $charge_value_time,
                $charge_value_energy,
                $charge_record_number,
                $charge_tariff_scheme,
                $charge_value_type,
                $charge_value_amount,
                $use_remain_charge_value_time,
                $use_remain_charge_value_energy,
                $idling_penalty_time,
                $idling_penalty_amount,
                $gmt_idling_penalty_start,
                $gmt_idling_penalty_stop,
                $actual_payment_amount,
                $user_email,
                $use_points,
                $points_transaction_id,
                $merchant_handling_fee,
                $merchant_receivable,
                $gmt_octopus_deduct,
                $gmt_receipt_start,
                $gmt_receipt_stop,
                $octopus_device_number,
                $octopus_card_number,
                $octopus_raw_card_number,
                $octopus_card_type,
                $octopus_balance,
                $is_admin_octopus_card,
                $is_free_octopus_card,
                $octopus_last_added_value_type,
                $octopus_last_added_value_date,
                $gmt_pos_deduct,
                $pos_transaction_id,
                $pos_payment_method_name,
                $pos_card_number,
                $pos_trace_no,
                $pos_reference_id,
                $charge_pre_authorization_record_number,
                $print_receipt_count,
                $gmt_last_print_receipt,
                $gmt_create,
            )
        );

        // 移除指定列
        if (filled($remove_column_list)) $excel_data[0] = array_diff($excel_data[0], $remove_column_list);
        //设置单元格样式
        // 将数组长度转换为 Excel 列名
        $last_column = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex(count($excel_data[0]));
        $worksheet->getStyle("A1:{$last_column}$total_rows")->applyFromArray($styleArray)->getFont()->setName('Times New Roman')->setSize(11);
        $worksheet->getStyle("A1:{$last_column}1")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('c0c0c0');
        $worksheet->getStyle("A1:{$last_column}$total_rows")->applyFromArray($styleArrayBody);

        foreach ($result as $report) {
            $excel_row = array(
                $receipt_number => $report['receipt_number'],
                $connector_name => $report['connector_name'],
                $payment_method => $report['payment_method'],
                $total_amount => $report['total_amount'],
                $gmt_start => $report['gmt_start'],
                $gmt_stop => $report['gmt_stop'],
                $charge_value_time => $report['charge_value_time'],
                $charge_value_energy => $report['charge_value_energy'],
                $charge_record_number => $report['charge_record_number'],
                $charge_tariff_scheme => $report['charge_tariff_scheme'],
                $charge_value_type => $report['charge_value_type'],
                $charge_value_amount => $report['charge_value_amount'],
                $use_remain_charge_value_time => $report['use_remain_charge_value_time'],
                $use_remain_charge_value_energy => $report['use_remain_charge_value_energy'],
                $idling_penalty_time => $report['idling_penalty_time'],
                $idling_penalty_amount => $report['idling_penalty_amount'],
                $gmt_idling_penalty_start => $report['gmt_idling_penalty_start'],
                $gmt_idling_penalty_stop => $report['gmt_idling_penalty_stop'],
                $actual_payment_amount => $report['actual_payment_amount'],
                $user_email => $report['user_email'],
                $use_points => $report['use_points'],
                $points_transaction_id => $report['points_transaction_id'],
                $merchant_handling_fee => $report['merchant_handling_fee'],
                $merchant_receivable => $report['merchant_receivable'],
                $gmt_octopus_deduct => $report['gmt_octopus_deduct'],
                $gmt_receipt_start => $report['gmt_receipt_start'],
                $gmt_receipt_stop => $report['gmt_receipt_stop'],
                $octopus_device_number => $report['octopus_device_number'],
                $octopus_card_number => $report['octopus_card_number'],
                $octopus_raw_card_number => $report['octopus_raw_card_number'],
                $octopus_card_type => $report['octopus_card_type'],
                $octopus_balance => $report['octopus_balance'],
                $is_admin_octopus_card => ($report['is_admin_octopus_card']) ? '✔' : '✘',
                $is_free_octopus_card => ($report['is_free_octopus_card']) ? '✔' : '✘',
                $octopus_last_added_value_type => (string)$report['octopus_last_added_value_type'],
                $octopus_last_added_value_date => $report['octopus_last_added_value_date'],
                $gmt_pos_deduct => $report['gmt_pos_deduct'],
                $pos_transaction_id => $report['pos_transaction_id'],
                $pos_payment_method_name => $report['pos_payment_method_name'],
                $pos_card_number => $report['pos_card_number'],
                $pos_trace_no => $report['pos_trace_no'],
                $pos_reference_id => $report['pos_reference_id'],
                $charge_pre_authorization_record_number => $report['charge_pre_authorization_record_number'],
                $print_receipt_count => $report['print_receipt_count'],
                $gmt_last_print_receipt => $report['gmt_last_print_receipt'],
                $gmt_create => $report['gmt_create'],
            );
            // 移除指定列
            if (filled($remove_column_list)) {
                foreach ($remove_column_list as $remove_column) {
                    unset($excel_row[$remove_column]);
                }
            }
            $excel_data[] = $excel_row;
        }

        $worksheet->fromArray($excel_data);
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $file_name = $web_title . ' ' . date('Y-m-d H:i:s') . '.xlsx';
        //设置单元格的宽度
        foreach ($spreadsheet->getActiveSheet()->getColumnIterator() as $column) {
            $spreadsheet->getActiveSheet()->getColumnDimension($column->getColumnIndex())->setAutoSize(true);
        }

        // 客户端文件下载
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $file_name . '"');
        header('Cache-Control: max-age=0');
        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
        exit;
    }

    /**
     * 搜索数据
     *
     * @param Request $request
     * @return mixed
     */
    protected function _searchList(Request $request): mixed
    {
        // dataTable字段
        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $payment_method = $request->input('payment_method_search');
        $charge_tariff_scheme = $request->input('charge_tariff_scheme_search');
        $charge_value_type = $request->input('charge_value_type_search');
        $charge_start_time = $request->input('charge_start_time_search');

        $where = array();
        $charge_start_time_search_list = self::getRangeDateTimeArray($charge_start_time ?: '') ?: null;

        if ($order == 'charge_tariff_scheme') $order = 'charge_record.charge_tariff_scheme';
        if ($order == 'charge_record_number') $order = 'charge_payment_record.charge_record_number';
        if ($order == 'gmt_start') $order = 'case when charge_record.gmt_power_on is null then charge_record.gmt_start else charge_record.gmt_power_on end';
        if ($order == 'gmt_create') $order = 'charge_payment_record.gmt_create';
        if ($order == 'gmt_modified') $order = 'charge_payment_record.gmt_modified';

        if ($order == 'receipt_number') {
            $sort_order = "CASE WHEN octopus_receipt_number IS NULL THEN pos_receipt_number ELSE octopus_receipt_number END $sort";
        } else {
            $sort_order = "$order $sort";
        }

        if (filled($payment_method)) {
            // 如果是导出查询的话，参数会以字符串参数，所以如果是字符串需要转成数组
            if (is_string($payment_method) && filled($payment_method_list = explode(',', $payment_method))) {
                $payment_method = $payment_method_list;
            }
            // 将支付方式全转成小写
            $lower_payment_method = array_map(fn ($item) => strtolower($item), $payment_method);
            $raw = 'CASE charge_payment_record.payment_device WHEN "' .
                PaymentDeviceEnum::Octopus .
                '" THEN charge_payment_record.payment_method IN (' . "'" . implode("','", $payment_method) . "'" . ') WHEN "' .
                PaymentDeviceEnum::Pos .
                '" THEN LOWER(IFNULL(charge_payment_record.pos_payment_method_name, charge_payment_record.payment_method)) IN (' . "'" . implode("','", $lower_payment_method) . "'" . ') END';
            $where[] = [$raw];
        }
        if (filled($charge_tariff_scheme)) {
            $where[] = ["charge_payment_record.charge_tariff_scheme = '$charge_tariff_scheme'"];
        }
        if (filled($charge_value_type)) {
            $where[] = ["charge_payment_record.charge_value_type = '$charge_value_type'"];
        }
        if (filled($charge_start_time_search_list)) {
            $where[] = ["IFNULL(charge_record.gmt_power_on,charge_record.gmt_start) >= '$charge_start_time_search_list[0]'"];
            $where[] = ["IFNULL(charge_record.gmt_power_on,charge_record.gmt_start) <= '$charge_start_time_search_list[1]'"];
        }

        $condition_list = null;
        foreach ($where as $condition) { // 拼接查询条件，用于在whereRaw执行，因为上面查询条件需要用到IFNULL函数
            if (blank($condition_list)) {
                $condition_list = $condition[0];
            } else {
                $condition_list = $condition_list . " AND " . $condition[0];
            }
        }

        // 不查询json字段
        $charge_payment_record = ChargePaymentRecord::with(['user'])
            ->select('charge_payment_record.*', 'charge_record.gmt_start as gmt_start', 'charge_record.gmt_power_on as gmt_power_on', 'charge_record.gmt_stop as gmt_stop', 'charge_record.connector_name as connector_name')
            ->leftJoin('charge_record', 'charge_payment_record.charge_record_number', '=', 'charge_record.charge_record_number')
            ->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('charge_payment_record.site_number', auth()->user()->site_number_list));
        if (filled($condition_list)) {
            $charge_payment_record = $charge_payment_record->whereRaw("$condition_list");
        }

        return $charge_payment_record->orderByRaw($sort_order)
            ->latest('charge_payment_record.gmt_create');
    }

    /**
     * 返回地址栏参数
     *
     * @param Request $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(Request $request): array
    {
        return array(
            'is_page' => 'true',
            'payment_method_search' => $request->get('payment_method_search'),
            'charge_tariff_scheme_search' => $request->get('charge_tariff_scheme_search'),
            'charge_value_type_search' => $request->get('charge_value_type_search'),
            'charge_start_time_search' => $request->get('charge_start_time_search'),
        );
    }
}

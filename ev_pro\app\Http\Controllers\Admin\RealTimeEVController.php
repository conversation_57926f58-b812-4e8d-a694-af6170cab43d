<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Foundation\Application;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Enums\{
    ConnectorStatus,
};
use App\Models\Modules\{
    Connector,
    ChargePoint,
};

class RealTimeEVController extends CommonController
{
    protected static string $module_name = 'realTimeEV'; // 模块名称

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'charge_point_search' => $request->input('charge_point_search'),
            'connector_number' => $request->input('connector_number_search'),
            'status' => $request->input('status_search'),
        );

        $data['charge_point_list'] = array();
        $charge_point_model = ChargePoint::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->oldest('sort_order')
            ->get();
        foreach ($charge_point_model as $charge_point) {
            $data['charge_point_list'][] = array(
                'value' => $charge_point->charge_point_number,
                'name' => $charge_point->name,
            );
        }

        $data['status_list'] = array();
        foreach (ConnectorStatus::asSelectArray() as $value => $name) {
            $data['status_list'][] = array(
                'name' => $name,
                'value' => $value,
            );
        }
        return view("pages.{$data['module_name']}", $data);
    }

    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'sort_order');
        $sort = $request->input('sort', 'asc');
        $length = (int)$request->input('length', 10);
        $charge_point_search = $request->input('charge_point_search');
        $connector_number_search = $request->input('connector_number_search');
        $status_search = $request->input('status_search');

        $where = array();

        if ($order == 'connector_setting_name') {
            $order = 'connector_setting.name';
        }
        if ($order == 'charge_point_name') {
            $order = 'charge_point.name';
        }
        if ($order == 'current_charge_record_number') {
            $order = 'connector.current_charge_record_number';
        }

        if (filled($charge_point_search)) $where[] = ['connector.charge_point_number', '=', $charge_point_search];
        if (filled($connector_number_search)) $where[] = ['connector.connector_number', 'like', "%$connector_number_search%"];
        if (filled($status_search)) $where[] = ['connector.status', '=', $status_search];

        $data_list = Connector::query()
            ->select('connector.*', 'connector_setting.name as connector_setting_name_init', 'charge_point.name as charge_point_name_init', 'charge_record.meter_value_voltage', 'charge_record.meter_value_current', 'charge_record.meter_value_power', 'charge_record.meter_value_power_factor', 'charge_record.meter_value_power_offered', 'charge_record.meter_value_start_vehicle_soc', 'charge_record.meter_value_vehicle_soc', 'charge_record.meter_value_temperature', 'charge_record.meter_value_fan_speed', 'charge_record.gmt_start', 'charge_record.gmt_stop', 'charge_record.gmt_power_on', 'charge_record.meter_start')
            ->leftJoin('connector_setting', 'connector.connector_setting_number', '=', 'connector_setting.connector_setting_number')
            ->leftJoin('charge_point', 'connector.charge_point_number', '=', 'charge_point.charge_point_number')
            ->leftJoin('charge_record', 'connector.current_charge_record_number', '=', 'charge_record.charge_record_number')
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('connector.site_number', auth()->user()->site_number_list))
            ->where($where)
            ->orderByRaw('CASE status
                WHEN "' . ConnectorStatus::Preparing . '" THEN 1
                WHEN "' . ConnectorStatus::Charging . '" THEN 2
                WHEN "' . ConnectorStatus::Queuing . '" THEN 3
                WHEN "' . ConnectorStatus::Finishing . '" THEN 4
                WHEN "' . ConnectorStatus::Available . '" THEN 5
                WHEN "' . ConnectorStatus::Unavailable . '" THEN 6
                WHEN "' . ConnectorStatus::Faulted . '" THEN 7
                WHEN "' . ConnectorStatus::Offline . '" THEN 8
                ELSE 9
            END')
            ->orderBy($order, $sort)
            ->latest('gmt_modified')
            ->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $status = $data->status;
            // 是否存在current_charge_record_number
            $flag = empty($data->current_charge_record_number);
            // 充電開始時間
            $gmt_start = $flag ? null : ($data->gmt_power_on ?? $data->gmt_start);
            // 充電時長
            $charging_duration = null;
            if (!$flag) {
                $gmt_start_long = 0;
                if (!empty($gmt_start)) $gmt_start_long = strtotime($gmt_start);
                $gmt_stop_long = empty($data->gmt_stop) ? time() : strtotime($data->gmt_stop);
                $charging_duration = ceil(($gmt_stop_long - $gmt_start_long) / 60);
            }

            // 備註
            $remark = $flag ? null : $data->remark;

            $result[] = array(
                'connector_id' => $data->connector_id, // 充电枪ID
                'charge_point_name' => $data->charge_point_name_init ?? '—/—', // 充电桩
                'connector_number' => $data->connector_number, // 充电枪编号
                'name' => $data->name, // 显示名称
                'connector_setting_name' => $data->connector_setting_name_init ?? '—/—', // 充电枪设置名称
                'connector_setting_number' => $data->connector_setting_number, // 充电枪设置编号
                'maximum_current' => $data->maximum_current ? (float)(bcdiv($data->maximum_current, 100, 2)) . __('common.unit_ampere') : '—/—', // 最大电流
                'is_fixed_maximum_current' => $data->is_fixed_maximum_current ?? '—/—', // 是否固定最大电流
                'safety_current' => $data->safety_current ? (float)(bcdiv($data->safety_current, 100, 2)) . __('common.unit_ampere') : '—/—', // 安全电流
                'status_source' => $status, // 状态(數據庫顯示的)
                'status' => ConnectorStatus::getDescription($status), // 状态(格式化後)
                'gmt_status' => $data->gmt_status ?? '—/—', // 状态时间
                'error_code' => $data->error_code ?? '—/—', // 错误码
                'info' => $data->info ?? '—/—', // 信息
                'queue_position' => $data->queue_position ?? '—/—', // 队列位置
                'current_charge_record_number' => $data->current_charge_record_number ?? '—/—', // 当前充电记录编号
                'current_voltage' => $data->current_voltage ? (float)(bcdiv($data->current_voltage ?? 0, 100, 2)) . __('common.unit_voltage') : '—/—', // 当前電壓
                'current_current' => $data->current_current ? (float)(bcdiv($data->current_current ?? 0, 100, 2)) . __('common.unit_ampere') : '—/—', // 当前電流
                'current_power' => $data->current_power ? (float)(bcdiv($data->current_power ?? 0, 1000, 3)) . __('common.unit_kw') : '—/—', // 当前功率
                'sort_order' => $data->sort_order, // 排序
                'remark' => $remark ?? '—/—', // 备注
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
                'gmt_start' => $gmt_start ?? '—/—', // 充電開始
                'charging_duration' => $charging_duration ?? '—/—', // 充電時長
                'meter_value_voltage' => $data->meter_value_voltage ?? '—/—', // 仪表值-电压
                'meter_value_current' => $data->meter_value_current ?? '—/—', // 仪表值-电流
                'meter_value_power' => $data->meter_value_power ?? '—/—', // 仪表值-功率
                'meter_value_power_factor' => $data->meter_value_power_factor ?? '—/—', // 仪表值-功率因数
                'meter_value_power_offered' => $data->meter_value_power_offered ?? '—/—', // 仪表值-最大功率
                'meter_value_start_vehicle_soc' => $data->meter_value_start_vehicle_soc ?? '—/—', // 仪表值-开始车辆电量
                'meter_value_vehicle_soc' => $data->meter_value_vehicle_soc ?? '—/—', // 仪表值-车辆电量
                'meter_value_temperature' => $data->meter_value_temperature ?? '—/—', // 仪表值-温度
                'meter_value_fan_speed' => $data->meter_value_fan_speed ?? '—/—', // 仪表值-风扇速度
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );

        return response()->json($json);
    }

    // ??判断字符串null为true
    public function checkNull($str, $default_val)
    {
        return filled($str) && 'null' != strtolower($str) ? $str : $default_val;
    }

    public function getStatus(Request $request, $id): JsonResponse
    {
        if (filled($id)) {
            $model = Connector::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->find($id);
            if (filled($model)) {
                $this->data = array(
                    'status' => ConnectorStatus::getDescription($model->status),
                    'gmt_status' => $model->gmt_status ?? '—/—',
                    'error_code' => $model->error_code ?? '—/—',
                    'info' => $model->info ?? '—/—',
                    'vendor_error_code' => $model->vendor_error_code ?? '—/—',
                );
            } else {
                $this->notFoundData('ID');
            }
        } else {
            $this->notFoundData('ID');
        }

        return $this->returnJson();
    }

    /**
     * 返回地址栏参数
     *
     * @param Request $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(Request $request): array
    {
        return array(
            'is_page' => 'true',
            'charge_point_search' => $request->get('charge_point_search'),
            'connector_number_search' => $request->get('connector_number_search'),
            'status_search' => $request->get('status_search'),
        );
    }
}

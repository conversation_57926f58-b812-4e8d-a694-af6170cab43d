<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;

class FreeOctopusCard extends Model
{

    protected $table = 'free_octopus_card';
    protected $primaryKey = 'free_octopus_card_id';

    const CREATED_AT = 'gmt_create'; //默认是小写的模型类名复数格式，此处为自定义表名
    const UPDATED_AT = 'gmt_modified'; //定义主键，默认为id

    // protected $fillable = [];
    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 类型转化器
     */
    protected $casts = [
        'is_enable' => 'bool',
    ];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [
        'is_enable' => true,
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];
}

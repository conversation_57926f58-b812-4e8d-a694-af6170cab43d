<?php

namespace App\Enums;

use BenSampo\Enum\Contracts\LocalizedEnum;
use BenSampo\Enum\Enum;
use ReflectionClass;

// ERP 支付方式 专用
final class PaymentMethodEnum extends Enum implements LocalizedEnum
{
    const VISA = 1;
    const VISA_OVERSEAS = 15;

    const MASTER = 2;
    const MASTER_OVERSEAS = 16;

//    const AE = 3;
//
//    const UNIONPAY = 4;

    const ALIPAY_HK = 5;

    const ALIPAY_CHINA = 6;

    const WECHAT_HK = 7;

    const WECHAT_CHINA = 8;

    // const APPLE_PAY = 9;

    // const GOOGLE_PAY = 10;

    const OCTOPUS = 11;

//    const FPS = 12;// 转数快

    // 根据传入的key获取对应的值
    public static function getValue(string $key): int
    {
        return match ($key) {
            'VISA' => self::VISA,
            'VISA_OVERSEAS' => self::VISA_OVERSEAS,
            'MASTER', 'Mastercard' => self::MASTER,
            'MASTER_OVERSEAS' => self::MASTER_OVERSEAS,
//            'AE' => self::AE,
//            'UNIONPAY' => self::UNIONPAY,
            'ALIPAY', 'Alipay', 'ALIPAY_HK' => self::ALIPAY_HK,
            'WECHAT', 'WechatPay', 'Wechatpay', 'WECHAT_HK' => self::WECHAT_HK,
            // 'APPLE_PAY' => self::APPLE_PAY,
            // 'GOOGLE_PAY' => self::GOOGLE_PAY,
            'OCTOPUS', 'Octopus' => self::OCTOPUS,
//            'FPS' => self::FPS,
            default => null, // 如果找不到匹配的字符串，返回 null
        };
    }

    // 获取所有k:v
    public static function getAllMethods(): array
    {
        $reflection = new ReflectionClass(__CLASS__);
        return $reflection->getConstants();
    }

    // 获取所有v
    public static function getAllValues(): array
    {
        $reflection = new ReflectionClass(__CLASS__);
        return array_values($reflection->getConstants());
    }

}

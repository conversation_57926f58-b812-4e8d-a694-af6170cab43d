<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;

class ConnectorSettingTrialChargeRule extends Model
{
    protected $table = 'connector_setting_trial_charge_rule'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'connector_setting_trial_charge_rule_id'; //定义主键，默认为id
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    // protected $fillable = [];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 类型转化器
     */
    protected $casts = [

    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * 一对一充电枪记录
     */
    public function connectorSetting()
    {
        return $this->hasOne(ConnectorSetting::class, 'connector_setting_number', 'connector_setting_number');
    }
}

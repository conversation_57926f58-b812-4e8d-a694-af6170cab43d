<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use App\Models\Modules\EventLog;
use App\Enums\{
    EventTypeEnum
};
use Illuminate\Support\Facades\DB;
use Illuminate\Contracts\View\{
    Factory,
    View
};
use Illuminate\Foundation\Application;
use Illuminate\Http\{
    JsonResponse,
    Request
};

class ErrorCodeReportPartBController extends CommonController
{
    protected static string $module_name = 'errorCodeReportPartB'; // 模块名称

    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'error_number_search' => $request->input('error_number_search'),
            'error_name_search' => $request->input('error_name_search'),
        );

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'error_number');
        $sort = $request->input('sort', 'asc');
        $length = (int)$request->input('length', 10);
        $error_number_search = $request->input('error_number_search');
        $error_name_search = $request->input('error_name_search');

        $where = array();
        $result = array();

        // 因为period是拼接gmt_stop和gmt_unlocked的时间，所以排序以gmt_stop时间去排序
        if ($order == 'period') {
            $order = 'stop_time';
        }

        if (filled($error_name_search)) {
            $where[] = ['target_name', 'like', "%$error_name_search%"];
        }
        if (filled($error_number_search)) {
            $where[] = ['event_code', 'like', "%$error_number_search%"];
        }

        $data_list = EventLog::query()
            ->leftJoin('charge_record', 'event_log.charge_record_number', 'charge_record.charge_record_number')
            ->select(
                DB::raw('event_code AS error_number'),
                DB::raw('target_name AS error_name'),
                DB::raw('event_type AS error_type'),
                DB::raw('description AS occurrence_in_the_period'),
                DB::raw('DATE_FORMAT(charge_record.gmt_stop,"%Y-%m-%d") AS stop_time'),
                DB::raw('DATE_FORMAT(charge_record.gmt_unlocked,"%Y-%m-%d") AS unlocked_time')
            )
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('charge_record.site_number', auth()->user()->site_number_list))
            ->where($where);

        $data_list = $data_list
            ->orderBy($order, $sort)
            ->paginate($length, ['*'], 'start');

        foreach ($data_list as $data) {
            // 如果还没有解锁，获取不到解锁时间即为—/—
            if (blank($data->unlocked_time)) {
                $data->unlocked_time = '—/—';
            }
            if (blank($data->stop_time)) {
                $data->stop_time = '—/—';
            }
            $result[] = array(
                'error_number' => $data->error_number ?? '—/—',
                'error_name' => $data->error_name ?? '—/—',
                'error_type' => EventTypeEnum::getDescription($data->error_type, $data->error_type),
                'occurrence_in_the_period' => filled($data->occurrence_in_the_period) ? $data->occurrence_in_the_period : '—/—',
                'period' => $data->stop_time . ' — ' . $data->unlocked_time,
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );

        return response()->json($json);
    }


    // 导出功能
    public function excelExport(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        // dataTable字段
        $order = $request->input('order', 'error_number');
        $sort = $request->input('sort', 'asc');
        $error_number_search = $request->input('error_number_search');
        $error_name_search = $request->input('error_name_search');

        $where = array();
        $result = array();

        if (filled($error_name_search)) {
            $where[] = ['target_name', 'like', "%$error_name_search%"];
        }
        if (filled($error_number_search)) {
            $where[] = ['event_code', 'like', "%$error_number_search%"];
        }

        $data_list = EventLog::query()
            ->leftJoin('charge_record', 'event_log.charge_record_number', 'charge_record.charge_record_number')
            ->select(
                DB::raw('event_code AS error_number'),
                DB::raw('target_name AS error_name'),
                DB::raw('event_type AS error_type'),
                DB::raw('description AS occurrence_in_the_period'),
                DB::raw('DATE_FORMAT(charge_record.gmt_stop,"%Y-%m-%d") AS stop_time'),
                DB::raw('DATE_FORMAT(charge_record.gmt_unlocked,"%Y-%m-%d") AS unlocked_time')
            )
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('charge_record.site_number', auth()->user()->site_number_list))
            ->where($where)
            ->orderBy($order, $sort);

        $data_list = $data_list->get();
        foreach ($data_list as $data) {
            // 如果还没有解锁，获取不到解锁时间即为—/—
            if (!filled($data->unlocked_time)) {
                $data->unlocked_time = '—/—';
            }
            if (!filled($data->stop_time)) {
                $data->stop_time = '—/—';
            }
            $result[] = array(
                'error_number' => $data->error_number ?? '—/—',
                'error_name' => $data->error_name ?? '—/—',
                'error_type' => EventTypeEnum::getDescription($data->error_type, $data->error_type),
                'occurrence_in_the_period' => filled($data->occurrence_in_the_period) ? $data->occurrence_in_the_period : '—/—',
                'period' => $data->stop_time . ' — ' . $data->unlocked_time
            );
        };

        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet(); //实例化 Spreadsheet 对象
        $worksheet = $spreadsheet->getActiveSheet();

        $web_title = __("$module_name.web_title");
        $excel_title = array(
            __("$module_name.web_title"),
            __("$module_name.error_number"),
            __("$module_name.error_name"),
            __("$module_name.error_type"),
            __("$module_name.occurrence_in_the_period"),
            __("$module_name.period")
        );

        // 工作簿名称为 "Error Code Report Part B"
        $worksheet->setTitle($web_title);

        // 设置表头名
        $worksheet->setCellValueByColumnAndRow(1, 1, $excel_title[0]);
        $worksheet->setCellValueByColumnAndRow(1, 2, $excel_title[1]);
        $worksheet->setCellValueByColumnAndRow(2, 2, $excel_title[2]);
        $worksheet->setCellValueByColumnAndRow(3, 2, $excel_title[3]);
        $worksheet->setCellValueByColumnAndRow(4, 2, $excel_title[4]);
        $worksheet->setCellValueByColumnAndRow(5, 2, $excel_title[5]);
        $worksheet->mergeCells('A1:E1');
        $styleArray = [
            'font' => [
                'bold' => false
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['argb' => '666666'],
                ],
            ],
        ];

        // 计算数组长度便于后面遍历
        $len = count($result);

        // 因为前面两格表头样式占了两行，所以要加2用于设置单元格样式
        $total_rows = $len + 2;

        //设置单元格样式
        $worksheet->getStyle("A1:E$total_rows")->applyFromArray($styleArray);
        $worksheet->getStyle('A1:E2')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('c0c0c0');

        $index = 0;
        foreach ($result as $item) {
            $line = $index + 3; //从表格第3行开始
            $worksheet->setCellValueByColumnAndRow(1, $line, $item['error_number']);
            $worksheet->setCellValueByColumnAndRow(2, $line, $item['error_name']);
            $worksheet->setCellValueByColumnAndRow(3, $line, $item['error_type']);
            $worksheet->setCellValueByColumnAndRow(4, $line, $item['occurrence_in_the_period']);
            $worksheet->setCellValueByColumnAndRow(5, $line, $item['period']);
            $index++;
        }
        $worksheet->getColumnDimension('A')->setWidth(15);
        $worksheet->getColumnDimension('B')->setWidth(15);
        $worksheet->getColumnDimension('C')->setWidth(50);
        $worksheet->getColumnDimension('D')->setWidth(50);
        $worksheet->getColumnDimension('E')->setWidth(50);

        // $width_value = 15;
        // foreach (range('A', 'E') as $index => $value) {
        //     if ($index > 1) $width_value = 50;
        //     $worksheet->getColumnDimension($value)->setWidth($width_value);
        // }

        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $file_name = 'Error Code Report Part B' . date('Y-m-d H:i:s') . '.xlsx';

        // 客户端文件下载
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $file_name . '"');
        header('Cache-Control: max-age=0');
        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
        exit;
    }
}

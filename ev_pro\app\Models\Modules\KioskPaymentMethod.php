<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;

class KioskPaymentMethod extends Model
{
    protected $table = 'kiosk_payment_method'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'kiosk_payment_method_id'; //定义主键，默认为id
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    // protected $fillable = [];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'sort_order' => 0, // 排序
        'is_enable' => false, // 是否启用
    ];

    /**
     * 类型转化器
     */
    protected $casts = [
        'is_enable' => 'bool',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];
}

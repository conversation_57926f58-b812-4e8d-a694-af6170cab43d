<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Redis;
use Illuminate\Http\{
    JsonResponse,
    RedirectResponse,
    Request,
};
use App\Http\Controllers\Admin\Traits\{
    Edit,
};
use Illuminate\Foundation\Application;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use App\Models\Modules\{ConnectorSetting, EnergyTariffTable, Merchant, MemberCardGroup, Site, UserGroup};
use App\Enums\{
    IdentityType,
};

class EnergyTariffTableController extends CommonController
{
    use Edit;

    protected static string $module_name = 'energyTariffTable'; // 模块名称
    protected static bool $module_check_site = true; // 标记该模块校验场地
    protected static string $default_group = 'default';
    protected static string $member_card_group = 'member_card_group';
    protected static string $user_group = 'user_group';

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new EnergyTariffTable;
    }

    /**
     * 初始页
     *
     * @param Request $request
     * @return View|Application|Factory
     */
    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'add_url' => action([self::class, 'add']),
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'name_search' => $request->get('name_search'),
            'site_search' => $request->get('site_search'),
        );

        // 场地下拉列表
        $data['site_list'] = $this->getSiteOptionList();

        return view("pages.{$data['module_name']}.list", $data);
    }

    /**
     * 列表接口
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $name_search = $request->input('name_search');
        $site_search = $request->input('site_search');
        $length = (int)$request->input('length', 10);

        if ($order == 'site_name') $order = 'site.name_json';

        $data_list = EnergyTariffTable::select('energy_tariff_table.*', 'site.name_json as site_name_json_init')
            ->leftJoin('site', 'energy_tariff_table.site_number', '=', 'site.site_number')
            ->when(filled($name_search), fn($query) => $query->where('energy_tariff_table.name', 'like', "%$name_search%"))
            ->when(filled($site_search), fn($query) => $query->where('energy_tariff_table.site_number', $site_search))
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('energy_tariff_table.site_number', auth()->user()->site_number_list))
            ->orderBy($order, $sort)
            ->latest('gmt_modified')
            ->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $post_paid_identity_type_list = $data->post_paid_identity_type_list ?? '';
            // 将，分隔的字符串转为数组
            $post_paid_identity_type_list = explode(',', $post_paid_identity_type_list);
            // 将数组中的元素转换为对应的描述
            $post_paid_identity_type_list = implode(',', array_map(fn ($item) => IdentityType::getDescription($item), $post_paid_identity_type_list));
            $result[] = array(
                'energy_tariff_table_id' => $data->energy_tariff_table_id, // 电量收费表ID
                'energy_tariff_table_number' => $data->energy_tariff_table_number, // 电量收费表编号
                'name' => $data->name, // 名称
                'site_name' => $this->getValueFromLanguageArray($data->site_name_json_init) ?? '—/—', // 場地名称
                'post_paid_identity_type_list' => $post_paid_identity_type_list, // 后付身份类型
                'post_paid_maximum_charge_time' => ($data->post_paid_maximum_charge_time / 60 ?? 0) . __('common.unit_mins'), // 后付最大充电时间
                'is_enable_admin_octopus_card_free_deduct' => $data->is_enable_admin_octopus_card_free_deduct, // 是否开启管理员八达通卡免费扣款
                'is_enable_free_octopus_card' => $data->is_enable_free_octopus_card, // 是否启用免费八达通卡
                'is_enable_round_up_tail_charge_value_calculation' => $data->is_enable_round_up_tail_charge_value_calculation, // 是否启用向上取整尾部充电量计算
                'sort_order' => $data->sort_order, // 排序
                'remark' => $data->remark ?? '—/—', // 备注
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );

        return response()->json($json);
    }

    /**
     * 添加
     *
     * @param Request $request
     * @return View|Factory|Application|RedirectResponse
     */
    public function add(Request $request): View|Factory|Application|RedirectResponse
    {
        $data = array();

        $model = $this->model;

        // 因为post提交表单和get链接都会传入收费表编号参数，所以用下划线标识该编号是copyToAdd携带的
        if (filled($energy_tariff_table_number = $request->input('_energy_tariff_table_number'))) {
            $model = $model->load('item')->when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->where('energy_tariff_table_number', $energy_tariff_table_number)
                ->firstOrFail();
            $energy_tariff_table_item_list = $model->item->sortBy('start_range');

            // 默认/会员卡组/用户组
            $default_item = [];
            $member_card_group_item_list = [];
            $user_group_item_list = [];
            foreach ($energy_tariff_table_item_list as $index => $item) {
                // 当前处理的数据数组
                unset($current_item_list); // 因为是引用变量，此处用unset删除变量
                $current_item_list = null; // 初始化当前处理数据
                if (blank($item->member_card_group_id) && blank($item->user_group_id)) {
                    // 默认
                    $current_item_list = &$default_item;
                } elseif (filled($item->member_card_group_id)) {
                    // 会员卡组
                    $current_item_list = &$member_card_group_item_list[$item->member_card_group_id];
                } elseif (filled($item->user_group_id)) {
                    // 用户组
                    $current_item_list = &$user_group_item_list[$item->user_group_id];
                }
                if (blank($current_item_list)) {
                    $current_item_list = [
                        'data' => [],
                        'member_card_group_id' => $item->member_card_group_id ?? null,
                        'user_group_id' => $item->user_group_id ?? null,
                        'member_card_group_name' => $this->getValueFromLanguageArray(MemberCardGroup::find($item->member_card_group_id)?->name_json) ?? '—/—',
                        'user_group_name' => $this->getValueFromLanguageArray(UserGroup::find($item->user_group_id)?->name_json) ?? '—/—',
                    ];
                }
                $current_item_list['data'][] = [
                    'start_range' => $item->start_range / 1000, // 转换为小数
                    'end_range' => $item->end_range / 1000, // 转换为小数
                    'off_peak_normal_rate' => (double)bcdiv($item->off_peak_normal_rate, 100, 1), // 转换为小数
                    'off_peak_concessionary_rate' => (double)bcdiv($item->off_peak_normal_rate, 100, 1), // 转换为小数
                    'on_peak_normal_rate' => (double)bcdiv($item->on_peak_normal_rate, 100, 1), // 转换为小数
                    'on_peak_concessionary_rate' => (double)bcdiv($item->on_peak_concessionary_rate, 100, 1), // 转换为小数
                ];
            }

            $data['default_item'] = $default_item;
            $data['member_card_group_item_list'] = array_values($member_card_group_item_list);
            $data['user_group_item_list'] = array_values($user_group_item_list);

            $model->energy_tariff_table_id = $model->energy_tariff_table_number = $model->name = null;
        } else {
            $data['default_item'] = [
                'data' => [],
                'member_card_group_id' => null,
                'user_group_id' => null,
                'member_card_group_name' => '—/—',
                'user_group_name' => '—/—',
            ];
            $data['member_card_group_item_list'] = [];
            $data['user_group_item_list'] = [];
        }

        if ($request->isMethod('post')) {
            return $this->modelValidateAndSave($request, $model);
        }

        $data['item'] = array();
        $data['model'] = $model;
        return $this->getForm($request, $data);
    }

    public function edit(Request $request, $energy_tariff_table_number): Factory|Application|View|RedirectResponse
    {
        $module_name = self::$module_name;
        $data = array();

        $model = EnergyTariffTable::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->where('energy_tariff_table_number', $energy_tariff_table_number)
            ->firstOrFail();
        if ($request->isMethod('post')) {
            return $this->modelValidateAndSave($request, $model);
        }

        $model->load('item');

        $energy_tariff_table_item_list = $model->item
            ->sortBy('start_range');
        // 默认/会员卡组/用户组
        $default_item = [];
        $member_card_group_item_list = [];
        $user_group_item_list = [];

        foreach ($energy_tariff_table_item_list as $item) {
            // 当前处理的数据数组
            unset($current_item_list); // 因为是引用变量，此处用unset删除变量
            $current_item_list = null; // 初始化当前处理数据
            if (blank($item->member_card_group_id) && blank($item->user_group_id)) {
                // 默认
                $current_item_list = &$default_item;
            } elseif (filled($item->member_card_group_id)) {
                // 会员卡组
                $current_item_list = &$member_card_group_item_list[$item->member_card_group_id];
            } elseif (filled($item->user_group_id)) {
                // 用户组
                $current_item_list = &$user_group_item_list[$item->user_group_id];
            }
            if (blank($current_item_list)) {
                $current_item_list = [
                    'data' => [],
                    'member_card_group_id' => $item->member_card_group_id ?? null,
                    'user_group_id' => $item->user_group_id ?? null,
                    'member_card_group_name' => $this->getValueFromLanguageArray(MemberCardGroup::find($item->member_card_group_id)?->name_json) ?? '—/—',
                    'user_group_name' => $this->getValueFromLanguageArray(UserGroup::find($item->user_group_id)?->name_json) ?? '—/—',
                ];
            }
            $current_item_list['data'][] = [
                'start_range' => $item->start_range / 1000, // 转换为小数
                'end_range' => $item->end_range / 1000, // 转换为小数
                'off_peak_normal_rate' => (double)bcdiv($item->off_peak_normal_rate, 100, 1), // 转换为小数
                'off_peak_concessionary_rate' => (double)bcdiv($item->off_peak_normal_rate, 100, 1), // 转换为小数
                'on_peak_normal_rate' => (double)bcdiv($item->on_peak_normal_rate, 100, 1), // 转换为小数
                'on_peak_concessionary_rate' => (double)bcdiv($item->on_peak_concessionary_rate, 100, 1), // 转换为小数
            ];

        }
        $data['default_item'] = $default_item;
        $data['member_card_group_item_list'] = array_values($member_card_group_item_list);
        $data['user_group_item_list'] = array_values($user_group_item_list);

        $data['model'] = $model;

        return $this->getForm($request, $data);
    }

    /**
     * 表单页
     *
     * @param Request $request
     * @param array $data
     * @return View|Application|Factory
     */
    protected function getForm(Request $request, array $data): View|Application|Factory
    {
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'],
            self::getUrlParams($request)
        );
        $data['memberCardGroupNotCheckDataUrl'] = action([ModalController::class, 'memberCardGroupNotCheckCheckbox']);
        $data['userGroupNotCheckDataUrl'] = action([ModalController::class, 'userGroupNotCheckCheckbox']);

        // 新增时才回显收费表编号和场地编号
        if (blank($data['model']->energy_tariff_table_id)) {
            $data['model']->energy_tariff_table_number = $request->old('energy_tariff_table_number', $data['model']->energy_tariff_table_number); // 电量收费表编号
            $site_number = isSuperAdministrator() || auth()->user()->site_number_list->count() > 1
                ? $request->old('site_number', $data['model']->site_number)
                : (auth()->user()->site_number_list->first() ?? null);
            $site_name = $this->getValueFromLanguageArray(Site::firstWhere('site_number', $site_number)?->name_json);
            $data['model']->site_number = $site_number; // 场地编号
            $data['model']->site_name = $site_name; // 场地名称
        }
        $data['model']->name = $request->old('name', $data['model']->name);  // 名称
        $data['model']->post_paid_identity_type_list = $request->old('post_paid_identity_type_list', explode(',', $data['model']->post_paid_identity_type_list ?? ''));  // 后付身份类型
        $data['model']->is_enable_admin_octopus_card_free_deduct = $request->old('is_enable_admin_octopus_card_free_deduct', $data['model']->is_enable_admin_octopus_card_free_deduct);
        $data['model']->is_enable_free_octopus_card = $request->old('is_enable_free_octopus_card', $data['model']->is_enable_free_octopus_card);
        $data['model']->is_enable_round_up_tail_charge_value_calculation = $request->old('is_enable_round_up_tail_charge_value_calculation', $data['model']->is_enable_round_up_tail_charge_value_calculation);
        $data['model']->post_paid_maximum_charge_time = $request->old('post_paid_maximum_charge_time', $data['model']->post_paid_maximum_charge_time / 60);  // 后付最大充电时间
        $data['model']->sort_order = $request->old('sort_order', $data['model']->sort_order);  // 排序
        $data['model']->remark = $request->old('remark', $data['model']->remark);  // 备注

        // 默认
        if ($request->old('default_item')) {
            $data['default_item'] = [];
            $default_item = $request->old('default_item');
            $data['default_item'] = $this->generateItemList($default_item['data']);
        }
        // 会员卡组
        if ($request->old('member_card_group_item_list')) {
            $data['member_card_group_item_list'] = [];
            $member_card_group_item_list = $request->old('member_card_group_item_list');
            foreach ($member_card_group_item_list as $index => $member_card_group_item) {
                $data['member_card_group_item_list'][$index] = $this->generateItemList($member_card_group_item['data']);
                $data['member_card_group_item_list'][$index]['member_card_group_id'] = $member_card_group_item['member_card_group_id'] ?? null;
                $data['member_card_group_item_list'][$index]['member_card_group_name'] = $member_card_group_item['member_card_group_name'] ?? '—/—';
            }
            $data['member_card_group_item_list'] = array_values($data['member_card_group_item_list']);
        }
        // 用户组
        if ($request->old('user_group_item_list')) {
            $data['user_group_item_list'] = [];
            $user_group_item_list = $request->old('user_group_item_list');
            foreach ($user_group_item_list as $index => $user_group_item) {
                $data['user_group_item_list'][$index] = $this->generateItemList($user_group_item['data']);
                $data['user_group_item_list'][$index]['user_group_id'] = $user_group_item['user_group_id'] ?? null;
                $data['user_group_item_list'][$index]['user_group_name'] = $user_group_item['user_group_name'] ?? '—/—';
            }
            $data['user_group_item_list'] = array_values($data['user_group_item_list']);
        }
        dump($data);
        return view("pages.{$data['module_name']}.form", $data);
    }


    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @param EnergyTariffTable $model
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    protected function modelValidateAndSave(Request $request, EnergyTariffTable $model): RedirectResponse
    {
        // 判断是否是新增
        if (blank($model->energy_tariff_table_id)) {
            $model = $this->model;
        }

        $request->validate(self::rules($request, $model), [], self::attributes());
        // 公共方法定义，因为需要先刪除子表数据，防止sql执行错误时可以回滚
        deleteItemAndSaveModel(function () use ($request, $model) {
            // 防止修改编号导致原本的无法被刪除，所以现在最上面关联刪除
            $model->item()->delete();

            // 新增时才保存收费表、场地和商户编号
            if (blank($model->energy_tariff_table_id)) {
                $model->energy_tariff_table_number = $request->input('energy_tariff_table_number');
                $model->site_number = $request->input('site_number');
                if (filled($model->site_number) && filled($site = Site::firstWhere('site_number', $model->site_number))) {
                    $model->merchant_number = $site->merchant_number;
                }
            }
            $model->name = $request->input('name'); // 名称
            $model->post_paid_identity_type_list = $request->input('post_paid_identity_type_list', array()); // 后付身份类型
            // 将数组转换为字符串
            $model->post_paid_identity_type_list = implode(',', $model->post_paid_identity_type_list);
            $model->post_paid_maximum_charge_time = $request->input('post_paid_maximum_charge_time', 0) * 60; // 后付最大充电时间
            $model->is_enable_admin_octopus_card_free_deduct = $request->input('is_enable_admin_octopus_card_free_deduct', 0);
            $model->is_enable_free_octopus_card = $request->input('is_enable_free_octopus_card', 0);
            $model->is_enable_round_up_tail_charge_value_calculation = $request->input('is_enable_round_up_tail_charge_value_calculation', 0);
            $model->sort_order = $request->input('sort_order', 0); // 排序
            $model->remark = $request->input('remark'); // 备注

            // 获取item列表
            $default_item_list = $request->input('default_item', array());
            $member_card_group_item_list = $request->input('member_card_group_item_list', array());
            $user_group_item_list = $request->input('user_group_item_list', array());
            $item_list = array_merge([$default_item_list], $member_card_group_item_list, $user_group_item_list);

            // 处理item
            $item_data_list = [];
            foreach ($item_list as $item_group) {
                foreach ($item_group['data'] as $key => $value) {
                    $item_data_list[] = [
                        'start_range' => intval(bcmul($value['start_range'], 1000)),
                        'end_range' => intval(bcmul($value['end_range'], 1000)),
                        'off_peak_normal_rate' => intval(bcmul($value['off_peak_normal_rate'], 100)),
                        'off_peak_concessionary_rate' => intval(bcmul($value['off_peak_concessionary_rate'], 100)),
                        'on_peak_normal_rate' => intval(bcmul($value['on_peak_normal_rate'], 100)),
                        'on_peak_concessionary_rate' => intval(bcmul($value['on_peak_concessionary_rate'], 100)),
                        'member_card_group_id' => $item_group['member_card_group_id'] ?? null,
                        'user_group_id' => $item_group['user_group_id'] ?? null,
                    ];
                }
            }

            // 默认
            $model->energy_tariff_table_item_json = array_values(collect($item_data_list)
                ->whereNull('member_card_group_id')
                ->whereNull('user_group_id')
                ->sortBy('start_range')
                ->values()
                ->toArray());
            // 会员卡组
            $model->member_card_group_energy_tariff_table_item_json = array_values(collect($item_data_list)
                ->whereNotNull('member_card_group_id')
                ->whereNull('user_group_id')
                ->sortBy('start_range')
                ->sortBy('member_card_group_id')
                ->values()
                ->toArray());
            // 用户组
            $model->user_group_energy_tariff_table_item_json = array_values(collect($item_data_list)
                ->whereNotNull('user_group_id')
                ->whereNull('member_card_group_id')
                ->sortBy('start_range')
                ->sortBy('user_group_id')
                ->values()
                ->toArray());

            $model->save();
            // 合并item数据
            $item_data_list = array_merge(
                $model->energy_tariff_table_item_json,
                $model->member_card_group_energy_tariff_table_item_json,
                $model->user_group_energy_tariff_table_item_json
            );
            // 保存item模型
            $model->item()->createMany($item_data_list);
        });
        self::delRedis($model->energy_tariff_table_number);
        self::setConnectorTokenByTariffTable($model);
        self::sendInitPushByKioskNumberList();

        return redirect()->action(
            [self::class, 'showPage'], self::getUrlParams($request)
        );
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function rules(?Request $request, ?Model $model): array
    {
        $module_name = self::$module_name;
        $rules = array(
            'name' => 'required|string|max:45',
            'post_paid_identity_type_list' => 'required|array',
            'post_paid_maximum_charge_time' => 'required|integer|min:1|max:999999',
            'is_enable_admin_octopus_card_free_deduct' => 'bool',
            'is_enable_free_octopus_card' => 'bool',
            'is_enable_round_up_tail_charge_value_calculation' => 'bool',
            'sort_order' => 'integer|min:0',
            'remark' => 'nullable|max:1000',
        );

        // 只有新增时才校验收费表和场地编号
        if (blank($model->energy_tariff_table_id)) {
            $rules['energy_tariff_table_number'] = [
                'required',
                'max:30',
                'regex:/^[a-zA-Z0-9]+$/', // 只能填写字母和数字
                'unique:App\Models\Modules\EnergyTariffTable,energy_tariff_table_number',
            ];
            if (isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) {
                $rules['site_number'] = [
                    'required',
                    'exists:App\Models\Modules\Site,site_number',
                    function ($attr, $value, $fail) use ($request, $model, $module_name) {
                        // 新增时校验非超级管理员且未拥有该场地提示错误
                        if (!isSuperAdministrator() && !in_array($value, auth()->user()->site_number_list->toArray())) {
                            $fail(__('common.text_not_found', ['field' => __("$module_name.site")]));
                        }
                    },
                ];
            }
        }

        // item规则
        $start_range_rule = 'required|integer|min:0|max:999999';
        $end_range_rule = 'required|integer|min:0|max:999999';
        $on_peak_rate_rule = ['required', 'numeric', 'min:0', 'max:999999', function ($attribute, $value, $fail) use ($module_name) {
            if (!preg_match("/^\d+(\.\d{0,1})?$/", $value)) {
                $message = __("$module_name.text_most_one_decimal", [
                    'rate' => $value
                ]);
                $fail($message);
                return;
            }
        }];
        $off_peak_rate_rule = $on_peak_rate_rule;

        // 会员卡组规则
        $member_card_group_rule = function ($attribute, $value, $fail) use ($module_name, $request) {
            // 判断选择的Member Card Group是否为当前场地下的
            $member_card_group = MemberCardGroup::when(
                !isSuperAdministrator(),
                fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list)
            )
                ->where('site_number', $request->input('site_number'))
                ->where('member_card_group_id', $value)
                ->first();
            if (blank($member_card_group)) $fail(__("$module_name.no_such_member_card_group"));
        };
        // 用户组规则
        $user_group_rule = function ($attribute, $value, $fail) use ($module_name, $request) {
            // 判断选择的User Group是否为当前商户下的
            $site = Site::firstWhere('site_number', $request->input('site_number'));
            $user_group = UserGroup::when(
                !isSuperAdministrator(),
                fn($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list)
            )
                ->where('merchant_number', $site?->merchant_number)
                ->where('user_group_id', $value)
                ->first();
            if (blank($user_group)) $fail(__("$module_name.no_such_user_group"));
        };

        // 默认收费表规则
        $rules['default_item'] = [
            'required',
            'array'
        ];

        $rules['default_item.data'] = ['required', 'array'];
        $rules['default_item.data.*.start_range'] = $start_range_rule;
        $rules['default_item.data.*.end_range'] = $end_range_rule . '|gt:default_item.data.*.start_range';;
        $rules['default_item.data.*.on_peak_normal_rate'] = $on_peak_rate_rule;
        $rules['default_item.data.*.on_peak_concessionary_rate'] = $on_peak_rate_rule;
        $rules['default_item.data.*.off_peak_normal_rate'] = $off_peak_rate_rule;
        $rules['default_item.data.*.off_peak_concessionary_rate'] = $off_peak_rate_rule;

        // 会员卡组规则
        $rules['member_card_group_item_list'] = ['nullable', 'array'];
        $rules['member_card_group_item_list.*.data'] = ['required', 'array'];
        $rules['member_card_group_item_list.*.data.*.start_range'] = $start_range_rule;
        $rules['member_card_group_item_list.*.data.*.end_range'] = $end_range_rule . '|gt:member_card_group_item_list.*.data.*.start_range';;
        $rules['member_card_group_item_list.*.data.*.on_peak_normal_rate'] = $on_peak_rate_rule;
        $rules['member_card_group_item_list.*.data.*.on_peak_concessionary_rate'] = $on_peak_rate_rule;
        $rules['member_card_group_item_list.*.data.*.off_peak_normal_rate'] = $off_peak_rate_rule;
        $rules['member_card_group_item_list.*.data.*.off_peak_concessionary_rate'] = $off_peak_rate_rule;
        $rules['member_card_group_item_list.*.member_card_group_id'] = [
            'nullable',
            'exists:App\Models\Modules\MemberCardGroup,member_card_group_id',
            $member_card_group_rule,
        ];

        // 用户组规则
        $rules['user_group_item_list'] = ['nullable', 'array'];
        $rules['user_group_item_list.*.data'] = ['required', 'array'];
        $rules['user_group_item_list.*.data.*.start_range'] = $start_range_rule;
        $rules['user_group_item_list.*.data.*.end_range'] = $end_range_rule . '|gt:user_group_item_list.*.data.*.start_range';;
        $rules['user_group_item_list.*.data.*.on_peak_normal_rate'] = $on_peak_rate_rule;
        $rules['user_group_item_list.*.data.*.on_peak_concessionary_rate'] = $on_peak_rate_rule;
        $rules['user_group_item_list.*.data.*.off_peak_normal_rate'] = $off_peak_rate_rule;
        $rules['user_group_item_list.*.data.*.off_peak_concessionary_rate'] = $off_peak_rate_rule;
        $rules['user_group_item_list.*.user_group_id'] = [
            'nullable',
            'exists:App\Models\Modules\UserGroup,user_group_id',
            $user_group_rule,
        ];

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'energy_tariff_table_number' => __("$module_name.energy_tariff_table_number"),
            'site_number' => __("$module_name.site"),
            'name' => __("$module_name.name"),
            'post_paid_identity_type_list' => __("$module_name.post_paid_identity_type_list"),
            'post_paid_maximum_charge_time' => __("$module_name.post_paid_maximum_charge_time"),
            'sort_order' => __("$module_name.sort_order"),
            'remark' => __("$module_name.remark"),
            'item' => __("$module_name.item"),
            'is_enable_admin_octopus_card_free_deduct' => __("$module_name.is_enable_admin_octopus_card_free_deduct"),
            'is_enable_free_octopus_card' => __("$module_name.is_enable_free_octopus_card"),
            'is_enable_round_up_tail_charge_value_calculation' => __("$module_name.is_enable_round_up_tail_charge_value_calculation"),

            'default_item' => __("$module_name.item"),
            'default_item.*.data' => __("$module_name.energy_tariff_table_item_cannot_empty"),
            'default_item.data.*.start_range' => __("$module_name.start_range"),
            'default_item.data.*.end_range' => __("$module_name.end_range"),
            'default_item.data.*.off_peak_normal_rate' => __("$module_name.off_peak_normal_rate"),
            'default_item.data.*.off_peak_concessionary_rate' => __("$module_name.off_peak_concessionary_rate"),
            'default_item.data.*.on_peak_normal_rate' => __("$module_name.on_peak_normal_rate"),
            'default_item.data.*.on_peak_concessionary_rate' => __("$module_name.on_peak_concessionary_rate"),

            'member_card_group_item_list.*.member_card_group_id' => __("$module_name.member_card_group"),
            'member_card_group_item_list.*.data' => __("$module_name.energy_tariff_table_item_cannot_empty"),
            'member_card_group_item_list.data.*.start_range' => __("$module_name.start_range"),
            'member_card_group_item_list.data.*.end_range' => __("$module_name.end_range"),
            'member_card_group_item_list.data.*.off_peak_rate' => __("$module_name.off_peak_normal_rate"),
            'member_card_group_item_list.data.*.off_peak_concessionary_rate' => __("$module_name.off_peak_concessionary_rate"),
            'member_card_group_item_list.data.*.on_peak_normal_rate' => __("$module_name.on_peak_normal_rate"),
            'member_card_group_item_list.data.*.on_peak_concessionary_rate' => __("$module_name.on_peak_concessionary_rate"),

            'user_group_item_list.*.user_group_id' => __("$module_name.user_group"),
            'user_group_item_list.*.data' => __("$module_name.energy_tariff_table_item_cannot_empty"),
            'user_group_item_list.data.*.start_range' => __("$module_name.start_range"),
            'user_group_item_list.data.*.end_range' => __("$module_name.end_range"),
            'user_group_item_list.data.*.off_peak_normal_rate' => __("$module_name.off_peak_normal_rate"),
            'user_group_item_list.data.*.off_peak_concessionary_rate' => __("$module_name.off_peak_concessionary_rate"),
            'user_group_item_list.data.*.on_peak_normal_rate' => __("$module_name.on_peak_normal_rate"),
            'user_group_item_list.data.*.on_peak_concessionary_rate' => __("$module_name.on_peak_concessionary_rate"),
        ];
    }

    /**
     * 返回地址栏参数
     *
     * @param Request|null $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => 'true',
            'name_search' => $request->get('name_search'),
            'site_search' => $request->get('site_search'),
        );
    }

    /**
     * 删除
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function delete(Request $request): JsonResponse
    {
        $energy_tariff_table_number = $request->input('energy_tariff_table_number');
        $module_name = self::$module_name;

        if (filled($energy_tariff_table_number) &&
            filled($energy_tariff_table = EnergyTariffTable::with('connectorSetting')
                ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->firstWhere('energy_tariff_table_number', $energy_tariff_table_number))
        ) {
            if (blank($connector_setting_list = $energy_tariff_table->connectorSetting)) {
                $energy_tariff_table->delete();
                self::delRedis($energy_tariff_table_number);
                self::sendInitPushByKioskNumberList();
            } else {
                $connector_setting_str = '';
                foreach ($connector_setting_list as $connector_setting) {
                    $connector_setting_str .= '<li>' . $connector_setting->name . '</li>';
                }
                $this->code = 201;
                $this->message = __('common.error_has_binding_unable_to_delete_tariff_table', [
                    'connector_setting' => $connector_setting_str
                ]);
            }
        } else {
            $this->notFoundData(__("$module_name.web_title"));
        }

        return $this->returnJson();
    }

    public static function delRedis($energy_tariff_table_number): void
    {
        foreach (['defaultFee', 'memberCardGroupFee', 'userGroupFee'] as $type) {
            foreach (config('languages') as $language_code => $language) {
                Redis::del("energyTariffTable:$energy_tariff_table_number:$type:$language_code");
            }
            Redis::del("energyTariffTable:$energy_tariff_table_number:$type:en_US_zh_HK");
        }
    }

    /**
     * @param $group_type 'default' || 'member_card_group' || 'user_group'
     * @param $group_id 0 || member_card_group_id || user_group_id
     * @param $item_list array
     * @return array
     */
    protected function generateItemList($form_item)
    {
        $item = [
            'data' => [],
        ];
        foreach ($form_item as $value) {
            $item['data'][] = [
                'start_range' => $value['start_range'] ?? null,
                'end_range' => $value['end_range'] ?? null,
                'off_peak_normal_rate' => $value['off_peak_normal_rate'] ?? null,
                'off_peak_concessionary_rate' => $value['off_peak_concessionary_rate'] ?? null,
                'on_peak_normal_rate' => $value['on_peak_normal_rate'] ?? null,
                'on_peak_concessionary_rate' => $value['on_peak_concessionary_rate'] ?? null,
            ];
        }

        return $item;
    }

}

<?php

namespace App\Enums;

use BenSampo\Enum\Enum;
use BenSampo\Enum\Contracts\LocalizedEnum;
use BenSampo\Enum\Exceptions\InvalidEnumMemberException;
use Lang;
use App\Enums\Traits\Tools;

/**
 * @method static Time
 * @method static Energy
 */
final class ChargeValueType extends Enum implements LocalizedEnum
{
    use Tools;

    const Time = 'TIME';
    const Energy = 'ENERGY';

    /**
     * 通过语言值获取语言文件
     *
     * @throws InvalidEnumMemberException
     */
    public static function getLocalDescription($value, $local = null): ?string
    {
        if (parent::isLocalizable()) {
            $localizedStringKey = parent::getLocalizationKey() . '.' . $value;

            if (Lang::has($localizedStringKey)) {
                return __($localizedStringKey, [], $local);
            }
        }

        return parent::getFriendlyName(parent::getKey($value));
    }

    /**
     * 通过语言值获取该语言下的枚举数组
     *
     * @throws InvalidEnumMemberException
     */
    public static function asLocalSelectArray($local = null): array
    {
        $array = parent::asArray();
        $selectArray = [];

        foreach ($array as $value) {
            $selectArray[$value] = self::getLocalDescription($value, $local);
        }

        return $selectArray;
    }
}

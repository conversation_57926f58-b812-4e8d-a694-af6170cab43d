<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;
use App\Models\Traits\Emoji;

class SimpleTariffTableItem extends Model
{
    use emoji;

    protected $table = 'simple_tariff_table_item'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'simple_tariff_table_item_id'; //定义主键，默认为id
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    protected $fillable = ['member_card_group_id', 'rate','user_group_id'];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [
        'rate' => 0, // 费率
    ];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];
}

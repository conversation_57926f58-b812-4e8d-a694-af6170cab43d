<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Auth;
use Illuminate\Http\{
    JsonResponse,
    Request,
};
use Illuminate\Foundation\Application;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use App\Enums\{
    IdentityType,
    ChargeArrearsStatus,
};
use App\Models\Modules\{
    ChargeArrearsRecord,
    ChargePaymentRecord,
};
use Illuminate\Support\Facades\Redis;


class ChargeArrearsRecordController extends CommonController
{
    protected static string $module_name = 'chargeArrearsRecord'; // 模块名称

    /**
     * 初始页
     *
     * @param Request $request
     * @return View|Application|Factory
     */
    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'charge_record_number_search' => $request->get('charge_record_number_search'),
            'identity_number_search' => $request->get('identity_number_search'),
            'gmt_create_search' => $request->get('gmt_create_search'),
        );
        $data['charge_arrears_status_search'] = filled($request->get('charge_arrears_status_search')) ?
            explode(',', $request->get('charge_arrears_status_search'))
            : [];

        $data['charge_arrears_status_list'] = array();
        foreach (ChargeArrearsStatus::asSelectArray() as $value => $name) {
            $data['charge_arrears_status_list'][] = array(
                'name' => $name,
                'value' => $value,
            );
        }

        return view("pages.{$data['module_name']}.list", $data);
    }

    /**
     * 列表接口
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $length = (int)$request->input('length', 10);
        $search = $this->_searchList($request);
        $data_list = $search->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {

            $result[] = array(
                'charge_arrears_record_id' => $data->charge_arrears_record_id, // 充电欠款记录ID
                'charge_arrears_record_number' => $data->charge_arrears_record_number, // 充电欠款记录编号
                'charge_record_number' => $data->charge_record_number, // 充电记录编号
                'charge_arrears_status' => ChargeArrearsStatus::getDescription($data->charge_arrears_status), // 充电欠款状态
                'gmt_charge_arrears_status' => $data->gmt_charge_arrears_status, // 充电欠款状态时间
                'estimate_arrears_charge_value_amount' => __('common.unit_hk') . (float)(bcdiv($data->estimate_arrears_charge_value_amount, 100, 1)), // 预估欠款充电量金额
                'estimate_arrears_idling_penalty_amount' => __('common.unit_hk') . (float)(bcdiv($data->estimate_arrears_idling_penalty_amount, 100, 1)), // 预估欠款闲置罚款金额
                'connector_number' => $data->connector_number, // 充电枪编号
                'connector_name' => $data->connector_name, // 充电枪名称
                'kiosk_number' => $data->kiosk_number, // Kiosk编号
                'kiosk_name' => $data->kiosk_name, // Kiosk名称
                'identity_type' => IdentityType::getDescription($data->identity_type), // 身份类型
                'identity_number' => $data->identity_number, // 身份号码
                'remark' => $data->remark, // 备注
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result
        );

        return response()->json($json);
    }

    /**
     * 视图页
     *
     * @param Request $request
     * @param $charge_arrears_record_number
     * @return View|Application|Factory
     */
    public function view(Request $request, $charge_arrears_record_number): View|Application|Factory
    {
        // dataTable字段
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'],
            self::getUrlParams($request)
        );

        // 获取充电欠款记录
        $charge_arrears_record_result = ChargeArrearsRecord::where('charge_arrears_record_number', $charge_arrears_record_number)
            ->firstOrFail();


        if (filled($charge_arrears_record_result)) {
            $is_show_manual_complete_button = false;
            switch ($charge_arrears_record_result->identity_type) {
                case IdentityType::Octopus:
                    $identity_type_image = existsImage('icon', 'payment_methods/' . strtolower(IdentityType::Octopus) . '.png');
                    // 如果是八达通，且状态为Unpaid，且存在payment_record，才显示按钮
                    if ($charge_arrears_record_result->charge_arrears_status === ChargeArrearsStatus::Unpaid) {
                        $is_charge_payment_record_exist = ChargePaymentRecord::where('charge_record_number', $charge_arrears_record_result->charge_record_number)->exists();
                        if ($is_charge_payment_record_exist) {
                            $is_show_manual_complete_button = true;
                        }
                    }
                    break;
                default:
                    $identity_type_image = null;
                    break;
            }
            // 获取充电欠款记录
            $charge_arrears_record_info = array(
                'charge_arrears_record_id' => $charge_arrears_record_result->charge_arrears_record_id,
                'charge_arrears_record_number' => $charge_arrears_record_result->charge_arrears_record_number,
                'charge_arrears_record_number_raw' => $charge_arrears_record_result->charge_arrears_record_number,
                'charge_arrears_status' => ChargeArrearsStatus::getDescription($charge_arrears_record_result->charge_arrears_status), // 充电欠款状态
                'charge_arrears_status_enum' => $charge_arrears_record_result->charge_arrears_status, // 充电欠款状态枚举
                'gmt_charge_arrears_status' => $charge_arrears_record_result->gmt_charge_arrears_status, // 充电欠款状态时间
                'estimate_arrears_charge_value_amount' => __('common.unit_hk') . (float)(bcdiv($charge_arrears_record_result->estimate_arrears_charge_value_amount, 100, 1)), // 预估欠款充电量金额
                'estimate_arrears_idling_penalty_amount' => __('common.unit_hk') . (float)(bcdiv($charge_arrears_record_result->estimate_arrears_idling_penalty_amount, 100, 1)), // 预估欠款闲置罚款金额
                'connector_number' => $charge_arrears_record_result->connector_number, // 充电枪编号
                'connector_name' => $charge_arrears_record_result->connector_name, // 充电枪名称
                'kiosk_number' => $charge_arrears_record_result->kiosk_number, // Kiosk编号
                'kiosk_name' => $charge_arrears_record_result->kiosk_name, // Kiosk名称
                'identity_type' => IdentityType::getDescription($charge_arrears_record_result->identity_type), // 身份类型
                'identity_type_image' => $identity_type_image, // 身份类型图片
                'identity_number' => $charge_arrears_record_result->identity_number, // 身份号码
                'status_log' => $charge_arrears_record_result->status_log, // 状态日志
                'remark' => $charge_arrears_record_result->remark, // 备注
                'gmt_create' => $charge_arrears_record_result->gmt_create->toDateTimeString(), // 创建时间
                'is_show_manual_complete_button' => $is_show_manual_complete_button, // 是否显示手动完成按钮
                'modify_status_url' => action([self::class, 'modifyStatus']), // 修改状态URL
            );

            // 将status_log按照换行符或者回车符分割成数组
            $charge_arrears_record_info['status_log_list'] = preg_split('/\r\n|\r|\n/', $charge_arrears_record_result->status_log, -1, PREG_SPLIT_NO_EMPTY);

            // 获取充电欠款记录编号
            $charge_arrears_record_number = $charge_arrears_record_result->charge_arrears_record_number;
            // 拆分充电欠款记录编号
            $charge_arrears_record_number_list = explode('-', $charge_arrears_record_number);
            $charge_arrears_record_number = end($charge_arrears_record_number_list);
            $charge_arrears_record_info['charge_arrears_record_number'] = $charge_arrears_record_number;
            // 获取充电记录编号
            $charge_record_number = $charge_arrears_record_result->charge_record_number;
            // 拆分充电记录编号
            $charge_record_number_list = explode('-', $charge_record_number);
            $charge_record_number = end($charge_record_number_list);
            $charge_arrears_record_info['charge_record_number'] = $charge_record_number;

            $data['data'] = $charge_arrears_record_info;
        }

        return view("pages.{$data['module_name']}.view", $data);
    }

    /**
     * 导出
     *
     * @param Request $request
     * @return void
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function excelExport(Request $request): void
    {
        // 获取数据
        $search = $this->_searchList($request);
        $data_list = $search->get();
        $module_name = self::$module_name;

        $result = array();
        foreach ($data_list as $data) {
            $result[] = array(
                'charge_arrears_record_id' => $data->charge_arrears_record_id, // 充电欠款记录ID
                'charge_arrears_record_number' => $data->charge_arrears_record_number, // 充电欠款记录编号
                'charge_record_number' => $data->charge_record_number, // 充电记录编号
                'charge_arrears_status' => ChargeArrearsStatus::getDescription($data->charge_arrears_status), // 充电欠款状态
                'gmt_charge_arrears_status' => $data->gmt_charge_arrears_status, // 充电欠款状态时间
                'estimate_arrears_charge_value_amount' => bcdiv($data->estimate_arrears_charge_value_amount, 100, 1), // 预估欠款充电量金额
                'estimate_arrears_idling_penalty_amount' => bcdiv($data->estimate_arrears_idling_penalty_amount, 100, 1), // 预估欠款闲置罚款金额
                'identity_type' => IdentityType::getDescription($data->identity_type), // 身份类型
                'identity_number' => $data->identity_number, // 身份号码
                'connector_number' => $data->connector_number, // 充电枪编号
                'connector_name' => $data->connector_name, // 充电枪名称
                'kiosk_number' => $data->kiosk_number, // Kiosk编号
                'kiosk_name' => $data->kiosk_name, // Kiosk名称
                'remark' => $data->remark, // 备注
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
            );
        }

        // 先把语言赋值才可以在后面array出来表头，不然会输出未编译前的字符串
        $charge_arrears_record_number = __("$module_name.charge_arrears_record_number");
        $charge_record_number = __("$module_name.charge_record_number");
        $charge_arrears_status = __("$module_name.charge_arrears_status");
        $gmt_charge_arrears_status = __("$module_name.gmt_charge_arrears_status");
        $estimate_arrears_charge_value_amount = __("$module_name.estimate_arrears_charge_value_amount") . ' ( ' . __('common.unit_hk_blank') . ' )';
        $estimate_arrears_idling_penalty_amount = __("$module_name.estimate_arrears_idling_penalty_amount") . ' ( ' . __('common.unit_hk_blank') . ' )';
        $identity_type = __("$module_name.identity_type");
        $identity_number = __("$module_name.identity_number");
        $connector_name = __("$module_name.connector_name");
        $kiosk_name = __("$module_name.kiosk_name");
        $remark = __("$module_name.remark");
        $gmt_create = __("$module_name.gmt_create");

        $web_title = __("$module_name.web_title");

        $remove_column_list = [];
        switch (env('CURRENT_PROJECT_NAME')) {
            default:
                // 需要移除的列
                $remove_column_list = [

                ];
                break;
        }

        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet(); //实例化 Spreadsheet 对象
        $worksheet = $spreadsheet->getActiveSheet();

        // 工作簿名称为 "Usage Report"
        $worksheet->setTitle($web_title);

        $styleArray = [
            'font' => [
                'bold' => false
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
        ];

        // 计算数组长度用于动态补充表格样式
        $len = count($result);
        // 因为前面两格表头样式占了一行，所以要加1用于设置单元格样式
        $total_rows = $len + 1;
        $styleArrayBody = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['argb' => '666666'],
                ],
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
        ];

        // 指定表头字段
        $excel_data = array(
            array(
                $charge_arrears_record_number,
                $charge_record_number,
                $charge_arrears_status,
                $gmt_charge_arrears_status,
                $estimate_arrears_charge_value_amount,
                $estimate_arrears_idling_penalty_amount,
                $identity_type,
                $identity_number,
                $connector_name,
                $kiosk_name,
                $remark,
                $gmt_create,
            )
        );

        // 移除指定列
        if (filled($remove_column_list)) $excel_data[0] = array_diff($excel_data[0], $remove_column_list);
        //设置单元格样式
        // 将数组长度转换为 Excel 列名
        $last_column = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex(count($excel_data[0]));
        $worksheet->getStyle("A1:{$last_column}$total_rows")->applyFromArray($styleArray)->getFont()->setName('Times New Roman')->setSize(11);
        $worksheet->getStyle("A1:{$last_column}1")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('c0c0c0');
        $worksheet->getStyle("A1:{$last_column}$total_rows")->applyFromArray($styleArrayBody);

        foreach ($result as $report) {
            $excel_row = array(
                $charge_arrears_record_number => $report['charge_arrears_record_number'],
                $charge_record_number => $report['charge_record_number'],
                $charge_arrears_status => $report['charge_arrears_status'],
                $gmt_charge_arrears_status => $report['gmt_charge_arrears_status'],
                $estimate_arrears_charge_value_amount => $report['estimate_arrears_charge_value_amount'],
                $estimate_arrears_idling_penalty_amount => $report['estimate_arrears_idling_penalty_amount'],
                $identity_type => $report['identity_type'],
                $identity_number => $report['identity_number'],
                $connector_name => $report['connector_name'],
                $kiosk_name => $report['kiosk_name'],
                $remark => $report['remark'],
                $gmt_create => $report['gmt_create'],
            );
            // 移除指定列
            if (filled($remove_column_list)) {
                foreach ($remove_column_list as $remove_column) {
                    unset($excel_row[$remove_column]);
                }
            }
            $excel_data[] = $excel_row;
        }

        $worksheet->fromArray($excel_data);
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $file_name = $web_title . ' ' . date('Y-m-d H:i:s') . '.xlsx';
        //设置单元格的宽度
        foreach ($spreadsheet->getActiveSheet()->getColumnIterator() as $column) {
            $spreadsheet->getActiveSheet()->getColumnDimension($column->getColumnIndex())->setAutoSize(true);
        }

        // 客户端文件下载
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $file_name . '"');
        header('Cache-Control: max-age=0');
        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
        exit;
    }

    /**
     * 搜索数据
     *
     * @param Request $request
     * @return mixed
     */
    protected function _searchList(Request $request): mixed
    {
        // dataTable字段
        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $charge_record_number = $request->input('charge_record_number_search');
        $identity_number = $request->input('identity_number_search');
        $charge_arrears_status = $request->input('charge_arrears_status_search');
        $gmt_create = $request->input('gmt_create_search');

        $gmt_create_search_list = self::getRangeDateTimeArray($gmt_create ?: '') ?: null;


        $charge_arrears_record = ChargeArrearsRecord::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->when(filled($charge_record_number), fn ($query) => $query->where('charge_record_number', 'like', "%$charge_record_number%"))
            ->when(filled($identity_number), fn ($query) => $query->where('identity_number', 'like', "%$identity_number%"))
            ->when(filled($charge_arrears_status), function ($query) use ($charge_arrears_status) {
                if (is_string($charge_arrears_status) && filled($charge_arrears_status_list = explode(',', $charge_arrears_status))) {
                    $charge_arrears_status = $charge_arrears_status_list;
                }
                $query->whereIn('charge_arrears_status', $charge_arrears_status);
            })
            ->when(filled($gmt_create_search_list), fn ($query) => $query->whereBetween('gmt_create', $gmt_create_search_list))
            ->orderBy($order, $sort)
            ->latest('gmt_create');

        return $charge_arrears_record;
    }

    /**
     * 返回地址栏参数
     *
     * @param Request $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(Request $request): array
    {
        return array(
            'is_page' => 'true',
            'charge_record_number_search' => $request->get('charge_record_number_search'),
            'identity_number_search' => $request->get('identity_number_search'),
            'charge_arrears_status_search' => $request->get('charge_arrears_status_search'),
            'gmt_create_search' => $request->get('gmt_create_search'),
        );
    }

    public function modifyStatus(Request $request): JsonResponse
    {
        $charge_arrears_status = $request->input('charge_arrears_status', ChargeArrearsStatus::Completed);
        $charge_arrears_record_number = $request->input('charge_arrears_record_number');
        $reason = $request->input('reason');

        if (blank($charge_arrears_record_number)) {
            $this->missingField('charge_arrears_record_number');
            return $this->returnJson();
        }

        $charge_arrears_record = ChargeArrearsRecord::firstWhere('charge_arrears_record_number', $charge_arrears_record_number);
        if (blank($charge_arrears_record)) {
            $this->notFoundData('charge_arrears_record');
            return $this->returnJson();
        }

        // 判断是否与viosk关联
        if (filled($charge_arrears_record?->connector?->chargePoint?->viosk)) {
            $viosk_number = $charge_arrears_record?->connector?->chargePoint?->viosk?->viosk_number;
            // 如果有关联，不修改状态，获取Redis中的值，追加到数组最后，去重，重新存Redis
            $redis_need_deduct_charge_arrears_record_number_list = Redis::get('need_deduct_charge_arrears_record_number_list:' . $viosk_number);
            $need_deduct_charge_arrears_record_number_list = null;
            if (filled($redis_need_deduct_charge_arrears_record_number_list)) {
                $need_deduct_charge_arrears_record_number_list = json_decode($redis_need_deduct_charge_arrears_record_number_list, true) ?: null;
            }
            // 追加到数组最后，去重，重新存Redis
            $need_deduct_charge_arrears_record_number_list[] = $charge_arrears_record_number;
            $need_deduct_charge_arrears_record_number_list = array_unique($need_deduct_charge_arrears_record_number_list);
            Redis::set('need_deduct_charge_arrears_record_number_list:' . $viosk_number, json_encode($need_deduct_charge_arrears_record_number_list));
            return $this->returnJson();
        }

        if (filled($charge_arrears_record->status_log)) {
            $charge_arrears_record->status_log .= PHP_EOL;
        }
        $current_date_time = now()->format('Y-m-d H:i:s');
        $charge_arrears_record->status_log .= "$current_date_time - by: cms, status: [{$charge_arrears_record->charge_arrears_status}] -> [" . $charge_arrears_status . "], reason: $reason";

        $charge_arrears_record->charge_arrears_status = $charge_arrears_status;
        $charge_arrears_record->gmt_charge_arrears_status = now();
        $result = $charge_arrears_record->save();

        if (!$result) {
            $this->modelSaveFail();
            return $this->returnJson();
        }

        return $this->returnJson();
    }
}

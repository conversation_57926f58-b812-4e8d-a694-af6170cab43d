<?php

namespace App\Enums;

use BenSampo\Enum\{
    Enum,
    Contracts\LocalizedEnum
};
use App\Enums\Traits\Tools;

/**
 * @method static Basic
 * @method static EnlargeCenterPage
 * @method static Card
 */
final class NewsCategoryCarouselThemeEnum extends Enum implements LocalizedEnum
{
    use Tools;

    const Basic = 'BASIC';
    const EnlargeCenterPage  = 'ENLARGE_CENTER_PAGE';
    const Card = 'CARD';
}

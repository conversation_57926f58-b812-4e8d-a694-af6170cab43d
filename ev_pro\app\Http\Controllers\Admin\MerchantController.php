<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use App\Models\Modules\{
    Merchant,
    MerchantDescription,
};
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Application;
use Illuminate\Http\{
    JsonResponse,
    RedirectResponse,
    Request,
};
use App\Http\Controllers\Admin\Traits\{
    Add,
    Edit,
};

class MerchantController extends CommonController
{
    use Add, Edit;

    protected static string $module_name = 'merchant'; // 模块名称

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new Merchant;
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'add_url' => action([self::class, 'add']),
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'name_search' => $request->get('name_search'),
        );

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', '');
        $sort = $request->input('sort', 'asc');
        $length = (int)$request->input('length', 10);
        $name_search = $request->input('name_search');

        $data_list = Merchant::with(['description'])
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))
            ->when(filled($name_search), function ($query) use ($name_search) {
                return $query->whereHas('description', function ($query) use ($name_search) {
                    return $query->where('name', 'like', "%{$name_search}%");
                });
            })
            ->when($order == 'name', fn($query) => $query->orderBy('name_json', $sort), fn($query) => $query->orderBy($order, $sort))
            ->latest('gmt_modified')
            ->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $merchant_name = $this->getValueFromLanguageArray($data->name_json) ?? '—/—';
            $result[] = array(
                'merchant_id' => $data->merchant_id, // 商戶ID
                'merchant_number' => $data->merchant_number, // 编号
                'name' => $merchant_name, // 名称
                'maximum_member_card_count' => $data->maximum_member_card_count ?? '—/—', // 最大会员卡数量
                'logo_image_url' => existsImage('public', $data->logo_image_url) ?: existsImage('icon', 'not_select_image.png'), // Logo图片路径
                'member_card_background_image_url' => existsImage('public', $data->member_card_background_image_url) ?: existsImage('icon', 'not_select_image.png'), // 会员卡背景图片路径
                'email' => $data->email ?? '—/—', // 邮箱
                'telephone' => $data->telephone ?? '—/—', // 电话
                'whatsapp' => $data->whatsapp ?? '—/—', // WhatsApp
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );

        return response()->json($json);
    }

    protected function getForm(Request $request, array $data): View|Application|Factory
    {
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'], self::getUrlParams($request),
        );

        // 读取系统语言并查询出对应KioskSettingDescription数据
        $data['item'] = array();

        // 新增时才回显商户编号
        if (blank($data['model']->merchant_id)) {
            $data['model']->merchant_number = $request->old('merchant_number', $data['model']->merchant_number); // 商户编号
        }

        $data['model']->logo_image_url = $request->old('logo_image_url', $data['model']->logo_image_url); // Logo图片
        $data['model']->app_name = $request->old('app_name', $data['model']->app_name); // APP名称
        $data['model']->maximum_member_card_count = $request->old('maximum_member_card_count', $data['model']->maximum_member_card_count); // 最大会员卡数量
        $data['model']->member_card_background_image_url = $request->old('member_card_background_image_url', $data['model']->member_card_background_image_url); // 会员卡背景图片
        $data['model']->email = $request->old('email', $data['model']->email); // 邮箱
        $data['model']->telephone = $request->old('telephone', $data['model']->telephone); // 电话
        $data['model']->whatsapp = $request->old('whatsapp', $data['model']->whatsapp); // WhatsApp
        $data['model']->mail_sender_host = $request->old('mail_sender_host', $data['model']->mail_sender_host); // 邮件发送主机
        $data['model']->mail_sender_port = $request->old('mail_sender_port', $data['model']->mail_sender_port); // 邮件发送端口
        $data['model']->mail_sender_username = $request->old('mail_sender_username', $data['model']->mail_sender_username); // 邮件发送用户名
        $data['model']->mail_sender_password = $request->old('mail_sender_password', $data['model']->mail_sender_password); // 邮件发送密码
        $data['model']->mail_sender_from = $request->old('mail_sender_from', $data['model']->mail_sender_from); // 邮件发送发送人
        $data['model']->access_you_url = $request->old('access_you_url', $data['model']->access_you_url); // AccessYou地址
        $data['model']->access_you_account_no = $request->old('access_you_account_no', $data['model']->access_you_account_no); // AccessYou账户编号
        $data['model']->access_you_user = $request->old('access_you_user', $data['model']->access_you_user); // AccessYou用户
        $data['model']->access_you_password = $request->old('access_you_password', $data['model']->access_you_password); // AccessYou密码
        $data['model']->access_you_tid_json = $request->old('access_you_tid_json', $data['model']->access_you_tid_json); // AccessYou模板JSON
        $data['model']->yedpay_online_api_url = $request->old('yedpay_online_api_url', $data['model']->yedpay_online_api_url); // Yedpay在线API地址
        $data['model']->yedpay_online_api_key = $request->old('yedpay_online_api_key', $data['model']->yedpay_online_api_key); // Yedpay在线API密钥
        $data['model']->yedpay_online_points_order_notify_url = $request->old('yedpay_online_points_order_notify_url', $data['model']->yedpay_online_points_order_notify_url); // Yedpay在线积分订单通知地址
        $data['model']->yedpay_online_points_order_return_url = $request->old('yedpay_online_points_order_return_url', $data['model']->yedpay_online_points_order_return_url); // Yedpay在线积分订单返回地址

        foreach (config('languages') as $language_code => $language_name) {
            // 有旧数据
            $merchant_description_old_list = $request->old('item');
            if (filled($merchant_description_old_list)) {
                $data['item'][$language_code] = array(
                    'language_name' => $language_name,
                    'language_code' => $language_code,
                    'name' => $merchant_description_old_list[$language_code]['name'] ?? null,
                    'merchant_description_id' => $merchant_description_old_list[$language_code]['merchant_description_id'] ?? null, // ID
                );
            } else {
                $merchant_description_result = $data['model']->description->where('language_code', $language_code)->first();
                $data['item'][$language_code] = array(
                    'language_name' => $language_name,
                    'language_code' => $language_code,
                    'name' => $merchant_description_result->name ?? null,
                    'merchant_description_id' => $merchant_description_result->merchant_description_id ?? null, // ID
                );
            }
        }

        return view("pages.{$data['module_name']}.form", $data);
    }

    public function delete(Request $request): JsonResponse
    {
        $merchant_number = $request->post('merchant_number');
        $module_name = self::$module_name;

        if (filled($merchant_number) && strtoupper($merchant_number) === strtoupper(env('APP_NAME'))) {
            $this->code = 40009;
            $this->message = __("$module_name.super_merchant_cannot_deleted");
            return $this->returnJson();
        }
        if (filled($merchant_number) &&
            filled($merchant = Merchant::with('site')
                ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))
                ->firstWhere('merchant_number', $merchant_number))
        ) {
            if ($merchant->site->count() == 0) {
                // 删除商戶
                $merchant->delete();
            } else {
                $this->code = 40001;
                $this->message = __('common.error_has_binding_unable_to_modify_merchant');
            }
        } else {
            $this->notFoundData(__("$module_name.web_title"));
        }

        return $this->returnJson();
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @param Merchant $model
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2023-12-21
     */
    protected function modelValidateAndSave(Request $request, Merchant $model): RedirectResponse
    {
        $request->validate(self::rules($request, $model), [], self::attributes());
        // 新增时才保存商户编号
        if (blank($model->merchant_id)) {
            $model->merchant_number = $request->input('merchant_number');
        }
        $model->logo_image_url = $request->input('logo_image_url');
        $model->member_card_background_image_url = $request->input('member_card_background_image_url');
        $model->app_name = $request->input('app_name');
        $model->maximum_member_card_count = $request->input('maximum_member_card_count');
        $model->email = $request->input('email');
        $model->telephone = $request->input('telephone');
        $model->whatsapp = $request->input('whatsapp');
        $model->mail_sender_host = $request->input('mail_sender_host');
        $model->mail_sender_port = $request->input('mail_sender_port');
        $model->mail_sender_username = $request->input('mail_sender_username');
        $model->mail_sender_password = $request->input('mail_sender_password');
        $model->mail_sender_from = $request->input('mail_sender_from');
        $model->access_you_url = $request->input('access_you_url');
        $model->access_you_account_no = $request->input('access_you_account_no');
        $model->access_you_user = $request->input('access_you_user');
        $model->access_you_password = $request->input('access_you_password');
        $model->yedpay_online_api_url = $request->input('yedpay_online_api_url');
        $model->yedpay_online_api_key = $request->input('yedpay_online_api_key');
        $model->yedpay_online_points_order_notify_url = $request->input('yedpay_online_points_order_notify_url');
        $model->yedpay_online_points_order_return_url = $request->input('yedpay_online_points_order_return_url');
        // 获取请求商户名称描述数据
        $merchant_description_list = $request->input('item', array());
        $name_json = array();
        foreach ($merchant_description_list as $merchant_description) {
            $name_json[$merchant_description['language_code']] = $merchant_description['name'];
        }
        $model->name_json = $name_json;
        $model->save();

        // 保存商户设置后再保存并关联商户描述
        foreach ($merchant_description_list as $merchant_description) {
            if (isset($merchant_description['merchant_description_id'])) {
                $merchant_description_model =
                    MerchantDescription::findOr($merchant_description['merchant_description_id'], fn() => new MerchantDescription);
            } else {
                $merchant_description_model = new MerchantDescription;
            }
            $merchant_description_model->merchant_number = $model->merchant_number;
            $merchant_description_model->language_code = $merchant_description['language_code'];
            $merchant_description_model->name = $merchant_description['name'];

            $merchant_description_model->save();
        }

        return redirect()->action(
            [self::class, 'showPage'], self::getUrlParams($request),
        );
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function rules(?Request $request, ?Model $model): array
    {
        $rules = array(
            'item.*.name' => 'required|max:45',
            'app_name' => 'nullable|max:45',
            'maximum_member_card_count' => 'nullable|max:9',
            'email' => 'nullable|max:45|email:rfc',
            'telephone' => 'nullable|max:45',
            'whatsapp' => 'nullable|max:45',
            'mail_sender_host' => 'nullable|max:45',
            'mail_sender_port' => 'nullable|max:5',
            'mail_sender_username' => 'nullable|max:45',
            'mail_sender_password' => 'nullable|max:45',
            'mail_sender_from' => 'nullable|max:100',
            'access_you_url' => 'nullable|max:255',
            'access_you_account_no' => 'nullable|max:45',
            'access_you_user' => 'nullable|max:45',
            'access_you_password' => 'nullable|max:45',
            'yedpay_online_api_url' => 'nullable|max:255',
            'yedpay_online_api_key' => 'nullable|max:255',
            'yedpay_online_points_order_notify_url' => 'nullable',
            'yedpay_online_points_order_return_url' => 'nullable',
        );

        // 只有新增时才校验商户编号
        if (blank($model->merchant_id)) {
            $rules['merchant_number'] = [
                'required',
                'max:45',
                'regex:/^[a-zA-Z0-9]+$/', // 只能填写字母和数字
                'unique:App\Models\Modules\Merchant,merchant_number',
            ];
        }

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'merchant_number' => __("$module_name.merchant_number"),
            'item.*.name' => __("$module_name.name"),
            'app_name' => __("$module_name.app_name"),
            'maximum_member_card_count' => __("$module_name.maximum_member_card_count"),
            'email' => __("$module_name.email"),
            'telephone' => __("$module_name.telephone"),
            'whatsapp' => __("$module_name.whatsapp"),
            'mail_sender_host' => __("$module_name.mail_sender_host"),
            'mail_sender_port' => __("$module_name.mail_sender_port"),
            'mail_sender_username' => __("$module_name.mail_sender_username"),
            'mail_sender_password' => __("$module_name.mail_sender_password"),
            'mail_sender_from' => __("$module_name.mail_sender_from"),
            'access_you_url' => __("$module_name.access_you_url"),
            'access_you_account_no' => __("$module_name.access_you_account_no"),
            'access_you_user' => __("$module_name.access_you_user"),
            'access_you_password' => __("$module_name.access_you_password"),
            'yedpay_online_api_url' => __("$module_name.yedpay_online_api_url"),
            'yedpay_online_api_key' => __("$module_name.yedpay_online_api_key"),
            'yedpay_online_points_order_notify_url' => __("$module_name.yedpay_online_points_order_notify_url"),
            'yedpay_online_points_order_return_url' => __("$module_name.yedpay_online_points_order_return_url"),
        ];
    }

    /**
     * 返回地址栏参数
     *
     * @param Request|null $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2023-12-21
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => 'true',
            'name_search' => $request->get('name_search'),
        );
    }

}

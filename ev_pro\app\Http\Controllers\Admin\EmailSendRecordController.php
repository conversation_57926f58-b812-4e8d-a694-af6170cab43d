<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Http\{
    JsonResponse,
    Request,
};
use Illuminate\Foundation\Application;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use App\Models\Modules\{
    EmailSendRecord,
};

class EmailSendRecordController extends CommonController
{

    protected static string $module_name = 'emailSendRecord'; // 模块名称
    protected EmailSendRecord $model;

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new EmailSendRecord;
    }

    /**
     * 初始页
     *
     * @param Request $request
     * @return View|Application|Factory
     */
    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'recipient_search' => $request->input('recipient_search'),
        );

        return view("pages.{$data['module_name']}.list", $data);
    }

    /**
     * 列表接口
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        $draw = (int)$request->input('draw', 1);
        $length = (int)$request->input('length', 10);
        $search = $this->_searchList($request);
        $data_list = $search->paginate($length, ['*'], 'start');
        $result = array();
        foreach ($data_list as $data) {
            // 判断发送状态 给前端拼接样式字符和多语言文件
            $status = match (true) {
                blank($data->gmt_sent) => strtotime($data->gmt_expiry) < strtotime('now') ? 'fail' : 'pending',
                default => 'success',
            };
            $result[] = array(
                'email_send_record_id' => $data->email_send_record_id, // 短信发送记录ID
                'charge_record_number' => $data->charge_record_number ?? '—/—', // 充电机编号
                'recipient' => $data->recipient, // 收件人
                'title' => $data->title ?? '—/—', // 标题
                'message' => $data->message ?? '—/—', // 消息
                'status' => $status, // 状态
                'gmt_sent' => $data->gmt_sent ?? '—/—', // 发送时间
                'gmt_expiry' => $data->gmt_expiry, // 过期时间
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );
        return response()->json($json);
    }

    /**
     * 返回地址栏参数
     *
     * @param Request|null $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => 'true',
            'recipient_search' => $request->get('recipient_search'),
        );
    }

    /**
     * 获取搜索数据
     *
     * @param Request $request
     * @return mixed
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-11-08
     */
    protected function _searchList(Request $request): mixed
    {
        // dataTable字段
        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $recipient_search = $request->input('recipient_search');

        return EmailSendRecord::when(!isSuperAdministrator(), fn($query) => $query->whereHas('chargeRecord', fn($where) => $where->whereIn('site_number', auth()->user()->site_number_list)))
            ->when(filled($recipient_search), fn($query) => $query->where('recipient', 'like', "%$recipient_search%"))
            ->orderBy($order, $sort)
            ->latest();
    }
}

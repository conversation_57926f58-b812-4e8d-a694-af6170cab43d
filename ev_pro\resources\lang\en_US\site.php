<?php

return [
    'web_title' => 'Site',

    // 字段
    'site_number' => 'Site Number',
    'name' => 'Name',
    'acronym' => 'Acronym',
    'is_public_site' => 'Is Public Site',
    'lms_mode' => 'LMS Mode',
    'merchant' => 'Merchant',
    'merchant_id' => 'Merchant',
    'region_id' => 'Region',
    'region' => 'Region',
    'currency_code' => 'Currency',
    'maximum_charging_connector_count' => 'Maximum Charging Connector Count',
    'main_image_url' => 'Main Image',
    'telephone' => 'Telephone',
    'email' => 'Email',
    'longitude' => 'Longitude',
    'latitude' => 'Latitude',
    'remark' => 'Remark',
    'sort_order' => 'Sort Order',
    'gmt_create' => 'Create Time',
    'gmt_modified' => 'Modified Time',
    'view_site' => 'View Site',
    'site_description' => 'Site Description',
    'address' => 'Address',
    'google_map_url' => 'Google Map Url',
    'apple_map_url' => 'Apple Map Url',
    'amap_map_url' => 'Amap Map Url',
    'opening_hours' => 'Opening Hours',
    'fee' => 'Fee',
    'description' => 'Description',
    'description_name' => 'Name',
    'error_site_mismatching' => 'Site is bound to Kiosk or Charge Point, please unbind first.',
    'site_number_already' => 'The Site Number has already been taken.',
    'error_region_merchant_must_be_belong_to_site_merchant' => 'The merchant of the region must match the merchant of the site.',
    // Map
    'select_location' => 'Select location',
    'update_map_location' => 'Update Map Location',
    'reset_map_location' => 'Reset',
    // validation
    'longitude_validation' => 'Cannot be greater than 180 °, cannot be less than -180 °',
    'latitude_validation' => 'Cannot be greater than 90 °, cannot be less than -90 °',
    // site image
    'site_image' => "Site Image",
    'image_url' => "Image",
];

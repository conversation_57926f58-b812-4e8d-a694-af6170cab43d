<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use App\Models\Modules\Permission\Role;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Foundation\Application;
use Illuminate\Http\{
    JsonResponse,
    Request,
};
use App\Models\User;
use App\Models\Modules\Merchant;
use Illuminate\Support\Facades\Hash;

class AdministratorController extends CommonController
{
    protected static string $module_name = 'administrator'; // 模块名称
    protected User $model;  // 用户模型

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new User;
    }

    /**
     * 初始页
     *
     * @param Request $request
     * @return View|Application|Factory
     */
    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'site_search' => $request->input('site_search'),
            'name_search' => $request->input('name_search', ''),
            'email_search' => $request->input('email_search', ''),
            'role_search' => $request->input('role_search', ''),
        );

        // 商户下拉列表
        $data['site_list'] = self::getSiteListGroupByMerchant();

        // 非超级管理员管理员不显示超级管理员组
        $data['role_list'] = Role::with('site')
            ->when(!isSuperAdministrator(), function ($query) {
                $query->whereNot('name', 'Super Administrator')
                    ->whereDoesntHave('site', fn($query) => $query->whereNotIn('site.site_number', auth()->user()->site_number_list))
                    ->whereHas('site', fn($query) => $query->whereIn('site.site_number', auth()->user()->site_number_list));
            })
            ->oldest('role_id')
            ->get();

        return view("pages.{$data['module_name']}.list", $data);
    }

    /**
     * 列表接口
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'sort_order');
        $sort = $request->input('sort', 'asc');
        $length = (int)$request->input('length', 10);
        $site_search = $request->input('site_search');
        $name_search = $request->input('name_search');
        $email_search = $request->input('email_search');
        $role_search = $request->input('role_search');

        $data_list = User::with(['roles.site'])
            ->when(filled($name_search), fn($query) => $query->where('administrator.name', 'like', '%' . $name_search . '%'))
            ->when(filled($email_search), fn($query) => $query->where('administrator.email', 'like', '%' . $email_search . '%'))
            ->when(filled($role_search), function ($query) use ($role_search) {
                $query->whereHas('roles', fn($query) => $query->where('role.role_id', $role_search));
            })
            ->when(filled($site_search), function ($query) use ($site_search) {
                $query->whereHas('roles.site', fn($query) => $query->where('site.site_number', $site_search));
            })
            ->when(!isSuperAdministrator(), function ($query) {
                $query->whereDoesntHave('roles.site', fn($query) => $query->whereNotIn('site.site_number', auth()->user()->site_number_list))
                    ->whereHas('roles.site', fn($query) => $query->whereNotNull('site.site_number'))
                    ->whereHas('roles', fn($query) => $query->whereNot('role.name', 'Super Administrator'));
            })
            ->distinct()
            ->orderBy($order, $sort)
            ->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $site_name = [];
            foreach ($data->roles as $role) {
                foreach ($role->site as $site) {
                    $site_name[] = self::getValueFromLanguageArray($site->name_json);
                }
            }
            $site_name = filled($site_name) ? implode('<br>', $site_name) : '—/—';
            $result[] = array(
                'administrator_id' => $data->administrator_id,
                'site_name' => $site_name,
                'name' => $data->name,
                'avatar_url' => existsImage('avatar', $data->avatar_url) ?: existsImage('icon', 'not_select_image.png'),
                'email' => $data->email,
                'remark' => $data->remark ?? '—/—',
                'role' => $data->roles,
                'gmt_create' => $data->gmt_create->toDateTimeString(),
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            'data' => $result,
        );

        return response()->json($json);
    }

    /**
     * 视图页
     *
     * @param Request $request
     * @param $administrator_id
     * @return View|Application|Factory
     */
    public function view(Request $request, $administrator_id): View|Application|Factory
    {
        // dataTable字段
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'],
            self::getUrlParams($request)
        );
        if (filled($request->get('module_name')) && "role" == $request->get('module_name')) {
            $data['cancel_url'] = action(
                [RoleController::class, 'showPage'],
                RoleController::getUrlParams($request)
            );
        }

        $model = User::findOrFail($administrator_id);
        if (!isSuperAdministrator()) {
            if (filled(array_diff($model->site_number_list->toArray(), auth()->user()->site_number_list->toArray()))) {
                abort(404);
            }
        }
        $data['model'] = array(
            'administrator_id' => $model->administrator_id,
            'name' => $model->name,
            'avatar_url' => existsImage('avatar', $model->avatar_url) ?: existsImage('icon', 'not_select_image.png'),
            'email' => $model->email,
            'remark' => $model->remark,
            'role' => $model->roles,
            'gmt_create' => $model->gmt_create->toDateTimeString(),
        );

        // 获取角色列表
        $data['role_list'] = Role::with('site')
            ->when(!isSuperAdministrator(), function ($query) use ($model) {
                $query->whereDoesntHave('site', fn($query) => $query->whereNotIn('site.site_number', auth()->user()->site_number_list))
                    ->whereHas('site', fn($query) => $query->whereIn('site.site_number', auth()->user()->site_number_list));
            })
            ->oldest('role_id')
            ->get();

        return view("pages.{$data['module_name']}.view", $data);
    }

    /**
     * 删除
     *
     * @param Request $request
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function delete(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        $administrator_id = $request->input('administrator_id');

        $model = User::find($administrator_id);
        if (filled($administrator_id) && filled($model)) {
            if ($model->administrator_id === 1) {
                // 超级管理员不能删除
                $this->code = 201;
                $this->message = __("$module_name.error_del_super_admin");
            } elseif ($administrator_id === auth()->user()->administrator_id) {
                // 不能删除自己
                $this->code = 201;
                $this->message = __("$module_name.error_del_self");
            } elseif (!isSuperAdministrator() && filled(array_diff($model->site_number_list->toArray(), auth()->user()->site_number_list->toArray()))) {
                // 无法删除site_number_list中有不属于自己的场地
                $this->code = 201;
                $this->message = __("$module_name.error_no_site_permission");
            } else {
                $model->destroy($administrator_id);
                $this->clearPermissionCache();
            }
        } else {
            $this->notFoundData('Administrator');
        }

        return $this->returnJson();
    }

    /**
     * 添加
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function add(Request $request): JsonResponse
    {
        $request->validate(self::rules($request), [], self::attributes());
        $name = $request->input('name');
        $email = $request->input('email');
        $password = $request->input('password');
        $role_id = $request->input('role_id');
        $remark = $request->input('remark');

        $model = new User;
        $model->name = $name;
        $model->email = $email;
        if (filled($password)) {
            $model->password = Hash::make($password);
        }
        $model->remark = $remark;

        $model->save();

        // 多对多同步关联
        $model->syncRoles($role_id);

        $this->data = $model;

        return $this->returnJson();
    }

    /**
     * 编辑
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function edit(Request $request): JsonResponse
    {
        $request->validate(self::rules($request), [], self::attributes());
        $administrator_id = $request->input('administrator_id');
        $name = $request->input('name');
        $email = $request->input('email');
        $password = $request->input('password');
        $role_id = $request->input('role_id');
        $remark = $request->input('remark');

        $model = User::find($administrator_id);
        if (filled($administrator_id) && filled($model)) {
            $model->name = $name;
            $model->email = $email;
            if (filled($password)) {
                $model->password = Hash::make($password);
            }
            $model->remark = $remark;
            $model->save();

            // 多对多同步关联
            if (isSuperAdministrator() || $model->administrator_id !== auth()->user()->administrator_id) {
                // 非超级管理员不能修改自己的角色
                $model->syncRoles($role_id);
            }


            $this->data = $model;
        } else {
            $this->notFoundData('Administrator');
        }

        return $this->returnJson();
    }

    /**
     * 修改密码
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function changePassword(Request $request): JsonResponse
    {
        $request->validate(self::passwordRules(), [], self::attributes());
        $administrator_id = $request->input('administrator_id');
        $password = $request->input('password');
        $module_name = self::$module_name;

        $model = User::find($administrator_id);
        if (filled($administrator_id) && filled($model)) {
            if (!isSuperAdministrator() && filled(array_diff($model->site_number_list->toArray(), auth()->user()->site_number_list->toArray()))) {
                // 无法修改site_number_list中有不属于自己的场地
                $this->code = 201;
                $this->message = __("$module_name.error_no_site_permission");
                return $this->returnJson();
            }
            $model->password = Hash::make($password);
            $model->save();

            $this->data = $model;
        } else {
            $this->notFoundData('Administrator');
        }

        return $this->returnJson();
    }

    /**
     * 修改头像
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function upload(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        $administrator_id = $request->input('administrator_id');

        $model = User::find($administrator_id);
        if (filled($administrator_id) && filled($model) && $request->hasFile('avatar')) {
            if (!isSuperAdministrator() && filled(array_diff($model->site_number_list->toArray(), auth()->user()->site_number_list->toArray()))) {
                // 无法修改site_number_list中有不属于自己的场地
                $this->code = 201;
                $this->message = __("$module_name.error_no_site_permission");
                return $this->returnJson();
            }
            $file = $request->file('avatar');

            $format_list = json_decode(file_get_contents(base_path() . '/format.json'), true);

            if (filled($format_list['image']) && in_array(strtolower($file->extension()), $format_list['image'])) {
                $file_name = $file->getClientOriginalName(); // 获取文件的原始名称
                $file->storeAs('', $file_name, 'avatar');

                $model->avatar_url = $file_name;
                $model->save();

                $this->data = $model;
            } else {
                $this->message = __("$module_name.administrator_avatar_upload_error_format");
                $this->code = 201;
            }
        } else {
            $this->missingField('administrator|file');
        }

        return $this->returnJson();
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @return array
     */
    protected static function rules(?Request $request): array
    {
        $module_name = self::$module_name;
        $rules = array(
            'name' => 'bail|required|min:2',
            'email' => 'required|email:rfc|unique:App\Models\User,email,' . $request->administrator_id . ',administrator_id',
        );
        // 新增时必填密码
        if (blank($request->administrator_id)) {
            $rules['password'] = 'required|min:4|confirmed';
        }
        $rules['role_id'] = [
            'required',
            'exists:App\Models\Modules\Permission\Role,role_id',
            function ($attribute, $value, $fail) use ($module_name, $request) {
                if (isSuperAdministrator()) {
                    return;
                }
                $role = Role::with('site')->find($value);
                $site_number_list = $role?->site?->pluck('site_number')?->toArray() ?? [];
                $diff = array_diff($site_number_list, auth()->user()->site_number_list->toArray());
                if (filled($diff)) {
                    $fail(__("$module_name.error_no_site_permission"));
                }
            },
        ];

        return $rules;
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @return array
     */
    protected static function passwordRules(): array
    {
        $rules = array(
            'password' => 'required|min:4|confirmed',
        );

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'name' => __("$module_name.name"),
            'email' => __("$module_name.email"),
            'role_id' => __("$module_name.role"),
            'password' => __("$module_name.password"),
        ];
    }

    /**
     * 返回地址栏参数
     *
     * @param Request|null $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => 'true',
            'name_search' => $request->get('name_search', ''),
            'email_search' => $request->get('email_search', ''),
            'role_search' => $request->get('role_search', ''),
            'site_search' => $request->get('site_search'),
        );
    }
}

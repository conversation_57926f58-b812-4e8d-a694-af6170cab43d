<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Exception;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use Illuminate\Foundation\Application;
use Illuminate\Http\{
    JsonResponse,
    Request,
};
use App\Models\Modules\Kiosk;
use Log;
use Storage;
use App\Enums\{
    WebsocketType,
    OctopusDataExchangeMode,
    OctopusDataExchangeType,
};

class OctopusDataExchangeController extends CommonController
{
    protected static string $module_name = 'octopusDataExchange'; // 模块名称

    /**
     * 初始页
     *
     * @param Request $request
     * @return View|Application|Factory
     */
    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'octopus_data_exchange_mode' => OctopusDataExchangeMode::Cms,
            'manual_upload_url' => action([self::class, 'manualUpload']),
            'manual_download_url' => action([self::class, 'manualDownload']),
            'start_octopus_upload_url' => action([self::class, 'startOctopusUpload']),
            'start_octopus_download_url' => action([self::class, 'startOctopusDownload']),
            'websocket_upload_type_push' => WebsocketType::OctopusDataExchangeUploadNotification,
            'websocket_download_type_push' => WebsocketType::OctopusDataExchangeDownloadNotification,
            'get_octopus_upload_log_url' => action([self::class, 'getOctopusUploadLog']),
            'get_octopus_download_log_url' => action([self::class, 'getOctopusDownloadLog']),
        );

        return view("pages.{$data['module_name']}.list", $data);
    }

    /**
     * 手动上数
     * @param Request $request
     * @return JsonResponse
     */
    public function manualUpload(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        $kiosk_number_list = $request->kiosk_number_list;

        if (blank($kiosk_number_list) || !is_array($kiosk_number_list)) {
            $this->missingField('Kiosk');
            return $this->returnJson();
        }

        // 判断kiosk全部存在
        $kiosk = Kiosk::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->whereHas('kioskSetting', function ($query) {
                $query->where('octopus_data_exchange_mode', OctopusDataExchangeMode::Cms);
            })
            ->whereIn('kiosk_number', $kiosk_number_list)
            ->count();
        if ($kiosk !== count($kiosk_number_list)) {
            $this->code = 40001;
            $this->message = __("$module_name.error_kiosk_not_exist");
            return $this->returnJson();
        }

        // websocket通知kiosk
        self::websocketPush(WebsocketType::OctopusDataExchangeUploadNotification, null, $kiosk_number_list);

        return $this->returnJson();
    }

    /**
     * 开始八达通上数
     * @param Request $request
     * @return void
     */
    public function startOctopusUpload(Request $request): void
    {
        try {
            // 八达通上数
            $log_file = 'log/cms_log/upload.log';
            // 不存在log文件则新建，存在清空文件内容
            Storage::disk('rwl')->put($log_file, '');

            if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
                // Windows
                $command = 'start /B ' . base_path() . '/job/win/upload.bat';
            } else {
                // Linux
                $command = 'sh ' . base_path() . '/job/linux/upload.sh';
            }

            $descriptorspec = array(
                0 => array("pipe", "r"),  // 标准输入
                1 => array("pipe", "w"),  // 标准输出
                2 => array("pipe", "w")   // 标准错误输出
            );
            $process = proc_open($command, $descriptorspec, $pipes);
            // 关闭标准输入
            fclose($pipes[0]);

            // 关闭标准输出和标准错误输出
            fclose($pipes[1]);
            fclose($pipes[2]);

            // 立即输出JSON响应
            $json = array(
                'code' => $this->code,
                'message' => $this->message,
            );
            echo json($json);

            // 等待进程结束
            proc_close($process);
        } catch (Exception) {
            $this->code = 40001;
            $this->message = 'Octopus Upload Failed!';
            $json = array(
                'code' => $this->code,
                'message' => $this->message,
            );
            echo json($json);
        }
    }

    /**
     * 获取八达通上数日志
     * @param Request $request
     * @return JsonResponse
     */
    public function getOctopusUploadLog(Request $request): JsonResponse
    {
        $log_file = 'log/cms_log/upload.log';

        $log = '';
        if (Storage::disk('rwl')->exists($log_file)) {
            $log = Storage::disk('rwl')->get($log_file);
            // 将编码转换为UTF8
            $log = mb_convert_encoding($log, 'UTF-8', 'GBK');
            // 格式化换行符
            $log = str_replace(["\r\n", "\n"], "<br>", $log);
        }

        $this->data = $log;

        return $this->returnJson();
    }

    /**
     * 手动下载
     * @param Request $request
     * @return JsonResponse
     */
    public function manualDownload(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        $kiosk_number_list = $request->kiosk_number_list;

        if (blank($kiosk_number_list) || !is_array($kiosk_number_list)) {
            $this->missingField('Kiosk');
            return $this->returnJson();
        }

        // 判断kiosk全部存在
        $kiosk = Kiosk::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->whereHas('kioskSetting', function ($query) {
                $query->where('octopus_data_exchange_mode', OctopusDataExchangeMode::Cms);
            })
            ->whereIn('kiosk_number', $kiosk_number_list)
            ->count();
        if ($kiosk !== count($kiosk_number_list)) {
            $this->code = 40001;
            $this->message = __("$module_name.error_kiosk_not_exist");
            return $this->returnJson();
        }

        // websocket通知kiosk
        self::websocketPush(WebsocketType::OctopusDataExchangeDownloadNotification, null, $kiosk_number_list);

        return $this->returnJson();
    }

    /**
     * 开始八达通下载
     * @param Request $request
     */
    public function startOctopusDownload(Request $request): void
    {
        try {
            // 八达通下载
            $log_file = 'log/cms_log/download.log';
            // 不存在log文件则新建，存在清空文件内容
            Storage::disk('rwl')->put($log_file, '');

            if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
                // Windows
                $command = 'start /B ' . base_path() . '/job/win/download.bat';
            } else {
                // Linux
                $command = 'sh ' . base_path() . '/job/linux/download.sh';
            }

            $descriptorspec = array(
                0 => array("pipe", "r"),  // 标准输入
                1 => array("pipe", "w"),  // 标准输出
                2 => array("pipe", "w")   // 标准错误输出
            );
            $process = proc_open($command, $descriptorspec, $pipes);
            // 关闭标准输入
            fclose($pipes[0]);

            // 关闭标准输出和标准错误输出
            fclose($pipes[1]);
            fclose($pipes[2]);

            // 立即输出JSON响应
            $json = array(
                'code' => $this->code,
                'message' => $this->message,
            );
            echo json($json);

            // 等待进程结束
            proc_close($process);
        } catch (Exception) {
            $this->code = 40001;
            $this->message = 'Octopus Download Failed!';
            $json = array(
                'code' => $this->code,
                'message' => $this->message,
            );
            echo json($json);
        }
    }

    /**
     * 获取八达通下载日志
     * @param Request $request
     * @return JsonResponse
     */
    public function getOctopusDownloadLog(Request $request): JsonResponse
    {
        $log_file = 'log/cms_log/download.log';

        $log = '';
        if (Storage::disk('rwl')->exists($log_file)) {
            $log = Storage::disk('rwl')->get($log_file);
            // 将编码转换为UTF8
            $log = mb_convert_encoding($log, 'UTF-8', 'GBK');
            // 格式化换行符
            $log = str_replace(["\r\n", "\n"], "<br>", $log);
        }

        $this->data = $log;

        return $this->returnJson();
    }

    /**
     * 八达通上数和下载，给用于定时任务调用
     * @param Request $request
     */
    public function octopusUploadAndDownload(Request $request): void
    {
        try {
            // 获取当前服务器时间
            $current_date = date('Ymd');
            $current_time = date('His');

            $upload_log_file = "log/cms_log/$current_date/upload_$current_date$current_time.log";
            // 不存在log文件则新建，存在清空文件内容
            Storage::disk('rwl')->put($upload_log_file, '');
            // 实际地址
            $upload_log_file_path = Storage::disk('rwl')->path($upload_log_file);

            $download_log_file = "log/cms_log/$current_date/download_$current_date$current_time.log";
            // 不存在log文件则新建，存在清空文件内容
            Storage::disk('rwl')->put($download_log_file, '');
            // 实际地址
            $download_log_file_path = Storage::disk('rwl')->path($download_log_file);

            if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
                // Windows
                $command = 'start /B ' . base_path() . '/job/win/schedule_octopus.bat ' . $upload_log_file_path . ' ' . $download_log_file_path;
            } else {
                // Linux
                $command = 'sh ' . base_path() . '/job/linux/schedule_octopus.sh ' . $upload_log_file_path . ' ' . $download_log_file_path . ' &';
            }

            $descriptorspec = array(
                0 => array("pipe", "r"),  // 标准输入
                1 => array("pipe", "w"),  // 标准输出
                2 => array("pipe", "w")   // 标准错误输出
            );

            $process = proc_open($command, $descriptorspec, $pipes);

            // 关闭标准输入rr
            fclose($pipes[0]);

            // 关闭标准输出和标准错误输出
            fclose($pipes[1]);
            fclose($pipes[2]);

            // 等待进程结束
            proc_close($process);

            // 立即输出JSON响应
            $json = array(
                'code' => 200,
                'message' => null,
            );
            echo json($json);
        } catch (Exception $e) {
            $code = 40001;
            $message = 'Octopus upload and download error!';
            $json = array(
                'code' => $code,
                'message' => $message,
            );
            echo json($json);
            Log::info($message . $e->getMessage());
        }
    }

    /**
     * 八达通上数下载，用于定时任务调用
     *
     * @param  Request      $request
     * @return JsonResponse
     * @Description
     * @example
     * @date 2024-05-24
     */
    public function cronOctopus(Request $request): JsonResponse
    {
        // 仅windows环境下用
        if (!strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
            $this->code = 403;
            $this->message = 'Only Windows environment can use!';
            return $this->returnJson();
        }
        $type = $request->type;
        $type_list = [OctopusDataExchangeType::Download, OctopusDataExchangeType::Upload];
        if (!in_array($type, $type_list)) {
            $this->missingField('Type');
            return $this->returnJson();
        }
        $this->data = false;
        try {
            $command_path = base_path('job/command/');
            $command = "start /B $command_path/win/$type.bat";

            $descriptorspec = array(
                0 => array("pipe", "r"),  // 标准输入
                1 => array("pipe", "w"),  // 标准输出
                2 => array("pipe", "w")   // 标准错误输出
            );

            $process = proc_open($command, $descriptorspec, $pipes);

            // 关闭标准输入rr
            fclose($pipes[0]);

            // 关闭标准输出和标准错误输出
            fclose($pipes[1]);
            fclose($pipes[2]);

            // 等待进程结束
            proc_close($process);

            // 立即输出JSON响应
            $this->data = true;
            return $this->returnJson();
        } catch (Exception $e) {
            $this->message = "Cron Octopus $type Api error!";
            Log::info($this->message . $e->getMessage());
            return $this->returnJson();
        }
    }
}

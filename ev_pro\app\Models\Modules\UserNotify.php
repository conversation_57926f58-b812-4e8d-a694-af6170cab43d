<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;
use App\Models\Traits\Emoji;

class UserNotify extends Model
{
    use emoji;

    protected $table = 'user_notify';
    protected $primaryKey = 'user_notify_id';

    const CREATED_AT = 'gmt_create'; //默认是小写的模型类名复数格式，此处为自定义表名
    const UPDATED_AT = 'gmt_modified'; //定义主键，默认为id

    // protected $fillable = [];
    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 类型转化器
     */
    protected $casts = [

    ];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [

    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * 为 array / JSON 序列化准备日期格式。
     */
    protected function serializeDate($date): string
    {
        return $date->format('Y-m-d H:i:s');
    }

    // 用户
    public function user()
    {
        return $this->hasOne(AppUser::class, 'user_id', 'user_id');
    }

    public function getFirstDescriptionAttribute()
    {
        $description = $this->description->first();
        if (filled($description)) return $description;

        $description = new UserNotifyDescription;
        foreach ($description->fillable as $key) {
            $description->$key = null;
        }
        return $description;
    }

    /**
     * 一对多关联description
     */
    public function description()
    {
        return $this->hasMany(UserNotifyDescription::class, 'user_notify_id', 'user_notify_id');
    }

    // 一对多关联用户通知-用户关联表
    public function userNotifyToUser()
    {
        return $this->hasMany(UserNotifyToUser::class, 'user_notify_id', 'user_notify_id');
    }
}

<?php

namespace App\Http\Controllers\Admin\Traits;

use Illuminate\Http\{
    JsonResponse,
    Request
};
use Illuminate\Database\Eloquent\Model;

trait Delete
{
    protected Model $model;

    public function delete(Request $request): JsonResponse
    {
        $number = $request->post('number');

        if (blank($number)) {
            $this->notFoundData('Number');
            return $this->returnJson();
        }

        // 当前角色为非超级管理员时且当前模块需要校验商户时筛选商户Id
        $model = $this->model::when(
            !isSuperAdministrator() && (isset(self::$module_check_merchant) && self::$module_check_merchant === true),
            fn($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list)
        )
            ->when(
                !isSuperAdministrator() && (isset(self::$module_check_site) && self::$module_check_site === true),
                fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list)
            )
            ->where(property_exists($this->model, 'table_number') ? $this->model::$table_number : $this->model->getKeyName(), $number)->firstOrFail();

        if (blank($model)) {
            $this->notFoundData('Number');
            return $this->returnJson();
        }

        $model->delete();
        self::sendInitPushByKioskNumberList();

        return $this->returnJson();
    }
}

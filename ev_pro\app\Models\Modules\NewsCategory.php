<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class NewsCategory extends Model
{
    protected $table = 'news_category';
    protected $primaryKey = 'news_category_id';

    const CREATED_AT = 'gmt_create'; //默认是小写的模型类名复数格式，此处为自定义表名
    const UPDATED_AT = 'gmt_modified'; //定义主键，默认为id

    // protected $fillable = [];
    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 类型转化器
     */
    protected $casts = [
        'is_show_carousel_indicator' => 'bool', // 是否显示轮播指示器
        'is_main_page_display_text_content' => 'bool', // 是否在首页显示文字内容
        'is_enable' => 'bool', // 是否启用
    ];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [
        'is_show_carousel_indicator' => 0, // 是否显示轮播指示器
        'sort_order' => 0, // 排序
        'is_main_page_display_text_content' => 1, // 是否在首页显示文字内容
        'is_enable' => 0, // 是否启用
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    public function getFirstDescriptionAttribute()
    {
        $description = $this->description->first();
        if (filled($description)) return $description;

        $description = new NewsCategoryDescription;
        foreach ($description->fillable as $key) {
            $description->$key = null;
        }
        return $description;
    }

    /**
     * 一对多关联description
     */
    public function description()
    {
        return $this->hasMany(NewsCategoryDescription::class, 'news_category_id', 'news_category_id');
    }

    // 分类下的新闻
    public function news(): BelongsToMany
    {
        return $this->belongsToMany(News::class, 'news_category_to_news', 'news_category_id', 'news_id')->withTimestamps();
    }
}

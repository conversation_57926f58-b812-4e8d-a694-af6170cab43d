<?php

namespace App\Http\Controllers\App;

use Debugbar;
use App\Http\Controllers\Common\CommonController;
use Illuminate\Http\{
    JsonResponse,
    Request,
};
use Illuminate\Support\Facades\Mail;

class AccountController extends CommonController
{
    public function __construct(Request $request)
    {
        parent::__construct($request);
        Debugbar::disable();
    }

    public function unsubscribeZhHk()
    {
        return view('pages.app.account.unsubscribe-zh-hk');
    }

    public function unsubscribeEnUs()
    {
        return view('pages.app.account.unsubscribe-en-us');
    }

    public function unsubscribeConfirmZhHk()
    {
        return view('pages.app.account.unsubscribe-confirm-zh-hk');
    }

    public function unsubscribeConfirmEnUs()
    {
        return view('pages.app.account.unsubscribe-confirm-en-us');
    }

    public function confirmUnsubscribe(Request $request): JsonResponse
    {
        $this->validate($request, [
            'email' => 'required|email',
        ]);
        $this->data = false;
        $app_cancel_account_admin_email = env('APP_CANCEL_ACCOUNT_ADMIN_EMAIL');
        if (empty($app_cancel_account_admin_email)) {
            $this->code = 500;
            logger()->error('APP_CANCEL_ACCOUNT_ADMIN_EMAIL is empty');
            return $this->returnJson();
        }
        $app_cancel_account_admin_email_list = explode(',', $app_cancel_account_admin_email);
        // 循环发送邮件
        $mail_title = 'App Account Cancellation Notification';
        $mail_content = 'A user submitted an account cancellation request.<br>Email Address: <b>' . $request->email . '</b>';
        foreach ($app_cancel_account_admin_email_list as $email) {
            try {
                Mail::html($mail_content, function ($message) use ($email, $mail_title) {
                    $message->to($email)
                        ->subject($mail_title)
                        ->from(env('MAIL_USERNAME'), env('MAIL_FROM_NAME'));
                });
            } catch (\Exception $e) {
                logger()->error($e->getMessage());
                $this->code = 201;
                return $this->returnJson();
            }
        }

        $this->data = true;

        return $this->returnJson();
    }
}

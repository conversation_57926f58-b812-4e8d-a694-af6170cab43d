<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;
use App\Models\Traits\Emoji;

class PeakTimeTableItem extends Model
{
    use emoji;

    protected $table = 'peak_time_table_item'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'peak_time_table_item_id'; //定义主键，默认为id
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    protected $fillable = ['peak_time_table_number', 'day_type', 'start_time', 'end_time'];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [
        'start_time' => 0, // 开始范围
        'end_time' => 0, // 结束范围
    ];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];
}

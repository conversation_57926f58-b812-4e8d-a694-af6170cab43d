<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use App\Models\Modules\{
    ChargeRecord,
};
use Illuminate\Contracts\View\{
    Factory,
    View
};
use Illuminate\Foundation\Application;
use Illuminate\Http\{
    JsonResponse,
    Request
};

class OperationReportThreeController extends CommonController
{
    protected static string $module_name = 'operationReportThree'; // 模块名称
    protected ChargeRecord $model;

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new ChargeRecord;
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'time_frame_search' => $request->get('time_frame_search')
        );

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'year');
        $sort = $request->input('sort', 'asc');
        $time_frame_search = $request->input('time_frame_search');
        // 获取数据
        $list_data = array_values($this->listData($time_frame_search));
        // 数据长度
        $list_data_total = count($list_data);

        // 正序、倒序
        $sort_order = strtolower($sort) == 'desc' ? SORT_DESC : SORT_ASC;
        // 年月一起排序
        if (strtolower($order) == 'year' || strtolower($order) == 'month') {
            $year_sort_list = array_column($list_data, 'year');
            $month_sort_list = array_column($list_data, 'month');
            // 多字段排序
            array_multisort($year_sort_list, $sort_order, $month_sort_list, $sort_order, $list_data);
        } else {
            // 取出对应排序key的数据
            $order_sort_list = array_column($list_data, $order);
            array_multisort($order_sort_list, $sort_order, $list_data);
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $list_data_total,
            'recordsFiltered' => $list_data_total,
            "data" => $list_data,
        );

        return response()->json($json);

    }

    public function excelExport(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        // dataTable字段
        $time_frame_search = $request->input('time_frame_search');

        $list_data = $this->listData($time_frame_search);

        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet(); //实例化 Spreadsheet 对象
        $worksheet = $spreadsheet->getActiveSheet();

        $web_title = __("$module_name.web_title");
        $excel_title = array(
            __("$module_name.number_of_starting"),
            __("$module_name.year"),
            __("$module_name.month"),
            __("$module_name.Monday"),
            __("$module_name.Tuesday"),
            __("$module_name.Wednesday"),
            __("$module_name.Thursday"),
            __("$module_name.Friday"),
            __("$module_name.Saturday"),
            __("$module_name.Sunday")
        );

        // 工作簿名称为 "Operation Report 3"
        $worksheet->setTitle($web_title);

        // 设置表头名
        $worksheet->setCellValueByColumnAndRow(3, 1, $excel_title[0]);
        $worksheet->setCellValueByColumnAndRow(1, 2, $excel_title[1]);
        $worksheet->setCellValueByColumnAndRow(2, 2, $excel_title[2]);
        $worksheet->setCellValueByColumnAndRow(3, 2, $excel_title[3]);
        $worksheet->setCellValueByColumnAndRow(4, 2, $excel_title[4]);
        $worksheet->setCellValueByColumnAndRow(5, 2, $excel_title[5]);
        $worksheet->setCellValueByColumnAndRow(6, 2, $excel_title[6]);
        $worksheet->setCellValueByColumnAndRow(7, 2, $excel_title[7]);
        $worksheet->setCellValueByColumnAndRow(8, 2, $excel_title[8]);
        $worksheet->setCellValueByColumnAndRow(9, 2, $excel_title[9]);
        $worksheet->mergeCells('C1:I1');

        // 计算数组长度便于后面遍历
        $len = count($list_data);

        // 因为前面两格表头样式占了两行，所以要加2用于设置单元格样式
        $total_rows = $len + 2;

        //设置单元格样式
        $styleArray = [
            'font' => [
                'bold' => false
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['argb' => '666666'],
                ],
            ],
        ];
        $worksheet->getStyle("A1:I$total_rows")->applyFromArray($styleArray);
        $worksheet->getStyle('A1:I2')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('c0c0c0');

        foreach (range('A', 'I') as $value) {
            $worksheet->getColumnDimension($value)->setWidth(10);
        }
        $index = 0;
        foreach ($list_data as $item) {
            $line = $index + 3; //从表格第3行开始
            $worksheet->setCellValueByColumnAndRow(1, $line, $item['year']);
            $worksheet->setCellValueByColumnAndRow(2, $line, (int)$item['month']);
            $worksheet->setCellValueByColumnAndRow(3, $line, $item['Monday']);
            $worksheet->setCellValueByColumnAndRow(4, $line, $item['Tuesday']);
            $worksheet->setCellValueByColumnAndRow(5, $line, $item['Wednesday']);
            $worksheet->setCellValueByColumnAndRow(6, $line, $item['Thursday']);
            $worksheet->setCellValueByColumnAndRow(7, $line, $item['Friday']);
            $worksheet->setCellValueByColumnAndRow(8, $line, $item['Saturday']);
            $worksheet->setCellValueByColumnAndRow(9, $line, $item['Sunday']);
            $index++;
        }

        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $file_name = $web_title . ' ' . date('Y-m-d H:i:s') . '.xlsx';

        // 客户端文件下载
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $file_name . '"');
        header('Cache-Control: max-age=0');
        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
        exit;
    }

    /**
     * 查询数据
     * @param $time_frame_search
     * @return array
     */
    protected function listData($time_frame_search = null): array
    {
        // 结束日期即当前月份的最后一天
        $end_date = date('Y-m-t 23:59:59');
        // 起始日期即开始时间的第一天 - 当前时间往前推12月
        $start_date = date('Y-m-01 00:00:00', strtotime('-11 month'));
        // 实际结果集
        $real_result_list = array();
        // 默认填充数据
        $default_result_list = array(
            'Monday' => 0,
            'Tuesday' => 0,
            'Wednesday' => 0,
            'Thursday' => 0,
            'Friday' => 0,
            'Saturday' => 0,
            'Sunday' => 0,
        );

        // 搜索时间不为空
        if (!empty($time_frame_search)) {
            // 得到时间区间，分割成开始时间和结束时间
            $time_frame_range = explode(' - ', $time_frame_search);
            // 判断是否是时间类型且为开始时间结束时间类型
            if (count($time_frame_range) == 2 && strtotime($time_frame_range[0]) && strtotime($time_frame_range[1]) && (strtotime($time_frame_range[0]) <= strtotime($time_frame_range[1]))) {
                $start_date = date('Y-m-01 00:00:00', strtotime($time_frame_range[0]));
                $end_date = date('Y-m-t 23:59:59', strtotime($time_frame_range[1]));
            }
        }

        // 获取起始到结束日期下的数据
        $result_list = ChargeRecord::selectRaw('DATE_FORMAT(charge_record.gmt_start,"%Y-%m") AS month_year_key')
            ->selectRaw("DATE_FORMAT(gmt_start,'%W') AS week")
            ->selectRaw("COUNT(*) AS count")
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->whereNotNull('charge_record.gmt_stop')
            ->whereBetween('charge_record.gmt_start', [$start_date, $end_date])
            ->groupBy('month_year_key', 'week')
            ->orderBy('month_year_key')
            ->orderBy('week')
            ->get()
            ->toArray();

        // 合并年月的后结果集 = 根据年月key获取对应年月的星期数
        $new_result_list = $num_week_list = array();
        // 循环原先一维数组，相同键值的进行存储
        foreach ($result_list as $key => $value) {
            $new_result_list[$value['month_year_key']][] = $value;
        }
        // 根据年月key获取对应年月的星期数
        foreach (array_keys($new_result_list) as $year_month) {
            $year = (int)date('Y', strtotime($year_month));
            $month = (int)date('m', strtotime($year_month));
            $num_week_list[$year_month] = $this->countWeeksInMonth($year, $month);
        }

        // 获取开始结束时间的时间戳
        $start_date_timestamp = strtotime($start_date);
        $end_date_timestamp = strtotime($end_date);
        // 根据开始结束日期区间的时间戳循环获取月份 - 例如2022-03 ~ 2023-02
        while ($start_date_timestamp <= $end_date_timestamp) {
            // 当前月份
            $current_month = date('Y-m', $start_date_timestamp);
            // 当前月份结果 - 当有搜索时间且数据为空时即填充0
            $default_result_list['year'] = date('Y', $start_date_timestamp);
            $default_result_list['month'] = date('m', $start_date_timestamp);
            // 将每月填充数据，默认为0
            $real_result_list[$current_month] = $default_result_list;

            if (!empty($new_result_list)) {
                // 如果查询数据存在该月，赋值数据
                if (array_key_exists($current_month, $new_result_list) && !empty($new_result_list[$current_month])) {
                    foreach ($new_result_list[$current_month] as &$item) {
                        // 移除key
                        unset($item['month_year_key']);
                        $num_week_count = $num_week_list[$current_month][$item['week']] ?? 1;
                        // 将每月填充数据，星期的平均值
                        $real_result_list[$current_month][$item['week']] = $item['count'] / $num_week_count;
                    }
                }
            }

            // 每次+1月份作为循环出口
            $start_date_timestamp = strtotime("+1 month", $start_date_timestamp);
        }

        return $real_result_list;
    }

    /**
     * 计算每月的周一至周日的数量
     * @param $year
     * @param $month
     * @return int[]
     */
    protected function countWeeksInMonth($year, $month): array
    {
        $numMondays = 0;
        $numTuesdays = 0;
        $numWednesdays = 0;
        $numThursdays = 0;
        $numFridays = 0;
        $numSaturdays = 0;
        $numSundays = 0;
        $numDays = date('t', mktime(0, 0, 0, $month, 1, $year));
        for ($day = 1; $day <= $numDays; $day++) {
            $date = date('Y-m-d', mktime(0, 0, 0, $month, $day, $year));
            switch (date('N', strtotime($date))) {
                case 1:
                    $numMondays++;
                    break;
                case 2:
                    $numTuesdays++;
                    break;
                case 3:
                    $numWednesdays++;
                    break;
                case 4:
                    $numThursdays++;
                    break;
                case 5:
                    $numFridays++;
                    break;
                case 6:
                    $numSaturdays++;
                    break;
                case 7:
                    $numSundays++;
                    break;
            }
        }

        return array(
            'Monday' => $numMondays,
            'Tuesday' => $numTuesdays,
            'Wednesday' => $numWednesdays,
            'Thursday' => $numThursdays,
            'Friday' => $numFridays,
            'Saturday' => $numSaturdays,
            'Sunday' => $numSundays,
        );
    }
}

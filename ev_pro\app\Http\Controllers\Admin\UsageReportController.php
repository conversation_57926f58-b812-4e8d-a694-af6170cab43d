<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Contracts\View\{
    Factory,
    View
};
use Illuminate\Foundation\Application;
use Illuminate\Http\{
    JsonResponse,
    Request
};
use App\Models\Modules\{
    ChargeRecord,
};
use PhpOffice\PhpSpreadsheet\Writer\Exception;
use App\Enums\{
    RemoteStopChargeReasonEnum,
};

class UsageReportController extends CommonController
{
    protected static string $module_name = 'usageReport'; // 模块名称

    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'time_frame_search' => $request->get('time_frame_search')
        );

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'year');
        $sort = $request->input('sort', 'asc');
        $time_frame_search = $request->input('time_frame_search');
        $result = array();
        // 获取数据
        $list_data = $this->listData($time_frame_search);
        // 数据长度
        $list_data_total = count($list_data);

        // 正序、倒序
        $sort_order = strtolower($sort) == 'desc' ? SORT_DESC : SORT_ASC;
        // 年月一起排序
        if (strtolower($order) == 'year' || strtolower($order) == 'month') {
            $year_sort_list = array_column($list_data, 'year');
            $month_sort_list = array_column($list_data, 'month');
            // 多字段排序
            array_multisort($year_sort_list, $sort_order, $month_sort_list, $sort_order, $list_data);
        } else {
            // 取出对应排序key的数据
            $order_sort_list = array_column($list_data, $order);
            array_multisort($order_sort_list, $sort_order, $list_data);
        }

        // 后统一格式化对应数据
        foreach ($list_data as $item) {
            $result[] = array(
                'year' => $item['year'],
                'month' => $item['month'],
                'revenue_from_charging_session' => __('common.unit_hk') . (double)(bcdiv($item['revenue_from_charging_session'], 100, 1)), // 收费时段收入
                'revenue_from_idling_penalty' => __('common.unit_hk') . (double)(bcdiv($item['revenue_from_idling_penalty'], 100, 1)), // 计算闲置罚款
                'total_power_consumption' => (double)(bcdiv($item['total_power_consumption'], 1000, 3)) . __('common.unit_kwh'), // 总的耗电
                'total_complete_charging_session' => __('common.unit_hk') . (double)(bcdiv($item['total_complete_charging_session'], 100, 1)), // 完整收费环节
                'total_charging_time' => $this->secTime($item['total_charging_time'] ?? 0), // 查询总的充电时间
                'average_charging_time_per_session' => $this->secTime($item['average_charging_time_per_session'] ?? 0), // 每次平均充电时间
                'standard_deviation_of_charging_time' => round($item['standard_deviation_of_charging_time'], 2), // 充电时间标准差
                'longest_charging_time' => $this->secTime($item['longest_charging_time'] ?? 0), // 最长充电时间
                'shortest_charging_time' => $this->secTime($item['shortest_charging_time'] ?? 0), // 最短充电时间
                'average_power_consumption_per_session' => (double)(bcdiv($item['average_power_consumption_per_session'], 1000, 3)) . __('common.unit_kwh'), // 每会话的平均耗电
                'standard_deviation_of_power_consumption' => round($item['standard_deviation_of_power_consumption'], 2), // 耗电标准差
                'most_power_consumption' => (double)(bcdiv($item['most_power_consumption'], 1000, 3)) . __('common.unit_kwh'), // 最大耗电
                'least_power_consumption' => (double)(bcdiv($item['least_power_consumption'], 1000, 3)) . __('common.unit_kwh'), // 最小耗电
                'number_of_waived_charging_session' => round($item['number_of_waived_charging_session']), // 放弃收费的次数
                'total_amount_of_power_consumption_from_waived_charging_session' => (double)(bcdiv($item['total_amount_of_power_consumption_from_waived_charging_session'], 1000, 3)) . __('common.unit_kwh'), // 放弃收费总耗电量
                'number_of_charging_session_stopped_by_admin_card' => round($item['number_of_charging_session_stopped_by_admin_card']), // 管理员卡废弃订单次数
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $list_data_total,
            'recordsFiltered' => $list_data_total,
            "data" => $result,
        );

        return response()->json($json);
    }

    // 导出功能

    /**
     * @throws Exception
     */
    public function excelExport(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        // dataTable字段
        $time_frame_search = $request->input('time_frame_search');
        // 获取数据
        $list_data = $this->listData($time_frame_search);

        // 先把语言赋值才可以在后面array出来表头，不然会输出未编译前的字符串
        $excel_title = array(
            __("$module_name.year"),
            __("$module_name.month"),
            __("$module_name.revenue_from_charging_session") . ' ( ' . __('common.unit_hk_blank') . ' )',
            __("$module_name.revenue_from_idling_penalty") . ' ( ' . __('common.unit_hk_blank') . ' )',
            __("$module_name.total_power_consumption") . ' ( ' . __('common.unit_kwh_blank') . ' )',
            __("$module_name.total_complete_charging_session") . ' ( ' . __('common.unit_hk_blank') . ' )',
            __("$module_name.total_charging_time") . ' ( ' . __('common.unit_mins_blank') . ' )',
            __("$module_name.average_charging_time_per_session") . ' ( ' . __('common.unit_mins_blank') . ' )',
            __("$module_name.standard_deviation_of_charging_time"),
            __("$module_name.longest_charging_time") . ' ( ' . __('common.unit_mins_blank') . ' )',
            __("$module_name.shortest_charging_time") . ' ( ' . __('common.unit_mins_blank') . ' )',
            __("$module_name.average_power_consumption_per_session") . ' ( ' . __('common.unit_kwh_blank') . ' )',
            __("$module_name.standard_deviation_of_power_consumption"),
            __("$module_name.most_power_consumption") . ' ( ' . __('common.unit_kwh_blank') . ' )',
            __("$module_name.least_power_consumption") . ' ( ' . __('common.unit_kwh_blank') . ' )',
            __("$module_name.number_of_waived_charging_session"),
            __("$module_name.total_amount_of_power_consumption_from_waived_charging_session") . ' ( ' . __('common.unit_kwh_blank') . ' )',
            __("$module_name.number_of_charging_session_stopped_by_admin_card"),
        );

        $excel_data = array(
            $excel_title,
        );

        // 后统一格式化对应数据
        // 两个强转string是因为不强转会导致导出excel时对应的列为空数据
        foreach ($list_data as $item) {
            $excel_data[] = array(
                $item['year'],
                (int)$item['month'],
                bcdiv($item['revenue_from_charging_session'], 100, 1),
                bcdiv($item['revenue_from_idling_penalty'], 100, 1),
                bcdiv($item['total_power_consumption'], 1000, 3),
                bcdiv($item['total_complete_charging_session'], 100, 1),
                $this->secondToMinute($item['total_charging_time']),
                $this->secondToMinute($item['average_charging_time_per_session']),
                (string)round($item['standard_deviation_of_charging_time'], 2),
                $this->secondToMinute($item['longest_charging_time']),
                $this->secondToMinute($item['shortest_charging_time']),
                bcdiv($item['average_power_consumption_per_session'], 1000, 3),
                (string)round($item['standard_deviation_of_power_consumption'], 2),
                bcdiv($item['most_power_consumption'], 1000, 3),
                bcdiv($item['least_power_consumption'], 1000, 3),
                (string)round($item['number_of_waived_charging_session']),
                bcdiv($item['total_amount_of_power_consumption_from_waived_charging_session'], 1000, 3),
                (string)round($item['number_of_charging_session_stopped_by_admin_card']),
            );
        }

        $this->_export($excel_data, __("$module_name.web_title"));

        return $this->returnJson();
    }

    /**
     * 查询数据
     * @param $time_frame_search
     * @return array
     */
    protected function listData($time_frame_search = null): array
    {
        // 结束日期即当前月份的最后一天
        $end_date = date('Y-m-t 23:59:59');
        // 起始日期即开始时间的第一天 - 当前时间往前推12月
        $start_date = date('Y-m-01 00:00:00', strtotime('-11 month'));

        // 实际结果集
        $real_result_list = array();
        // 默认填充数据
        $default_result_list = array(
            'revenue_from_charging_session' => 0,
            'revenue_from_idling_penalty' => 0,
            'total_power_consumption' => 0,
            'total_complete_charging_session' => 0,
            'total_charging_time' => 0,
            'average_charging_time_per_session' => 0,
            'standard_deviation_of_charging_time' => 0,
            'longest_charging_time' => 0,
            'shortest_charging_time' => 0,
            'average_power_consumption_per_session' => 0,
            'standard_deviation_of_power_consumption' => 0,
            'most_power_consumption' => 0,
            'least_power_consumption' => 0,
            'number_of_waived_charging_session' => 0,
            'total_amount_of_power_consumption_from_waived_charging_session' => 0,
            'number_of_charging_session_stopped_by_admin_card' => 0,
        );

        // 搜索时间不为空
        if (filled($time_frame_search)) {
            // 得到时间区间，分割成开始时间和结束时间
            $time_frame_range = explode(' - ', $time_frame_search);
            // 判断是否是时间类型且为开始时间结束时间类型
            if (count($time_frame_range) == 2 && strtotime($time_frame_range[0]) && strtotime($time_frame_range[1]) && (strtotime($time_frame_range[0]) <= strtotime($time_frame_range[1]))) {
                $start_date = date('Y-m-01 00:00:00', strtotime($time_frame_range[0]));
                $end_date = date('Y-m-t 23:59:59', strtotime($time_frame_range[1]));
            }
        }

        // 获取起始到结束日期下的数据
        $result_list = ChargeRecord::leftJoin('charge_payment_record', 'charge_record.charge_record_number', '=', 'charge_payment_record.charge_record_number')
            ->selectRaw('DATE_FORMAT(charge_record.gmt_start,"%Y-%m") AS month_year_key')     // 查询年月用于后面分割年和月还有sql查询相关年月的数据
            ->selectRaw('SUM(UNIX_TIMESTAMP(charge_record.gmt_stop)-UNIX_TIMESTAMP(charge_record.gmt_start)) AS total_charging_time')     // 查询总的充电时间
            ->selectRaw('SUM(charge_payment_record.total_amount) AS total_complete_charging_session') // 完整收费环节用的charge_payment_record里的total_amount字段查询
            ->selectRaw('SUM(charge_payment_record.charge_value_amount) AS revenue_from_charging_session') // 收费时段收入
            ->selectRaw('SUM(charge_payment_record.idling_penalty_amount) AS revenue_from_idling_penalty') // 计算闲置罚款
            ->selectRaw('SUM(charge_record.charged_energy) AS total_power_consumption') // 总的耗电
            ->selectRaw('SUM(UNIX_TIMESTAMP(charge_record.gmt_stop)-UNIX_TIMESTAMP(charge_record.gmt_start)) / count(*) AS average_charging_time_per_session') // 每次平均充电时间
            ->selectRaw('STDDEV(UNIX_TIMESTAMP(charge_record.gmt_stop)-UNIX_TIMESTAMP(charge_record.gmt_start)) AS standard_deviation_of_charging_time') // 充电时间标准差
            ->selectRaw('MAX(UNIX_TIMESTAMP(charge_record.gmt_stop)-UNIX_TIMESTAMP(charge_record.gmt_start)) AS longest_charging_time') // 最长充电时间
            ->selectRaw('MIN(UNIX_TIMESTAMP(charge_record.gmt_stop)-UNIX_TIMESTAMP(charge_record.gmt_start)) AS shortest_charging_time') // 最短充电时间
            ->selectRaw('AVG(charged_energy / (UNIX_TIMESTAMP(charge_record.gmt_stop)-UNIX_TIMESTAMP(charge_record.gmt_start))) AS average_power_consumption_per_session') // 每会话的平均耗电
            ->selectRaw('STDDEV(charged_energy / (UNIX_TIMESTAMP(charge_record.gmt_stop)-UNIX_TIMESTAMP(charge_record.gmt_start))) AS standard_deviation_of_power_consumption') // 耗电标准差
            ->selectRaw('MAX(charged_energy / (UNIX_TIMESTAMP(charge_record.gmt_stop)-UNIX_TIMESTAMP(charge_record.gmt_start))) AS most_power_consumption') // 最大耗电
            ->selectRaw('MIN(charged_energy / (UNIX_TIMESTAMP(charge_record.gmt_stop)-UNIX_TIMESTAMP(charge_record.gmt_start))) AS least_power_consumption') // 最小耗电
            ->selectRaw("SUM(CASE WHEN ((remote_stop_reason = '" . RemoteStopChargeReasonEnum::KioskWaived . "' OR remote_stop_reason = '" . RemoteStopChargeReasonEnum::CsWaived . "') AND DATE_FORMAT(charge_record.gmt_start,'%Y-%m')) THEN 1 ELSE 0 END) AS number_of_waived_charging_session") // 放弃收费的次数
            ->selectRaw("SUM(CASE WHEN ((remote_stop_reason = '" . RemoteStopChargeReasonEnum::KioskWaived . "' OR remote_stop_reason = '" . RemoteStopChargeReasonEnum::CsWaived . "') AND DATE_FORMAT(charge_record.gmt_start,'%Y-%m')) THEN charged_energy ELSE 0 END) AS total_amount_of_power_consumption_from_waived_charging_session") // 放弃收费总耗电量
            ->selectRaw("SUM(CASE WHEN ((remote_stop_reason = '" . RemoteStopChargeReasonEnum::AdminOctopusCard . "') AND DATE_FORMAT(charge_record.gmt_start,'%Y-%m')) THEN 1 ELSE 0 END) AS number_of_charging_session_stopped_by_admin_card") // 管理员卡废弃订单次数
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('charge_record.site_number', auth()->user()->site_number_list))
            ->whereNotNull('charge_record.gmt_stop')
            ->whereBetween('charge_record.gmt_start', [$start_date, $end_date])
            ->groupBy('month_year_key')
            ->get()
            ->toArray();

        // 获取开始结束时间的时间戳
        $start_date_timestamp = strtotime($start_date);
        $end_date_timestamp = strtotime($end_date);
        // 根据开始结束日期区间的时间戳循环获取月份 - 例如2022-03 ~ 2023-02
        while ($start_date_timestamp <= $end_date_timestamp) {
            // 当前月份
            $current_month = date('Y-m', $start_date_timestamp);
            // 当前月份结果 - 当有搜索时间且数据为空时即填充0
            $default_result_list['year'] = date('Y', $start_date_timestamp);
            $default_result_list['month'] = date('m', $start_date_timestamp);
            // 将每月填充数据，默认为0
            $real_result_list[$current_month] = $default_result_list;

            if (!empty($result_list)) {
                // 将查询的结果集根据month_year_key拆分
                $new_result_list = array_column($result_list, null, 'month_year_key');
                // 如果查询数据存在该月，赋值数据
                if (array_key_exists($current_month, $new_result_list)) {
                    // 移除key
                    unset($new_result_list[$current_month]['month_year_key']);
                    // 将查询数据全部转成number类型
                    $new_result_list[$current_month] = array_map(function ($v) {
                        // 转化成数字
                        return (double)$v;
                    }, $new_result_list[$current_month]);
                    $new_result_list[$current_month]['year'] = date('Y', $start_date_timestamp);
                    $new_result_list[$current_month]['month'] = date('m', $start_date_timestamp);
                    $real_result_list[$current_month] = $new_result_list[$current_month];
                }
            }

            // 每次+1月份作为循环出口
            $start_date_timestamp = strtotime("+1 month", $start_date_timestamp);
        }

        return $real_result_list;
    }

    // 用于时间计算 计算时分秒
    protected function secTime($time): string
    {
        if (is_numeric($time)) {
            $hour = floor($time / 3600);
            $minute = floor(($time - 3600 * $hour) / 60);
            $second = floor((($time - 3600 * $hour) - 60 * $minute) % 60);
            return $hour . __('common.unit_h') . $minute . __('common.unit_m') . $second . __('common.unit_s');
        }
        return 0 . __('common.unit_s');
    }

    // 将秒数转成分钟，不保留小数并向上取整
    protected function secondToMinute($time): string
    {
        if (blank($time) || !is_numeric($time)) return '0';

        return (string)ceil($time / 60);
    }
}

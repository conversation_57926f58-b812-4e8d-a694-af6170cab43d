<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use Illuminate\Foundation\Application;
use Illuminate\Http\{
    RedirectResponse,
    Request,
};
use Validator;

class YedpaySettingController extends CommonController
{
    protected static string $module_name = 'yedpaySetting'; // 模块名称

    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    public function showPage(?Request $request): View|Application|Factory|RedirectResponse
    {
        if ($request->isMethod('post')) {
            return $this->modelValidateAndSave($request);
        }

        // Yed支付配置数据
        $config = getArrayFromJsonFile('yedpay_config', false) ?? array();
        $api_key = $request->old('api_key', $config['API_KEY'] ?? '');
        $sign_key = $request->old('sign_key', $config['SIGN_KEY'] ?? '');

        $data = array(
            'module_name' => self::$module_name,
            'api_key' => $api_key,
            'sign_key' => $sign_key,
        );

        return view("pages.{$data['module_name']}.view", $data);
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    protected function modelValidateAndSave(Request $request): RedirectResponse
    {
        $request->validate(self::rules($request), [], self::attributes());
        $api_key = $request->api_key;
        $sign_key = $request->sign_key;

        if (filled($api_key) && filled($sign_key)) {
            // 先清空再保存数据至json文件
            saveArrayToJSONFile(array('API_KEY' => $api_key, 'SIGN_KEY' => $sign_key), 'yedpay_config');
            // 设置缓存数据，刷新后自动清除
            session()->flash('is_yedpay_setting_saved');
        }

        return redirect()->action([self::class, 'showPage']);
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @return array
     */
    protected static function rules(?Request $request): array
    {
        $rules = array(
            'api_key' => 'required|max:45',
            'sign_key' => 'required|max:45',
        );

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'api_key' => __("$module_name.api_key"),
            'sign_key' => __("$module_name.sign_key"),
        ];
    }

}

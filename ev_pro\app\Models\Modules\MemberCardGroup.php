<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;

class MemberCardGroup extends Model
{
    protected $table = 'member_card_group';
    protected $primaryKey = 'member_card_group_id';

    const CREATED_AT = 'gmt_create'; //默认是小写的模型类名复数格式，此处为自定义表名
    const UPDATED_AT = 'gmt_modified'; //定义主键，默认为id

    // protected $fillable = [];
    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 类型转化器
     */
    protected $casts = [
        'name_json' => 'array',
    ];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [

    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    protected function asJson($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    // 一对一关联商户
    public function merchant()
    {
        return $this->hasOne(Merchant::class, 'merchant_number', 'merchant_number');
    }

    // 一对一关联场地
    public function site()
    {
        return $this->hasOne(Site::class, 'site_number', 'site_number');
    }

    // 一对多关联会员卡
    public function memberCard()
    {
        return $this->hasMany(MemberCard::class, 'member_card_group_id', 'member_card_group_id');
    }

    /**
     * 一对多关联description
     */
    public function description()
    {
        return $this->hasMany(MemberCardGroupDescription::class, 'member_card_group_id', 'member_card_group_id');
    }

    /**
     * 一对多关联simpleTariffTableItem
     */
    public function simpleTariffTableItem()
    {
        return $this->hasMany(SimpleTariffTableItem::class, 'member_card_group_id', 'member_card_group_id');
    }

    /**
     * 一对多关联TimeTariffTableItem
     */
    public function timeTariffTableItem()
    {
        return $this->hasMany(TimeTariffTableItem::class, 'member_card_group_id', 'member_card_group_id');
    }

    /**
     * 一对多关联EnergyTariffTableItem
     */
    public function energyTariffTableItem()
    {
        return $this->hasMany(EnergyTariffTableItem::class, 'member_card_group_id', 'member_card_group_id');
    }

    /**
     * 一对多关联IdlingPenaltyTariffTableItem
     */
    public function idlingPenaltyTariffTableItem()
    {
        return $this->hasMany(IdlingPenaltyTariffTableItem::class, 'member_card_group_id', 'member_card_group_id');
    }

}

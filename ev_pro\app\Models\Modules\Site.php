<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;
use app\Models\Modules\Permission\Role;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Site extends Model
{
    protected $table = 'site'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'site_id'; //定义主键，默认为id
    // 表的唯一number
    public static $table_number = 'site_number';
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    // protected $fillable = [];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [
        'maximum_charging_connector_count' => 0,
        'sort_order' => 0, // 排序
    ];

    /**
     * 类型转化器
     */
    protected $casts = [
        'name_json' => 'array',
        'is_public_site' => 'boolean',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    protected function asJson($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    // creating event
    public static function boot()
    {
        parent::boot();

        static::deleting(function ($model) {
            $model->description()->delete();
            $model->image()->delete();
        });
    }

    /**
     * 一对多关联充电机
     */
    public function chargePoint()
    {
        return $this->hasMany(ChargePoint::class, 'site_number', 'site_number');
    }

    /**
     * 一对多关联Kiosk
     */
    public function kiosk()
    {
        return $this->hasMany(Kiosk::class, 'site_number', 'site_number');
    }

    /**
     * 一对多关联description
     */
    public function description()
    {
        return $this->hasMany(SiteDescription::class, 'site_number', 'site_number');
    }

    /**
     * 一对多关联image
     */
    public function image()
    {
        return $this->hasMany(SiteImage::class, 'site_number', 'site_number');
    }

    /**
     * 一对一关联商户
     */
    public function merchant()
    {
        return $this->hasOne(Merchant::class, 'merchant_number', 'merchant_number');
    }

    /**
     * 一对多关联chargeRecord
     */
    public function chargeRecord()
    {
        return $this->hasMany(ChargeRecord::class, 'site_number', 'site_number');
    }

    // 角色分配到的场地中间表
    public function role(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'role_to_site', 'site_number', 'role_id', 'site_number', 'role_id');
    }

    /**
     * 一对一关联地区
     */
    public function region()
    {
        return $this->hasOne(Region::class, 'region_id', 'region_id');
    }
}

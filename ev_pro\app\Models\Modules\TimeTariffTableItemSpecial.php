<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;

class TimeTariffTableItemSpecial extends Model
{
    protected $table = 'time_tariff_table_item_special'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'time_tariff_table_item_special_id'; //定义主键，默认为id
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    protected $fillable = ['time_tariff_table_item_number', 'start_time', 'end_time', 'normal_rate', 'concessionary_rate'];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [
        'start_time' => 0, // 开始时间
        'end_time' => 0, // 结束时间
        'normal_rate' => 0, // 正常收费
        'concessionary_rate' => 0, // 优惠收费
    ];

    /**
     * 类型转化器
     */
    protected $casts = [

    ];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];
}

<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use App\Models\Modules\Permission\Permission;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Foundation\Application;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class PermissionController extends CommonController
{
    protected static string $module_name = 'permission'; // 模块名称
    protected Permission $model;

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new Permission;
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $name_search = $request->input('name_search', '');
        $path_search = $request->input('path_search', '');

        $data = array(
            'module_name' => self::$module_name,
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'name_search' => $name_search,
            'path_search' => $path_search,
        );

        return view("pages.{$data['module_name']}.list", $data);
    }

    /**
     * 根据id获取permission
     */
    public function getPermission(Request $request): JsonResponse
    {
        $permission_id = $request->input('permission_id');

        if (!empty($permission_id) && filled($permission = Permission::find($permission_id))) {
            $this->data = array(
                'permission_id' => $permission->permission_id,
                'path' => $permission->path,
                'name' => $permission->name,
                'remark' => $permission->remark ?? '',
                'gmt_create' => $permission->gmt_create->toDateTimeString(),
            );
        } else {
            $this->notFoundData('Permission');
        }

        return $this->returnJson();
    }

    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'sort_order');
        $sort = $request->input('sort', 'asc');
        $length = (int)$request->input('length', 10);
        $path_search = $request->input('path_search', '');
        $name_search = $request->input('name_search', '');

        $where = array();

        if (filled($path_search)) {
            $where[] = ['path', 'like', '%' . $path_search . '%'];
        }
        if (filled($name_search)) {
            $where[] = ['name', 'like', '%' . $name_search . '%'];
        }

        $permission_list = Permission::where($where)
            ->orderBy($order, $sort)
            ->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($permission_list as $permission) {
            $result[] = array(
                'permission_id' => $permission->permission_id,
                'path' => $permission->path,
                'name' => $permission->name,
                'remark' => $permission->remark ?? '-/-',
                'gmt_create' => $permission->gmt_create->toDateTimeString(),
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $permission_list->total(),
            'recordsFiltered' => $permission_list->total(),
            "data" => $result,
        );

        return response()->json($json);
    }

    public function delete(Request $request): JsonResponse
    {
        $permission_id = $request->input('permission_id');

        if (!empty($permission_id) && filled(Permission::find($permission_id))) {
            Permission::destroy($permission_id);
            $this->clearPermissionCache();
        } else {
            $this->notFoundData('Permission');
        }

        return $this->returnJson();
    }

    public function add(Request $request): JsonResponse
    {
        $request->validate(self::rules($request), [], self::attributes());
        $name = $request->input('name');
        $path = $request->input('path');
        $remark = $request->input('remark');

        $model = new Permission;
        $model->name = $name;
        $model->path = $path;
        $model->remark = $remark;
        $model->save();

        $this->clearPermissionCache();

        $this->data = $model;

        return $this->returnJson();
    }

    public function edit(Request $request): JsonResponse
    {
        $request->validate(self::rules($request), [], self::attributes());
        $permission_id = $request->input('permission_id');
        $name = $request->input('name');
        $path = $request->input('path');
        $remark = $request->input('remark');

        if (!empty($permission_id) && filled($model = Permission::find($permission_id))) {
            $model->name = $name;
            $model->path = $path;
            $model->remark = $remark;
            $model->save();

            $this->clearPermissionCache();

            $this->data = $model;
        } else {
            $this->notFoundData('Permission');
        }

        return $this->returnJson();
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param $permission
     * @return array
     */
    protected function rules($permission): array
    {
        $rules = array(
            'name' => 'required',
            'path' => [
                'required',
                Rule::unique('permission', 'path')->ignore($permission->permission_id, 'permission_id')
            ],
        );

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'name' => __("$module_name.name"),
            'path' => __("$module_name.path"),
        ];
    }

    /**
     * 返回地址栏参数
     *
     * @param Request $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected function getUrlParams(Request $request): array
    {
        return array(
            'is_page' => 'true',
            'name_search' => $request->get('name_search', ''),
            'path' => $request->get('path', ''),
        );
    }
}

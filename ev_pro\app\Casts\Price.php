<?php

namespace App\Casts;

use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;

/**
 * 转换金额
 */
class Price implements CastsAttributes
{
    /**
     * 将取出的金额转换为 '元'
     *
     * @param Model $model
     * @param string $key
     * @param mixed $value
     * @param array $attributes
     * @return string
     */
    public function get($model, $key, $value, $attributes)
    {
        return bcdiv($value, 100, 2);
    }

    /**
     * 转换为 '分' 存储
     *
     * @param Model $model
     * @param string $key
     * @param int $value
     * @param array $attributes
     * @return string
     */
    public function set($model, $key, $value, $attributes)
    {
        return bcmul($value, 100);
    }
}

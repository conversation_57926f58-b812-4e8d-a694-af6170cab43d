<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Exception;
use Illuminate\Contracts\View\{
    Factory,
    View
};
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Application;
use Illuminate\Validation\Rule;
use Illuminate\Http\{
    JsonResponse,
    RedirectResponse,
    Request
};
use App\Enums\{
    WebsocketType,
    PaymentMethod,
    OctopusDataExchangeMode,
    PosVendorEnum,
    OctopusDeviceType,
};
use App\Models\Modules\{
    Kiosk,
    KioskSetting,
    Site,
    Zone,
    ChargePoint,
    KioskPaymentMethod,
};

class KioskController extends CommonController
{
    protected ?Kiosk $model = null;
    protected static string $module_name = 'kiosk'; // 模块名称

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new Kiosk;
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $kiosk_number_search = $request->input('kiosk_number_search');
        $name_search = $request->input('name_search');

        $data = array(
            'module_name' => self::$module_name,
            'add_url' => action([self::class, 'add']),
            'list_url' => action([self::class, 'list']),
            'setting_list_url' => action([KioskSettingController::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'download_template_url' => existsImage('template', 'kiosk_template.xlsx'),
            'name_search' => $name_search,
            'kiosk_number_search' => $kiosk_number_search,
        );

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'sort_order');
        $sort = $request->input('sort', 'asc');
        $length = (int)$request->input('length', 10);
        $kiosk_number_search = $request->input('kiosk_number_search');
        $name_search = $request->input('name_search');

        if ($order == 'kiosk_setting_name') $order = 'kiosk_setting.name';
        if ($order == 'site_name') $order = 'site.name_json';
        if ($order == 'zone_name') $order = 'zone.name_json';

        $data_list = Kiosk::query()
            ->select('kiosk.*', 'kiosk_setting.name as kiosk_setting_name_init', 'site.name_json as site_name_json_init', 'zone.name_json as zone_name_json_init')
            ->leftJoin('kiosk_setting', 'kiosk.kiosk_setting_number', '=', 'kiosk_setting.kiosk_setting_number')
            ->leftJoin('site', 'kiosk.site_number', '=', 'site.site_number')
            ->leftJoin('zone', 'kiosk.zone_number', '=', 'zone.zone_number')
            ->when(filled($kiosk_number_search), fn($query) => $query->where('kiosk_number', 'like', "%$kiosk_number_search%"))
            ->when(filled($name_search), fn($query) => $query->where('kiosk.name', 'like', "%$name_search%"))
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('kiosk.site_number', auth()->user()->site_number_list))
            ->orderBy($order, $sort)
            ->orderBy('gmt_modified', 'desc')
            ->paginate($length, ['*'], 'start');

        $result = array();

        foreach ($data_list as $data) {
            $result[] = array(
                'kiosk_id' => $data->kiosk_id, // Kiosk ID
                'kiosk_number' => $data->kiosk_number, // Kiosk编号
                'kiosk_setting_name' => $data->kiosk_setting_name_init ?? '—/—', // 设置名称
                'kiosk_setting_number' => $data->kiosk_setting_number, // 设置编号
                'name' => $data->name, // 名称
                'current_display_screen' => $data->current_display_screen ?? '—/—', // 用于记录屏幕操作日志时同时记录Kiosk当前所在屏幕
                'current_build_version' => $data->current_build_version ?? '—/—', // 当前构建版本
                'site_name' => $this->getValueFromLanguageArray($data->site_name_json_init) ?? '—/—', // 场地
                'zone_name' => $this->getValueFromLanguageArray($data->zone_name_json_init) ?? '—/—', // 区域
                'ip_address' => $data->ip_address ?? '—/—', // IP地址
                'gmt_last_boot' => $data->gmt_last_boot ?? '—/—', // 最后启动时间
                'gmt_last_alive' => $data->gmt_last_alive ?? '—/—', // 最后活跃时间
                'octopus_device_type' => OctopusDeviceType::getDescription($data->octopus_device_type), // 八达通设备类型
                'octopus_com_port' => $data->octopus_com_port, // 八达通串口
                'octopus_baud_rate' => $data->octopus_baud_rate, // 八达通波特率
                'octopus_data_exchange_program_local_path' => $data->octopus_data_exchange_program_local_path, // 八达通上传本间本地路径
                'octopus_pre_authorization_amount' => is_null($data->octopus_pre_authorization_amount) ? '—/—' : (__('common.unit_hk') . (float)bcdiv($data->octopus_pre_authorization_amount, 100, 1)), // 八达通预授权金额
                'octopus_device_number' => $data->octopus_device_number ?? '—/—', // 八达通设备编号
                'gmt_octopus_last_upload' => $data->gmt_octopus_last_upload ?? '—/—', // 八达通最后上传时间
                'gmt_octopus_last_download' => $data->gmt_octopus_last_download ?? '—/—', // 八达通最后下载时间
                'pos_vendor' => PosVendorEnum::getDescription($data->pos_vendor), // POS供应商
                'yedpay_pos_api_url' => $data->yedpay_pos_api_url ?? '—/—', // Yedpay POS机API路径
                'yedpay_pos_api_key' => $data->yedpay_pos_api_key ?? '—/—', // Yedpay POS机API 键
                'yedpay_pos_pre_authorization_amount' => is_null($data->yedpay_pos_pre_authorization_amount) ? '—/—' : (__('common.unit_hk') . (float)bcdiv($data->yedpay_pos_pre_authorization_amount, 100, 1)), // Yedpay POS机预授权金额
                'soe_pay_pos_api_url' => $data->soe_pay_pos_api_url ?? '—/—', // SoePay POS机API路径
                'soe_pay_pos_api_key' => $data->soe_pay_pos_api_key ?? '—/—', // SoePay POS机API键
                'soe_pay_pos_api_token' => $data->soe_pay_pos_api_token ?? '—/—', // SoePay POS机API Token
                'torch_port_name' => $data->torch_port_name ?? '—/—', // 闪光灯串口名称
                'torch_baud_rate' => $data->torch_baud_rate ?? '—/—', // 闪光灯波特率
                'torch_data_bits' => $data->torch_data_bits ?? '—/—', // 闪光灯数据比特
                'sort_order' => $data->sort_order, // 排序
                'remark' => $data->remark ?? '—/—', // 备注
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
                'status' => false,   // 连接状态 - 是否在线
            );
        }

        // 获取当前所有kiosk状态
        if ($data_list->count() > 0) {
            // 获取所有kiosk的在线状态，返回在线的kiosk数组
            $online_kiosk_list = $this->websocketIsUidOnlineByKioskList($data_list->items());
            if (filled($online_kiosk_list)) {
                // 取出在线的kiosk数组
                $online_kiosk_number_list = collect($online_kiosk_list)->pluck('kiosk_number')->toArray();
                // 从结果集找出在线的kiosk并且修改是否在线
                $result = collect($result)->map(function ($item) use ($online_kiosk_number_list) {
                    $item['status'] = in_array($item['kiosk_number'], $online_kiosk_number_list);
                    return $item;
                })->toArray();
            }
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );

        return response()->json($json);
    }

    protected function getForm(Request $request, array $data): View|Application|Factory
    {
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'],
            self::getUrlParams($request)
        );
        if (blank($data['model']->kiosk_id)) {
            $data['model']->kiosk_number = $request->old('kiosk_number', $data['model']->kiosk_number); // Kiosk编号
        }

        $site_number = $data['model']->site_number;
        if ((isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) && blank($data['model']?->kiosk_id)) {
            // 如果是新增，并且是超级管理员或者管理员的商户大于1的情况下就直接获取页面传入的商户编号
            $site_number = $request->old('site_number', $data['model']->site_number);
        } else if (blank($data['model']?->kiosk_id) && auth()->user()->site_number_list->count() === 1) {
            // 如果是新增并且管理员商户只有一个就直接赋值
            $site_number = auth()->user()->site_number_list->first();
        }

        $data['model']->site_number = $site_number; // 场地编号
        $site_name = $this->getValueFromLanguageArray(
            Site::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->firstWhere('site_number', $site_number)?->name_json
        );
        $data['model']->site_name = $site_name; // 场地名称
        $data['model']->name = $request->old('name', $data['model']->name); // 名称
        $data['model']->mac_address = $request->old('mac_address'); // MAC地址
        $data['model']->zone_number = $request->old('zone_number', $data['model']->zone_number); // 区域编号
        $zone_name = $this->getValueFromLanguageArray(
            Zone::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->firstWhere('zone_number', $data['model']->zone_number)?->name_json);
        $data['model']->zone_name = $zone_name; // 区域名称
        $data['model']->kiosk_setting_number = $request->old('kiosk_setting_number', $data['model']->kiosk_setting_number); // 设置ID
        $kiosk_setting_name =
            KioskSetting::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->firstWhere('kiosk_setting_number', $data['model']->kiosk_setting_number)?->name;
        $data['model']->kiosk_setting_name = $kiosk_setting_name; // kiosk group名称
        $data['model']->ip_address = $request->old('ip_address', $data['model']->ip_address); // IP地址
        $data['model']->sort_order = $request->old('sort_order', $data['model']->sort_order); // 排序
        $data['model']->remark = $request->old('remark', $data['model']->remark); // 备注
        $data['model']->octopus_device_type = $request->old('octopus_device_type', $data['model']->octopus_device_type); // 八达通设备类型
        $data['model']->octopus_com_port = $request->old('octopus_com_port', $data['model']->octopus_com_port); // 八达通串口
        $data['model']->octopus_baud_rate = $request->old('octopus_baud_rate', $data['model']->octopus_baud_rate); // 八达通波特率
        $data['model']->octopus_data_exchange_program_local_path = $request->old('octopus_data_exchange_program_local_path', $data['model']->octopus_data_exchange_program_local_path); // 八达通上传本间本地路径
        $data['model']->octopus_pre_authorization_amount = $request->old('octopus_pre_authorization_amount', filled($data['model']->octopus_pre_authorization_amount) ? (float)bcdiv($data['model']->octopus_pre_authorization_amount, 100, 1) : null); // 八达通预授权金额
        $data['model']->pos_vendor = $request->old('pos_vendor', $data['model']->pos_vendor); // POS供应商
        $data['model']->yedpay_pos_api_url = $request->old('yedpay_pos_api_url', $data['model']->yedpay_pos_api_url); // Yedpay POS机API路径
        $data['model']->yedpay_pos_api_key = $request->old('yedpay_pos_api_key', $data['model']->yedpay_pos_api_key); // Yedpay POS机API 键
        $data['model']->yedpay_pos_pre_authorization_amount = $request->old('yedpay_pos_pre_authorization_amount', filled($data['model']->yedpay_pos_pre_authorization_amount) ? (float)bcdiv($data['model']->yedpay_pos_pre_authorization_amount, 100, 1) : null); // Yedpay POS机预授权金额
        $data['model']->soe_pay_pos_api_url = $request->old('soe_pay_pos_api_url', $data['model']->soe_pay_pos_api_url); // SoePay POS机API路径
        $data['model']->soe_pay_pos_api_key = $request->old('soe_pay_pos_api_key', $data['model']->soe_pay_pos_api_key); // SoePay POS机API键
        $data['model']->soe_pay_pos_api_token = $request->old('soe_pay_pos_api_token', $data['model']->soe_pay_pos_api_token); // SoePay POS机API Token
        $data['model']->torch_port_name = $request->old('torch_port_name', $data['model']->torch_port_name); // 闪光灯串口名称
        $data['model']->torch_baud_rate = $request->old('torch_baud_rate', $data['model']->torch_baud_rate); // 闪光灯波特率
        $data['model']->torch_data_bits = $request->old('torch_data_bits', $data['model']->torch_data_bits); // 闪光灯数据比特

        $data['octopus_device_type_list'] = OctopusDeviceType::asSelectArray();
        $data['pos_vendor_list'] = PosVendorEnum::asSelectArray();
        $data['yedpay'] = PosVendorEnum::Yedpay;
        $data['yedpay_pcg'] = PosVendorEnum::Yedpay_PCG;
        $data['soe_pay'] = PosVendorEnum::SoePay;

        return view("pages.{$data['module_name']}.form", $data);
    }

    public function add(Request $request): Application|View|Factory|RedirectResponse
    {
        $data = array();

        $model = $this->model;
        // 如果有kiosk_number的存在就是复制除了kiosk_number和name以外的收费表数据输出到新增页面
        if (filled($kiosk_number = $request->input('_kiosk_number'))) {
            $model = $model->when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->where('kiosk_number', $kiosk_number)->firstOrFail();
            $model->kiosk_number = $model->kiosk_id = $model->name = null;
        }

        if ($request->isMethod('post')) {
            return $this->modelValidateAndSave($request, $model);
        }

        $data['model'] = $model;

        return $this->getForm($request, $data);
    }

    public function edit(Request $request, string $kiosk_number): View|Application|Factory|RedirectResponse|null
    {
        $data = array();

        $model = $this->model->when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->where('kiosk_number', $kiosk_number)->firstOrFail();

        if ($request->isMethod('post')) {
            return $this->modelValidateAndSave($request, $model);
        }
        $data['model'] = $model;

        return $this->getForm($request, $data);
    }

    public function delete(Request $request): JsonResponse
    {
        $kiosk_number = $request->post('kiosk_number');
        $module_name = self::$module_name;

        $kiosk = Kiosk::with('chargePoint')->when(!isSuperAdministrator(), fn($query) => $query->whereIn('kiosk.site_number', auth()->user()->site_number_list))->firstWhere('kiosk_number', $kiosk_number);
        if (filled($kiosk_number) && filled($kiosk)) {
            // 如果未绑定ChargePoint才可以删除
            if (($kiosk->chargePoint?->count() ?? 0) === 0 && ($kiosk->kioskPaymentMethod?->count() ?? 0) === 0) {
                // 删除kiosk
                $kiosk->delete();

                self::sendInitPushByKioskNumberList();
            } else {
                $this->code = 40001;
                $this->message = __("$module_name.error_has_binding_unable_to_delete_kiosk");
            }
        } else {
            $this->code = 404;
            $this->message = __('common.text_not_found', ['field' => __("$module_name.kiosk_number")]);
        }

        return $this->returnJson();
    }

    public function editSetting(Request $request): JsonResponse
    {
        $module_name = self::$module_name;

        $kiosk_number = $request->input('kiosk_number');
        $kiosk_setting_number = $request->input('kiosk_setting_number');

        $kiosk_setting = KioskSetting::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('kiosk_setting_number', $kiosk_setting_number);
        $kiosk = Kiosk::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('kiosk_number', $kiosk_number);
        if (blank($kiosk_number) || blank($kiosk) || blank($kiosk_setting_number) || blank($kiosk_setting)) {
            $this->missingField(__("$module_name.web_title") . '|' . __("$module_name.kiosk_setting"));
            return $this->returnJson();
        }

        // site不一致无法修改
        if ($kiosk_setting->site_number != $kiosk->site_number) {
            $this->code = 201;
            $this->message = __('common.site_inconsistent_with_field', ['field' => 'Kiosk: ' . $kiosk->name]);
            return $this->returnJson();
        }

        $kiosk->kiosk_setting_number = $kiosk_setting_number;
        $kiosk->save();

        self::sendInitPushByKioskNumberList();

        return $this->returnJson();
    }

    // 初始化Kiosk
    public function initKiosks(Request $request): JsonResponse
    {
        $kiosk_number = $request->input('kiosk_number');
        // 如果不传kiosk_number,并且是超级管理员才能全推送
        if (blank($kiosk_number)) {
            self::sendInitPushByKioskNumberList();
            return $this->returnJson();
        }
        // 如果传入了kiosk_number需要判断是否拥有该kiosk所属场地权限
        if (filled($kiosk_number) && filled(Kiosk::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('kiosk_number', $kiosk_number))) {
            self::sendInitPush(kiosk_number: $kiosk_number);
            return $this->returnJson();
        }

        $this->code = 201;
        $this->message = __('common.site_inconsistent_with_field', ['field' => 'Kiosk']);
        return $this->returnJson();
    }

    /**
     * 获取kiosk绑定的charge point
     *
     * @param Request $request
     * @param [type] $number
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-18
     */
    public function getChargePoint(Request $request, $number): JsonResponse
    {
        $module_name = self::$module_name;

        $kiosk = Kiosk::with('chargePoint')->when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('kiosk_number', $number);
        if (blank($kiosk)) {
            $this->missingField(__("$module_name.web_title"));
        } else {
            // 查出选中的充电机用于左右拖拽
            $kiosk_charge_point_list = $kiosk->chargePoint?->sortBy('pivot.sort_order') ?? array();
            foreach ($kiosk_charge_point_list as $kiosk_charge_point) {
                $this->data['kiosk_charge_point'][] = array(
                    'name' => $kiosk_charge_point->name,
                    'charge_point_number' => $kiosk_charge_point->charge_point_number,
                );
            }

            // 取出未绑定的充电机编号
            $other_charge_point_number_list = array();

            // 判断是否有数据
            if (isset($this->data['kiosk_charge_point'])) {
                for ($i = 0; $i < count($this->data['kiosk_charge_point']); $i++) {
                    $charge_point_number = $this->data['kiosk_charge_point'][$i]['charge_point_number'];
                    $other_charge_point_number_list[] = $charge_point_number;
                }
            }
            // 排除掉选中的充电机用于左右拖拽
            $other_charge_point_list = ChargePoint::whereNotIn('charge_point_number', $other_charge_point_number_list)
                ->where('site_number', $kiosk->site_number)
                ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->orderBy('sort_order')
                ->get();
            foreach ($other_charge_point_list as $other_charge_point) {
                $this->data['other_charge_point'][] = array(
                    'name' => $other_charge_point->name,
                    'charge_point_number' => $other_charge_point->charge_point_number,
                );
            }
        }

        return $this->returnJson();
    }

    /**
     * 提交保存kiosk关联的charge point
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-18
     */
    public function submitChargePoint(Request $request): JsonResponse
    {
        $module_name = self::$module_name;

        $number = $request->input('number');
        $charge_point_number_list = $request->input('charge_point_number_list', []);

        $kiosk = Kiosk::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('kiosk_number', $number);
        if (blank($number) || blank($kiosk)) {
            $this->missingField(__("$module_name.web_title"));
            return $this->returnJson();
        }

        $charge_point_list = array();
        // 给sort_order赋值用于排序
        foreach ($charge_point_number_list as $index => $charge_point_number) {
            $charge_point_list[$charge_point_number] = array(
                'sort_order' => $index,
                'is_enable' => 1,
            );
        }

        // 判断充电机与kiosk的场地是否一致
        $charge_point_result_count = ChargePoint::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->where('site_number', $kiosk->site_number)
            ->whereIn('charge_point_number', $charge_point_number_list)
            ->count();
        // 如果长度不一致说明充电机数组存在商户不同
        if ($charge_point_result_count != count($charge_point_number_list)) {
            $this->code = 201;
            $this->message = __('common.site_inconsistent_with_field', ['field' => 'Kiosk: ' . $kiosk->name]);
            return $this->returnJson();
        }

        $kiosk->chargePoint()->sync($charge_point_list);

        self::sendInitPushByKioskNumberList();

        return $this->returnJson();
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @param Kiosk $model
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    protected function modelValidateAndSave(Request $request, Kiosk $model): RedirectResponse
    {
        $request->validate(self::rules($request, $model), [], self::attributes());
        // 判断是否是新增
        if (blank($model->kiosk_id)) {
            $model = $this->model;
            $model->kiosk_number = $request->input('kiosk_number');
            $site_number = $model->site_number;

            if (isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) {
                // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下就直接获取页面传入的场地编号
                $site_number = $request->input('site_number');
            } else if (auth()->user()->site_number_list->count() === 1) {
                // 如果是新增并且管理员场地只有一个就直接赋值
                $site_number = auth()->user()->site_number_list->first();
            }
            $model->site_number = $site_number;
            $model->merchant_number = Site::firstWhere('site_number', $model->site_number)?->merchant_number;
        }

        $pos_vendor = $request->input('pos_vendor');
        // 拼接年月日
        $year_month_day = date('Y') . '-01-01 ';
        $model->name = $request->input('name');
        $model->zone_number = $request->input('zone_number');
        $model->octopus_device_type = $request->input('octopus_device_type');
        $model->octopus_com_port = $request->input('octopus_com_port');
        $model->octopus_baud_rate = $request->input('octopus_baud_rate');
        $model->octopus_data_exchange_program_local_path = $request->input('octopus_data_exchange_program_local_path');
        $model->octopus_pre_authorization_amount =$request->input('octopus_pre_authorization_amount');
        if (filled($model->octopus_pre_authorization_amount)) {
            $model->octopus_pre_authorization_amount = bcmul($model->octopus_pre_authorization_amount, 100);
        }
        $model->pos_vendor = $pos_vendor;
        if ($pos_vendor == PosVendorEnum::Yedpay) {
            $model->yedpay_pos_api_url = $request->input('yedpay_pos_api_url');
            $model->yedpay_pos_api_key = $request->input('yedpay_pos_api_key');
        } else if ($pos_vendor == PosVendorEnum::Yedpay_PCG) {
            $model->yedpay_pos_api_url = $request->input('yedpay_pos_api_url');
            $model->yedpay_pos_api_key = $request->input('yedpay_pos_api_key');
            $model->yedpay_pos_pre_authorization_amount = $request->input('yedpay_pos_pre_authorization_amount');
            if (filled($model->yedpay_pos_pre_authorization_amount)) {
                $model->yedpay_pos_pre_authorization_amount = bcmul($model->yedpay_pos_pre_authorization_amount, 100);
            }
        } else if ($pos_vendor == PosVendorEnum::SoePay) {
            $model->soe_pay_pos_api_url = $request->input('soe_pay_pos_api_url');
            $model->soe_pay_pos_api_key = $request->input('soe_pay_pos_api_key');
            $model->soe_pay_pos_api_token = $request->input('soe_pay_pos_api_token');
        } else {
            $model->yedpay_pos_api_url = null;
            $model->yedpay_pos_api_key = null;
            $model->yedpay_pos_pre_authorization_amount = null;
            $model->soe_pay_pos_api_url = null;
            $model->soe_pay_pos_api_key = null;
            $model->soe_pay_pos_api_token = null;
        }
        $model->torch_port_name = $request->input('torch_port_name');
        $model->torch_baud_rate = $request->input('torch_baud_rate');
        $model->torch_data_bits = $request->input('torch_data_bits');
        $model->sort_order = $request->input('sort_order', 0);
        $model->remark = $request->input('remark');
        $model->kiosk_setting_number = $request->input('kiosk_setting_number');

        // Mac地址加密生成license code
        $mac_address = $request->input('mac_address');
        if (filled($mac_address) && config('app.is_enable_automatic_encryption_of_license_code')) $model->license_code = self::licenseCodeEncryption($mac_address);

        $model->save();

        self::sendInitPushByKioskNumberList();

        return redirect()->action(
            [self::class, 'showPage'],
            self::getUrlParams($request)
        );
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function rules(?Request $request, ?Model $model): array
    {
        $module_name = self::$module_name;
        $site_number = $model->site_number;
        if ((isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) && blank($model?->kiosk_id)) {
            // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下就直接获取页面传入的场地编号
            $site_number = $request->input('site_number');
        } else if (blank($model?->kiosk_id) && auth()->user()->site_number_list->count() === 1) {
            // 如果是新增并且管理员场地只有一个就直接赋值
            $site_number = auth()->user()->site_number_list->first();
        }
        $rules = array(
            'name' => 'required|max:45',
            'mac_address' => ['nullable', function ($attr, $value, $fail) use ($module_name, $model) {
                if (filled($value)) {
                    $kiosk = Kiosk::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                        ->where('license_code', self::licenseCodeEncryption($value))
                        ->first();
                    if (filled($kiosk) && $kiosk->kiosk_id != $model->kiosk_id) {
                        $fail(__("$module_name.error_mac_address_exists"));
                    }
                }
            }],
            'zone_number' => [
                'nullable',
                'exists:App\Models\Modules\Zone,zone_number',
                function ($attr, $value, $fail) use ($site_number, $module_name) {
                    // 如果存在site判断是否商户一致
                    if (filled($value) && filled($site_number)) {
                        $zone = Zone::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('zone_number', $value);
                        if (blank($zone)) {
                            $fail(__('common.text_not_found', ['field' => __("$module_name.zone")]));
                        } else if ($site_number != $zone->site_number) {
                            $fail(__('common.site_inconsistent_with_field', ['field' => __("$module_name.zone")]));
                        }
                    }
                },
            ],
            'kiosk_setting_number' => [
                'required',
                'exists:App\Models\Modules\KioskSetting,kiosk_setting_number',
                function ($attr, $value, $fail) use ($site_number, $module_name) {
                    // 如果存在site判断是否商户一致
                    if (filled($site_number)) {
                        $kiosk_setting = KioskSetting::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('kiosk_setting_number', $value);
                        if (blank($kiosk_setting)) {
                            $fail(__('common.text_not_found', ['field' => __("$module_name.kiosk_setting")]));
                        } else if ($site_number != $kiosk_setting->site_number) {
                            $fail(__('common.site_inconsistent_with_field', ['field' => __("$module_name.kiosk_setting")]));
                        }
                    }
                },
            ],
            'octopus_device_type' => 'nullable|enum_value:' . OctopusDeviceType::class,
            'octopus_com_port' => 'nullable|integer|min:0|max:999999',
            'octopus_baud_rate' => 'nullable|integer|min:0|max:999999',
            'octopus_data_exchange_program_local_path' => 'nullable|max:1000',
            'octopus_pre_authorization_amount' => 'nullable|numeric|min:0|max:999999',
            'pos_vendor' => 'nullable|enum_value:' . PosVendorEnum::class,
            // YEDPAY和YEDPAY_PCG需要填写
            'yedpay_pos_api_url' => 'exclude_unless:pos_vendor,' . PosVendorEnum::Yedpay . ',' . PosVendorEnum::Yedpay_PCG . '|required|max:255',
            'yedpay_pos_api_key' => 'exclude_unless:pos_vendor,' . PosVendorEnum::Yedpay . ',' . PosVendorEnum::Yedpay_PCG . '|required|max:255',
            // YEDPAY_PCG需要填写
            'yedpay_pos_pre_authorization_amount' => 'exclude_unless:pos_vendor,' . PosVendorEnum::Yedpay_PCG . '|required|numeric|min:0|max:999999',
            'soe_pay_pos_api_url' => 'exclude_unless:pos_vendor,' . PosVendorEnum::SoePay . '|required|max:255',
            'soe_pay_pos_api_key' => 'exclude_unless:pos_vendor,' . PosVendorEnum::SoePay . '|required|max:255',
            'soe_pay_pos_api_token' => 'exclude_unless:pos_vendor,' . PosVendorEnum::SoePay . '|required|max:255',
            'torch_port_name' => 'nullable|max:45',
            'torch_baud_rate' => 'nullable|integer|min:0|max:999999',
            'torch_data_bits' => 'nullable|integer|min:0|max:999999',
            'sort_order' => 'required|integer|min:0',
            'remark' => 'nullable|max:1000',
        );
        // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下才需要判断场地编号是否存在
        if (blank($model?->kiosk_id)) {
            $rules['kiosk_number'] = [
                'required',
                'unique:App\Models\Modules\Kiosk,kiosk_number',
                'max:30',
                'regex:/^[a-zA-Z0-9]+$/', // 只能填写字母和数字
            ];
            if (isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) {
                $rules['site_number'] = [
                    'required',
                    'exists:App\Models\Modules\Site,site_number',
                    function ($attr, $value, $fail) use ($module_name) {
                        // 判断选择的site是否为当前管理员的场地下的
                        $site = Site::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('site_number', $value);
                        if (blank($site)) $fail(__('common.text_not_found', ['field' => __("$module_name.site")]));
                    },
                ];
            }
        }

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'kiosk_number' => __("$module_name.kiosk_number"),
            'name' => __("$module_name.name"),
            'mac_address' => __("$module_name.mac_address"),
            'site_number' => __("$module_name.site"),
            'zone_number' => __("$module_name.zone"),
            'kiosk_setting_number' => __("$module_name.kiosk_setting"),
            'octopus_device_type' => __("$module_name.octopus_device_type"),
            'octopus_com_port' => __("$module_name.octopus_com_port"),
            'octopus_baud_rate' => __("$module_name.octopus_baud_rate"),
            'octopus_data_exchange_program_local_path' => __("$module_name.octopus_data_exchange_program_local_path"),
            'octopus_pre_authorization_amount' => __("$module_name.octopus_pre_authorization_amount"),
            'pos_vendor' => __("$module_name.pos_vendor"),
            'yedpay_pos_api_url' => __("$module_name.yedpay_pos_api_url"),
            'yedpay_pos_api_key' => __("$module_name.yedpay_pos_api_key"),
            'yedpay_pos_pre_authorization_amount' => __("$module_name.yedpay_pos_pre_authorization_amount"),
            'soe_pay_pos_api_url' => __("$module_name.soe_pay_pos_api_url"),
            'soe_pay_pos_api_key' => __("$module_name.soe_pay_pos_api_key"),
            'soe_pay_pos_api_token' => __("$module_name.soe_pay_pos_api_token"),
            'torch_port_name' => __("$module_name.torch_port_name"),
            'torch_baud_rate' => __("$module_name.torch_baud_rate"),
            'torch_data_bits' => __("$module_name.torch_data_bits"),
            'sort_order' => __("$module_name.sort_order"),
            'remark' => __("$module_name.remark"),
        ];
    }

    /**
     * 返回地址栏参数
     *
     * @param Request $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => 'true',
            'kiosk_number_search' => $request->get('kiosk_number_search'),
            'name_search' => $request->get('name_search'),
        );
    }

    /**
     * 软重启
     *
     * @param Request $request
     * @param [type] $number
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-10-28
     */
    public function softReboot(Request $request, $number): JsonResponse
    {
        $module_name = self::$module_name;
        if (!empty($number)) {
            $model = Kiosk::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('kiosk_number', $number);
            if (filled($model)) {
                self::websocketPush(WebsocketType::SoftRebootNotification, null, $model->kiosk_number);
            } else {
                $this->notFoundData(__("$module_name.kiosk_number"));
            }
        } else {
            $this->notFoundData(__("$module_name.kiosk_number"));
        }
        return $this->returnJson();
    }

    /**
     * 硬重启
     *
     * @param Request $request
     * @param [type] $number
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-10-28
     */
    public function hardReboot(Request $request, $number): JsonResponse
    {
        $module_name = self::$module_name;
        if (!empty($number)) {
            $model = Kiosk::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('kiosk_number', $number);
            if (filled($model)) {
                self::websocketPush(WebsocketType::HardRebootNotification, null, $model->kiosk_number);
            } else {
                $this->notFoundData(__("$module_name.kiosk_number"));
            }
        } else {
            $this->notFoundData(__("$module_name.kiosk_number"));
        }
        return $this->returnJson();
    }

    /**
     * 软关机
     *
     * @param Request $request
     * @param [type] $number
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-10-28
     */
    public function softShutdown(Request $request, $number): JsonResponse
    {
        $module_name = self::$module_name;
        if (!empty($number)) {
            $model = Kiosk::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('kiosk_number', $number);
            if (filled($model)) {
                self::websocketPush(WebsocketType::SoftShutdownNotification, null, $model->kiosk_number);
            } else {
                $this->notFoundData(__("$module_name.kiosk_number"));
            }
        } else {
            $this->notFoundData(__("$module_name.kiosk_number"));
        }
        return $this->returnJson();
    }

    /**
     * 硬关机
     *
     * @param Request $request
     * @param [type] $number
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-10-28
     */
    public function hardShutdown(Request $request, $number): JsonResponse
    {
        $module_name = self::$module_name;
        if (!empty($number)) {
            $model = Kiosk::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('kiosk_number', $number);
            if (filled($model)) {
                self::websocketPush(WebsocketType::HardShutdownNotification, null, $model->kiosk_number);
            } else {
                $this->notFoundData(__("$module_name.kiosk_number"));
            }
        } else {
            $this->notFoundData(__("$module_name.kiosk_number"));
        }
        return $this->returnJson();
    }

    /**
     * 获取kiosk绑定的charge point
     *
     * @param Request $request
     * @param [type] $number
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-18
     */
    public function getKioskPayment(Request $request, $number): JsonResponse
    {
        $module_name = self::$module_name;
        if (
            empty($number) ||
            blank($model = Kiosk::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('kiosk_number', $number))
        ) {
            $this->missingField(__("$module_name.kiosk_number"));
        } else {
            $kiosk_payments = KioskPaymentMethod::select('payment_method', 'kiosk_number', 'is_enable')
                ->where('kiosk_number', $model->kiosk_number)
                ->orderBy('sort_order')
                ->get();

            $this->data['kiosk_payment'] = array();
            $this->data['payment_method'] = array();

            // 获取已选择的支付方式
            foreach ($kiosk_payments as $kiosk_payment) {
                $this->data['kiosk_payment'][] = array(
                    'value' => $kiosk_payment->payment_method,
                    'name' => PaymentMethod::getDescription($kiosk_payment->payment_method),
                    'image' => existsImage('icon', 'payment_methods/' . strtolower($kiosk_payment->payment_method) . '.png') ?? '',
                    'is_enable' => $kiosk_payment->is_enable,
                );
            }

            // 获取除去已选择的支付方式枚举
            $payment_methods = PaymentMethod::asArray();
            foreach ($payment_methods as $value) {
                $kiosk_payment_method = KioskPaymentMethod::select('payment_method', 'kiosk_number')
                    ->where('kiosk_number', $model->kiosk_number)
                    ->where('payment_method', $value)
                    ->first();
                if (!$kiosk_payment_method) {
                    $this->data['payment_method'][] = array(
                        'value' => $value,
                        'name' => PaymentMethod::getDescription($value),
                        'image' => existsImage('icon', 'payment_methods/' . strtolower($value) . '.png') ?? '',
                    );
                }
            }
        }

        return $this->returnJson();
    }

    /**
     * 提交保存kiosk关联的charge point
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-18
     */
    public function submitKioskPayment(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        $number = $request->input('number');
        $kiosk_payment_method_list = $request->input('kiosk_payment_method_list', []);

        $model = Kiosk::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('kiosk_number', $number);
        if (empty($number) || blank($model)) {
            $this->missingField(__("$module_name.kiosk_number"));
        } else {
            // 先清除原付款方式
            KioskPaymentMethod::where('kiosk_number', $model->kiosk_number)->delete();
            // 再添加付款方式
            foreach ($kiosk_payment_method_list as $kiosk_payment_method) {
                $kioskPaymentMethod = new KioskPaymentMethod;
                $kioskPaymentMethod->kiosk_number = $model->kiosk_number;
                $kioskPaymentMethod->payment_method = $kiosk_payment_method['payment_method'];
                $kioskPaymentMethod->is_enable = $kiosk_payment_method['is_enable'];
                $kioskPaymentMethod->sort_order = $kiosk_payment_method['sort_order'];
                $kioskPaymentMethod->save();
            }
        }
        self::sendInitPushByKioskNumberList();

        return $this->returnJson();
    }

    /**
     * 上传 TODO确定成型后更改
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-11-08
     */
    public function uploadKiosk(Request $request): JsonResponse
    {
        try {
            $result_keys = array(
                'kiosk_number',
                'name',
                'mac_address',
                'kiosk_setting_number',
                'site_number',
                'zone_number',
                'octopus_device_type',
                'octopus_com_port',
                'octopus_baud_rate',
                'octopus_data_exchange_program_local_path',
                'octopus_pre_authorization_amount',
                'pos_vendor',
                'yedpay_pos_api_url',
                'yedpay_pos_api_key',
                'yedpay_pos_pre_authorization_amount',
                'soe_pay_pos_api_url',
                'soe_pay_pos_api_key',
                'soe_pay_pos_api_token',
                'torch_port_name',
                'torch_baud_rate',
                'torch_data_bits',
                'sort_order',
                'remark',
            );

            // 获取文件数据
            $format_result = $this->_importDataAndKeys($request, $result_keys);
            if ($format_result['code'] != 200) {
                $this->code = 20001;
                $this->data = null;
                // 提示信息
                $this->message = __('common.import_error_format');
                return $this->returnJson();
            }
            // 获取文件数据结果集
            $format_result_list = $format_result['data'];
            // 移除第一行表头
            array_shift($format_result_list);

            // 是否可上传
            $enable_upload = true;
            // 结果集
            $result = array();
            $mac_address_list = array();

            // 枚举值用于判断是否是枚举值
            $pos_vendor = PosVendorEnum::asArray();
            $octopus_device_type_list = OctopusDeviceType::asArray();
            // 当前用户所拥有的场地权限
            $site_array = isSuperAdministrator() ? Site::pluck('site_number')->toArray() : auth()->user()->site_number_list->toArray();
            foreach ($format_result_list as $item) {
                $check_rules = $this->importKioskRules($item, $result_keys, $mac_address_list, $pos_vendor, $site_array, $octopus_device_type_list);
                $mac_address_list = $check_rules['mac_address_list'];
                if (!$check_rules['check_result']) $enable_upload = false;

                $result_item = array(
                    'check_result' => $check_rules['check_result'],
                    'error_message' => $check_rules['error_message'],
                    'kiosk_number' => $item['kiosk_number'] ?? '',
                    'name' => $item['name'] ?? '',
                    'mac_address' => $item['mac_address'] ?? '',
                    'kiosk_setting_number' => $item['kiosk_setting_number'] ?? '',
                    'site_number' => $item['site_number'] ?? '',
                    'zone_number' => $item['zone_number'] ?? '',
                    'octopus_device_type' => $item['octopus_device_type'] ?? '',
                    'octopus_com_port' => $item['octopus_com_port'] ?? '',
                    'octopus_baud_rate' => $item['octopus_baud_rate'] ?? '',
                    'octopus_data_exchange_program_local_path' => $item['octopus_data_exchange_program_local_path'] ?? '',
                    'octopus_pre_authorization_amount' => $item['octopus_pre_authorization_amount'] ?? '',
                    'pos_vendor' => $item['pos_vendor'] ?? '',
                    'yedpay_pos_api_url' => $item['yedpay_pos_api_url'] ?? '',
                    'yedpay_pos_api_key' => $item['yedpay_pos_api_key'] ?? '',
                    'yedpay_pos_pre_authorization_amount' => $item['yedpay_pos_pre_authorization_amount'] ?? '',
                    'soe_pay_pos_api_url' => $item['soe_pay_pos_api_url'] ?? '',
                    'soe_pay_pos_api_key' => $item['soe_pay_pos_api_key'] ?? '',
                    'soe_pay_pos_api_token' => $item['soe_pay_pos_api_token'] ?? '',
                    'torch_port_name' => $item['torch_port_name'] ?? '',
                    'torch_baud_rate' => $item['torch_baud_rate'] ?? '',
                    'torch_data_bits' => $item['torch_data_bits'] ?? '',
                    'sort_order' => $item['sort_order'] ?? 0,
                    'remark' => $item['remark'] ?? '',
                );

                $result[] = $result_item;
            }

            $this->data = array(
                'result_list' => $result,
                'enable_upload' => $enable_upload,
            );

            return $this->returnJson();
        } catch (Exception) {
            $this->code = 20001;
            $this->data = null;
            // 提示信息
            $this->message = __('common.import_error_format');
            return $this->returnJson();
        }
    }

    /**
     * 导入
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-11-08
     */
    public function importKiosk(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        // kiosk列表
        $kiosk_list = $request->input('kiosk_list', array());

        if (empty($kiosk_list)) {
            $this->missingField('KIOSK');
            return $this->returnJson();
        }

        $result_keys = array(
            'kiosk_number',
            'name',
            'mac_address',
            'kiosk_setting_number',
            'site_number',
            'zone_number',
            'octopus_device_type',
            'octopus_com_port',
            'octopus_baud_rate',
            'octopus_data_exchange_program_local_path',
            'octopus_pre_authorization_amount',
            'pos_vendor',
            'yedpay_pos_api_url',
            'yedpay_pos_api_key',
            'yedpay_pos_pre_authorization_amount',
            'soe_pay_pos_api_url',
            'soe_pay_pos_api_key',
            'soe_pay_pos_api_token',
            'torch_port_name',
            'torch_baud_rate',
            'torch_data_bits',
            'sort_order',
            'remark',
        );
        $mac_address_list = array();
        // 枚举值用于判断是否是枚举值
        $pos_vendor_list = PosVendorEnum::asArray();
        $octopus_device_type_list = OctopusDeviceType::asArray();
        $illegal_kiosk_number = array();
        // 当前用户所拥有的场地权限
        $site_array = isSuperAdministrator() ? Site::pluck('site_number')->toArray() : auth()->user()->site_number_list->toArray();
        // 当前用户所拥有的场地编号和商户编号，用于后面直接获取对应场地的商户编号而不需要多次查询数据库
        $site_merchant_map = array();
        if (isSuperAdministrator()) {
            $site_merchant_map = Site::pluck('merchant_number', 'site_number');
        } else {
            auth()->user()->loadMissing('roles.site');
            foreach (auth()->user()->roles as $role) {
                foreach ($role->site as $site) {
                    $site_merchant_map[$site->site_number] = $site->merchant_number;
                }
            }
        }
        foreach ($kiosk_list as $item) {
            $check_rules = $this->importKioskRules($item, $result_keys, $mac_address_list, $pos_vendor_list, $site_array, $octopus_device_type_list);
            $mac_address_list = $check_rules['mac_address_list'];
            if (!$check_rules['check_result']) {
                $this->code = 40001;
                $this->message = __('kiosk.data_has_modified');
                continue;
            }
            // 存在更新，不存在新增 因为编号唯一所以这里不用判断商户
            $kiosk_model = Kiosk::firstOrNew(['kiosk_number' => $item['kiosk_number']]);

            // 如果用firstOrNew的话要用exists，如果用exists()会出现都为true的情况
            if ($kiosk_model->exists) {
                // 如果存在并且Kiosk场地编号不在权限范围内就不给修改
                if (!in_array($kiosk_model->site_number, $site_array)) {
                    $illegal_kiosk_number[] = $item['kiosk_number'];
                    continue;
                }
                // 如果存在，并且相关设置被更改为不同场地时就不给修改
                if ($kiosk_model->site_number != $item['site_number']) {
                    $site_no_relevant_setting_data[] = $item['kiosk_number'];
                    continue;
                }
            } else {
                // 只有新增才能修改Kiosk编号和场地及商户编号
                $kiosk_model->kiosk_number = $item['kiosk_number'];
                $kiosk_model->site_number = $item['site_number'];
                $kiosk_model->merchant_number = $site_merchant_map[$item['site_number']];
            }
            $pos_vendor = $item['pos_vendor'] ?? '';
            $kiosk_model->name = $item['name'];
            $kiosk_model->license_code = config('app.is_enable_automatic_encryption_of_license_code') && filled($item['mac_address']) ? self::licenseCodeEncryption($item['mac_address']) : null;
            $kiosk_model->kiosk_setting_number = $item['kiosk_setting_number'];
            $kiosk_model->zone_number = $item['zone_number'];
            $kiosk_model->octopus_device_type = $item['octopus_device_type'];
            $kiosk_model->octopus_com_port = $item['octopus_com_port'];
            $kiosk_model->octopus_baud_rate = $item['octopus_baud_rate'];
            $kiosk_model->octopus_data_exchange_program_local_path = $item['octopus_data_exchange_program_local_path'];
            $kiosk_model->octopus_pre_authorization_amount = $item['octopus_pre_authorization_amount'];
            if (filled($kiosk_model->octopus_pre_authorization_amount)) {
                $kiosk_model->octopus_pre_authorization_amount = bcmul($kiosk_model->octopus_pre_authorization_amount, 100);
            }
            $kiosk_model->pos_vendor = $pos_vendor;
            if ($pos_vendor == PosVendorEnum::Yedpay) {
                $kiosk_model->yedpay_pos_api_url = $item['yedpay_pos_api_url'];
                $kiosk_model->yedpay_pos_api_key = $item['yedpay_pos_api_key'];
            } else if ($pos_vendor == PosVendorEnum::Yedpay_PCG) {
                $kiosk_model->yedpay_pos_pre_authorization_amount = $item['yedpay_pos_pre_authorization_amount'];
                if (filled($kiosk_model->yedpay_pos_pre_authorization_amount)) {
                    $kiosk_model->yedpay_pos_pre_authorization_amount = bcmul($kiosk_model->yedpay_pos_pre_authorization_amount, 100);
                }
            } else if ($pos_vendor == PosVendorEnum::SoePay) {
                $kiosk_model->soe_pay_pos_api_url = $item['soe_pay_pos_api_url'];
                $kiosk_model->soe_pay_pos_api_key = $item['soe_pay_pos_api_key'];
                $kiosk_model->soe_pay_pos_api_token = $item['soe_pay_pos_api_token'];
            } else {
                $kiosk_model->yedpay_pos_api_url = null;
                $kiosk_model->yedpay_pos_api_key = null;
                $kiosk_model->yedpay_pos_pre_authorization_amount = null;
                $kiosk_model->soe_pay_pos_api_url = null;
                $kiosk_model->soe_pay_pos_api_key = null;
                $kiosk_model->soe_pay_pos_api_token = null;
            }
            $kiosk_model->torch_port_name = $item['torch_port_name'];
            $kiosk_model->torch_baud_rate = $item['torch_baud_rate'];
            $kiosk_model->torch_data_bits = $item['torch_data_bits'];
            $kiosk_model->sort_order = $item['sort_order'] ?? 0;
            $kiosk_model->remark = $item['remark'];
            $kiosk_model->save();
        }


        if (!empty($site_no_relevant_setting_data)) {
            $this->code = 40002;
            $this->message = __("$module_name.kiosk_number_no_relevant_setting_data") . ': ' . implode(', ', $site_no_relevant_setting_data);
        }

        if (!empty($illegal_kiosk_number)) {
            $this->code = 40002;
            $this->message = __("$module_name.kiosk_number_already_exists") . ': ' . implode(', ', $illegal_kiosk_number);
        }
        self::sendInitPushByKioskNumberList();
        return $this->returnJson();
    }

    public function importKioskRules($item, $result_keys, $mac_address_list, $pos_vendor_list, $site_array, $octopus_device_type_list): array
    {
        $module_name = self::$module_name;
        if (empty($item) || array_diff($result_keys, array_keys($item))) return array(
            'check_result' => false,
            'mac_address_list' => $mac_address_list,
        );
        // 校验结果
        $check_result = true;
        // 错误信息
        $error_message = null;
        // 如果存在空值不通过
        $check_item_1 = array_slice($item, 0, 2);
        $check_item_2 = array_slice($item, 3, 2);
        $check_item_3 = array_slice($item, 7, 3);
        // 校验是否为1到30位数字和字母组成的字符串
        $check_kiosk_number = (isset($item['kiosk_number']) && !empty($item['kiosk_number']) && !preg_match('/^[a-zA-Z0-9]{1,30}$/', $item['kiosk_number']));
        // 可能不填，不填就直接判断为false不用判断枚举值是否合法
        $check_enum_pos_vendor = false;
        if (!empty($item['pos_vendor'])) {
            // 判断是否是枚举值
            $check_enum_pos_vendor = (!in_array($item['pos_vendor'], $pos_vendor_list));
            if ($item['pos_vendor'] == PosVendorEnum::Yedpay) {
                $check_item_2[12] = $item['yedpay_pos_api_url'] ?? null;
                $check_item_2[13] = $item['yedpay_pos_api_key'] ?? null;
            } else if ($item['pos_vendor'] == PosVendorEnum::Yedpay_PCG) {
                $check_item_2[12] = $item['yedpay_pos_api_url'] ?? null;
                $check_item_2[13] = $item['yedpay_pos_api_key'] ?? null;
                $check_item_2[14] = $item['yedpay_pos_pre_authorization_amount'] ?? null;
            } else if ($item['pos_vendor'] == PosVendorEnum::SoePay) {
                $check_item_2[15] = $item['soe_pay_pos_api_url'] ?? null;
                $check_item_2[16] = $item['soe_pay_pos_api_key'] ?? null;
                $check_item_2[17] = $item['soe_pay_pos_api_token'] ?? null;
            }
        }
        // 判断八达通设备类型是否合法
        $check_enum_octopus_device_type = false;
        if (!empty($item['octopus_device_type'])) {
            $check_enum_octopus_device_type = (!in_array($item['octopus_device_type'], $octopus_device_type_list));
        }
        // 必填
        $check_empty = in_array(null, $check_item_1, true) ||
            in_array('null', $check_item_1, true) ||
            in_array(null, $check_item_2, true) ||
            in_array('null', $check_item_2, true) ||
            in_array(null, $check_item_3, true) ||
            in_array('null', $check_item_3, true);
        // 校验非必填时如果填写是否为数字
        $check_empty_numeric = (isset($item['sort_order']) && !is_null($item['sort_order']) && !is_numeric($item['sort_order']));
        // 判断上传数据中MAC地址是否有重复
        if (!config('app.is_enable_automatic_encryption_of_license_code') || !filled($item['mac_address'])) {
            $check_mac_address = false;
        } else if (!in_array($item['mac_address'], $mac_address_list)) {
            $mac_address_list[] = $item['mac_address'];
            // 判断上传数据中MAC地址是否和数据库数据有重复, 排除kiosk_number相同的情况
            $check_mac_address = Kiosk::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->where('license_code', self::licenseCodeEncryption($item['mac_address']))->where('kiosk_number', '!=', $item['kiosk_number'])
                ->exists();
        } else {
            $check_mac_address = true;
        }
        // 判断该场地是否有权限并且存在
        $check_site = !in_array($item['site_number'], $site_array);
        $check_kiosk_setting = $check_zone = false;
        /* 判断该场地下是否存在对应的设置，并且判断仅在有场地编号时才进入判断*/
        if (!$check_site) {
            $check_kiosk_setting = KioskSetting::where('site_number', $item['site_number'])->where('kiosk_setting_number', $item['kiosk_setting_number'])->doesntExist();

            $check_zone = (filled($item['zone_number']) && Zone::where('site_number', $item['site_number'])->where('zone_number', $item['zone_number'])->doesntExist());
        }

        // 校验不通过disable
        if ($check_empty || $check_empty_numeric || $check_kiosk_number || $check_mac_address || $check_enum_pos_vendor || $check_site || $check_zone || $check_kiosk_setting) {
            $check_result = false;
            $error_messages = [];

            if ($check_empty) {
                $error_messages[] = __("$module_name.fill_required_data");
            }
            if ($check_empty_numeric) {
                $error_messages[] = __("$module_name.sort_order") . __("$module_name.fill_number");
            }
            if ($check_kiosk_number) {
                $error_messages[] = __("$module_name.kiosk_number_must_letter_number");
            }
            if ($check_mac_address) {
                $error_messages[] = __("$module_name.mac_address_duplicate");
            }
            if ($check_enum_pos_vendor) {
                $error_messages[] = __("$module_name.pos_vendor_does_not_exist");
            }
            if ($check_enum_octopus_device_type) {
                $error_messages[] = __("$module_name.octopus_device_type_does_not_exist");
            }
            if ($check_kiosk_setting) {
                $error_messages[] = __("$module_name.kiosk_setting_does_not_exist");
            }
            if ($check_site) {
                $error_messages[] = __("$module_name.site_does_not_exist");
            }
            if ($check_zone) {
                $error_messages[] = __("$module_name.zone_does_not_exist");
            }

            // 将错误消息数组连接成一个字符串，用 <br> 分隔
            $error_message = implode('<br>', $error_messages);
        }

        return array(
            'check_result' => $check_result,
            'mac_address_list' => $mac_address_list,
            'error_message' => $error_message
        );
    }
}

@extends('layouts.contentLayoutMaster')
{{-- page title --}}
@section('title', __("$module_name.web_title"))

{{-- vendor styles --}}
@section('vendor-styles')
    <link rel="stylesheet" type="text/css" href="{{ asset('css/plugins/forms/validation/form-validation.css') }}">
    <link rel="stylesheet" type="text/css" href="{{asset('css/pages/theme-ui.css')}}">
    <link rel="stylesheet" href="{{asset('vendors/css/forms/select/select2.min.css')}}">
    <link rel="stylesheet" type="text/css"
          href="{{asset('vendors/css/tables/datatable/dataTables.bootstrap4.min.css')}}">
    <link rel="stylesheet" type="text/css"
          href="{{asset('vendors/css/tables/datatable/responsive.bootstrap4.min.css')}}">
    <link rel="stylesheet" type="text/css" href="{{asset('vendors/css/tables/datatable/buttons.bootstrap4.min.css')}}">
    <link rel="stylesheet" type="text/css"
          href="{{asset('vendors/css/tables/datatable/fixedColumns.bootstrap4.min.css')}}">
@endsection

{{-- page styles --}}
@section('page-styles')
    <link rel="stylesheet" type="text/css" href="{{ asset('css/pages/app-users.css') }}">
@endsection

@section('content')
    <!-- users edit start -->
    <section class="users-edit">
        @csrf
        <div class="breadcrumb_head">
            <h4 class="breadcrumb-wrapper">
                <span
                    class="text-muted fw-light">{{ __("$module_name.web_title") }} /</span> {{ __("$module_name.app_user_view") }}
            </h4>
        </div>

        <div class="breadcrumb_btn">
            <button class="btn btn-primary glow mb-sm-0 mr-0 mr-sm-0" data-toggle="tooltip" title="{{ __("$module_name.btn_refresh_points_balance") }}" onclick="getUserLatestPointsWalletBalance()">
                {{ __("$module_name.btn_refresh_points_balance") }}
            </button>
            <a class="btn btn-light glow" href="{{ $cancel_url }}" role="button">{{ __('common.cancel_btn') }}</a>
        </div>

        <div class="content-wrapper breadcrumb_content" style="padding: 0px;">
            <!-- Content -->
            <div class="container-xxl flex-grow-1">

                <div class="row">
                    <!-- Detail Left -->
                    <div class="col-sm-12 col-md-4">
                        <!-- User Card -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <div class="user-avatar-section">
                                    <div class="d-flex align-items-center flex-column">
                                        @can('appUser/edit')
                                            <a href="javascript:void(0);" onclick="onClickAvatar()">
                                                <img class="img-fluid rounded my-2"
                                                     src="{{ $model['avatar_url'] }}"
                                                     height="110" width="110">
                                            </a>
                                            <input type="file" name="avatar_url" accept="image/*"
                                                   onchange="updateAvatar({{ $model['user_id'] }})" hidden/>
                                        @else
                                            <img class="img-fluid rounded my-2"
                                                 src="{{ $model['avatar_url'] }}"
                                                 height="110" width="110">
                                        @endcan
                                    </div>
                                </div>
                                <h5 class="pb-2 border-bottom mb-2">{{ __("$module_name.app_user_detail") }}</h5>
                                <div class="info-container">
                                    <ul class="list-unstyled">
                                        <li class="mb-1">
                                            <span class="fw-bold me-2">{{ __("$module_name.merchant") }}: </span>
                                            <span>{{ $model['merchant_name'] ?? '—/—' }}</span>
                                        </li>
                                        <li class="mb-1">
                                            <span class="fw-bold me-2">{{ __("$module_name.user_group") }}: </span>
                                            <span>{{ $model['user_group_name'] ?? '—/—' }}</span>
                                        </li>
                                        <li class="mb-1">
                                            <span class="fw-bold me-2">{{ __("$module_name.email") }}: </span>
                                            <span>{{ $model['email'] ?? '—/—' }}</span>
                                        </li>
                                        <li class="mb-1">
                                            <span class="fw-bold me-2">{{ __("$module_name.nickname") }}: </span>
                                            <span>{{ $model['nickname'] }}</span>
                                        </li>
                                        <li class="mb-1">
                                            <span class="fw-bold me-2">{{ __("$module_name.telephone") }}: </span>
                                            <span>{{ $model['telephone'] ?? '—/—' }}</span>
                                        </li>
                                        <li class="mb-1">
                                            <span class="fw-bold me-2">{{ __("$module_name.region") }}: </span>
                                            <span>{{ $model['region_name'] ?? '—/—' }}</span>
                                        </li>
                                        <li class="mb-1">
                                            <span class="fw-bold me-2">{{ __("$module_name.points_balance") }}: </span>
                                            @if (filled($model['points_wallet_list']))
                                                <span class="points_balance_span" id="points_balance_list">
                                                    @foreach ($model['points_wallet_list'] as $points_wallet)
                                                        {{ $points_wallet['currency_symbol'] }} {{ $points_wallet['points_balance'] }}
                                                        @if (!$loop->last); @endif
                                                    @endforeach
                                                </span>
                                            @endif

                                        </li>
                                        <li class="mb-1">
                                            <span class="fw-bold me-2">{{ __("$module_name.site") }}: </span>
                                            <span>{{ $model['site_name'] ?? '—/—' }}</span>
                                        </li>
                                    </ul>
                                    @can('appUser/edit')
                                        <div class="d-flex justify-content-center pt-2">
                                            <button class="btn btn-primary"
                                                    onclick='editAppUser(@json($model))'>{{ __('common.text_edit') }}</button>
                                        </div>
                                    @endcan
                                </div>
                            </div>
                        </div>
                        <!-- /User Card -->
                    </div>
                    <!--/ Detail Left -->

                    <!-- Detail Right -->
                    <div class="col-sm-12 col-md-8">
                        <!-- Navigation Bar -->
                        <ul class="nav nav-tabs mb-2" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link d-flex align-items-center active"
                                   id="description-tab-check"
                                   href="#description-check"
                                   data-toggle="tab"
                                   aria-controls="description-check" role="tab" aria-selected="true">
                                    <i class="bx bx-lock-alt"></i>{{ __("$module_name.app_user_security") }}
                                </a>
                            </li>
                            @can('userCredential')
                                <li class="nav-item">
                                    <a class="nav-link d-flex align-items-center"
                                       id="user-credential-tab-check"
                                       href="#user-credential-check"
                                       data-toggle="tab"
                                       onclick="onTabUserCredentialClick()"
                                       aria-controls="user-credential-check" role="tab" aria-selected="true">
                                        <i class="bx bxs-credit-card-front"></i>{{ __("$module_name.app_user_user_credential") }}
                                    </a>
                                </li>
                            @endcan
                            {{-- @can('memberCard')
                                <li class="nav-item">
                                    <a class="nav-link d-flex align-items-center"
                                       id="member-card-tab-check"
                                       href="#member-card-check"
                                       data-toggle="tab"
                                       onclick="onTabMemberCardClick()"
                                       aria-controls="member-card-check" role="tab" aria-selected="true">
                                        <i class="bx bxs-credit-card-front"></i>{{ __("$module_name.member_card") }}
                                    </a>
                                </li>
                            @endcan --}}
                            @can('pointsTransaction')
                                <li class="nav-item">
                                    <a class="nav-link d-flex align-items-center"
                                       id="points-transaction-tab-check"
                                       href="#points-transaction-check"
                                       data-toggle="tab"
                                       onclick="onTabPointsTransactionClick()"
                                       aria-controls="points-transaction-check" role="tab" aria-selected="true">
                                        <i class='bx bx-food-menu'></i>{{ __("$module_name.app_user_points_transaction") }}
                                    </a>
                                </li>
                            @endcan
                            @can('vehicle')
                                <li class="nav-item">
                                    <a class="nav-link d-flex align-items-center"
                                       id="vehicle-tab-check"
                                       href="#vehicle-check"
                                       data-toggle="tab"
                                       onclick="onTabVehicleClick()"
                                       aria-controls="vehicle-check" role="tab" aria-selected="true">
                                        <i class='bx bx-car'></i>{{ __("$module_name.app_user_vehicle") }}
                                    </a>
                                </li>
                            @endcan
                            @can('userNotify')
                                <li class="nav-item">
                                    <a class="nav-link d-flex align-items-center"
                                       id="notify-tab-check"
                                       href="#notify-check"
                                       data-toggle="tab"
                                       onclick="onTabNotifyClick()"
                                       aria-controls="notify-check" role="tab" aria-selected="true">
                                        <i class='bx bxs-bell'></i>{{ __("$module_name.app_user_notify") }}
                                    </a>
                                </li>
                            @endcan
                        </ul>

                        <div class="tab-content">
                            {{-- 基本信息--}}
                            <div class="tab-pane fade show"
                                 id="general-check"
                                 aria-labelledby="general-tab-check"
                                 role="tabpanel">
                                <div class="card mb-4">
                                    <h5 class="card-header">{{ __('common.title_general') }}</h5>
                                    <div class="card-body">
                                        <div
                                            class="info-container text-center">
                                            <span>{{ __('common.no_content') }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {{-- 安全 --}}
                            <div class="tab-pane active fade show"
                                 id="description-check"
                                 aria-labelledby="description-tab-check"
                                 role="tabpanel">
                                <div class="card">
                                    <h5 class="card-header">{{ __("$module_name.app_user_change_password") }}</h5>
                                    <div class="card-body">
                                        @can('appUser/edit')
                                            <div class="alert alert-warning" role="alert">
                                                <h6 class="alert-heading mb-1">{{ __("$module_name.app_user_change_password_title") }}</h6>
                                                <span>{{ __("$module_name.app_user_change_password_option") }}</span>
                                            </div>
                                            <div class="row mb-2">
                                                <div class="col-12 col-sm-6">
                                                    <div class="form-group required">
                                                        <div class="controls">
                                                            <label>{{ __("$module_name.password") }}</label>
                                                            <input type="password"
                                                                   class="form-control"
                                                                   placeholder="{{ __("$module_name.password") }}"
                                                                   value="" name="password">
                                                            <div class="invalid-feedback"></div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-12 col-sm-6">
                                                    <div class="form-group required">
                                                        <div class="controls">
                                                            <label>{{ __("$module_name.password_confirmation") }}</label>
                                                            <input type="password"
                                                                   class="form-control"
                                                                   placeholder="{{ __("$module_name.password_confirmation") }}"
                                                                   value="" name="password_confirmation">
                                                            <div class="invalid-feedback"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <button type="button" class="btn btn-primary"
                                                    onclick="onChangePassword({{ $model['user_id'] }})">{{ __("$module_name.app_user_change_password") }}
                                            </button>
                                        @else
                                            <div
                                                class="info-container text-center">
                                                <span>{{ __('common.no_permission') }}</span>
                                            </div>
                                        @endcannot
                                    </div>
                                </div>
                            </div>

                            @can('userCredential')
                                {{-- 用户凭证 --}}
                                <div class="tab-pane fade"
                                     id="user-credential-check"
                                     aria-labelledby="user-credential-tab-check"
                                     role="tabpanel">
                                    <div class="card">
                                        <div class="card-body d-flex justify-content-between align-items-center row">
                                            <div class="col-12 col-sm-12 col-lg-6">
                                                <label>{{ __("$module_name.user_credential_credential_type") }}</label>
                                                <select name="credential_type_search" class="form-control select2 select2_not_search"
                                                        data-placeholder="{{ __("$module_name.user_credential_credential_type") }}" id="credential_type_search">
                                                    <option value=""></option>
                                                    @foreach ($model['user_credential_type_list'] as $value)
                                                        <option value="{{ $value['value'] }}">{{ $value['name'] }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="col-12 col-sm-12 col-lg-6">
                                                <label>{{ __("$module_name.user_credential_credential_number") }}</label>
                                                <input id="credential_number_search" class="form-control cmb-10"
                                                       placeholder="{{ __("$module_name.user_credential_credential_number") }}"
                                                       value=''/>
                                            </div>

                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table id="userCredentialDatatable"
                                                       class="table table-striped invoice-data-table nowrap table-hover"
                                                       style="width:100%" data-url="{{ $user_credential_list_url }}">
                                                    <thead>
                                                    <tr>
                                                        <th data-priority="1">{{ __("$module_name.user_credential_credential_type") }}</th>
                                                        <th>{{ __("$module_name.user_credential_credential_number") }}</th>
                                                        <th>{{ __("$module_name.user_credential_is_enable") }}</th>
                                                        <th>{{ __("$module_name.user_credential_sort_order") }}</th>
                                                        <th>{{ __("$module_name.user_credential_remark") }}</th>
                                                        <th class="nosort">{{ __("$module_name.user_credential_gmt_create") }}</th>
                                                        <th class="init_sort">{{ __("$module_name.user_credential_gmt_modified") }}</th>
                                                        @canany(['userCredential/edit', 'userCredential/delete'])
                                                            <th class="nosort"
                                                                data-priority="2">{{ __('common.text_action') }}</th>
                                                        @endcanany
                                                    </tr>
                                                    </thead>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endcan

                            @can('memberCard')
                                {{-- 会员卡 --}}
                                <div class="tab-pane fade"
                                     id="member-card-check"
                                     aria-labelledby="member-card-tab-check"
                                     role="tabpanel">
                                    <div class="card">
                                        <div class="card-body d-flex justify-content-between align-items-center row">
                                            <div class="col-12 col-sm-12 col-lg-4">
                                                <label>{{ __("$module_name.member_card_key") }}</label>
                                                <input id="member_card_key_search" class="form-control cmb-10"
                                                       placeholder="{{ __("$module_name.member_card_key") }}"
                                                       value=''/>
                                            </div>
                                            <div class="col-12 col-sm-12 col-lg-4">
                                                <label>{{ __("$module_name.octopus_card_number") }}</label>
                                                <input id="octopus_card_number_search" class="form-control cmb-10"
                                                       placeholder="{{ __("$module_name.octopus_card_number") }}"
                                                       value=''/>
                                            </div>

                                            <div class="col-12 col-sm-12 col-lg-4">
                                                <label>{{ __("$module_name.rfid_card_number") }}</label>
                                                <input id="rfid_card_number_search" class="form-control cmb-10"
                                                       placeholder="{{ __("$module_name.rfid_card_number") }}"
                                                       value=''/>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table id="memberCardDatatable"
                                                       class="table table-striped invoice-data-table nowrap table-hover"
                                                       style="width:100%" data-url="{{ $member_card_list_url }}">
                                                    <thead>
                                                    <tr>
                                                        @if(isSuperAdministrator() || auth()->user()->site_number_list->count() > 1)
                                                            <th data-priority="1">{{ __("$module_name.site") }}</th>
                                                        @endif
                                                        <th>{{ __("$module_name.member_card_key") }}</th>
                                                        <th>{{ __("$module_name.member_card_group") }}</th>
                                                        <th>{{ __("$module_name.member_name") }}</th>
                                                        <th>{{ __("$module_name.member_email") }}</th>
                                                        <th>{{ __("$module_name.octopus_card_number") }}</th>
                                                        <th>{{ __("$module_name.rfid_card_number") }}</th>
                                                        <th>{{ __("$module_name.gmt_create") }}</th>
                                                        @canany(['memberCard/edit', 'memberCard/delete'])
                                                            <th class="nosort"
                                                                data-priority="2">{{ __('common.text_action') }}</th>
                                                        @endcanany
                                                    </tr>
                                                    </thead>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endcan

                            @can('pointsTransaction')
                                {{-- 积分交易记录 --}}
                                <div class="tab-pane fade"
                                     id="points-transaction-check"
                                     aria-labelledby="points-transaction-tab-check"
                                     role="tabpanel">
                                     {{-- 钱包列表tab --}}
                                     <ul class="nav nav-tabs mt-2 mb-2" role="tablist" @if (count($model['points_wallet_list']) < 2) style="display: none;" @endif>
                                        @foreach($model['points_wallet_list'] as $points_wallet)
                                            <li class="nav-item">
                                                <a class="nav-link d-flex align-items-center @if($loop->first) active @endif"
                                                id="points-wallet-{{ $points_wallet['points_wallet_id'] }}-tab"
                                                href="#points-wallet-{{ $points_wallet['points_wallet_id'] }}"
                                                data-toggle="tab"
                                                aria-controls="points-wallet-{{ $points_wallet['points_wallet_id'] }}"
                                                role="tab"
                                                onclick="onTabPointsWalletClick('{{ $points_wallet['points_wallet_id'] }}')"
                                                aria-selected="true">{{ $points_wallet['currency_code'] }}</a>
                                            </li>
                                        @endforeach
                                    </ul>

                                    <div class="tab-content">
                                        @forelse ($model['points_wallet_list'] as $points_wallet)
                                            <div class="tab-pane @if ($loop->first) active @endif fade show"
                                                id="points-wallet-{{ $points_wallet['points_wallet_id'] }}"
                                                aria-labelledby="points-wallet-{{ $points_wallet['points_wallet_id'] }}-tab"
                                                role="tabpanel">
                                                <div class="card">
                                                    <div class="card-header">
                                                        <h4 class="card-title points-balance">{{ __("$module_name.points_balance") }}: {{ $points_wallet['currency_symbol'] }} {{ $points_wallet['points_balance'] }}</h4>
                                                    </div>
                                                    <div class="card-body d-flex justify-content-between align-items-center row">
                                                        <div class="col-12 col-sm-12 col-lg-4">
                                                            <label>{{ __("$module_name.points_transaction_transaction_number") }}</label>
                                                            <input id="transaction_number_search_{{ $points_wallet['points_wallet_id'] }}"
                                                                class="form-control cmb-10 enter_search"
                                                                placeholder="{{ __("$module_name.points_transaction_transaction_number") }}"
                                                                value=''/>
                                                        </div>

                                                        <div class="col-12 col-sm-12 col-lg-4">
                                                            <label>{{ __("$module_name.points_transaction_transaction_type") }}</label>
                                                            <select name="transaction_type_search_{{ $points_wallet['points_wallet_id'] }}"
                                                                    class="form-control select2 select2_not_search"
                                                                    data-placeholder="{{ __("$module_name.points_transaction_transaction_type") }}"
                                                                    id="transaction_type_search_{{ $points_wallet['points_wallet_id'] }}">
                                                                <option value=""></option>
                                                                @foreach ($model['transaction_type_list'] as $value)
                                                                    <option value="{{ $value['value'] }}">{{ $value['name'] }}</option>
                                                                @endforeach
                                                            </select>
                                                        </div>

                                                        <div class="col-12 col-sm-12 col-lg-4">
                                                            <label>{{ __("$module_name.points_transaction_transaction_category") }}</label>
                                                            <select name="transaction_category_search_{{ $points_wallet['points_wallet_id'] }}"
                                                                    class="form-control select2 select2_not_search"
                                                                    data-placeholder="{{ __("$module_name.points_transaction_transaction_category") }}"
                                                                    id="transaction_category_search_{{ $points_wallet['points_wallet_id'] }}">
                                                                <option value=""></option>
                                                                @foreach ($model['transaction_category_list'] as $value)
                                                                    <option value="{{ $value['value'] }}">{{ $value['name'] }}</option>
                                                                @endforeach
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="card-body">
                                                        <div class="table-responsive">
                                                            <table id="pointsTransactionDatatable_{{ $points_wallet['points_wallet_id'] }}"
                                                                class="table table-striped invoice-data-table nowrap table-hover"
                                                                style="width:100%" data-url="{{ $points_transaction_list_url }}">
                                                                <thead>
                                                                <tr>
                                                                    <th>{{ __("$module_name.points_transaction_transaction_number") }}</th>
                                                                    <th>{{ __("$module_name.points_transaction_transaction_type") }}</th>
                                                                    <th>{{ __("$module_name.points_transaction_transaction_category") }}</th>
                                                                    <th>{{ __("$module_name.points_transaction_amount") }}</th>
                                                                    <th>{{ __("$module_name.points_transaction_points_balance") }}</th>
                                                                    <th>{{ __("$module_name.gmt_create") }}</th>
                                                                </tr>
                                                                </thead>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        @empty
                                            <div class="card mb-4">
                                                <div class="card-body">
                                                    <div
                                                        class="info-container text-center">
                                                        <span>{{ __('common.no_content') }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforelse
                                    </div>
                                </div>
                            @endcan

                            @can('vehicle')
                                {{-- 车辆 --}}
                                <div class="tab-pane fade"
                                     id="vehicle-check"
                                     aria-labelledby="vehicle-tab-check"
                                     role="tabpanel">
                                    <div class="card">
                                        <div class="card-body d-flex justify-content-between align-items-center row">
                                            <div class="col-12 col-sm-12 col-lg-12">
                                                <label>{{ __("$module_name.vehicle_plate_number") }}</label>
                                                <input id="plate_number_search" class="form-control cmb-10 enter_search"
                                                       placeholder="{{ __("$module_name.vehicle_plate_number") }}"
                                                       value=''/>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table id="vehicleDatatable"
                                                       class="table table-striped invoice-data-table nowrap table-hover"
                                                       style="width:100%" data-url="{{ $vehicle_list_url }}">
                                                    <thead>
                                                    <tr>
                                                        <th>{{ __("$module_name.vehicle_plate_number") }}</th>
                                                        <th>{{ __("$module_name.vehicle_is_common") }}</th>
                                                        <th>{{ __("$module_name.gmt_create") }}</th>
                                                    </tr>
                                                    </thead>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endcan

                            @can('userNotify')
                                {{-- 通知 --}}
                                <div class="tab-pane fade"
                                     id="notify-check"
                                     aria-labelledby="notify-tab-check"
                                     role="tabpanel">
                                    <div class="card">
                                        <div class="card-body d-flex justify-content-between align-items-center row">
                                            <div class="col-12 col-sm-12 col-lg-4">
                                                <label>{{ __("$module_name.notify_gmt_release") }}</label>
                                                <input id="gmt_release_search"
                                                       class="form-control cmb-10 enter_search laydatetime-range"
                                                       placeholder="{{ __("$module_name.notify_gmt_release") }}"
                                                       value=''/>
                                            </div>
                                            <div class="col-12 col-sm-12 col-lg-4">
                                                <label>{{ __("$module_name.notify_title") }}</label>
                                                <input id="title_search" class="form-control cmb-10 enter_search"
                                                       placeholder="{{ __("$module_name.notify_title") }}"
                                                       value=''/>
                                            </div>
                                            <div class="col-12 col-sm-12 col-lg-4">
                                                <label>{{ __("$module_name.notify_content") }}</label>
                                                <input id="content_search" class="form-control cmb-10 enter_search"
                                                       placeholder="{{ __("$module_name.notify_content") }}"
                                                       value=''/>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table id="notifyDatatable"
                                                       class="table table-striped invoice-data-table nowrap table-hover"
                                                       style="width:100%" data-url="{{ $notify_list_url }}">
                                                    <thead>
                                                    <tr>
                                                        <th data-priority="1">{{ __("$module_name.notify_gmt_release") }}</th>
                                                        <th class="nosort">{{ __("$module_name.notify_image") }}</th>
                                                        <th class="nosort">{{ __("$module_name.notify_title") }}</th>
                                                        <th class="nosort">{{ __("$module_name.notify_content") }}</th>
                                                        <th>{{ __("$module_name.notify_gmt_read") }}</th>
                                                        <th>{{ __("$module_name.notify_gmt_create") }}</th>
                                                        <th>{{ __("$module_name.notify_gmt_modified") }}</th>
                                                    </tr>
                                                    </thead>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endcan

                        </div>
                    </div>
                    <!--/ Detail Right -->
                </div>
            </div>
            <!-- / Content -->
        </div>

        {{-- Modal --}}
        <div class="modal fade" id="editAppUserModal" data-backdrop="static" data-keyboard="false"
             tabindex="-1"
             aria-labelledby="staticBackdropLabelEditAppUserModal" aria-hidden="true">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title"
                            id="staticBackdropLabelEditAppUserModal">{{ __("$module_name.app_user_edit_user_information") }}</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"
                                onclick="resetAppUserModal()">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" value="" name="user_id"/>

                        <div class="col-12 col-sm-12">
                            <div class="form-group required">
                                <div class="controls">
                                    <label>{{ __("$module_name.merchant") }}</label>
                                    <select name="merchant_number" class="form-control select2 select2_not_search" data-placeholder="{{ __("$module_name.merchant") }}" onchange="onChangeMerchantEdit(this)">
                                        <option value=""></option>
                                        @foreach ($merchant_list as $merchant)
                                            <option value="{{ $merchant['value'] }}">{{ $merchant['name'] }}</option>
                                        @endforeach
                                    </select>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                        </div>

                        <div class="col-12 col-sm-12">
                            <div class="form-group">
                                <div class="controls">
                                    <label>{{ __("$module_name.user_group") }}</label>
                                    <select name="user_group_id" class="form-control select2 select2_search" data-placeholder="{{ __("$module_name.user_group") }}">
                                        <option value=""></option>
                                        @foreach ($user_group_list as $user_group)
                                            <optgroup value="{{ $user_group['merchant_number'] }}" label="{{ $user_group['merchant_name'] }}">
                                                @foreach ($user_group['user_group_list'] as $item)
                                                    <option value="{{ $item['user_group_id'] }}">{{ $item['user_group_name'] }}</option>
                                                @endforeach
                                            </optgroup>
                                        @endforeach
                                    </select>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                        </div>

                        <div class="col-12 col-sm-12">
                            <div class="form-group required">
                                <div class="controls">
                                    <label>{{ __("$module_name.email") }}</label>
                                    <input type="text"
                                           class="form-control"
                                           placeholder="{{ __("$module_name.email") }}"
                                           value="" name="email">
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                        </div>

                        <div class="col-12 col-sm-12">
                            <div class="form-group">
                                <div class="controls">
                                    <label>{{ __("$module_name.nickname") }}</label>
                                    <input type="text"
                                           class="form-control"
                                           placeholder="{{ __("$module_name.nickname") }}"
                                           value="" name="nickname">
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                        </div>

                        <div class="col-12 col-sm-12">
                            <div class="form-group">
                                <div class="controls">
                                    <label>{{ __("$module_name.telephone") }}</label>
                                    <input type="text"
                                           class="form-control"
                                           placeholder="{{ __("$module_name.telephone") }}"
                                           value="" name="telephone">
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                        </div>

                        <div class="col-12 col-sm-12">
                            <div class="form-group">
                                <div class="controls">
                                    <label>{{ __("$module_name.region") }}</label>
                                    <div class="row mt-1">
                                        <div id="region-info" class="col-12">
                                            <input type="hidden" name="region_id"
                                                   value=""/>
                                            <div id="region-info-name" class="btn btn-primary glow mb-1 mr-sm-1"
                                                 onclick="showRegionTreeModal()" role="button">
                                                <i class="bx bx-cog"
                                                   style="margin-right: 5px;"></i>{{ __('common.text_select_default') }}
                                            </div>
                                            <div class="btn btn-warning mb-1 mr-sm-1" onclick="clearRegion()"
                                                 role="button">{{ __('common.text_clear') }}</div>
                                        </div>
                                    </div>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                        </div>

                        <div class="col-12 col-sm-12">
                            <div class="form-group">
                                <div class="controls">
                                    <label>{{ __("$module_name.site") }}</label>
                                    <div class="row mt-1">
                                        <div id="site-info" class="col-12">
                                            <input type="hidden" name="site_number_list"
                                                   value=""/>
                                            <div id="site-info-name" class="btn btn-primary glow mb-1 mr-sm-1"
                                                 onclick="showSiteManagerCheckboxModal()" role="button">
                                                <i class="bx bx-cog"
                                                   style="margin-right: 5px;"></i>{{ __('common.text_select_default') }}
                                            </div>
                                            <div class="btn btn-warning mb-1 mr-sm-1" onclick="clearSite()"
                                                 role="button">{{ __('common.text_clear') }}</div>
                                        </div>
                                    </div>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary"
                                data-dismiss="modal"
                                onclick="resetAppUserModal()">{{ __('common.close_btn') }}</button>
                        <button type="button" class="btn btn-primary"
                                onclick="saveAppUser()">{{ __('common.submit_btn') }}</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade" id="pointsBalanceModal" data-backdrop="static" data-keyboard="false"
             tabindex="-1"
             aria-labelledby="staticBackdropLabelPointsBalanceModal" aria-hidden="true">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title"
                            id="staticBackdropLabelPointsBalanceModal"></h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"
                                onclick="resetPointsBalanceModal()">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="col-12 col-md-12 col-sm-12">
                            <div class="connector_setTitle">
                                <p class="name current-points-balance"></p>
                            </div>
                        </div>
                        <div class="col-12 col-sm-12">
                            <div class="form-group required">
                                <div class="controls">
                                    <label>{{ __("$module_name.please_fill_points_balance") }}</label>
                                    <input type="number" oninput='if(value.length > 9)value=value.slice(0,9)'
                                           class="form-control @error('points_balance') is-invalid @enderror"
                                           placeholder="{{ __("$module_name.please_fill_points_balance") }}" value=""
                                           name="points_balance">
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary"
                                data-dismiss="modal"
                                onclick="resetPointsBalanceModal()">{{ __('common.close_btn') }}</button>
                        <button id="points_balance_submit" type="button"
                                class="btn btn-primary">{{ __('common.submit_btn') }}</button>
                    </div>
                </div>
            </div>
        </div>
    </section>
    @can('memberCard/edit')
        <x-member-card-binding-app-user-manager-modal/>
    @endcan
    <x-site-manager-checkbox-modal/>
    <x-region-tree-modal
        :is-parent-can-check="true"
    />

    <script>
        const textTransactionTypeIncome = "{{ \App\Enums\TransactionType::getDescription(\App\Enums\TransactionType::Income) }}";
        const textTransactionTypeExpense = "{{ \App\Enums\TransactionType::getDescription(\App\Enums\TransactionType::Expense) }}";
        const textAdd = "{{ __('common.text_add') }}";
        const textBinding = "{{ __('common.text_binding') }}";
        const textUnbinding = "{{ __('common.text_unbinding') }}";
        const textSearch = "{{ __('common.text_search') }}";
        const textRefresh = "{{ __('common.text_refresh') }}";
        const textDeviceInformation = "{{ __("$module_name.user_device_device_information") }}";
        const textSwalBtnConfirm = "{{ __("common.swal_btn_confirm") }}";
        const textSelectDefault = "{{ __("common.text_select_default") }}";
        const swalUnbindingTitle = "{{ __("$module_name.swal_unbinding_title") }}";
        const swalUnbindingText = "{{ __("$module_name.swal_unbinding_text") }}";
        const textPointsBalanceTopUp = "{{ __("$module_name.points_balance_top_up") }}";
        const textPointsBalanceDeduct = "{{ __("$module_name.points_balance_deduct") }}";
        const userGroupList = @json($user_group_list);

        const userId = '{{ $model['user_id'] }}';// 该用户id

        // 用户凭证模块
        @can('userCredential')
            const userCredentialDatatableId = '#userCredentialDatatable';// 列表id
            const userCredentialOrder = [[6, "desc"]];// 列表默认排序字段
            var userCredentialDatatable;
            var userCredentialDatatableColumns = [];
            // 显示列表字段
            userCredentialDatatableColumns.push(
                {"data": "credential_type"},
                {"data": "credential_number"},
                {
                    "data": "is_enable",
                    "render": function (data, type, row, meta) {
                        var iconType = data ? "bx-check" : "bx-x";
                        return `<div class="tf_icon">
                            <i class="bx ${iconType}"></i>
                        </div>`;
                    }
                },
                {"data": "sort_order"},
                {"data": "remark"},
                {"data": "gmt_create"},
                {"data": "gmt_modified"},
            );
            // 新增列表属性
            var userCredentialDataTableAddOption = {
                "dom": `<"row"<"col-sm-12 col-md-2"l><"col-sm-12 col-md-10 d-flex justify-content-end align-self-center"B>>t<"row"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>`,
                "buttons": [
                    {
                        text: `<i class="bx bx-search-alt"></i>`,
                        attr: {
                            'class': 'btn btn-primary glow search_button', 'onclick': 'onSearchUserCredential()',
                            'data-toggle': 'tooltip', "title": textSearch,
                        },
                    },
                    @can('userCredential/add')
                    {
                        text: `<i class="bx bx-plus"></i>`,
                        attr: {
                            'class': 'btn btn-primary glow add_button ml-1',
                            'onclick': 'openUserCredentialAddFormModal("{{ $user_credential_add_url }}")',
                            'data-toggle': 'tooltip',
                            "title": textAdd,
                        },
                    }
                    @endcan
                ]
            };


            // user credential tab点击事件
            function onTabUserCredentialClick() {
                // 如果选中为active就不继续往后触发事件
                if ($("#user-credential-check").hasClass('active')) return;
                onSearchUserCredential();
            }

            // user credential搜索按钮点击事件 如果存在就刷新表格
            function onSearchUserCredential(isSave = true) {
                userCredentialDatatable?.draw(isSave);
            }

            // user credential删除
            function onDeleteUserCredential(user_credential_id) {
                Swal.fire({
                    title: swalDeleteTitle,
                    text: swalDeleteText,
                    confirmButtonText: swalBtnConfirm,
                    cancelButtonText: swalBtnCancel,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonClass: 'btn btn-primary',
                    cancelButtonClass: 'btn btn-danger ml-1',
                    buttonsStyling: false,
                }).then(function (result) {
                    if (result.isConfirmed) {
                        ajaxPostRequestJsonAndCallback({
                            url: "/userCredential/delete",
                            params: {
                                id: user_credential_id,
                            },
                            successCallback: () => {
                                Swal.fire({
                                    confirmButtonText: swalBtnConfirm,
                                    text: swalSuccessText,
                                    icon: 'success',
                                    showCancelButton: false,
                                    confirmButtonColor: '#3085d6',
                                    confirmButtonClass: 'btn btn-primary',
                                    buttonsStyling: false,
                                }).then(() => onSearchUserCredential(true));
                            },
                            errorCallback: (message) => {
                                Swal.fire({
                                    title: swalWarningTitle,
                                    confirmButtonText: swalBtnConfirm,
                                    text: message,
                                    icon: 'error',
                                    showCancelButton: false,
                                    confirmButtonColor: '#3085d6',
                                    confirmButtonClass: 'btn btn-primary',
                                    buttonsStyling: false,
                                }).then(() => onSearchUserCredential(false));
                            },
                        });
                    }
                });
            }

            // 小窗口打开用户凭证新增表单页面
            function openUserCredentialAddFormModal(url) {
                openNewWindow(url, 'userCredentialAddFormModal', () => onSearchUserCredential(), 1000, 800);
            }

            // 小窗口打开用户凭证编辑表单页面
            function openUserCredentialEditFormModal(url) {
                url += `?user_search=${userId}`;
                openNewWindow(url, 'userCredentialEditFormModal', () => onSearchUserCredential(), 1000, 800);
            }
        @endcan

        // 会员卡模块
        @can('memberCard')
            const memberCardDatatableId = '#memberCardDatatable';// 列表id
            const memberCardOrder = [[@if(isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) 7 @else 6 @endif, "desc"]];// 列表默认排序字段
            var memberCardDatatable;
            var memberCardDatatableColumns = [];
            @if(isSuperAdministrator() || auth()->user()->site_number_list->count() > 1)
            memberCardDatatableColumns.push({"data": "site_name"});
            @endif
            // 显示列表字段
            memberCardDatatableColumns.push(
                {"data": "member_card_key"},
                {"data": "member_card_group_name"},
                {"data": "member_name"},
                {"data": "member_email"},
                {"data": "octopus_card_number"},
                {"data": "rfid_card_number"},
                {"data": "gmt_create"},
            );
            // 新增列表属性
            var memberCardDataTableAddOption = {
                "dom": `<"row"<"col-sm-12 col-md-2"l><"col-sm-12 col-md-10 d-flex justify-content-end align-self-center"B>>t<"row"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>`,
                "buttons": [
                    {
                        text: `<i class="bx bx-search-alt"></i>`,
                        attr: {
                            'class': 'btn btn-primary glow search_button', 'onclick': 'onSearchMemberCard()',
                            'data-toggle': 'tooltip', "title": textSearch,
                        },
                    },
                        @can('appUser/edit')
                        @can('memberCard/edit')
                    {
                        text: `<i class="bx bx-link"></i>`,
                        attr: {
                            'class': 'btn btn-primary glow binding_button ml-1', 'onclick': 'showMemberCardManagerModal()',
                            'data-toggle': 'tooltip', "title": textBinding,
                        },
                    },
                        @endcan
                        @endcan
                        @can('memberCard/add')
                    {
                        text: `<i class="bx bx-plus"></i>`,
                        attr: {
                            'class': 'btn btn-primary glow add_button ml-1',
                            'onclick': 'openMemberCardFormModal("{{ $member_card_add_url }}")',
                            'data-toggle': 'tooltip',
                            "title": textAdd,
                        },
                    }
                    @endcan
                ]
            };


            // member card tab点击事件
            function onTabMemberCardClick() {
                // 如果选中为active就不继续往后触发事件
                if ($("#member-card-check").hasClass('active')) return;
                onSearchMemberCard();
            }

            // member card搜索按钮点击事件 如果存在就刷新表格
            function onSearchMemberCard(isSave = true) {
                memberCardDatatable?.draw(isSave);
            }

            // member card解绑
            function onUnbindingMemberCard(member_card_id) {
                Swal.fire({
                    title: swalUnbindingTitle,
                    text: swalUnbindingText,
                    confirmButtonText: swalBtnConfirm,
                    cancelButtonText: swalBtnCancel,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonClass: 'btn btn-primary',
                    cancelButtonClass: 'btn btn-danger ml-1',
                    buttonsStyling: false,
                }).then(function (result) {
                    if (result.isConfirmed) {
                        ajaxPostRequestJsonAndCallback({
                            url: "/memberCard/updateBindingAppUser",
                            params: {
                                member_card_id: member_card_id,
                                _token: $("input[name='_token']").val(),
                            },
                            successCallback: () => {
                                Swal.fire({
                                    confirmButtonText: swalBtnConfirm,
                                    text: swalSuccessText,
                                    icon: 'success',
                                    showCancelButton: false,
                                    confirmButtonColor: '#3085d6',
                                    confirmButtonClass: 'btn btn-primary',
                                    buttonsStyling: false,
                                }).then(() => onSearchMemberCard(true));
                            },
                            errorCallback: (message) => {
                                Swal.fire({
                                    title: swalWarningTitle,
                                    confirmButtonText: swalBtnConfirm,
                                    text: message,
                                    icon: 'error',
                                    showCancelButton: false,
                                    confirmButtonColor: '#3085d6',
                                    confirmButtonClass: 'btn btn-primary',
                                    buttonsStyling: false,
                                }).then(() => onSearchMemberCard(false));
                            },
                        });
                    }
                });
            }

            // member card删除
            function onDeleteMemberCard(member_card_id) {
                Swal.fire({
                    title: swalDeleteTitle,
                    text: swalDeleteText,
                    confirmButtonText: swalBtnConfirm,
                    cancelButtonText: swalBtnCancel,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonClass: 'btn btn-primary',
                    cancelButtonClass: 'btn btn-danger ml-1',
                    buttonsStyling: false,
                }).then(function (result) {
                    if (result.isConfirmed) {
                        ajaxPostRequestJsonAndCallback({
                            url: "/memberCard/delete",
                            params: {
                                id: member_card_id,
                                _token: $("input[name='_token']").val(),
                            },
                            successCallback: () => {
                                Swal.fire({
                                    confirmButtonText: swalBtnConfirm,
                                    text: swalSuccessText,
                                    icon: 'success',
                                    showCancelButton: false,
                                    confirmButtonColor: '#3085d6',
                                    confirmButtonClass: 'btn btn-primary',
                                    buttonsStyling: false,
                                }).then(() => onSearchMemberCard(true));
                            },
                            errorCallback: (message) => {
                                Swal.fire({
                                    title: swalWarningTitle,
                                    confirmButtonText: swalBtnConfirm,
                                    text: message,
                                    icon: 'error',
                                    showCancelButton: false,
                                    confirmButtonColor: '#3085d6',
                                    confirmButtonClass: 'btn btn-primary',
                                    buttonsStyling: false,
                                }).then(() => onSearchMemberCard(false));
                            },
                        });
                    }
                });
            }

            // 设置会员卡弹窗
            function showMemberCardManagerModal() {
                initManagerModalMemberCardDataTable();
                $('#managerModalMemberCard').modal('show');
            }

            // 保存会员卡manager模型
            function managerModalMemberCardSave() {
                // 拼接已选会员卡ids
                var member_card_ids =
                    $('input.checkbox_member_card:checked').map((index, element) => $(element).val()).toArray().join(',');

                if (member_card_ids) {
                    ajaxPostRequestJsonAndCallback({
                        url: "/memberCard/updateBindingAppUser",
                        params: {
                            member_card_id: member_card_ids,
                            user_id: userId,
                            _token: $("input[name='_token']").val(),
                        },
                        successCallback: () => {
                            Swal.fire({
                                confirmButtonText: swalBtnConfirm,
                                text: swalSuccessText,
                                icon: 'success',
                                showCancelButton: false,
                                confirmButtonColor: '#3085d6',
                                confirmButtonClass: 'btn btn-primary',
                                buttonsStyling: false,
                            }).then(() => {
                                // 关闭弹窗
                                $('#managerModalMemberCard .close').click();

                                onSearchMemberCard(true);
                            });
                        },
                        errorCallback: (message) => {
                            Swal.fire({
                                title: swalWarningTitle,
                                confirmButtonText: swalBtnConfirm,
                                text: message,
                                icon: 'error',
                                showCancelButton: false,
                                confirmButtonColor: '#3085d6',
                                confirmButtonClass: 'btn btn-primary',
                                buttonsStyling: false,
                            }).then(() => onSearchMemberCard(false));
                        },
                    });
                } else {
                    Swal.fire({
                        title: swalWarningTitle,
                        confirmButtonText: swalBtnConfirm,
                        text: textSelectDefault,
                        icon: 'error',
                        showCancelButton: false,
                        confirmButtonColor: '#3085d6',
                        confirmButtonClass: 'btn btn-primary',
                        buttonsStyling: false,
                    });
                }
            }

            // 检查且选中，目前无需选中，但需要保留该函数
            function checkAndSelectCheckbox() {
            }

            // 小窗口打开会员卡表单页面
            function openMemberCardFormModal(url) {
                openNewWindow(url, 'memberCardFormModal', () => onSearchMemberCard(), 1000, 800);
            }
        @endcan

        @can('pointsTransaction')
        var currentPointsWalletId = "{{ $model['points_wallet_list'][0]['points_wallet_id'] ?? 0 }}"; // 当前选中的积分钱包ID
        const pointsTransactionDatatableId = '#pointsTransactionDatatable_';// 列表id
        const pointsTransactionOrder = [[5, "desc"]];// 列表默认排序字段

        const isResponsivePointsTransaction = true;
        var pointsTransactionDatatableList = {};

        // 新增列表属性
        var pointsTransactionDataTableAddOption = {
            "dom": `<"row"<"col-sm-12 col-md-2"l><"col-sm-12 col-md-10 d-flex justify-content-end align-self-center"B>>t<"row"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>`,
            "buttons": [
                    @can('appUser/edit')
                {
                    text: `<i class='bx bx-plus-circle' ></i>`,
                    attr: {
                        'class': 'btn btn-primary glow', 'onclick': 'showSystemTopUp()',
                        'data-toggle': 'tooltip', "title": textPointsBalanceTopUp,
                    },
                },
                {
                    text: `<i class='bx bx-minus-circle' ></i>`,
                    attr: {
                        'class': 'btn btn-primary glow ml-1', 'onclick': 'showSystemDeduct()',
                        'data-toggle': 'tooltip', "title": textPointsBalanceDeduct,
                    },
                },
                    @endcan
                {
                    text: `<i class="bx bx-search-alt"></i>`,
                    attr: {
                        'class': 'btn btn-primary glow search_button ml-1',
                        'onclick': 'onClickPointsTransactionSearch()',
                        'data-toggle': 'tooltip',
                        "title": textSearch,
                    },
                }
            ]
        };
        // 显示列表字段
        var pointsTransactionDatatableColumns = [
            {"data": "transaction_number"},
            {
                "data": "transaction_type",
                "render": function (data, type, row) {
                    var svgSrc;
                    if (data == textTransactionTypeExpense) {
                        svgSrc = "../../fonts/LivIconsEvo/svg/app-user-expense.svg";
                    } else if (data == textTransactionTypeIncome) {
                        svgSrc = "../../fonts/LivIconsEvo/svg/app-user-income.svg";
                    }
                    return `<div><img src="${svgSrc}" alt="${data}" width="25px" height="25px" /><span style="margin-left: 10px;">${data}</span></div>`;
                }
            },
            {"data": "transaction_category"},
            {"data": "amount"},
            {"data": "points_balance"},
            {"data": "gmt_create"},
        ];

        @can('appUser/edit')
        function showSystemTopUp() {
            $('#staticBackdropLabelPointsBalanceModal').text(textPointsBalanceTopUp);
            const currentPointBalance = $(`#points-wallet-${currentPointsWalletId} .card-header .points-balance`).text();
            $('#pointsBalanceModal .modal-body .current-points-balance').text(currentPointBalance);
            $('#points_balance_submit').on('click', function () {
                pointsBalanceTopUp();
            });
            $('#pointsBalanceModal').modal('show');
        }

        function showSystemDeduct() {
            $('#staticBackdropLabelPointsBalanceModal').text(textPointsBalanceDeduct);
            const currentPointBalance = $(`#points-wallet-${currentPointsWalletId} .card-header .points-balance`).text();
            $('#pointsBalanceModal .modal-body .current-points-balance').text(currentPointBalance);
            $('#points_balance_submit').on('click', function () {
                pointsBalanceDeduct();
            });
            $('#pointsBalanceModal').modal('show');
        }

        function resetPointsBalanceModal() {
            $('#pointsBalanceModal input[name=points_balance]').val(null);
            $('#pointsBalanceModal .modal-body .current-points-balance').text('');
            $('#points_balance_submit').off('click');
            // 隐藏错误提示
            modalErrorMessageShowChanged('pointsBalanceModal', 'points_balance', '');
        }

        // 积分增值
        function pointsBalanceTopUp() {
            var pointsBalance = $('#pointsBalanceModal input[name=points_balance]').val();
            ajaxPostRequestJsonAndCallback({
                url: "/appUser/systemTopUp",
                params: {
                    points_balance: pointsBalance,
                    user_id: userId,
                    points_wallet_id: currentPointsWalletId,
                },
                successCallback: function (data) {
                    Swal.fire({
                        confirmButtonText: swalBtnConfirm,
                        text: swalSuccessText,
                        icon: 'success',
                        showCancelButton: false,
                        confirmButtonColor: '#3085d6',
                        confirmButtonClass: 'btn btn-primary',
                        buttonsStyling: false,
                    }).then(() => {
                        pointsTransactionDatatableList[currentPointsWalletId].draw(true);
                        getUserLatestPointsWalletBalance();
                        // 关闭弹窗
                        $('#pointsBalanceModal .close').click();
                    });
                },
                statusCodeCallback: function (data) {
                    var errors = data.responseJSON.errors;
                    // 处理错误提示
                    modalErrorMessageShowChanged('pointsBalanceModal', 'points_balance', errors.points_balance);
                    if (errors.points_wallet_id) {
                        Swal.fire({
                            title: swalWarningTitle,
                            confirmButtonText: swalBtnConfirm,
                            text: errors.points_wallet_id,
                            icon: 'error',
                            showCancelButton: false,
                            confirmButtonColor: '#3085d6',
                            confirmButtonClass: 'btn btn-primary',
                            buttonsStyling: false,
                        }).then();
                    }
                },
                errorCallback: (message) => {
                    Swal.fire({
                        title: swalWarningTitle,
                        confirmButtonText: swalBtnConfirm,
                        text: message,
                        icon: 'error',
                        showCancelButton: false,
                        confirmButtonColor: '#3085d6',
                        confirmButtonClass: 'btn btn-primary',
                        buttonsStyling: false,
                    }).then();
                },
            });
        }

        // 积分扣除
        function pointsBalanceDeduct() {
            var pointsBalance = $('#pointsBalanceModal input[name=points_balance]').val();
            ajaxPostRequestJsonAndCallback({
                url: "/appUser/systemDeduct",
                params: {
                    points_balance: pointsBalance,
                    user_id: userId,
                    points_wallet_id: currentPointsWalletId,
                },
                successCallback: function (data) {
                    Swal.fire({
                        confirmButtonText: swalBtnConfirm,
                        text: swalSuccessText,
                        icon: 'success',
                        showCancelButton: false,
                        confirmButtonColor: '#3085d6',
                        confirmButtonClass: 'btn btn-primary',
                        buttonsStyling: false,
                    }).then(() => {
                        pointsTransactionDatatableList[currentPointsWalletId].draw(true);
                        getUserLatestPointsWalletBalance();
                        // 关闭弹窗
                        $('#pointsBalanceModal .close').click();
                    });
                },
                statusCodeCallback: function (data) {
                    var errors = data.responseJSON.errors;
                    // 处理错误提示
                    modalErrorMessageShowChanged('pointsBalanceModal', 'points_balance', errors.points_balance);
                    if (errors.points_wallet_id) {
                        Swal.fire({
                            title: swalWarningTitle,
                            confirmButtonText: swalBtnConfirm,
                            text: errors.points_wallet_id,
                            icon: 'error',
                            showCancelButton: false,
                            confirmButtonColor: '#3085d6',
                            confirmButtonClass: 'btn btn-primary',
                            buttonsStyling: false,
                        }).then();
                    }
                },
                errorCallback: (message) => {
                    Swal.fire({
                        title: swalWarningTitle,
                        confirmButtonText: swalBtnConfirm,
                        text: message,
                        icon: 'error',
                        showCancelButton: false,
                        confirmButtonColor: '#3085d6',
                        confirmButtonClass: 'btn btn-primary',
                        buttonsStyling: false,
                    }).then();
                },
            });
        }
        @endcan

        // points transaction tab点击事件
        function onTabPointsTransactionClick() {
            // 如果选中为active就不继续往后触发事件
            if ($("#points-transaction-check").hasClass('active')) return;
            // 如果没有钱包ID就不继续往后触发事件
            if (isEmptyString(currentPointsWalletId) || currentPointsWalletId == '0') return;
            onClickPointsTransactionSearch();
        }

        // points wallet tab点击事件
        function onTabPointsWalletClick(pointsWalletId) {
            currentPointsWalletId = pointsWalletId;
            // 如果没有钱包ID就不继续往后触发事件
            if (isEmptyString(currentPointsWalletId) || currentPointsWalletId == '0') return;
            // 如果选中为active就不继续往后触发事件
            if ($(`#points-wallet-${currentPointsWalletId}-tab`).hasClass('active')) return;
            onClickPointsTransactionSearch();
        }

        // points transaction搜索按钮点击事件 如果存在就刷新表格
        function onClickPointsTransactionSearch() {
            if (pointsTransactionDatatableList[currentPointsWalletId] != null) {
                pointsTransactionDatatableList[currentPointsWalletId].draw(true);
                pointsTransactionDatatableList[currentPointsWalletId].columns.adjust();
            }
        }
        @endcan
        @can('vehicle')
        const vehicleDatatableId = '#vehicleDatatable';// 列表id
        const vehicleOrder = [[2, "desc"]]; // 列表默认排序字段

        var vehicleDatatable;

        // 新增列表属性
        var vehicleDataTableAddOption = {
            "dom": `<"row"<"col-sm-12 col-md-2"l><"col-sm-12 col-md-10 d-flex justify-content-end align-self-center"B>>t<"row"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>`,
            "buttons": [
                {
                    text: `<i class="bx bx-search-alt"></i>`,
                    attr: {
                        'class': 'btn btn-primary glow search_button', 'onclick': 'onClickVehicleSearch()',
                        'data-toggle': 'tooltip', "title": textSearch,
                    },
                }
            ]
        };
        // 显示列表字段
        var vehicleDatatableColumns = [
            {"data": "plate_number"},
            {
                "data": "is_common",
                "render": function (data, type, row, meta) {
                    var iconType = data ? "bx-check" : "bx-x";
                    return `<div class="tf_icon">
                                        <i class="bx ${iconType}"></i>
                                    </div>`;
                }
            },
            {"data": "gmt_create"},
        ];

        // vehicle tab点击事件
        function onTabVehicleClick() {
            // 如果选中为active就不继续往后触发事件
            if ($("#vehicle-check").hasClass('active')) return;
            onClickVehicleSearch();
        }

        // vehicle搜索按钮点击事件 如果存在就刷新表格
        function onClickVehicleSearch() {
            if (vehicleDatatable != null) {
                vehicleDatatable.draw(true);
            }
        }
        @endcan
        @can('userNotify')
        const notifyDatatableId = '#notifyDatatable';// 列表id
        const notifyOrder = [[5, "desc"]]; // 列表默认排序字段

        var notifyDatatable;
        var notifyDescriptionList = {};

        // 新增列表属性
        var notifyDataTableAddOption = {
            "dom": `<"row"<"col-sm-12 col-md-2"l><"col-sm-12 col-md-10 d-flex justify-content-end align-self-center"B>>t<"row"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>`,
            "buttons": [
                {
                    text: `<i class="bx bx-search-alt"></i>`,
                    attr: {
                        'class': 'btn btn-primary glow search_button', 'onclick': 'onClickNotifySearch()',
                        'data-toggle': 'tooltip', "title": textSearch,
                    },
                }
            ]
        };
        // 显示列表字段
        var notifyDatatableColumns = [
            {"data": "gmt_release"},
            {
                "data": "image_url",
                "render": function (data, type, row, meta) {
                    return data ? `<a href="${data}" target="_blank">
                            <div class="avatar">
                                <img class="round" src="${data}"
                                    alt="Avatar" width="40px" height="40px" />
                            </div>
                        </a>` : '';
                }
            },
            {"data": "title"},
            {
                "data": "content",
                "render": function (data, type, row, meta) {
                    var content = data;
                    // 判断多语言中是否有值
                    var isShowMore = false;
                    $.each(row?.description_list ?? {}, function (index, description) {
                        if (description.content && description.content.length > 0) {
                            isShowMore = true;
                            return false;
                        }
                    });
                    if (isShowMore) {
                        // 截取20个字符，多余的用...代替
                        if (content.length > 20) {
                            content = content.substring(0, 20) + `...`;
                        }
                        content += ` <i class='bx bx-caret-down-square view-more-btn' title=${data} onclick="viewMore(${row.user_notify_id})" ></i>`;
                        notifyDescriptionList[row.user_notify_id] = row.description_list;
                    }

                    return content;
                }
            },
            {"data": "gmt_read"},
            {"data": "gmt_create"},
            {"data": "gmt_modified"},
        ];

        // notify tab点击事件
        function onTabNotifyClick() {
            // 如果选中为active就不继续往后触发事件
            if ($("#notify-check").hasClass('active')) return;
            onClickNotifySearch();
        }

        // notify搜索按钮点击事件 如果存在就刷新表格
        function onClickNotifySearch() {
            if (notifyDatatable != null) {
                notifyDatatable.draw(true);
            }
        }

        @endcan

            window.onload = function () {
            // 回车搜索
            $('.enter_search').on('keydown', function (event) {
                if (event.key === 'Enter') {
                    activeElementClick();
                }
            });

            @can('userCredential')
                // 获取listDataTable
                var userCredentialDatatableFixedColumns;
                @canany(['userCredential/edit', 'userCredential/delete'])
                    userCredentialDatatableFixedColumns = {
                        left: 0,
                        right: 1,
                    };
                    userCredentialDatatableColumns.push({
                        "data": "user_credential_id",
                        "render": function (data, type, row, meta) {
                            var action = '';
                                @can('userCredential/edit')
                                    action += `<button style="border:0px;" class="badge-circle badge-circle-xs badge-circle-light-primary block" onclick="openUserCredentialEditFormModal('{{ $user_credential_edit_url }}${data}')" data-toggle="tooltip" title="${textEdit}"><i class="bx bx-edit-alt"></i></button>`;
                                @endcan
                                @can('userCredential/delete')
                                    action += `<button style="border:0px;" class="badge-circle badge-circle-xs badge-circle-light-danger block" onclick="onDeleteUserCredential(${data})" data-toggle="tooltip" title="${textDelete}"><i class="bx bx-trash"></i></button>`;
                                @endcan
                                return action;
                        }
                    });
                @endcanany
                    userCredentialDatatable = listDataTableDraw({
                    "dataTableId": userCredentialDatatableId,
                    'dataTableFixedColumns': userCredentialDatatableFixedColumns,
                    "dataTableColumns": userCredentialDatatableColumns,
                    "dataTableOrder": userCredentialOrder,
                    'dataTableAddOptions': userCredentialDataTableAddOption,
                });

                // 点击切换tab时候更新表头宽度
                $('#user-credential-tab-check').on('shown.bs.tab', function () {
                    if (userCredentialDatatable == null) return;
                    userCredentialDatatable.columns.adjust();
                });
            @endcan

            @can('memberCard')
                // 获取listDataTable
                var memberCardDatatableFixedColumns;
                @canany(['memberCard/edit', 'memberCard/delete'])
                    memberCardDatatableFixedColumns = {
                    left: 0,
                    right: 1,
                };
                memberCardDatatableColumns.push({
                    "data": "member_card_id",
                    "render": function (data, type, row, meta) {
                        var action = '';
                        @can('memberCard/edit')
                            action += `<button style="border:0px;" class="badge-circle badge-circle-xs badge-circle-light-primary block" onclick="onUnbindingMemberCard(${data})" data-toggle="tooltip" title="${textUnbinding}"><i class="bx bx-unlink"></i></button>`;
                        @endcan
                            @can('memberCard/delete')
                            action += `<button style="border:0px;" class="badge-circle badge-circle-xs badge-circle-light-danger block" onclick="onDeleteMemberCard(${data})" data-toggle="tooltip" title="${textDelete}"><i class="bx bx-trash"></i></button>`;
                        @endcan
                            return action;
                    }
                });
                @endcanany
                    memberCardDatatable = listDataTableDraw({
                    "dataTableId": memberCardDatatableId,
                    'dataTableFixedColumns': memberCardDatatableFixedColumns,
                    "dataTableColumns": memberCardDatatableColumns,
                    "dataTableOrder": memberCardOrder,
                    'dataTableAddOptions': memberCardDataTableAddOption,
                });

                // 点击切换tab时候更新表头宽度
                $('#member-card-tab-check').on('shown.bs.tab', function () {
                    if (memberCardDatatable == null) return;
                    memberCardDatatable.columns.adjust();
                });
            @endcan

            @can('pointsTransaction')
            // 获取listDataTable
            @foreach ($model['points_wallet_list'] as $points_wallet)
                pointsTransactionDatatableList["{{ $points_wallet['points_wallet_id'] }}"] = listDataTableDraw({
                    "dataTableId": pointsTransactionDatatableId + "{{ $points_wallet['points_wallet_id'] }}",
                    "dataTableColumns": pointsTransactionDatatableColumns,
                    "dataTableOrder": pointsTransactionOrder,
                    "isResponsive": isResponsivePointsTransaction,
                    "responsiveRowData": 'transaction_number',
                    "dataTableAddOptions": pointsTransactionDataTableAddOption
                });
            @endforeach


            // 点击切换tab时候更新表头宽度
            $('#points-transaction-check .nav-item > .nav-link').on('shown.bs.tab', function () {
                if (pointsTransactionDatatableList[currentPointsWalletId] == null) return;
                pointsTransactionDatatableList[currentPointsWalletId].columns.adjust();
            });
            @endcan


            @can('vehicle')
            // 获取listDataTable
            vehicleDatatable = listDataTableDraw({
                "dataTableId": vehicleDatatableId,
                "dataTableColumns": vehicleDatatableColumns,
                "dataTableOrder": vehicleOrder,
                "dataTableAddOptions": vehicleDataTableAddOption
            });

            $('#vehicle-tab-check').on('shown.bs.tab', function () {
                if (vehicleDatatable == null) return;
                vehicleDatatable.columns.adjust();
            });
            @endcan

            @can('userNotify')
            // 获取listDataTable
            notifyDatatable = listDataTableDraw({
                "dataTableId": notifyDatatableId,
                "dataTableColumns": notifyDatatableColumns,
                "dataTableOrder": notifyOrder,
                "dataTableAddOptions": notifyDataTableAddOption
            });

            $('#notify-tab-check').on('shown.bs.tab', function () {
                if (notifyDatatable == null) return;
                notifyDatatable.columns.adjust();
            });
            @endcan
        }

        // 用于判断触发正在查看页面的回车搜索
        function activeElementClick() {
            // 检查是否有元素具有类名 "active"，并获取第一个匹配的元素
            var activeElement = $('div.active').first();

            if (activeElement.length > 0) {
                // 查找元素下的类名为 "search_button" 的按钮并触发点击事件
                var searchButton = activeElement.find('.search_button');
                if (searchButton.length > 0) {
                    searchButton.click();
                }
            }
        }

        // 重置管理员弹窗数据
        function resetAppUserModal() {
            $('#editAppUserModal input[name=user_id]').val(null);
            $('#editAppUserModal select[name=merchant_number]').val(null).trigger('change');
            $('#editAppUserModal select[name=user_group_id]').val(null).trigger('change');
            $('#editAppUserModal input[name=nickname]').val(null);
            $('#editAppUserModal input[name=telephone]').val(null);
            $('#editAppUserModal input[name=email]').val(null);
            // 地区
            $('#editAppUserModal input[name=region_id]').val(null);
            var iconHtmlText = $('#region-info-name i').prop('outerHTML');
            $('#editAppUserModal #region-info-name').html(iconHtmlText + textSelectDefault);
            // 场地
            $('#editAppUserModal input[name=site_number_list]').val(null);
            var iconHtmlText = $('#site-info-name i').prop('outerHTML');
            $('#editAppUserModal #site-info-name').html(iconHtmlText + textSelectDefault);

            // 隐藏错误提示
            modalErrorMessageShowChanged('editAppUserModal', 'merchant_number', '', 'select');
            modalErrorMessageShowChanged('editAppUserModal', 'user_group_id', '', 'select');
            modalErrorMessageShowChanged('editAppUserModal', 'nickname', '');
            modalErrorMessageShowChanged('editAppUserModal', 'telephone', '');
            modalErrorMessageShowChanged('editAppUserModal', 'email', '');
            modalErrorMessageShowChanged('editAppUserModal', 'region_id', '', 'manager-select');
            modalErrorMessageShowChanged('editAppUserModal', 'site_number_list', '', 'manager-select');
        }

        // 修改信息
        function editAppUser(data = null) {
            if (data) {
                // 设置弹窗数据
                $('#editAppUserModal select[name=merchant_number]').val(data.merchant_number).trigger('change');
                $('#editAppUserModal select[name=user_group_id]').val(data.user_group_id).trigger('change');
                $('#editAppUserModal input[name=user_id]').val(data.user_id);
                $('#editAppUserModal input[name=nickname]').val(data.nickname);
                $('#editAppUserModal input[name=telephone]').val(data.telephone);
                $('#editAppUserModal input[name=email]').val(data.email);
                // 地区
                $('#editAppUserModal input[name=region_id]').val(data.region_id);
                var iconHtmlText = $('#region-info-name i').prop('outerHTML');
                $('#editAppUserModal #region-info-name').html(iconHtmlText + (data.region_id ? data.region_name : textSelectDefault));
                // 场地
                $('#editAppUserModal input[name=site_number_list]').val(data.site_number_list);
                var iconHtmlText = $('#site-info-name i').prop('outerHTML');
                $('#editAppUserModal #site-info-name').html(iconHtmlText + (data.site_name || textSelectDefault));
                $('#editAppUserModal').modal('show');
            }
        }

        // 保存管理员
        function saveAppUser() {
            Swal.fire({
                title: swalWarningTitle,
                text: swalSubmitText,
                confirmButtonText: swalBtnConfirm,
                cancelButtonText: swalBtnCancel,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonClass: 'btn btn-primary',
                cancelButtonClass: 'btn btn-danger ml-1',
                buttonsStyling: false,
            }).then(function (result) {
                if (result.isConfirmed) {
                    // 获取弹窗数据
                    var user_id = $('#editAppUserModal input[name=user_id]').val();
                    var merchant_number = $('#editAppUserModal select[name=merchant_number]').val();
                    var user_group_id = $('#editAppUserModal select[name=user_group_id]').val();
                    var nickname = $('#editAppUserModal input[name=nickname]').val();
                    var telephone = $('#editAppUserModal input[name=telephone]').val();
                    var email = $('#editAppUserModal input[name=email]').val();
                    var region_id = $('#editAppUserModal input[name=region_id]').val();
                    var site_number_list = $('#editAppUserModal input[name=site_number_list]').val();

                    ajaxPostRequestJsonAndCallback({
                        url: "/appUser/edit",
                        params: {
                            user_id: user_id,
                            merchant_number: merchant_number,
                            user_group_id: user_group_id,
                            nickname: nickname,
                            telephone: telephone,
                            email: email,
                            region_id: region_id,
                            site_number_list: site_number_list
                        },
                        successCallback: function (data) {
                            $('#editAppUserModal .close').click();
                            window.location.reload();
                        },
                        statusCodeCallback: function (data) {
                            var errors = data.responseJSON.errors;
                            // 处理错误提示
                            modalErrorMessageShowChanged('editAppUserModal', 'merchant_number', errors.merchant_number, 'select');
                            modalErrorMessageShowChanged('editAppUserModal', 'user_group_id', errors.user_group_id, 'select');
                            modalErrorMessageShowChanged('editAppUserModal', 'nickname', errors.nickname);
                            modalErrorMessageShowChanged('editAppUserModal', 'telephone', errors.telephone);
                            modalErrorMessageShowChanged('editAppUserModal', 'email', errors.email);
                            modalErrorMessageShowChanged('editAppUserModal', 'region_id', errors.region_id, 'manager-select');
                            modalErrorMessageShowChanged('editAppUserModal', 'site_number_list', errors.site_number_list, 'manager-select');
                        },
                    });
                }
            });
        };

        // 选择商户时，设置用户组分组显示
        function onChangeMerchantEdit(that) {
            var merchantElement = $(that);
            var userGroupElement = $("select[name='user_group_id']");

            userGroupElement.html(generateUserGroupOptionHtml(userGroupElement.val(), merchantElement.val())).select2();
        }

        // 生成用户组选项html
        function generateUserGroupOptionHtml(defaultUserGroupId = null, merchantNumber = null) {
            var html = '<option value=""></option>';
            userGroupList.forEach(item => {
                if (merchantNumber && item.merchant_number !== merchantNumber) {
                    return;
                }
                html += `<optgroup value="${item.merchant_number}" label="${item.merchant_name}">`;
                item.user_group_list.forEach(userGroup => {
                    html += `<option value="${userGroup.user_group_id}" ${defaultUserGroupId == userGroup.user_group_id ? 'selected' : ''}>${userGroup.user_group_name}</option>`;
                });
                html += '</optgroup>';
            });
            return html;
        }

        // Site设置弹窗
        function showSiteManagerCheckboxModal() {
            // 获取当前选中值
            var site_number_list = $("#site-info input[name=site_number_list]").val();
            if (site_number_list && site_number_list.length > 0) {
                checkSites(site_number_list.split(','));
            }
            $('#siteManagerCheckboxModal').modal('show');
        }

        function siteManagerCheckboxModalSave() {
            // 获取选中数据
            var site_number_list = [];
            var site_name_list = [];
            $("#siteManagerCheckboxModal input[name='site_number']:checked").each(function () {
                site_number_list.push($(this).val());
                site_name_list.push($(this).parent().next().children().text());
            });

            // 关闭弹窗
            $("#siteManagerCheckboxModal .close").click();
            if (site_number_list && site_number_list.length > 0 && site_name_list && site_name_list.length > 0) {
                // 更改选中值
                $("#site-info input[name=site_number_list]").val(site_number_list.toString());
                var iconHtmlText = $('#site-info-name i').prop('outerHTML');
                $('#site-info-name').html(iconHtmlText + site_name_list.join(' | '));
            } else {
                // 无选择清空值
                clearSite();
            }
        }

        function clearSite() {
            $("#site-info input[name=site_number_list]").val(null);
            var iconHtmlText = $('#site-info-name i').prop('outerHTML');
            $('#site-info-name').html(iconHtmlText + textSelectDefault);
        }

        // Region设置弹窗
        function showRegionTreeModal() {
            // 获取当前选中值
            checkRegion($("#region-info input[name=region_id]").val(), $("#editAppUserModal select[name=merchant_number]").val());
            $('#regionTreeModal').modal('show');
        }

        function regionTreeModalSave() {
            // 获取选中数据
            var regionId = $("#regionTreeModal input[name='region_id']:not(:disabled):checked").val();
            var regionName = $("#regionTreeModal input[name='region_id']:not(:disabled):checked").closest("label").text().trim();
            // 关闭弹窗
            $("#regionTreeModal .close").click();
            if (regionId) {
                // 更改选中值
                $("#region-info input[name=region_id]").val(regionId);
                let iconHtmlText = $('#region-info-name i')[0].outerHTML;
                $('#region-info-name').html(iconHtmlText + regionName);
            }
        }

        function clearRegion() {
            $("#region-info input[name=region_id]").val(null);
            let iconHtmlText = $('#region-info-name i')[0].outerHTML;
            $('#region-info-name').html(iconHtmlText + textSelectDefault);
        }


        // 修改密码
        function onChangePassword(user_id) {
            Swal.fire({
                title: swalWarningTitle,
                text: swalSubmitText,
                confirmButtonText: swalBtnConfirm,
                cancelButtonText: swalBtnCancel,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonClass: 'btn btn-primary',
                cancelButtonClass: 'btn btn-danger ml-1',
                buttonsStyling: false,
            }).then(function (result) {
                if (result.isConfirmed) {
                    var password = $('input[name=password]').val();
                    var password_confirmation = $('input[name=password_confirmation]').val();

                    ajaxPostRequestJsonAndCallback({
                        url: "/appUser/changePassword",
                        params: {
                            user_id: user_id,
                            password: password,
                            password_confirmation: password_confirmation,
                            _token: $("input[name='_token']").val()
                        },
                        successCallback: function (data) {
                            modalErrorMessageShowChanged('', 'password', '');
                            modalErrorMessageShowChanged('', 'password_confirmation', '');
                            Swal.fire({
                                confirmButtonText: swalBtnConfirm,
                                text: swalSuccessText,
                                icon: 'success',
                                showCancelButton: false,
                                confirmButtonColor: '#3085d6',
                                confirmButtonClass: 'btn btn-primary',
                                buttonsStyling: false,
                            }).then(function (result) {
                                window.location.reload();
                            });

                        },
                        statusCodeCallback: function (data) {
                            var errors = data.responseJSON.errors;
                            modalErrorMessageShowChanged('', 'password', errors.password);
                            modalErrorMessageShowChanged('', 'password_confirmation', errors.password_confirmation);
                        },
                    });
                }
            });
        }

        // 点击头像
        function onClickAvatar() {
            $("input[name='avatar_url']").trigger('click');
        }

        // 上传头像
        function updateAvatar(user_id) {
            Swal.fire({
                title: swalWarningTitle,
                text: swalSubmitText,
                confirmButtonText: swalBtnConfirm,
                cancelButtonText: swalBtnCancel,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonClass: 'btn btn-primary',
                cancelButtonClass: 'btn btn-danger ml-1',
                buttonsStyling: false,
            }).then(function (result) {
                if (result.isConfirmed) {
                    // 获取头像文件
                    var formData = new FormData();
                    formData.append('user_id', user_id);
                    formData.append('avatar_url', $("input[name='avatar_url']").prop('files')[0]);
                    formData.append('_token', $("input[name='_token']").val());
                    ajaxFormDataAndCallback({
                        url: "/appUser/upload",
                        formData: formData,
                        successCallback: function (data) {
                            window.location.reload();
                        },
                    });
                }
            });
        };

        @canany(['userCredential', 'memberCard', 'pointsTransaction', 'vehicle', 'userNotify'])
        // 搜索条件
        function dataTableSearchCriteria(d, dataTableId = null) {
            d.user_id = userId;
            switch (dataTableId) {
                @can('userCredential')
                case userCredentialDatatableId:
                    d.credential_type_search = $('#credential_type_search').val();
                    d.credential_number_search = $('#credential_number_search').val();
                    break;
                @endcan
                @can('memberCard')
                case memberCardDatatableId:
                    d.member_card_key_search = $('#member_card_key_search').val();
                    d.octopus_card_number_search = $('#octopus_card_number_search').val();
                    d.rfid_card_number_search = $('#rfid_card_number_search').val();
                    break;
                @endcan
                @can('pointsTransaction')
                @foreach ($model['points_wallet_list'] as $points_wallet)
                    case pointsTransactionDatatableId + "{{ $points_wallet['points_wallet_id'] }}":
                        d.points_wallet_id = {{ $points_wallet['points_wallet_id'] }};
                        d.transaction_number_search = $('#transaction_number_search_{{ $points_wallet['points_wallet_id'] }}').val();
                        d.transaction_type_search = $('#transaction_type_search_{{ $points_wallet['points_wallet_id'] }}').val();
                        d.transaction_category_search = $('#transaction_category_search_{{ $points_wallet['points_wallet_id'] }}').val();
                        break;
                @endforeach

                @endcan
                @can('vehicle')
                case vehicleDatatableId:
                    d.plate_number_search = $('#plate_number_search').val();
                    break;
                @endcan
                @can('userNotify')
                case notifyDatatableId:
                    d.gmt_release_search = $('#gmt_release_search').val();
                    d.title_search = $('#title_search').val();
                    d.content_search = $('#content_search').val();
                    break;
                @endcan
                default:
                    break;
            }
        }
        @endcanany

        function viewMore(id) {
            content = notifyDescriptionList[id];
            var template = `<ul class="nav nav-tabs mb-2 justify-content-around" role="tablist">
                @foreach ($language_list as $language_code => $language_text)
            <li class="nav-item">
                <a class="nav-link d-flex align-items-center @if ($language_code === app()->getLocale()) active @endif"
                        id="general-tab-check"
                        href="#view-more-{{ $language_code }}"
                        data-toggle="tab"
                        aria-controls="general-check" role="tab" aria-selected="true">
                        <i class="bx bx-detail"></i>{{ $language_text }}</a>
                </li>
                @endforeach
            </ul>
            <div class="tab-content">
                @foreach ($language_list as $language_code => $language_text)
            <div class="tab-pane @if ($language_code === app()->getLocale()) active @endif fade show"
                            id="view-more-{{ $language_code }}"
                            aria-labelledby="view-more-{{ $language_code }}-tab"
                            role="tabpanel">
                                ${content?.{{$language_code}}?.image_url ? `<a href="${content?.{{$language_code}}?.image_url}" target="_blank">
                                        <div class="avatar_url">
                                            <img class="round" src="${content?.{{$language_code}}?.image_url}"
                                                alt="Avatar" width="40px" height="40px" />
                                        </div>
                                    </a>` : ''}
                                <h2>${content?.{{$language_code}}?.title ?? ''}</h2>
                                <p>${content?.{{$language_code}}?.content ?? ''}</p>
                    </div>
                @endforeach
            </div>`;
            Swal.fire({
                title: '{{ __("$module_name.notify_content") }}',
                width: '40em',
                html: template,
                confirmButtonText: '{{ __('common.confirm_btn') }}',
            })
        }

        function getUserLatestPointsWalletBalance() {
            ajaxPostRequestJsonAndCallback({
                url: "/appUser/getUserLatestPointsWalletBalance",
                params: {
                    user_id: userId,
                },
                successCallback: function (data) {
                    var pointBalanceList = [];
                    for (let i = 0; i < data.data.length; i++) {
                        const element = data.data[i];
                        const pointBalance = element.currency_symbol + ' ' + element.points_balance;
                        pointBalanceList.push(pointBalance);
                        $("#points-wallet-" + element.points_wallet_id + ' .points-balance').text("{{ __("$module_name.points_balance") }}: " + pointBalance);
                    }
                    $("#points_balance_list").html(pointBalanceList.join('; '));
                }
            });
        }

    </script>
@endsection

{{-- vendor scripts --}}
@section('vendor-scripts')
    <script src="{{asset('vendors/js/forms/select/select2.full.min.js')}}"></script>
    <script src="{{ asset('vendors/js/forms/validation/jquery.validate.min.js') }}"></script>
    <script src="{{asset('vendors/js/forms/validation/jquery.validate.min.js')}}"></script>
    <script src="{{asset('vendors/js/forms/spinner/jquery.bootstrap-touchspin.js')}}"></script>
    {{-- datatable --}}
    <script src="{{asset('vendors/js/tables/datatable/jquery.dataTables.min.js')}}"></script>
    <script src="{{asset('vendors/js/tables/datatable/dataTables.bootstrap4.min.js')}}"></script>
    <script src="{{asset('vendors/js/tables/datatable/dataTables.buttons.min.js')}}"></script>
    <script src="{{asset('vendors/js/tables/datatable/buttons.bootstrap4.min.js')}}"></script>
    <script src="{{asset('vendors/js/tables/datatable/dataTables.fixedColumns.min.js')}}"></script>
    <script src="{{asset('vendors/js/tables/datatable/fixedColumns.bootstrap4.min.js')}}"></script>
    <script src="{{asset('vendors/js/tables/datatable/dataTables.responsive.min.js')}}"></script> {{-- 加号展开的相关js --}}
    <script src="{{asset('vendors/js/tables/datatable/responsive.bootstrap4.min.js')}}"></script> {{-- 加号的相关js --}}
    <script src="{{asset('vendors/js/extensions/sweetalert2.all.min.js')}}"></script>
    <script src="{{asset('vendors/js/extensions/polyfill.min.js')}}"></script>
    <script src="{{asset('vendors/js/laydate/laydate.js')}}"></script>
@endsection

{{-- page scripts --}}
@section('page-scripts')
    <script src="{{ mix('js/scripts/pages/app-users.js') }}"></script>
    <script src="{{ mix('js/scripts/navs/navs.js') }}"></script>
    <script src="{{ mix('js/scripts/pages/common/common.js') }}"></script>
    <script src="{{mix('js/scripts/pages/common/laydate.js')}}"></script> {{-- datetime picker --}}

    {{-- module manage dialog --}}
    <script src="{{mix('js/scripts/forms/custom-options.js')}}"></script>
    <link rel="stylesheet" href="{{mix('css/plugins/forms/custom-options.css')}}">
@endsection

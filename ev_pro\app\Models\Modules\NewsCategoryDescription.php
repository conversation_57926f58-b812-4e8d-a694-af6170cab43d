<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;

class NewsCategoryDescription extends Model
{
    protected $table = 'news_category_description';
    protected $primaryKey = 'news_category_description_id';

    const CREATED_AT = 'gmt_create'; //默认是小写的模型类名复数格式，此处为自定义表名
    const UPDATED_AT = 'gmt_modified'; //定义主键，默认为id

    public $fillable = [
        'news_category_description_id',
        'language_code',
        'title',
    ];
    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 类型转化器
     */
    protected $casts = [

    ];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [

    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * 新闻
     */
    public function newsCategory()
    {
        return $this->hasOne(NewsCategory::class, 'news_category_id', 'news_category_id');
    }
}

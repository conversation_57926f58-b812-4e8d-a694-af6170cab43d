<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;

class Kiosk extends Model
{
    protected $table = 'kiosk'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'kiosk_id'; //定义主键，默认为id
    // 表的唯一number
    public static $table_number = 'kiosk_number';
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    // protected $fillable = [];

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'sort_order' => 0, // 排序
        'octopus_com_port' => 0, // 八达通串口
        'octopus_baud_rate' => 0, // 八达通波特率
    ];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * Kiosk 关联 Charge Point
     * 多对多关联使用其他字段而不是用主键id时，belongsToMany有7个参数配置
     */
    public function chargePoint()
    {
        return $this->belongsToMany(ChargePoint::class, 'kiosk_to_charge_point', 'kiosk_number', 'charge_point_number', 'kiosk_number', 'charge_point_number')->withPivot('sort_order', 'is_enable')->withTimestamps();
    }

    /**
     * 获取与用户相关的电话记录
     */
    public function kioskSetting()
    {
        return $this->hasOne(KioskSetting::class, 'kiosk_setting_number', 'kiosk_setting_number');
    }

    /**
     * 一对多关联kioskPaymentMethod
     */
    public function kioskPaymentMethod()
    {
        return $this->hasMany(KioskPaymentMethod::class, 'kiosk_number', 'kiosk_number');
    }

    /**
     * Kiosk 关联 site
     */
    public function site()
    {
        return $this->hasOne(Site::class, 'site_number', 'site_number');
    }
}

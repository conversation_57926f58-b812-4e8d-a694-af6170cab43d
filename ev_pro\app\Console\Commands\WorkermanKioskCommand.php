<?php

namespace App\Console\Commands;

use App\Workerman\KioskEvents;
use GatewayWorker\BusinessWorker;
use GatewayWorker\Gateway;
use GatewayWorker\Register;
use Illuminate\Console\Command;
use Workerman\Worker;

class WorkermanKioskCommand extends Command
{

    protected $signature = 'workermanKiosk
                            {action : action}
                            {--start=all : start}
                            {--d : daemon mode}';

    protected $description = 'Start a Workerman Kiosk server.';

    public function handle(): void
    {
        global $argv;
        $action = $this->argument('action');
        /**
         * 针对 Windows 一次执行，无法注册多个协议的特殊处理
         */
        if ($action === 'single') {
            $start = $this->option('start');
            if ($start === 'register') {
                $this->startRegister();
            } elseif ($start === 'gateway') {
                $this->startGateWay();
            } elseif ($start === 'worker') {
                $this->startBusinessWorker();
            }
            Worker::runAll();

            return;
        }

        /**
         * argv[0] 默认是，当前文件，可以不修改
         */
        //$argv[0] = 'wk';
        $argv[1] = $action;
        // 控制是否进入 daemon 模式
        $argv[2] = $this->option('d') ? '-d' : '';

        $this->start();
    }

    private function start(): void
    {
        $this->startGateWay();
        $this->startBusinessWorker();
        $this->startRegister();
        Worker::runAll();
    }

    private function startBusinessWorker(): void
    {
        $worker = new BusinessWorker();
        $worker->name = 'BusinessWorkerKiosk';
        $worker->count = 1;
        $worker->registerAddress = '127.0.0.1:' . env('WEBSOCKET_KIOSK_PUSH_PORT');
        $worker->eventHandler = KioskEvents::class;
    }

    private function startGateWay(): void
    {
        $gateway = new Gateway("websocket://0.0.0.0:" . env('WEBSOCKET_KIOSK_PORT'));
        $gateway->name = 'GatewayKiosk';
        $gateway->count = 1;
        $gateway->lanIp = '127.0.0.1';
        $gateway->startPort = 2900;
        /*$gateway->pingInterval = 30;
        $gateway->pingNotResponseLimit = 1;
        $gateway->pingData = '{"type":"ping"}';*/
        $gateway->registerAddress = '127.0.0.1:' . env('WEBSOCKET_KIOSK_PUSH_PORT');
    }

    private function startRegister(): void
    {
        new Register('text://0.0.0.0:' . env('WEBSOCKET_KIOSK_PUSH_PORT'));
    }
}

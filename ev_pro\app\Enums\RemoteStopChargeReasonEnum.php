<?php

namespace App\Enums;

use BenSampo\Enum\Enum;
use BenSampo\Enum\Contracts\LocalizedEnum;
use App\Enums\Traits\Tools;

/**
 * @method static KioskComplete
 * @method static KioskWaived
 * @method static KioskPaidComplete
 * @method static AdminOctopusCard
 * @method static CancelQueue
 * @method static CsWaived
 * @method static CsComplete
 * @method static AppUserOperate
 */
final class RemoteStopChargeReasonEnum extends Enum implements LocalizedEnum
{
    use Tools;

    const KioskComplete = 'KIOSK_COMPLETE';
    const KioskWaived = 'KIOSK_WAIVED';
    const KioskPaidComplete = 'KIOSK_PAID_COMPLETE';
    const AdminOctopusCard = 'ADMIN_OCTOPUS_CARD';
    const CancelQueue = 'CANCEL_QUEUE';
    const CsWaived = 'CS_WAIVED';
    const CsComplete = 'CS_COMPLETE';
    const AppUserOperate = 'APP_USER_OPERATE';
}

<?php

namespace App\Enums;

use BenSampo\Enum\Enum;
use BenSampo\Enum\Contracts\LocalizedEnum;
use App\Enums\Traits\Tools;

/**
 * @method static EVPOWER16
 * @method static EVProTDTDC
 * @method static EVPOWERCS
 * @method static SCHNEIDER16
 * @method static SCHNEIDERPROAC16
 * @method static STAR16
 * @method static STARDC
 * @method static WYDC
 * @method static XSMART16
 * @method static TOPAC
 * @method static ATCDC
 * @method static GREATWAY16
 */
final class ChargePointVendor extends Enum implements LocalizedEnum
{
    use Tools;

    const EVPower16 = 'EVPOWER16';
    const EVProTDTDC = 'EVPROTDTDC';
    const EVPowerCS = 'EVPOWERCS';
    const Schneider16 = 'SCHNEIDER16';
    const SchneiderProAC16 = 'SCHNEIDERPROAC16';
    const SchneiderProDC = 'SCHNEIDERPRODC';
    const Star16 = 'STAR16';
    const StarDC = 'STARDC';
    const WYDC = 'WYDC';
    const XSMART16 = 'XSMART16';
    const ATCDC = 'ATCDC';
    const GREATWAY16 = 'GREATWAY16';
}

<?php

namespace App\Http\Controllers\Admin;

use App\Enums\ChargeTariffScheme;
use App\Enums\ChargeValueType;
use App\Enums\IdentityType;
use App\Enums\LmsMode;
use App\Enums\PaymentMethod;
use App\Http\Controllers\Common\CommonController;
use App\Jobs\RequestNotifyKioskChargeRecordJob;
use App\Jobs\SendEmailJob;
use App\Jobs\SendSMSJob;
use App\Models\Modules\ChargeRecord;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Foundation\Application;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Enums\WebsocketType;
use App\Helpers\ChargeFeeCalculation;
use DateTime;
use App\Models\Modules\SimpleTariffTable;
use App\Models\Modules\TimeTariffTable;
use App\Models\Modules\PublicHoliday;
use App\Models\Modules\EnergyTariffTable;
use App\Models\Modules\PeakTariffTable;
use App\Models\Modules\PeakTimeTable;
use App\Models\Modules\IdlingPenaltyTariffTable;
use App\Models\Modules\ChargedEnergyRecord;
use App\Models\Modules\{
    MaintenanceRecord,
    ChargePoint,
    EventLog,
    Connector,
    Site,
    ChargePaymentRecord,
    ChargePaymentRecordCalculation,
    PointsTransaction,
    ChargeRecordTariffRule,
    KioskSetting,
    KioskSettingDescription
};
use App\Enums\{
    ChargePointVendor,
    ConnectorStatus,
    EventTypeEnum,
    TariffTableType,
    DayType,
    TransactionType,
};
use Illuminate\Contracts\Database\Eloquent\Builder;
use Ramsey\Collection\Collection;
use Illuminate\Support\Facades\Mail;

class TestController extends CommonController
{
    protected static string $module_name = 'test'; // 模块名称

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'send_url' => action([self::class, 'send']),
            'websocket_type_push' => WebsocketType::ChargeFeeCalculationNotification
        );

        return view("pages.{$data['module_name']}.list", $data);
    }



    /**
     * 展示积分交易列表
     *
     * @param Request $request
     * @return JsonResponse
     */
    /**
     * 展示积分交易列表
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function listPointsTransaction(Request $request): JsonResponse
    {
        // dataTable字段
        $page_size = (int)$request->input('page_size', 10);
        $current_page = (int)$request->input('current_page', 1);
        $user_id = 1;
        $date_time = $request->date_time;

        if (empty($user_id)) {
            $this->missingField('User');
            return $this->returnJson();
        }

        $points_transaction_result_list = PointsTransaction::where('user_id', $user_id)
            ->when(filled($date_time), function ($query) use ($date_time) {
                $query->whereYear('gmt_create', date('Y', strtotime($date_time)))->whereMonth('gmt_create', date('m', strtotime($date_time)));
            })
            ->latest()
            ->paginate($page_size, ['*'], 'current_page', $current_page);
        // 显示gmt_create字段
        $points_transaction_result_list->data = $points_transaction_result_list->makeVisible(['gmt_create']);
        $results = array();
        foreach ($points_transaction_result_list as $points_transaction_result) {
            $date = date('Y-m', strtotime($points_transaction_result->gmt_create));
            if (!isset($results[$date])) {
                $results[$date] = [
                    'year_month' => $date,
                    'income' => 0,
                    'expense' => 0,
                    'transactions_list' => [],
                ];
            }

            $results[$date]['transactions_list'][] = $points_transaction_result;
        }
        $data_time_year_month = array_keys($results);

        $count = count($data_time_year_month);
        $sum_total_list = PointsTransaction::selectRaw(
            'DATE_FORMAT(gmt_create, "%Y-%m") as year_months,SUM(CASE WHEN transaction_type = ? THEN amount ELSE 0 END) as income,SUM(CASE WHEN transaction_type = ? THEN amount ELSE 0 END) as expense',
            [TransactionType::Income, TransactionType::Expense]
        )
            ->groupBy('year_months')
            ->when($count > 0, function ($query) use ($count, $data_time_year_month) {
                if ($count > 1) {
                    $query->whereRaw('DATE_FORMAT(gmt_create, "%Y-%m") <= "' . $data_time_year_month[0] . '"')->whereRaw('DATE_FORMAT(gmt_create, "%Y-%m") >= "' . $data_time_year_month[$count - 1] . '"');
                } else {
                    $query->whereRaw('DATE_FORMAT(gmt_create, "%Y-%m") = "' . $data_time_year_month[0] . '"');
                }
            })
            ->get();

        foreach ($sum_total_list as $sum_total) {
            $time = $sum_total->year_months;
            if (isset($results[$time])) {
                $results[$time]['income'] = (int)$sum_total->income ?? 0;
                $results[$time]['expense'] = (int)$sum_total->expense ?? 0;
            }
        }


        $this->data = array(
            'current_page' => $points_transaction_result_list->currentPage(),
            'total' => $points_transaction_result_list->total(),
            'page_total' => $points_transaction_result_list->lastPage(),
            "data" => array_values($results),
        );
        return $this->returnJson();
    }

    /**
     * 跳转已充电量页面
     *
     * @param Request $request
     * @return View|Application|Factory
     */
    public function muiChargeEnergyRecord(Request $request): View|Application|Factory
    {
        $language_code = $request->input('language_code', app()->getLocale()); // 语言编码

        self::setLanguage($language_code);

        $data = array(
            'module_name' => self::$module_name,
        );

        return view("pages.{$data['module_name']}.muiChargeEnergyRecord", $data);
    }

    /**
     * 获取已充电量
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getChargedEnergyRecordList(Request $request): JsonResponse
    {
        $this->data = ChargedEnergyRecord::where('charge_record_id', $request->id)->get()->makeVisible(['gmt_create']);

        return $this->returnJson();
    }

    /**
     * 跳转充电记录收费方案页面
     *
     * @param Request $request
     * @return View|Application|Factory
     */
    public function muiChargeRecordTariffScheme(Request $request): View|Application|Factory
    {
        $language_code = $request->input('language_code', app()->getLocale()); // 语言编码

        self::setLanguage($language_code);

        $data = array(
            'module_name' => self::$module_name,
        );

        return view("pages.{$data['module_name']}.muiChargeRecordTariffScheme", $data);
    }

    /**
     * 获取收费表规则
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getChargeRecordTariffRule(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        $charge_record_id = $request->id;
        $language_code = $request->input('language_code', app()->getLocale()); // 语言编码
        self::setLanguage($language_code);

        if (
            empty($charge_record_id) ||
            !filled($charge_record_model = ChargeRecord::find($charge_record_id)) || !filled($charge_record_model->tariffTableRule)
        ) {
            $this->notFoundData('Charge Record');
            return $this->returnJson();
        }

        // 收费表信息
        $tariff_scheme_list = array();
        if (filled($charge_record_model->tariffTableRule->simple_tariff_table_json)) {
            $tariff_scheme_list['simple_tariff_table_json'] = array(
                'title' => __("$module_name.simple_tariff_table_list"),
                'data' => $charge_record_model->tariffTableRule->simple_tariff_table_json,
            );
        }
        if (filled($charge_record_model->tariffTableRule->time_tariff_table_item_json)) {
            $tariff_scheme_list['time_tariff_table_item_json'] = array(
                'title' => __("$module_name.time_tariff_table_item_list"),
                'data' => $charge_record_model->tariffTableRule->time_tariff_table_item_json,
            );
        }
        if (filled($charge_record_model->tariffTableRule->energy_tariff_table_item_json)) {
            $tariff_scheme_list['energy_tariff_table_item_json'] = array(
                'title' => __("$module_name.energy_tariff_table_item_list"),
                'data' => $charge_record_model->tariffTableRule->energy_tariff_table_item_json,
            );
        }
        if (filled($charge_record_model->tariffTableRule->idling_penalty_tariff_table_item_json)) {
            $tariff_scheme_list['idling_penalty_tariff_table_item_json'] = array(
                'title' => __("$module_name.idling_penalty_tariff_table_item_list"),
                'data' => $charge_record_model->tariffTableRule->idling_penalty_tariff_table_item_json,
            );
        }

        $this->data = array(
            'tariff_scheme_list' => $tariff_scheme_list,
            'is_show_concessionary_rate' => LmsMode::EvenDistribution == $charge_record_model->lms_mode,
        );

        return $this->returnJson();
    }

    /**
     * 跳转充电记录收费方案页面
     *
     * @param Request $request
     * @return View|Application|Factory
     */
    public function muiChargePaymentRecordCalculation(Request $request): View|Application|Factory
    {
        $language_code = $request->input('language_code', app()->getLocale()); // 语言编码

        self::setLanguage($language_code);

        $data = array(
            'module_name' => self::$module_name,
        );

        return view("pages.{$data['module_name']}.muiChargePaymentRecordCalculation", $data);
    }

    /**
     * 跳转充电记录收费方案页面
     *
     * @param Request $request
     * @return View|Application|Factory
     */
    public function muiChargePaymentRecordCalculation2(Request $request): View|Application|Factory
    {
        $language_code = $request->input('language_code', app()->getLocale()); // 语言编码

        self::setLanguage($language_code);

        $data = array(
            'module_name' => self::$module_name,
        );

        return view("pages.{$data['module_name']}.muiChargePaymentRecordCalculation2", $data);
    }

    /**
     * 获取收费表规则
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getChargePaymentRecordCalculation(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        $charge_payment_record_id = $request->id;
        $language_code = $request->input('language_code', app()->getLocale()); // 语言编码
        self::setLanguage($language_code);

        if (
            empty($charge_payment_record_id) ||
            !filled($charge_payment_record_calculation_model = ChargePaymentRecordCalculation::where('charge_payment_record_id', $charge_payment_record_id)->first())
        ) {
            $this->notFoundData('Charge Payment Record Calculation');
            return $this->returnJson();
        }

        $charge_payment_record = ChargePaymentRecord::find($charge_payment_record_id);
        $tariff_table_type = $charge_payment_record->chargeRecord->tariff_table_type ?? '';
        $charge_value_type = $charge_payment_record->chargeRecord->charge_value_type ?? '';

        $charge_payment_record_calculation_model->charge_value_amount_calculation_json = is_string($charge_payment_record_calculation_model->charge_value_amount_calculation_json) ? json_decode($charge_payment_record_calculation_model->charge_value_amount_calculation_json, true) : $charge_payment_record_calculation_model->charge_value_amount_calculation_json;
        $charge_payment_record_calculation_model->idling_penalty_amount_calculation_json = is_string($charge_payment_record_calculation_model->idling_penalty_amount_calculation_json) ? json_decode($charge_payment_record_calculation_model->idling_penalty_amount_calculation_json, true) : $charge_payment_record_calculation_model->idling_penalty_amount_calculation_json;

        $this->data = array(
            'charge_value_type' => $charge_value_type,
            'tariff_table_type' => $tariff_table_type,
            'charge_value_amount_calculation_json' => $charge_payment_record_calculation_model->charge_value_amount_calculation_json, // 充电量计算json
            'idling_penalty_amount_calculation_json' => $charge_payment_record_calculation_model->idling_penalty_amount_calculation_json, // 闲时罚款计算json
        );

        return $this->returnJson();
    }

    /**
     * 跳转充电枪绑定收费方案页面
     *
     * @param Request $request
     * @return View|Application|Factory
     */
    public function muiConnectorTariffScheme(Request $request): View|Application|Factory
    {
        $language_code = $request->input('language_code', app()->getLocale()); // 语言编码

        self::setLanguage($language_code);

        $data = array(
            'module_name' => self::$module_name,
        );

        return view("pages.{$data['module_name']}.muiConnectorTariffScheme", $data);
    }

    /**
     * 获取收费表规则
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getConnectorTariffScheme(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        $connector_id = $request->id;
        $language_code = $request->input('language_code', app()->getLocale()); // 语言编码
        self::setLanguage($language_code);

        if (
            empty($connector_id) ||
            !filled($connector = Connector::with('setting')->find($connector_id))
        ) {
            $this->notFoundData('Connector');
            return $this->returnJson();
        }

        // 收费表信息
        $tariff_scheme_list = array();
        switch ($connector->setting->tariff_table_type) {
            case TariffTableType::SimpleTariffTable:
                $simple_tariff_table = $connector?->setting?->simpleTariffTable;
                $idling_penalty_tariff_table = $connector?->setting?->idlingPenaltyTariffTable?->item()->oldest('idling_penalty_tariff_table_item.start_range')->get();
                // 简单收费表
                if (filled($simple_tariff_table)) {
                    $tariff_scheme_list['simple_tariff_table_json'] = array(
                        'title' => __("$module_name.simple_tariff_table_list"),
                        'data' => $simple_tariff_table,
                    );
                }
                // 闲时罚款表
                if (filled($idling_penalty_tariff_table)) {
                    $tariff_scheme_list['idling_penalty_tariff_table_item_json'] = array(
                        'title' => __("$module_name.idling_penalty_tariff_table_item_list"),
                        'data' => $idling_penalty_tariff_table,
                    );
                }
                break;
            case TariffTableType::ComplexTimeTariffTable:
                $time_tariff_table_item_json = $connector?->setting?->timeTariffTable?->item()
                    ->orderByRaw('CASE WHEN time_tariff_table_item.day_type = "' . DayType::Monday . '" THEN 1 WHEN time_tariff_table_item.day_type = "' . DayType::Tuesday . '" THEN 2 WHEN time_tariff_table_item.day_type = "' . DayType::Wednesday . '" THEN 3 WHEN time_tariff_table_item.day_type = "' . DayType::Thursday . '" THEN 4 WHEN time_tariff_table_item.day_type = "' . DayType::Friday . '" THEN 5 WHEN time_tariff_table_item.day_type = "' . DayType::Saturday . '" THEN 6 WHEN time_tariff_table_item.day_type = "' . DayType::Sunday . '" THEN 7 WHEN time_tariff_table_item.day_type = "' . DayType::PublicHoliday . '" THEN 8 END')
                    ->oldest('time_tariff_table_item.start_time')
                    ->get();
                $idling_penalty_tariff_table = $connector?->setting?->idlingPenaltyTariffTable?->item()->oldest('idling_penalty_tariff_table_item.start_range')->get();
                // 复杂时间收费表
                if (filled($time_tariff_table_item_json)) {
                    $tariff_scheme_list['time_tariff_table_item_json'] = array(
                        'title' => __("$module_name.time_tariff_table_item_list"),
                        'data' => $time_tariff_table_item_json,
                    );
                }
                // 闲时罚款表
                if (filled($idling_penalty_tariff_table)) {
                    $tariff_scheme_list['idling_penalty_tariff_table_item_json'] = array(
                        'title' => __("$module_name.idling_penalty_tariff_table_item_list"),
                        'data' => $idling_penalty_tariff_table,
                    );
                }
                break;
            case TariffTableType::ComplexEnergyTariffTable:
                $energy_tariff_table = $connector?->setting?->energyTariffTable?->item()->oldest('energy_tariff_table_item.start_range')->get();
                $idling_penalty_tariff_table = $connector?->setting?->idlingPenaltyTariffTable?->item()->oldest('idling_penalty_tariff_table_item.start_range')->get();
                $peak_time_table = $connector?->setting?->peakTimeTable;
                // 复杂电量收费表
                if (filled($energy_tariff_table)) {
                    $tariff_scheme_list['energy_tariff_table_item_json'] = array(
                        'title' => __("$module_name.energy_tariff_table_item_list"),
                        'data' => $energy_tariff_table,
                    );
                }
                // 闲时罚款表
                if (filled($idling_penalty_tariff_table)) {
                    $tariff_scheme_list['idling_penalty_tariff_table_item_json'] = array(
                        'title' => __("$module_name.idling_penalty_tariff_table_item_list"),
                        'data' => $idling_penalty_tariff_table,
                    );
                }
                break;
            default:
                break;
        }

        $site = Site::find($connector->chargePoint->site_id);
        $lms_mode = $site ? $site->lms_mode : '';

        $this->data = array(
            'tariff_scheme_list' => $tariff_scheme_list,
            'is_show_concessionary_rate' => LmsMode::EvenDistribution == $lms_mode,
        );

        return $this->returnJson();
    }

    public function send(Request $request): JsonResponse
    {
        $content = $request->post('content');
        $client_id = $request->post('client_id');

        self::websocketPush(WebsocketType::ChargeFeeCalculationNotification, $content, 1, $client_id);

        return $this->returnJson();
    }

    public function test(Request $request): JsonResponse
    {
        /* $connector_id = $request->input('charge_record_id');

        if (!empty($charge_record_id) && filled($charge_record_model = ChargeRecord::find($charge_record_id))) {
            $this->requestNotifyKioskChargeRecord($charge_record_model);

            $this->sendMailAndSMS($charge_record_model);
        }

        return $this->returnJson(); */
        // 发送测试邮件
        /* $send_content = 'test sms message…';
        $send_title = 'EVPro Charge';
        $send_email = '<EMAIL>';
        Mail::raw($send_content, function ($message) use ($send_email, $send_title) {
            $message->from('<EMAIL>', 'EVPro')
                ->to($send_email)
                ->subject($send_title);
        }); */
        $kiosk_setting_description = KioskSettingDescription::all();
        $this->data = $kiosk_setting_description;
        return $this->returnJson();
    }

    /**
     * 请求通知kiosk充电记录
     *
     * @param ChargeRecord $charge_record
     * @return void
     */
    protected function requestNotifyKioskChargeRecord(ChargeRecord $charge_record): void
    {
        // 通知Kiosk充电记录
        if (isset($charge_record->connector->chargePoint->comServer->api_url) && filled($charge_record->connector->chargePoint->comServer->api_url)) {
            RequestNotifyKioskChargeRecordJob::dispatch($charge_record);
        }
    }

    /**
     * 发送邮件及短信
     *
     * @param ChargeRecord $charge_record
     * @return void
     */
    protected function sendMailAndSMS(ChargeRecord $charge_record): void
    {
        // 存在邮箱发送邮件
        if (!empty($charge_record->reminder_email)) {
            $send_content = $this->formatChargeRecordReminderSendMessage($charge_record, '<br>');
            $send_title = 'EVPro Charge';

            // 将发送邮件任务分发给队列
            SendEmailJob::dispatch($charge_record->reminder_email, $send_title, $send_content, $charge_record);
        }

        // 存在电话发送短信
        if (!empty($charge_record->reminder_telephone)) {
            // 发送短信，如果发送失败保存log
            $send_content = 'test sms message…'/*$this->formatChargeRecordReminderSendMessage($charge_record, PHP_EOL)*/;

            // 将发送短信任务分发给队列
            SendSMSJob::dispatch($charge_record->reminder_telephone, $send_content, $charge_record);
        }
    }

    // 格式化信息发送内容
    protected function formatChargeRecordReminderSendMessage(ChargeRecord $charge_record, $CRLF): string
    {
        // 充电记录
        // 解锁码
        $identity_number = '';
        // 隊列位置
        $queue_position = '';

        // 支付
        // 收據號碼 = 扣除金額 = 要求充电数量 = 充电完成时间 = 付款方式 = 付款信息
        $receipt_number = $amount_deducted = $charging_value_requested = $charging_completed_time = $payment_method = $payment_info = '';

        // 如果身份类型为解锁码且解锁码不为空
        if ($charge_record->identity_type == IdentityType::UnplugCode && filled($charge_record->identity_number)) {
            $identity_number = $CRLF . 'Unlock Code 解鎖密碼: ' . ($charge_record->identity_number ?? '') .
                $CRLF . '請記住解鎖密碼，用於充電完成後拔除充需電纜' .
                $CRLF . 'Please keep the unlock code for unplug charging cable after charging completed';
        }

        // 如果隊列位置不為空則顯示隊列信息
        if (filled($connector = $charge_record->connector) && filled($connector->queue_position)) {
            $queue_position = $CRLF . 'Queue Position 隊列位置: ' . ($connector->queue_position ?? '');
        }

        if (filled($charge_payment_record = $charge_record->chargePaymentRecord()->first())) {
            // 收據號碼
            $receipt_number = $CRLF . 'Receipt no. 收據號碼: ' . $charge_payment_record->receipt_number;

            // 扣除金額
            $amount_deducted = $CRLF . 'Amount deducted 扣除金額: HK$' . (($charge_payment_record->charge_value_amount ?? 0) / 100);

            // 是否有闲置罚款
            if ($charge_payment_record->idling_penalty_amount > 0) {
                $amount_deducted = $CRLF . 'Idling penalty 閒置罰款: HK$' . ($charge_payment_record->idling_penalty_amount / 100) .
                    $CRLF . 'Total Amount 合共金額: HK$' . ((($charge_payment_record->idling_penalty_amount ?? 0) / 100) + (($charge_payment_record->charge_value_amount ?? 0) / 100));
            }

            // 付款方式
            $payment_method = $CRLF . 'Payment Method 付款方式: ' . (PaymentMethod::getDescription($charge_payment_record->payment_method) ?? '');

            // 判断付款方式
            switch ($charge_payment_record->payment_method) {
                case PaymentMethod::Octopus:
                    $payment_info = $CRLF . 'Octopus no. 八達通卡號: ' . ($charge_payment_record->octopus_card_number ?? '') .
                        $CRLF . 'Device no. 八達通機號: ' . ($charge_payment_record->octopus_device_number ?? '');
                    if ($charge_payment_record->octopus_card_type != '1') {
                        $payment_info .= $CRLF . 'Remaining Value 餘額: HK$' . number_format(($charge_payment_record->octopus_balance ?? 0) / 100, 2);
                    }
                    break;
                case PaymentMethod::Alipay:
                    $payment_info = $CRLF . 'AliPay Acc. 支付寶帳號: ' . ($charge_payment_record->pos_card_number ?? '') .
                        $CRLF . 'Trans. ID 交易編號: ' . ($charge_payment_record->pos_transaction_id ?? '') .
                        $CRLF . 'Ref no. 參考編號: ' . ($charge_payment_record->pos_reference_id ?? '');
                    break;
                case PaymentMethod::MasterCard:
                case PaymentMethod::Visa:
                    $payment_info = $CRLF . 'Credit Card no. 信用卡號: ' . ($charge_payment_record->pos_card_number ?? '') .
                        $CRLF . 'Trace no. 追蹤編號: ' . ($charge_payment_record->pos_trace_no ?? '') .
                        $CRLF . 'Trans. ID 交易編號: ' . ($charge_payment_record->pos_transaction_id ?? '') .
                        $CRLF . 'Ref no. 參考編號: ' . ($charge_payment_record->pos_reference_id ?? '');
                    break;
                default:
                    break;
            }
        }

        // 收费方案
        switch ($charge_record->charge_tariff_scheme) {
            case ChargeTariffScheme::PrePaid:
                // 判断充电类型
                switch ($charge_record->charge_value_type) {
                    case ChargeValueType::Time:
                        $charging_value_requested = $CRLF . 'Charging time requested 要求充電時間: ' . (round(($charge_record->pre_paid_charge_value ?? 0) / 60)) . '分';
                        $charging_completed_time = $CRLF . 'Charging will be completed by 充電完成時間: ' . date('Y/m/d H:i', strtotime(($charge_record->gmt_power_on ?? $charge_record->gmt_start)) + $charge_record->pre_paid_charge_value);
                        break;
                    case ChargeValueType::Energy:
                        $charging_value_requested = $CRLF . 'Charging energy requested 要求充電電量: ' . (round(($charge_record->pre_paid_charge_value ?? 0) / 1000, 3)) . '千瓦時';
                        break;
                }
                break;
            case ChargeTariffScheme::PostPaid:
                // 判断充电类型
                switch ($charge_record->charge_value_type) {
                    case ChargeValueType::Time:
                        $charging_value_requested = $CRLF . 'Charged time 充電時間: ' . (round(($charge_record->charged_time ?? 0) / 60)) . '分';
                        break;
                    case ChargeValueType::Energy:
                        $charging_value_requested = $CRLF . 'Charged energy 充電電量: ' . (round(($charge_record->charged_energy ?? 0) / 1000, 3)) . '千瓦時';
                        break;
                }
        }

        return 'Date/Time 日期/時間: ' . date('Y/m/d H:i', strtotime($charge_record->gmt_power_on ?? $charge_record->gmt_start)) .
            $CRLF . 'EV Charging Point 電動車充電泊位: ' . ($charge_record->charge_point_name ?? '') .
            $charging_value_requested .
            $charging_completed_time .
            $queue_position .
            $receipt_number .
            $amount_deducted .
            $payment_method .
            $payment_info .
            $identity_number;
    }

    public function simpleTimeTariff()
    {
        $start_date_time = new DateTime('2020-12-30 00:00:00');
        $end_date_time = new DateTime('2021-01-01 08:00:00');

        $simple_tariff_table = SimpleTariffTable::find(23);
        dd(ChargeFeeCalculation::calculateSimpleTimeTariff($start_date_time, $end_date_time, $simple_tariff_table->toArray()));
    }

    public function simpleEnergyTariff()
    {
        $energy = 100 * 1000;
        $simple_tariff_table = SimpleTariffTable::find(24);

        dd(ChargeFeeCalculation::calculateSimpleEnergyTariff($energy, $simple_tariff_table->toArray()));
    }

    public function timeTariff()
    {
        $start_date_time = new DateTime('2022-12-31 12:00:00');
        $end_date_time = new DateTime('2023-01-04 7:00:00');

        $time_tariff_table = TimeTariffTable::find(43);
        $public_holiday_list = PublicHoliday::all()->toArray();
        $lms_mode = LmsMode::NoLms;

        dd(ChargeFeeCalculation::calculateTimeTariff($start_date_time, $end_date_time, $time_tariff_table->charge_value_interval, $time_tariff_table->item()->orderBy('day_type')->orderBy('start_time')->get()->toArray(), $public_holiday_list, $lms_mode));
    }

    public function energyTariff()
    {
        $energy_tariff_table = EnergyTariffTable::find(1);
        $peak_tariff_table = PeakTimeTable::find(1);
        $energy_tariff_table_item = $energy_tariff_table->item()->orderBy('start_range')->get()->toArray();
        $peak_tariff_table_item = $peak_tariff_table->item()->orderBy('start_time')->get()->toArray();
        $public_holiday_list = PublicHoliday::all()->toArray();
        $lms_mode = LmsMode::NoLms;
        $charged_energy_record_list = [
            [
                'charge_record_charged_energy' => 0,
                'this_time_record_energy' => 15000,
                'gmt_create' => '2023-03-09 14:55:07'
            ]
        ];

        dd(ChargeFeeCalculation::calculateEnergyTariff($charged_energy_record_list, $energy_tariff_table_item, $peak_tariff_table_item, $public_holiday_list, $lms_mode));
    }

    public function idlingPenalty()
    {
        $total = 2300;
        $total = bcdiv($total, 100, 2);
        $total = number_format($total, 2, '.', '');
        dd($total);
    }

    /**
     * 處理維修記錄關聯的Charge Point下的Connector，設置他們的is_bypass_mode
     * @param $maintenance_record_list
     * @param $charge_point_list
     * @return mixed
     */
    public function setIsBypassModeWithMaintenanceRecord($maintenance_record_list, $charge_point_list)
    {
        // 從維修記錄的site_id_list(,隔開)中提取所有的Site Id
        $site_id_list = [];
        foreach ($maintenance_record_list as $maintenance_record) {
            $site_id_list = array_merge($site_id_list, explode(',', $maintenance_record->site_id_list));
        }
        // 如果沒有Site Id，則不處理
        if (blank($site_id_list)) return $charge_point_list;
        // 篩選出Site下的Charge Point
        $maintenance_charge_point_list = $charge_point_list->whereIn('site_id', $site_id_list);

        // 獲取要處理的api_url_list
        $api_url_list = $this->getApiUrlList($maintenance_charge_point_list, 0);
        // Curl設置is_bypass_mode
        if (!empty($api_url_list)) $this->curlSetConnectorIsBypassMode($api_url_list, 1);
        // 去除charge_point_list中的maintenance_charge_point_list，返回沒有維修記錄的Charge Point
        return $charge_point_list->diff($maintenance_charge_point_list);
    }

    /**
     * 處理無維修記錄但是is_bypass_mode=1的數據
     *
     * @param [type] $charge_point_list
     * @return void
     * @Description
     * @example
     * @date 2023-09-13
     */
    public function setIsBypassModeWithOutMaintenanceRecord($charge_point_list)
    {
        // 獲取要處理的api_url_list
        $api_url_list = $this->getApiUrlList($charge_point_list, 1);
        // Curl設置is_bypass_mode
        if (!empty($api_url_list)) $this->curlSetConnectorIsBypassMode($api_url_list, 0);
    }

    /**
     * 獲取要處理的api_url_list
     *
     * @param [type] $charge_point_list
     * @param integer $is_bypass_mode
     * @return array
     * @Description
     * @example
     * @date 2023-09-13
     */
    public function getApiUrlList($charge_point_list, int $is_bypass_mode): array
    {
        // 查詢這些Charge Point下的Connector是否存在狀態非離線且is_bypass_mode對應的數據。如果有，獲取Charge Point的Charge Point Cs的api_url
        $api_url_list = [];
        foreach ($charge_point_list as $charge_point) {
            $maintenance_connector_count = $charge_point->connector()
                ->where('status', '!=', ConnectorStatus::Offline)
                ->where('is_bypass_mode', $is_bypass_mode)
                ->count();

            if (($maintenance_connector_count > 0) && filled($charge_point->chargePointCs->api_url) && filled($charge_point->chargePointCs->api_token)) {
                // 拿到Connector所屬的Charge Point的Charge Point Cs的api_url
                $api_url_list[$charge_point->chargePointCs->charge_point_cs_id] = [
                    'api_url' => $charge_point->chargePointCs->api_url,
                    'api_token' => $charge_point->chargePointCs->api_token,
                    'name' => $charge_point->chargePointCs->name,
                    'number' => $charge_point->chargePointCs->charge_point_cs_number,
                ];
            }
        }

        // 重置鍵名
        return array_values($api_url_list);
    }

    public function curlSetConnectorIsBypassMode($api_url_list, int $is_bypass_mode)
    {
        foreach ($api_url_list as $api_url) {
            try {
                $params = array(
                    'key' => $api_url['api_token'],
                    'mode' => $is_bypass_mode === 1 ? 'on' : 'off',
                );

                // 获取返回信息
                $response = CommonController::curlPostJson(
                    $api_url['api_url'] . '/command/setbypassmode',
                    json_encode($params),
                    array('Content-Type: application/json; charset=utf-8'),
                );

                $format_result = json_decode(htmlspecialchars_decode($response), true);

                // 失败时保存EventLog
                if (is_null($format_result) || (isset($format_result['status']) && $format_result['status'] != 200) || (isset($format_result['response']) && strtolower($format_result['response']) != 'success')) {
                    $event_log_model = new EventLog;
                    $event_log_model->target = 'CMS';
                    $event_log_model->target_number = $api_url['number'];
                    $event_log_model->target_name = $api_url['name'];
                    $event_log_model->event_type = EventTypeEnum::PostChargePointCSBypassFailure;
                    $event_log_model->description = 'CMS Change Bypass Mode Error: ' . $response;

                    CommonController::saveEventLogOrRules($event_log_model, true);
                }
            } catch (\Exception $e) {
                // 失败时保存EventLog
                $event_log_model = new EventLog;
                $event_log_model->target = 'CMS';
                $event_log_model->target_number = $api_url['number'];
                $event_log_model->target_name = $api_url['name'];
                $event_log_model->event_type = EventTypeEnum::PostChargePointCSBypassFailure;
                $event_log_model->description = 'CMS Change Bypass Mode Error: ' . $e->getMessage();

                CommonController::saveEventLogOrRules($event_log_model, true);
            }
        }
    }

    public function iocOperate()
    {
        return view("pages.test.iocOperate");
    }

    public function testPush()
    {
        $time = date('Y-m-d H:i:s');
        $title = [
            'en' => 'test' . $time,
            'zh-Hans' => '测试' . $time,
            'zh-Hant' => '測試' . $time,
        ];
        $content = [
            'en' => 'test content' . $time,
            'zh-Hans' => '测试内容' . $time,
            'zh-Hant' => '測試內容' . $time,
        ];

        $data = [
            'news_id' => 10086
        ];

        $push_time = '2024-12-19 18:00:00';

        /* $result = self::push(
            title: $title,
            content: $content,
            include_external_user_ids: ['5'],
            data: $data,
            push_time: $push_time,
        );
        dump($result); */
    }
}

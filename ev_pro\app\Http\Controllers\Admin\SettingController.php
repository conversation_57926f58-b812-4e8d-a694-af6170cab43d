<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Application;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Admin\Traits\{
    Add,
    Edit
};
use App\Models\Modules\{
    Setting,
};


class SettingController extends CommonController
{
    use Add, Edit;

    protected static string $module_name = 'setting'; // 模块名称

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new Setting;
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'add_url' => action([self::class, 'add']),
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'key_search' => $request->get('key_search'),
            'value_search' => $request->get('value_search'),
        );

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'key');
        $sort = $request->input('gmt_modified', 'desc');
        $length = (int)$request->input('length', 10);
        $key_search = $request->input('key_search');
        $value_search = $request->input('value_search');

        $where = array();

        if (filled($key_search)) {
            $where[] = ['key', 'like', "%$key_search%"];
        }

        if (filled($value_search)) {
            $where[] = ['value', 'like', "%$value_search%"];
        }

        $data_list = Setting::where($where)
            ->orderBy($order, $sort)
            ->latest('gmt_modified')
            ->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $result[] = array(
                'setting_id' => $data->setting_id, // 设置ID
                'key' => $data->key, // 键
                'value' => $data->value, // 值
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_create->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result
        );
        return response()->json($json);
    }

    protected function getForm(Request $request, array $data): View|Application|Factory
    {
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'], self::getUrlParams($request)
        );

        $data['model']->key = $request->old('key', $data['model']->key); // 键
        $data['model']->value = $request->old('value', $data['model']->value); // 值

        return view("pages.{$data['module_name']}.form", $data);
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @param Setting $model
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    protected function modelValidateAndSave(Request $request, Setting $model): RedirectResponse
    {
        $request->validate(self::rules($request, $model), [], self::attributes());
        $model->key = $request->input('key');
        $model->value = $request->input('value');
        $model->save();

        self::sendInitPushByKioskNumberList();

        return redirect()->action(
            [self::class, 'showPage'], self::getUrlParams($request)
        );
    }

    public function delete(Request $request): JsonResponse
    {
        $id = $request->post('id');

        if (!empty($id)) {
            $model = $this->model::find($id);
            if (filled($model)) {
                $model->delete();

                self::sendInitPushByKioskNumberList();
            } else {
                $this->notFoundData('ID');
            }
        } else {
            $this->notFoundData('ID');
        }

        return $this->returnJson();
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function rules(?Request $request, ?Model $model): array
    {
        $rules = array(
            'key' => 'required|max:45',
            'value' => 'required|max:1000',
        );

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'key' => __("$module_name.key"),
            'value' => __("$module_name.value"),
        ];
    }

    /**
     * 返回地址栏参数
     *
     * @param Request|null $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => 'true',
            'key_search' => $request->get('key_search'),
            'value_search' => $request->get('value_search'),
        );
    }

}

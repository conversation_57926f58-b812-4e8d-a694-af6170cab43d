<?php

namespace App\Http\Controllers\Admin;

use App\Enums\ChargeTariffScheme;
use App\Http\Controllers\Common\CommonController;
use App\Models\Modules\ChargeRecord;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Foundation\Application;
use Illuminate\Http\{
    RedirectResponse,
    Request,
};

class AdminSMSNotificationController extends CommonController
{
    protected static string $module_name = 'adminSMSNotification'; // 模块名称
    protected static string $default_area_prefix = '+852 '; // 默认电话区号前缀

    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    public function showPage(?Request $request): View|Application|Factory|RedirectResponse
    {
        if ($request->isMethod('post')) {
            return $this->modelValidateAndSave($request);
        }

        $notification_list = array();
        // 通知列表数据
        $notification_init_list =
            $request->old('notification', getArrayFromJsonFile('admin_sms_notification', false) ?: array());
        foreach ($notification_init_list as $index => $notification) {
            if (!isset($notification['telephone']) || blank($notification['telephone'])) continue;
            // 将默认区号去除
            // 将第一个空格及前面的字符全部去除
            $telephone = preg_replace('/^.*?\s/', '', $notification['telephone']);
            $notification_list[] = array(
                'key' => $index,
                'telephone' => $telephone,
            );
        }

        $data = array(
            'module_name' => self::$module_name,
            'notification_list' => $notification_list,
            'default_area_prefix' => self::$default_area_prefix,
        );

        return view("pages.{$data['module_name']}.view", $data);
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @throws GuzzleException
     * <AUTHOR>
     * @date 2022-05-14
     * @example
     */
    protected function modelValidateAndSave(Request $request): RedirectResponse
    {
        $request->validate(self::rules($request), [], self::attributes());
        $notification_list = $request->input('notification', array());

        $notification_list = collect($notification_list)->map(function ($item) {
            // 将号码拼接默认区号
            $item['telephone'] = self::$default_area_prefix . $item['telephone'];
            unset($item['key']);
            return $item;
        })->values()->toArray();

        // 保存数据至json文件
        saveArrayToJSONFile($notification_list, 'admin_sms_notification');
        // 设置缓存数据，刷新后自动清除
        session()->flash('is_notification_saved');

        return redirect()->action([self::class, 'showPage']);
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @return array
     */
    protected static function rules(?Request $request): array
    {
        $rules = array();

        $rules['notification.*.telephone'] = 'required|digits_between:1,11|numeric|distinct';

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'notification.*.telephone' => __("$module_name.telephone"),
        ];
    }
}

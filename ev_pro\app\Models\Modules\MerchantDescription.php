<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;
use App\Models\Traits\Emoji;

class MerchantDescription extends Model
{
    use emoji;

    protected $table = 'merchant_description'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'merchant_description_id'; //定义主键，默认为id
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    // protected $fillable = [];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * 获取商户
     */
    public function merchant()
    {
        return $this->hasOne(Merchant::class, 'merchant_number', 'merchant_number');
    }

}

<?php

namespace App\Models\Modules;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Model;
use App\Http\Controllers\App\TariffTableController;

class ChargePaymentRecord extends Model
{

    protected $table = 'charge_payment_record';
    protected $primaryKey = 'charge_payment_record_id';

    const CREATED_AT = 'gmt_create'; //默认是小写的模型类名复数格式，此处为自定义表名
    const UPDATED_AT = 'gmt_modified'; //定义主键，默认为id

    protected $appends = ['fee_calculation_url'];

    // protected $fillable = [];
    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 类型转化器
     */
    protected $casts = [
        'is_top_up' => 'bool',
        'is_admin_octopus_card' => 'bool',
        'is_free_octopus_card' => 'bool',
        'is_idling_penalty_only' => 'bool',
        'is_charge_arrears' => 'bool',
    ];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [
        'is_top_up' => false, // 是否续充
        'is_admin_octopus_card' => false, // 是否为管理员八达通
        'is_free_octopus_card' => false, // 是否为免费八达通卡
        'charge_value' => 0, // 充电量
        'charge_value_amount' => 0, // 充电量金额
        'use_remain_charge_value' => 0, // 使用剩余充电量
        'idling_penalty_time' => 0, // 闲置罚款时间
        'idling_penalty_amount' => 0, // 闲置罚款金额
        'total_amount' => 0, // 合共金额
    ];

    /**
     * 全部字段
     *
     * @var array
     */
    public array $columns = [
        'charge_payment_record_id',
        'charge_payment_record_number',
        'merchant_number',
        'site_number',
        'charge_record_number',
        'payment_device',
        'payment_method',
        'payment_status',
        'gmt_payment_status',
        'charge_tariff_scheme',
        'charge_value_type',
        'is_top_up',
        'is_idling_penalty_only',
        'is_charge_arrears',
        'charge_value',
        'charge_value_amount',
        'idling_penalty_time',
        'idling_penalty_amount',
        'gmt_idling_penalty_start',
        'gmt_idling_penalty_stop',
        'total_amount',
        'actual_payment_amount',
        'user_id',
        'use_points',
        'points_transaction_id',
        'merchant_handling_fee',
        'merchant_receivable',
        'use_remain_charge_value',
        'gmt_receipt_start',
        'gmt_receipt_stop',
        'kiosk_number',
        'kiosk_name',
        'octopus_transaction_id',
        'octopus_receipt_number',
        'gmt_octopus_deduct',
        'octopus_device_number',
        'octopus_card_type',
        'octopus_card_number',
        'octopus_raw_card_number',
        'octopus_balance',
        'is_admin_octopus_card',
        'is_free_octopus_card',
        'octopus_last_added_value_type',
        'octopus_last_added_value_date',
        'octopus_response_json',
        'pos_vendor',
        'pos_receipt_number',
        'gmt_pos_deduct',
        'pos_transaction_id',
        'pos_payment_method_name',
        'pos_card_number',
        'pos_trace_no',
        'pos_reference_id',
        'pos_response_json',
        'charge_pre_authorization_record_number',
        'gmt_sync_to_cloud',
        'gmt_create',
        'gmt_modified',
    ];

    public function scopeExclude($query, $exclude = [])
    {
        return $query->select(array_diff($this->columns, (array)$exclude));
    }


    public function getFeeCalculationUrlAttribute()
    {
        return action(
            [
                TariffTableController::class, 'chargePaymentRecordTariffTable'
            ],
            [
                'charge_payment_record_id' => $this->charge_payment_record_id,
                'language_code' => app()->getLocale(),
            ]
        );
    }

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * 为 array / JSON 序列化准备日期格式
     *
     * @param DateTimeInterface $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    /**
     * 一对一关联充电纪录
     */
    public function chargeRecord()
    {
        return $this->hasOne(ChargeRecord::class, 'charge_record_number', 'charge_record_number');
    }

    /**
     * 一对一关联充电支付纪录计算
     */
    public function chargePaymentRecordCalculation()
    {
        return $this->hasOne(ChargePaymentRecordCalculation::class, 'charge_payment_record_number', 'charge_payment_record_number');
    }

    /**
     * 一对一关联 充电预授权记录
     */
    public function chargePreAuthorizationRecord()
    {
        return $this->hasOne(ChargePreAuthorizationRecord::class, 'charge_pre_authorization_record_number', 'charge_pre_authorization_record_number');
    }

    /**
     * 一对一关联用户表
     */
    public function user()
    {
        return $this->hasOne(AppUser::class, 'user_id', 'user_id');
    }

    /**
     * 一对一关联kiosk
     */
    public function kiosk()
    {
        return $this->hasOne(Kiosk::class, 'kiosk_number', 'kiosk_number');
    }

    /**
     * 一对一关联points_transaction
     */
    public function pointsTransaction()
    {
        return $this->hasOne(PointsTransaction::class, 'points_transaction_id', 'points_transaction_id');
    }
}

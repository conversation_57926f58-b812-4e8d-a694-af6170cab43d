<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use App\Http\Controllers\Admin\Traits\{
    Add,
    Edit,
};
use Illuminate\Foundation\Application;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\{
    JsonResponse,
    RedirectResponse,
    Request,
};
use App\Models\Modules\{
    AppVersion,
    AppVersionDescription,
    Merchant,
};
use App\Enums\{
    AppPlatform,
};

class AppVersionController extends CommonController
{
    use Add, Edit;
    protected static string $module_name = 'appVersion'; // 模块名称
    protected static bool $module_check_merchant = true; // 标记该模块校验商户

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new AppVersion;
    }

    public function showPage(?Request $request): View|Application|Factory|RedirectResponse
    {
       $data = [
            'module_name' => self::$module_name,
            'add_url' => action([self::class, 'add']),
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'app_code_search' => $request->get('app_code_search'),
            'platform_search' => $request->get('platform_search'),
            'merchant_search' => $request->get('merchant_search'),
            'gmt_release_search' => $request->get('gmt_release_search'),
        ];

        // 获取App平台列表
        $data['platform_list'] = [];
        foreach (AppPlatform::asSelectArray() as $value => $name) {
            $data['platform_list'][] = [
                'name' => $name,
                'value' => $value,
            ];
        }
        // 商户下拉列表
        $data['merchant_list'] = $this->getMerchantOptionList();

        return view("pages.{$data['module_name']}.list", $data);
    }

    /**
     * 列表接口
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $length = (int)$request->input('length', 10);
        $search = $this->_searchList($request);
        $data_list = $search->paginate($length, ['*'], 'start');

        $result = array();

        foreach ($data_list as $data) {
            $merchant_name = $this->getValueFromLanguageArray($data?->merchant?->name_json) ?? '—/—';
            $result[] = array(
                'app_version_id' => $data->app_version_id,
                'app_code' => $data->app_code,
                'merchant_name' => $merchant_name,
                'platform' => AppPlatform::getDescription($data->platform),
                'platform_icon' => match ($data->platform) {
                    AppPlatform::IOS => 'bx bxl-apple',
                    AppPlatform::ANDROID => 'bx bxl-android',
                    default => '',
                },
                'version_code' => $data->version_code,
                'version_name' => $data->version_name,
                'is_mandatory' => $data->is_mandatory,
                'gmt_release' => $data->gmt_release,
                'is_enable' => $data->is_enable,
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            'data' => $result,
        );

        return response()->json($json);
    }

    /**
     * 表单页
     *
     * @param Request $request
     * @param array $data
     * @return View|Application|Factory
     */
    protected function getForm(Request $request, array $data): View|Application|Factory
    {
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action([self::class, 'showPage'], self::getUrlParams($request));

        // 新增时才回显商户编号
        if (blank($data['model']->app_version_id)) {
            // 超级管理员或者多商户能选商户，否则只取所属的单商户
            $merchant_number = isSuperAdministrator() || auth()->user()->merchant_number_list->count() > 1
                ? $request->old('merchant_number', $data['model']->merchant_number)
                : (auth()->user()->merchant_number_list->first() ?? null);
            $data['model']->merchant_number = $merchant_number; // 商户编号
        }
        $merchant = Merchant::firstWhere('merchant_number',  $data['model']->merchant_number);
        $data['model']->merchant_name = $this->getValueFromLanguageArray($merchant?->name_json) ?? '—/—';

        $data['model']->app_code = $request->old('app_code', $data['model']->app_code); // App标识
        $data['model']->platform = $request->old('platform', $data['model']->platform); // 平台
        $data['model']->version_code = $request->old('version_code', $data['model']->version_code); // 版本号
        $data['model']->version_name = $request->old('version_name', $data['model']->version_name); // 版本名称
        $data['model']->is_mandatory = $request->old('is_mandatory', $data['model']->is_mandatory); // 是否强制更新
        $data['model']->gmt_release = $request->old('gmt_release', $data['model']->gmt_release); // 发布时间
        $data['model']->is_enable = $request->old('is_enable', $data['model']->is_enable); // 是否启用

        // 先加载description关联
        if (filled($data['model']->app_version_id)) $data['model']->load(['description']);
        $app_version_description_old_list = $request->old('description');
        foreach (config('languages') as $language_code => $language_name) {
            // 有旧数据
            if (filled($app_version_description_old_list)) {
                $data['description_list'][$language_code] = [
                    'language_name' => $language_name,
                    'app_version_description_id' => $app_version_description_old_list[$language_code]['app_version_description_id'] ?? null, // ID
                    'app_version_id' => $app_version_description_old_list[$language_code]['app_version_id'] ?? null, // ID
                    'language_code' => $language_code,
                    'release_note' => $app_version_description_old_list[$language_code]['release_note'] ?? null,
                    'update_url' => $app_version_description_old_list[$language_code]['update_url'] ?? null,
                ];
            } else {
                $app_version_description_result = $data['model']->description->where('language_code', $language_code)->first();
                $data['description_list'][$language_code] = [
                    'language_name' => $language_name,
                    'app_version_description_id' => $app_version_description_result->app_version_description_id ?? null, // ID
                    'app_version_id' => $app_version_description_result->app_version_id ?? null, // ID
                    'language_code' => $language_code,
                    'release_note' => $app_version_description_result->release_note ?? null,
                    'update_url' => $app_version_description_result->update_url ?? null,
                ];
            }
        }

        $data['platform_list'] = [];
        foreach (AppPlatform::asSelectArray() as $value => $name) {
            $data['platform_list'][] = [
                'name' => $name,
                'value' => $value,
            ];
        }

        return view("pages.{$data['module_name']}.form", $data);
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    protected function modelValidateAndSave(Request $request, AppVersion $model): RedirectResponse
    {
        $request->validate(self::rules($request, $model), [], self::attributes());
        // 新增时才保存商户编号
        if (blank($model->app_version_id)) {
            $model->merchant_number = $request->input('merchant_number');
        }
        $model->app_code = $request->input('app_code');
        $model->platform = $request->input('platform');
        $model->version_code = $request->input('version_code');
        $model->version_name = $request->input('version_name');
        $model->is_mandatory = $request->input('is_mandatory');
        $model->gmt_release = $request->input('gmt_release');
        $model->is_enable = $request->input('is_enable');

        $model->save();

        // 保存App版本描述后再保存并关联App版本描述
        foreach ($request->input('description_list', []) as $description) {
            if (isset($description['app_version_description_id'])) {
                $app_version_description_model = AppVersionDescription::findOr($description['app_version_description_id'], fn() => new AppVersionDescription);
            } else {
                $app_version_description_model = new AppVersionDescription;
            }
            $app_version_description_model->app_version_id = $model->app_version_id;
            $app_version_description_model->language_code = $description['language_code'];
            $app_version_description_model->release_note = $description['release_note'];
            $app_version_description_model->update_url = $description['update_url'];

            $app_version_description_model->save();
        }

        return redirect()->action(
            [self::class, 'showPage'], self::getUrlParams($request),
        );
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function rules(?Request $request, ?Model $model): array
    {
        $module_name = self::$module_name;

        $rules = [
            'app_code' => 'required|max:45',
            'platform' => 'required|enum_value:' . AppPlatform::class,
            'version_code' => 'required|integer|min:0|max:999999999',
            'version_name' => 'required|max:45',
            'is_mandatory' => 'required|bool',
            'gmt_release' => 'required|date',
            'is_enable' => 'required|bool',
        ];

        // 只有新增时才校验场地和商户编号
        if (blank($model->app_version_id)) {
            if (isSuperAdministrator() || auth()->user()->merchant_number_list->count() > 1) {
                $rules['merchant_number'] = [
                    'required',
                    'exists:App\Models\Modules\Merchant,merchant_number',
                    function ($attr, $value, $fail) use ($request, $model, $module_name) {
                        // 新增时校验非超级管理员且未拥有该商户提示错误
                        if (!isSuperAdministrator() && !in_array($value, auth()->user()->merchant_number_list->toArray())) {
                            $fail(__('common.text_not_found', ['field' => __("$module_name.merchant")]));
                        }
                    },
                ];
            }
        }


        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'app_code' => __("$module_name.app_code"),
            'platform' => __("$module_name.platform"),
            'version_code' => __("$module_name.version_code"),
            'version_name' => __("$module_name.version_name"),
            'is_mandatory' => __("$module_name.is_mandatory"),
            'gmt_release' => __("$module_name.gmt_release"),
            'is_enable' => __("$module_name.is_enable"),
        ];
    }


    /**
     * 返回地址栏参数
     *
     * @param Request|null $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => 'true',
            'app_code_search' => $request->get('app_code_search'),
            'platform_search' => $request->get('platform_search'),
            'merchant_search' => $request->get('merchant_search'),
            'gmt_release_search' => $request->get('gmt_release_search'),
        );
    }

    /**
     * 获取搜索数据
     *
     * @param Request $request
     * @return mixed
     * @Description
     * @example
     */
    protected function _searchList(Request $request): mixed
    {
        // dataTable字段
        $order = $request->input('order', 'gmt_release');
        $sort = $request->input('sort', 'desc');
        $app_code = $request->input('app_code_search');
        $platform = $request->input('platform_search');
        $merchant_number = $request->input('merchant_search');
        $gmt_release = $request->gmt_release_search;
        $gmt_release_list = self::getRangeDateTimeArray($gmt_release ?: '') ?: null;

        $where = array();
        if (filled($app_code)) {
            $where[] = ['app_code', 'like', "%$app_code%"];
        }
        if (filled($platform)) {
            $where[] = ['platform', '=', $platform];
        }
        if (filled($merchant_number)) {
            $where[] = ['merchant_number', '=', $merchant_number];
        }

        return AppVersion::with('merchant')
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))
            ->when(filled($gmt_release_list), fn($query) => $query->whereBetween('gmt_release', $gmt_release_list))
            ->where($where)
            ->orderBy($order, $sort)
            ->latest();
    }
}

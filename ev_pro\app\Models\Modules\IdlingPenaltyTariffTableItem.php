<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;
use App\Models\Traits\Emoji;

class IdlingPenaltyTariffTableItem extends Model
{
    use emoji;

    protected $table = 'idling_penalty_tariff_table_item'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'idling_penalty_tariff_table_item_id'; //定义主键，默认为id
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    protected $fillable = ['member_card_group_id', 'user_group_id', 'start_range', 'end_range', 'off_peak_rate', 'on_peak_rate'];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [
        'start_range' => 0, // 开始范围
        'end_range' => 0, // 结束范围
        'off_peak_rate' => 0, // 非高峰收费
        'on_peak_rate' => 0, // 高峰收费
    ];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];
}

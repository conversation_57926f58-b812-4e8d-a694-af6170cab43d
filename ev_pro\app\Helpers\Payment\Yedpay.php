<?php

namespace App\Helpers\Payment;

use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use App\Models\Modules\{
    ChargeRecord,
    AppUser,
    PointsTransaction,
};
use App\Enums\{
    ChargeTariffScheme,
    TransactionType,
    TransactionCategory,
    PaymentStatusEnum,
};
use App\Http\Controllers\Payment\YedpayController;


class Yedpay
{
    // Yedpay API Gateway
    public static $api_gateway = 'https://api.yedpay.com/v1/';
    /**
     * 生成Yedpay的checkout url
     *
     * @param [type] $total_amount 单位 分
     * @param [type] $custom_id 字母数字下划线和破折号，最长36位
     * @return string
     * @Description
     * @example
     * @date 2024-01-05
     */
    public static function checkout($total_amount, $custom_id): string|bool
    {
        // 使用数学函数将total_amount转换为元
        $total_amount = bcdiv($total_amount, 100, 2);
        $post_data = [
            'amount' => number_format($total_amount, 2, '.', ''),
            'currency' => 'HKD',
            // 暂时先返回主页
            'return_url' => config('app.url'),
            'notify_url' => action([YedpayController::class, 'callback']),
            'custom_id' => env('APP_NAME') . '_' . $custom_id,
        ];

        $return_data = self::httpPost('online-payment', $post_data);
        if (blank($return_data)) {
            return false;
        }
        if (!isset($return_data['success']) || $return_data['success'] != true || !isset($return_data['data']['checkout_url'])) {
            logger()->error('Payment::Yedpay checkout failed', [
                'post_data' => $post_data,
                'return_data' => $return_data
            ]);
            return false;
        }
        // debug模式下记录日志
        if (config('app.debug')) {
            logger()->debug('Payment::Yedpay checkout success', [
                'post_data' => $post_data,
                'return_data' => $return_data
            ]);
        }
        return $return_data['data']['checkout_url'];
    }

    /**
     * Yedpay的return url回调
     *
     * @param array $response
     * @return boolean
     * @Description
     * @example
     * @date 2024-01-06
     */
    public static function callback(array $response = []): bool
    {
        // 响应为空不处理
        if (blank($response)) {
            return false;
        }
        // debug模式下记录日志
        if (config('app.debug')) {
            logger()->debug('Payment::Yedpay callback', [
                'response' => $response,
            ]);
        }
        // 校验签名
        if (!self::verifySignature($response)) {
            logger()->error('Payment::Yedpay callback verify signature failed', [
                'response' => $response
            ]);
            return false;
        }
        $success = boolval($response['success'] ?? false);
        $transaction = $response['transaction'] ?? [];
        $charge_record_id = $transaction['custom_id'] ?? null;
        if (!$success || blank($transaction) || blank($charge_record_id)) {
            logger()->error('Payment::Yedpay callback failed', [
                'response' => $response
            ]);
            return false;
        }
        // 判断充电记录存在
        $charge_record = ChargeRecord::with(['chargePaymentRecord' => function ($query) {
            $query->where('payment_status', PaymentStatusEnum::Pending);
        }])
            ->where('charge_record_id', $charge_record_id)
            ->first();
        if (blank($charge_record)) {
            logger()->error('Payment::Yedpay callback charge record not found', [
                'charge_record_id' => $charge_record_id
            ]);
            logger()->error('Payment::Yedpay callback failed', [
                'response' => $response
            ]);
            return false;
        }
        // 判断charge record的user_id必须存在
        if (blank($charge_record->user_id)) {
            logger()->error('Payment::Yedpay callback charge record user_id not found', [
                'charge_record_id' => $charge_record_id
            ]);
            logger()->error('Payment::Yedpay callback failed', [
                'response' => $response
            ]);
            return false;
        }
        // 判断是否已经支付
        $transaction_status = $transaction['status'] ?? null;
        if ($transaction_status !== 'paid') {
            return false;
        }

        // 循环更新payment record状态
        try {
            $result = DB::transaction(function () use ($charge_record, $transaction) {
                // 生成积分交易记录
                $points_transaction = new PointsTransaction;
                $points_transaction->user_id = $charge_record->user_id;
                $points_transaction->transaction_number = PaymentCommon::generatePointsTransactionNumber();
                $points_transaction->transaction_type = TransactionType::Expense;
                $points_transaction->transaction_category = TransactionCategory::ChargePayment;
                $points_transaction->save();

                // 实际支付金额 转分
                $actual_payment_amount = bcmul($transaction['amount'] ?? "0.00", 100, 0);
                #TODO 暂时将使用charge record的use_points字段，后续会新加字段存
                $use_points = $charge_record->use_points ?? 0; // 使用的积分
                #TODO 预留优惠券抵扣字段字段
                $coupon_deduction = 0; // 优惠券抵扣
                foreach ($charge_record->chargePaymentRecord as $charge_payment_record) {
                    $charge_payment_record->payment_status = PaymentStatusEnum::Completed; // 已完成
                    $charge_payment_record->gmt_payment_status = now(); // 支付时间
                    #TODO 预留，首先计算优惠券抵扣
                    /* if ($coupon_deduction > 0) {
                        // 总金额如果大于优惠券抵扣，抵扣所有优惠券抵扣；否则抵扣总金额等量的优惠券抵扣
                        $charge_payment_record->coupon_deduction = $charge_payment_record->total_amount > $coupon_deduction ? $coupon_deduction : $charge_payment_record->total_amount; // 优惠券抵扣
                        // 扣除对应优惠券抵扣
                        $coupon_deduction = bcsub($coupon_deduction, $charge_payment_record->coupon_deduction, 0);
                    } */
                    // 然后计算积分抵扣
                    $charge_payment_record->use_points = 0; // 使用的积分
                    if ($use_points > 0) {
                        // 使用的积分。总金额如果大于积分，抵扣所有积分；否则抵扣总金额等量的积分
                        $charge_payment_record->use_points = $charge_payment_record->total_amount > $use_points ? $use_points : $charge_payment_record->total_amount;
                        // 扣除对应积分
                        $use_points = bcsub($use_points, $charge_payment_record->use_points, 0);
                        // 如果有积分抵扣，存储积分交易ID
                        $charge_payment_record->points_transaction_id = $points_transaction->transaction_id;
                    }
                    // 再计算实际支付金额
                    $charge_payment_record->actual_payment_amount = 0; // 实际支付金额
                    if ($actual_payment_amount > 0) {
                        // 实际支付金额。总金额-抵扣的积分（应付金额）如果大于实际支付金额，抵扣所有实际支付金额；否则抵扣总金额等量的实际支付金额
                        $receivable_amount = bcsub($charge_payment_record->total_amount, $charge_payment_record->use_points, 0); // 应付金额
                        $charge_payment_record->actual_payment_amount = $receivable_amount > $actual_payment_amount ? $actual_payment_amount : $receivable_amount; // 实际支付金额
                        // 扣除对应实际支付金额
                        $actual_payment_amount = bcsub($actual_payment_amount, $charge_payment_record->actual_payment_amount, 0);
                    }

                    $charge_payment_record->save();
                }
                // 如果是预付
                if ($charge_record->charge_tariff_scheme === ChargeTariffScheme::PrePaid) {
                    $charge_record->pre_paid_purchase_charge_value = $charge_payment_record->charge_value; // 预付购买充电量
                }
                $charge_record->use_points = $use_points; // 使用的积分
                $charge_record->actual_payment_amount = $actual_payment_amount; // 实际支付金额
                $charge_record->save();

                // 扣除用户积分
                // 获取悲观锁
                $user = AppUser::where('user_id', $charge_record->user_id)->lockForUpdate()->first();
                $user->points_balance -= $charge_record->use_points;
                $user->save();
                // 回填积分交易记录
                $points_transaction->amount = $charge_record->use_points;
                $points_transaction->points_balance = $user->points_balance;
                $points_transaction->save();

                return true;
            });

            if ($result) {
                // 事务成功提交
                return true;
            } else {
                // 事务回滚
                logger()->error('Payment::Yedpay callback update payment record failed', [
                    'charge_record_id' => $charge_record_id,
                    'actual_payment_amount' => $transaction['amount'] ?? "0.00"
                ]);
                logger()->error('Payment::Yedpay callback failed', [
                    'response' => $response
                ]);
                return false;
            }
        } catch (Exception $e) {
            logger()->error('Payment::Yedpay callback update payment record failed', [
                'charge_record_id' => $charge_record_id,
                'actual_payment_amount' => $transaction['amount'] ?? "0.00",
                'exception' => $e->getMessage()
            ]);
            logger()->error('Payment::Yedpay callback failed', [
                'response' => $response
            ]);
            return false;
        }
    }

    /**
     * 与Yedpay建立连接
     *
     * @param string $url
     * @param string $key
     * @param array $post_data
     * @return array
     * @Description
     * @example
     * @date 2024-01-05
     */
    public static function httpPost(string $url, array $post_data = []): array|bool
    {
        // 从根目录的json文件夹获取yedpay_config.json文件的内容
        $yedpay_config_array = getArrayFromJsonFile('yedpay_config');
        // 无法解析或者为空
        if (blank($yedpay_config_array)) {
            logger()->error('Payment::Yedpay cannot get config json file');
            return false;
        }
        $yedpay_api_key = $yedpay_config_array['API_KEY'];
        // 无法获取到API_KEY
        if (blank($yedpay_api_key)) {
            logger()->error('Payment::Yedpay cannot get API_KEY from config json file');
            return false;
        }

        $url = self::$api_gateway . $url;
        $response = Http::retry(3, 100)
            ->withOptions([
                'verify' => false
            ])
            ->withHeaders([
                'Authorization' => 'API-KEY ' . $yedpay_api_key,
                'Content-Type' => 'application/x-www-form-urlencoded',
                // 设置当前语言
                'Accept-Language' => match (app()->getLocale()) {
                    'zh_HK' => 'zh',
                    default => 'en',
                },
            ])
            ->post($url, $post_data);
        // 记录访问错误日志
        if ($response->failed()) {
            logger()->error('Payment::Yedpay http post failed', [
                'url' => $url,
                'post_data' => $post_data,
                'response' => $response->json()
            ]);
            return false;
        }

        return $response->json();
    }

    public static function verifySignature(array $response = []): bool
    {
        // 响应为空不处理
        if (blank($response)) {
            return false;
        }
        // 从根目录的json文件夹获取yedpay_config.json文件的内容
        $yedpay_config_array = getArrayFromJsonFile('yedpay_config');
        // 无法解析或者为空
        if (blank($yedpay_config_array)) {
            logger()->error('Payment::Yedpay cannot get config json file');
            return false;
        }
        $yedpay_sign_key = $yedpay_config_array['SIGN_KEY'];
        // 无法获取到SIGN_KEY
        if (blank($yedpay_sign_key)) {
            logger()->error('Payment::Yedpay cannot get SIGN_KEY from config json file');
            return false;
        }

        // 开始校验签名
        // Exclude unnecessary values 排除不必要的值
        // sign and sign_type should be excluded 应排除sign和sign_type
        $exclude_keys = ['sign', 'sign_type'];
        $processed_response = array_diff_key($response, array_flip($exclude_keys));
        // Sort array key (only the first level) in ascending order. 按升序排序数组键（仅第一级）
        ksort($processed_response);
        // HTTP build query and url decode HTTP构建查询并url解码
        $processed_response = urldecode(http_build_query($processed_response));
        switch ($response['sign_type'] ?? null) {
                // Recently support HMAC_SHA256 ONLY 最近只支持HMAC_SHA256
            case 'HMAC_SHA256':
                $sign = hash_hmac('sha256', $processed_response, $yedpay_sign_key);
                break;
            default:
                logger()->error('Payment::Yedpay unknown sign type', [
                    'sign_type' => $response['sign_type']
                ]);
                logger()->error('Payment::Yedpay unknown response', [
                    'response' => $response
                ]);
                return false;
        }

        return $sign === $response['sign'];
    }
}

<?php

namespace App\Models\Modules;

use App\Models\Traits\Emoji;
use Illuminate\Database\Eloquent\Model;

class PowerGroup extends Model
{
    use emoji;

    protected $table = 'power_group'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'power_group_id'; //定义主键，默认为id
    // 表的唯一number
    public static $table_number = 'power_group_number';
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    // protected $fillable = [];

    /**
     * 默认值
     */
    protected $attributes = [
        'current_voltage' => 0 // 当前电压
    ];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    public static function find(array|string|null $power_group_number)
    {
    }
}

<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\{
    JsonResponse,
    RedirectResponse,
    Request,
};
use App\Http\Controllers\Admin\Traits\{
    Edit,
};
use Illuminate\Foundation\Application;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use App\Enums\{
    ChargePointVendor,
    ConnectorStatus,
};
use App\Models\Modules\{
    ChargePoint,
    ComServer,
    Kiosk,
    ChargePointCs,
    ChargePointSetting,
    Site,
    Zone,
    Connector,
    ConnectorSetting,
    Merchant,
};
use Illuminate\Validation\ValidationException;

class ChargePointController extends CommonController
{
    use Edit;

    protected static string $module_name = 'chargePoint'; // 模块名称
    protected static string $delimiter; // 分隔符
    protected static bool $module_check_site = true; // 标记该模块校验场地

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new ChargePoint;
        self::$delimiter = env('DELIMITER', '-');
    }

    /**
     * 初始页
     *
     * @param Request $request
     * @return View|Application|Factory
     */
    public function showPage(Request $request): View|Application|Factory
    {
        $charge_point_number_search = $request->input('charge_point_number_search');
        $name_search = $request->input('name_search');
        $kiosk_list = Kiosk::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->oldest('sort_order')
            ->get();
        $data = array(
            'module_name' => self::$module_name,
            'add_url' => action([self::class, 'add']),
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'charge_point_number_search' => $charge_point_number_search,
            'name_search' => $name_search,
            'kiosk_list' => $kiosk_list,
            'download_template_url' => existsImage('template', 'charger_template.xlsx'),
            'send_message_to_charge_point_url' => action([self::class, 'sendMessageToChargePoint']),
            'site_search' => $request->get('site_search'),
            'site_list' => $this->getSiteOptionList(),
        );

        return view("pages.{$data['module_name']}.list", $data);
    }

    /**
     * 列表接口
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'sort_order');
        $sort = $request->input('sort', 'asc');
        $length = (int)$request->input('length', 10);
        $charge_point_number_search = $request->input('charge_point_number_search');
        $name_search = $request->input('name_search');
        $site_search = $request->input('site_search');

        if ($order == 'charge_point_cs_number') $order = 'charge_point_cs.name';
        if ($order == 'charge_point_setting') $order = 'charge_point_setting.name';
        if ($order == 'com_server_name') $order = 'com_server.name';
        if ($order == 'site_name') $order = 'site.name_json';

        $connector_status_sql_in = '"' . ConnectorStatus::Preparing . '",
        "' . ConnectorStatus::Charging . '",
        "' . ConnectorStatus::Queuing . '",
        "' . ConnectorStatus::Finishing . '",
        "' . ConnectorStatus::Available . '",
        "' . ConnectorStatus::Unavailable . '",
        "' . ConnectorStatus::Faulted . '",
        "' . ConnectorStatus::Offline . '"';

        $data_list = ChargePoint::with(['connector'])
            ->select('charge_point.*', 'charge_point_setting.name as charge_point_setting_name_init', 'com_server.name as com_server_name_init', 'charge_point_cs.name as charge_point_cs_name_init', 'site.name_json as site_name_json_init')
            ->leftJoin('charge_point_setting', 'charge_point.charge_point_setting_number', '=', 'charge_point_setting.charge_point_setting_number')
            ->leftJoin('charge_point_cs', 'charge_point.charge_point_cs_number', '=', 'charge_point_cs.charge_point_cs_number')
            ->leftJoin('com_server', 'charge_point.com_server_number', '=', 'com_server.com_server_number')
            ->leftJoin('site', 'charge_point.site_number', '=', 'site.site_number')
            ->when(filled($charge_point_number_search), fn ($query) => $query->where('charge_point.charge_point_number', 'like', "%$charge_point_number_search%"))
            ->when(filled($name_search), fn ($query) => $query->where('charge_point.name', 'like', "%$name_search%"))
            ->when(filled($site_search), fn ($query) => $query->where('charge_point.site_number', $site_search))
            ->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('charge_point.site_number', auth()->user()->site_number_list))
            ->orderByRaw('CASE WHEN EXISTS (
                SELECT 1 FROM connector WHERE connector.charge_point_number = charge_point.charge_point_number AND connector.status IN (' . $connector_status_sql_in . ')
            ) THEN 0 ELSE 1 END, CASE WHEN EXISTS (
                SELECT 1 FROM connector WHERE connector.charge_point_number = charge_point.charge_point_number AND connector.status IN (' . $connector_status_sql_in . ')
            ) THEN (
                SELECT MIN(CASE status
                    WHEN "' . ConnectorStatus::Preparing . '" THEN 1
                    WHEN "' . ConnectorStatus::Charging . '" THEN 2
                    WHEN "' . ConnectorStatus::Queuing . '" THEN 3
                    WHEN "' . ConnectorStatus::Finishing . '" THEN 4
                    WHEN "' . ConnectorStatus::Available . '" THEN 5
                    WHEN "' . ConnectorStatus::Unavailable . '" THEN 6
                    WHEN "' . ConnectorStatus::Faulted . '" THEN 7
                    WHEN "' . ConnectorStatus::Offline . '" THEN 8
                    ELSE 9
                END) FROM connector WHERE connector.charge_point_number = charge_point.charge_point_number AND connector.status IN (' . $connector_status_sql_in . ')
            ) ELSE 9 END')
            ->orderBy($order, $sort)
            ->latest()
            ->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $site_name = $this->getValueFromLanguageArray($data->site_name_json_init) ?? '—/—';
            $result[] = array(
                'charge_point_id' => $data->charge_point_id, // ID
                'charge_point_number' => $data->charge_point_number, // 充电桩编码
                'name' => $data->name, // 名称
                'vendor' => ChargePointVendor::getDescription($data->vendor), // 供应商
                'com_server_name' => $data->com_server_name_init ?? '—/—', // 中央服务器地址
                'charge_point_cs_number' => $data->charge_point_cs_name_init ?? '—/—', // 充电机中央服务器名称
                'connector_list' => $data->connector?->sortBy('sort_order')->values() ?? '—/—', // 充电枪
                'charge_point_setting' => $data->charge_point_setting_name_init ?? '—/—', // 充电机设置名称
                'charge_point_setting_number' => $data->charge_point_setting_number, // 充电机设置ID
                'site_name' => $site_name, // 场地ID
                'model' => $data->model ?? '—/—', // 型号
                'firmware_version' => $data->firmware_version ?? '—/—', // 固件版本
                'ip_address' => $data->ip_address ?? '—/—', // IP地址
                'gmt_last_boot' => $data->gmt_last_boot ?? '—/—', // 最后启动时间
                'gmt_last_alive' => $data->gmt_last_alive ?? '—/—', // 最后活跃时间
                'sort_order' => $data->sort_order, // 排序
                'remark' => $data->remark ?? '—/—', // 备注
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result
        );
        return response()->json($json);
    }

    /**
     * 表单页
     *
     * @param Request $request
     * @param array $data
     * @return View|Application|Factory
     */
    protected function getForm(Request $request, array $data): View|Application|Factory
    {
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'],
            self::getUrlParams($request)
        );
        // 编辑时根据分隔符拆分number取第二位
        $charge_point_number_original = $data['model']->charge_point_number;
        $charge_point_number = explode(self::$delimiter, $data['model']->charge_point_number)[1] ?? '';
        $charge_point_setting_name = ChargePointSetting::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->firstWhere('charge_point_setting_number', $request->old('charge_point_setting_number', $data['model']->charge_point_setting_number))?->name ?? '';

        $site_name = $this->getValueFromLanguageArray(
            Site::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->firstWhere('site_number', $request->old('site_number', $data['model']->site_number))?->name_json
        ) ?? '';
        $com_server_name = ComServer::firstWhere('com_server_number', $request->old('com_server_number', $data['model']->com_server_number))?->name ?? '';
        $charge_point_cs_name = ChargePointCs::firstWhere('charge_point_cs_number', $request->old('charge_point_cs_number', $data['model']->charge_point_cs_number))?->name ?? '';

        $data['model']->charge_point_id = $request->old('charge_point_id', $data['model']->charge_point_id); // 充电桩ID
        $data['model']->charge_point_number = (blank($data['model']->charge_point_id)) ? $request->old('charge_point_number', $charge_point_number) : $charge_point_number; // 充电桩编码
        if (blank($data['model']->charge_point_id)) $data['model']->vendor = $request->old('vendor', $data['model']->vendor); // 供应商
        $data['model']->name = $request->old('name', $data['model']->name); // 名称
        $data['model']->com_server_number = $request->old('com_server_number', $data['model']->com_server_number); // 中央服务器编号
        $data['model']->com_server_name = $com_server_name; // 中央服务器名称
        $data['model']->charge_point_setting_number = $request->old('charge_point_setting_number', $data['model']->charge_point_setting_number); // 充电机设置名称
        $data['model']->charge_point_setting_name = $charge_point_setting_name; // 充电机设置名称
        $data['model']->site_number = $request->old('site_number', $data['model']->site_number); // 场地编号
        $data['model']->site_name = $site_name; // 场地名称
        $data['model']->charge_point_cs_number = $request->old('charge_point_cs_number', $data['model']->charge_point_cs_number); // 充电机中央服务器编号
        $data['model']->charge_point_cs_name = $charge_point_cs_name; // 充电机中央服务器名称
        $data['model']->sort_order = $request->old('sort_order', $data['model']->sort_order); // 排序
        $data['model']->remark = $request->old('remark', $data['model']->remark); // 备注

        $data['com_server_list'] = $data['charge_point_cs_list'] = $data['charge_point_setting_list'] = $data['site_list'] = array();

        $data['connector_result_list'] = [];

        // 如果存在旧数据
        $connector_list = $request->old('connector', (filled($data['model']->charge_point_number)
            ? Connector::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->where('charge_point_number', $charge_point_number_original)->oldest('sort_order')
            ->get()
            : []));

        // 防止是Copy To Add没数据导致充电枪数据空白
        if (blank($connector_list) && filled($data['model']->connector)) $connector_list = $data['model']->connector;

        foreach ($connector_list as $key => $connector) {
            $connector_setting_number = $connector['connector_setting_number'] ?? '';
            $connector_setting_name = ConnectorSetting::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->firstWhere('connector_setting_number', $connector_setting_number)?->name ?? '';
            $zone_number = $connector['zone_number'] ?? '';
            $zone_name = $this->getValueFromLanguageArray(Zone::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->firstWhere('zone_number', $zone_number)?->name_json) ?? '';
            // 有旧数据时取值方式不一致
            if ($request->old('connector')) {
                $connector_number = $connector['connector_number'];
                $connector_name = $connector['connector_name'];
            } else {
                $connector_number = explode(self::$delimiter, $connector['connector_number'])[2] ?? '';
                $connector_name = $connector['name'] ?? '';
            }
            $data['connector_result_list'][$key] = array(
                'connector_id' => $connector['connector_id'] ?? '', // 充电枪id
                'connector_number' => $connector_number, // 充电枪编号
                'connector_name' => $connector_name, // 显示名称
                'connector_setting_number' => $connector_setting_number, // 充电枪设置编号
                'connector_setting_name' => $connector_setting_name, // 充电枪设置名称
                'zone_number' => $zone_number, // 充电枪区域编号
                'zone_name' => $zone_name, // 充电枪区域名称
                'sort_order' => $connector['sort_order'], // 排序
                'remark' => $connector['remark'] ?? '', // 备注
            );
        }
        return view("pages.{$data['module_name']}.form", $data);
    }

    /**
     * 添加
     *
     * @param Request $request
     * @return Factory|Application|View|RedirectResponse
     */
    public function add(Request $request): Factory|Application|View|RedirectResponse
    {
        $data = array();

        $model = $this->model;

        // 如果有charge_point_number的存在就是复制除了charge_point_number和name以外的收费表数据输出到新增页面
        if (filled($charge_point_number = $request->input('_charge_point_number'))) {
            $old_data_model = $model->with(['connector' => fn ($query) => $query->oldest('sort_order')])
                ->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('charge_point.site_number', auth()->user()->site_number_list))
                ->where('charge_point_number', $charge_point_number)
                ->firstOrFail();
            $model = $old_data_model->replicate(['name', 'charge_point_number']);

            foreach ($model->connector as $connector) {
                $connector->connector_id = $connector->charge_point_number = $connector->name = $connector->connector_number = null;
            }
        }

        if ($request->isMethod('post')) {
            return $this->modelValidateAndSave($request, $model);
        }

        $data['model'] = $model;
        return $this->getForm($request, $data);
    }

    /**
     * 切换设置接口
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function editSetting(Request $request): JsonResponse
    {
        $module_name = self::$module_name;

        $charge_point_number = $request->input('charge_point_number');
        $charge_point_setting_number = $request->input('charge_point_setting_number');
        $charge_point_setting = ChargePointSetting::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('charge_point_setting_number', $charge_point_setting_number);
        $charge_point = ChargePoint::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('charge_point_number', $charge_point_number);
        if (blank($charge_point_number) || blank($charge_point_setting_number) || blank($charge_point) || blank($charge_point_setting)) {
            $this->missingField(__("$module_name.web_title") . '|' . __("$module_name.charge_point_setting"));
            return $this->returnJson();
        }

        // 场地不一致无法修改
        if ($charge_point_setting->site_number != $charge_point->site_number) {
            $this->code = 201;
            $this->message = __('common.merchant_inconsistent_with_field', ['field' => $charge_point->name]);
            return $this->returnJson();
        }

        $charge_point->charge_point_setting_number = $charge_point_setting_number;
        $charge_point->save();

        self::sendInitPushByKioskNumberList();

        return $this->returnJson();
    }

    /**
     * 获取charge point绑定的kiosk
     *
     * @param [type] $id
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-18
     */
    public function getKiosk($charge_point_number): JsonResponse
    {
        $module_name = self::$module_name;
        $charge_point = ChargePoint::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('charge_point_number', $charge_point_number);
        $where = array();
        $this->data['kiosk'] = array();
        $this->data['not_selected_kiosk'] = array();
        if (!filled($charge_point)) {
            $this->missingData(__("$module_name.charge_point_number"));
        } else {
            // 查出选中的Kiosk用于左右拖拽
            $kiosk_list = $charge_point->kiosk()->get();
            foreach ($kiosk_list as $kiosk) {
                $this->data['kiosk'][] = array(
                    'value' => $kiosk->kiosk_number,
                    'name' => $kiosk->name,
                    'kiosk_number' => $kiosk->kiosk_number,
                );
            }
            // 判断是否有数据
            if (isset($this->data['kiosk'])) {
                for ($i = 0; $i < count($this->data['kiosk']); $i++) {
                    $value = $this->data['kiosk'][$i]['value'];
                    $where[] = ['kiosk_number', '!=', $value];
                }
            }
            // 只显示该site id下的Kiosk
            $where[] = ['site_number', $charge_point->site_number];
            // 排除掉选中的Kiosk用于左右拖拽
            $not_selected_kiosk_list = Kiosk::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->where($where)
                ->oldest('sort_order')
                ->get();
            foreach ($not_selected_kiosk_list as $kiosk) {
                $this->data['not_selected_kiosk'][] = array(
                    'value' => $kiosk->kiosk_number,
                    'name' => $kiosk->name,
                    'kiosk_number' => $kiosk->kiosk_number,
                );
            }
        }

        return $this->returnJson();
    }

    /**
     * 提交保存charge point关联的kiosk
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-18
     */
    public function submitKiosk(Request $request): JsonResponse
    {
        $module_name = self::$module_name;

        $charge_point_number = $request->input('charge_point_number');
        $kiosk_number_list = $request->input('kiosk_number_list', []);

        $charge_point = ChargePoint::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('charge_point_number', $charge_point_number);
        if (blank($charge_point_number) || blank($charge_point)) {
            $this->missingField(__("$module_name.web_title"));
            return $this->returnJson();
        }

        $kiosk_list = Kiosk::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->whereIn('kiosk_number', $kiosk_number_list);
        // 判断场地是否一致
        $kiosk_result_count = $kiosk_list->where('site_number', $charge_point->site_number)
            ->count();
        if ($kiosk_result_count != count($kiosk_number_list)) {
            $this->code = 201;
            $this->message = __('common.merchant_inconsistent_with_field', ['field' => $charge_point->name]);
            return $this->returnJson();
        }
        // 判断site是否一致
        $kiosk_site_number_list = $kiosk_list->pluck('site_number')->toArray();
        if (filled($kiosk_number_list) && filled(array_diff($kiosk_site_number_list, [$charge_point->site_number]))) {
            $this->code = 40001;
            $this->message = __("$module_name.site_mismatching");
        }

        $charge_point->kiosk()->sync($kiosk_number_list);

        self::sendInitPushByKioskNumberList();

        return $this->returnJson();
    }

    /**
     * 删除
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function delete(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        $charge_point_number = $request->post('charge_point_number');

        $model = ChargePoint::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('charge_point_number', $charge_point_number);
        if (!filled($charge_point_number) || !filled($model)) {
            $this->notFoundData(__("$module_name.charge_point_number"));
            return $this->returnJson();
        }

        if ($model->kiosk?->count() > 0) {
            $this->code = 202;
            $this->message = __('common.error_has_binding_unable_to_modify_some_one', ['field' => __("$module_name.charge_point_number")]);
            return $this->returnJson();
        }

        // 判断是否存在正在使用中的充电枪
        $select_connector_list = Connector::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->where('charge_point_number', $charge_point_number)
            ->whereNotNull('current_charge_record_number')
            ->whereNotNull('current_charge_transaction_id')
            ->get();
        if (count($select_connector_list) > 0) {
            $this->code = 201;
            $this->message = __("$module_name.not_allowed_to_delete");
            return $this->returnJson();
        }

        // 并且删除关联的充电枪
        $model->connector()->delete();
        // 删除充电机
        $model->delete();

        self::sendInitPushByKioskNumberList();

        return $this->returnJson();
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @param ChargePoint $model
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    protected function modelValidateAndSave(Request $request, ChargePoint $model): RedirectResponse
    {
        $request->validate(self::rules($request, $model), [], self::attributes());
        // 添加的时候，判断同场地下是否存在充电中的充电枪，status=CHARGING|QUEUING，如果有，提示当前场地有充电中的充电枪，请先停止再添加
        $is_charging_connector = Connector::where('site_number', $model->site_number)
        ->whereIn('status', [ConnectorStatus::Charging, ConnectorStatus::Queuing])
        ->exists();

        // 判断是否是新增
        if (blank($model->charge_point_id)) {
            $model = $this->model;
            $model->vendor = $request->input('vendor');
            $model->charge_point_number = $model->vendor . self::$delimiter . $request->input('charge_point_number');
            $site_number = $model->site_number;

            if (isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) {
                // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下就直接获取页面传入的场地编号
                $site_number = $request->input('site_number');
            } else if (auth()->user()->site_number_list->count() === 1) {
                // 如果是新增并且管理员场地只有一个就直接赋值
                $site_number = auth()->user()->site_number_list->first();
            }
            $model->site_number = $site_number;
            $model->merchant_number = Site::firstWhere('site_number', $model->site_number)?->merchant_number;

            // 如果存在充电中的充电枪，提示当前场地有充电中的充电枪，请先停止再添加
            if ($is_charging_connector) {
                $module_name = self::$module_name;
                throw ValidationException::withMessages([
                    'charge_point_id' => [__("$module_name.charging_connector_exists")],
                ]);
            }
        } else {
            if ($is_charging_connector) {
                // 编辑的时候，如果有新增充电枪，判断同场地下是否存在充电中的充电枪，status=CHARGING|QUEUING，如果有，提示当前场地有充电中的充电枪，请先停止再添加
                $connector_list = $request->input('connector', array());
                // 获取connector_number数组
                $connector_number_list = array_column($connector_list, 'connector_number');
                foreach ($connector_number_list as &$connector_number) {
                    $connector_number = $model->charge_point_number . self::$delimiter . $connector_number;
                }
                // 判断是否存在新增的充电枪
                $connector_count = Connector::whereIn('connector_number', $connector_number_list)
                ->count();
                if ($connector_count !== count($connector_number_list)) {
                    $module_name = self::$module_name;
                    throw ValidationException::withMessages([
                        'charge_point_id' => [__("$module_name.charging_connector_exists")],
                    ]);
                }
            }
        }
        // 如果为EVPowerCS时才更改charge_point_cs_number
        if (ChargePointVendor::EVPowerCS()->is($model->vendor)) {
            $model->charge_point_cs_number = $request->input('charge_point_cs_number');
        }
        // 先赋值，为了后续能够把原有的充电机下的充电枪给拿出来去修改
        $charge_point_number = $model->charge_point_number;
        $merchant_number = $model->merchant_number;
        $site_number = $model->site_number;

        $model->name = $request->input('name');
        $model->com_server_number = $request->input('com_server_number', 0);
        $model->charge_point_setting_number = $request->input('charge_point_setting_number');
        $model->sort_order = $request->input('sort_order', 0);
        $model->remark = $request->input('remark');

        // 获取请求充电枪数据
        $connector_list = $request->input('connector', array());

        // charge point number加密生成许可码
        if (config('app.is_enable_automatic_encryption_of_license_code')) $model->license_code = self::licenseCodeEncryption($model->charge_point_number);

        // 保存充电机后再保存并关联充电枪
        $model->save();
        // 查询数据库所有充电枪
        $connector_list_db = Connector::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->where('charge_point_number', $charge_point_number)
            ->get();

        // 已处理的connector_id与connector_number对应关系数组
        $connector_id_number_list = [];
        // 已处理的connector模型
        $connector_model_list = collect();

        // 第一次循环处理number重复的充电枪
        foreach ($connector_list as $index => $connector) {
            $connector_number = $model->charge_point_number . self::$delimiter . $connector['connector_number'];
            // 判断connector_number是否已存在$connector_list_db中，如果存在，直接将对应的模型赋值给$connector_model
            $connector_model = $connector_list_db->firstWhere('connector_number', $connector_number);
            // 如果未找到，跳出循环
            if (blank($connector_model)) {
                continue;
            }
            $connector_model->charge_point_number = $model->charge_point_number;
            $connector_model->merchant_number = $merchant_number;
            $connector_model->site_number = $site_number;
            self::fillConnectorModel($connector_model, $connector);
            $connector_model->save();
            $connector_model_list->push($connector_model);

            // 记录已处理的充电枪id
            $connector_id_number_list[$connector_model->connector_id] = $connector_model->connector_number;
            // 移除已处理的form数据
            unset($connector_list[$index]);
        }
        // 第二次循环处理其他充电枪
        foreach ($connector_list as $connector) {
            $connector_number = $model->charge_point_number . self::$delimiter . $connector['connector_number'];
            // 判断$connector是否带有主键id且主键id不在$connector_id_number_list中，如果在$connector_list_db有，直接将对应的模型赋值给$connector_model，否则新建模型
            $connector_model = (isset($connector['connector_id']) && filled($connector['connector_id'])) && !isset($connector_id_number_list[$connector['connector_id']]) ? ($connector_list_db->firstWhere('connector_id', $connector['connector_id']) ?: new Connector) : new Connector;
            /* 通过是否存在id来判断是否是编辑，如果是编辑就为原来的编号，如果是新增就为新的编号，
            并且编号在上一个循环已经筛选过了，如果数据库存在该编号就会被移除掉，所以在这里面的编号不会和之前的冲突 */
            $connector_number = filled($connector_model->connector_id) ? $connector_model->connector_number : $connector_number;
            $connector_model->connector_number = $connector_number;
            $connector_model->charge_point_number = $model->charge_point_number;
            $connector_model->merchant_number = $merchant_number;
            $connector_model->site_number = $site_number;
            self::fillConnectorModel($connector_model, $connector);
            $connector_model->save();
            $connector_model_list->push($connector_model);

            // 记录已处理的充电枪id
            $connector_id_number_list[$connector_model->connector_id] = $connector_number;
            // 移除已处理的form数据
            unset($connector_list[$index]);
        }

        unset($connector_list_db);
        // 删除不存在的充电枪
        $updated_connector_id_list = array_keys($connector_id_number_list);
        Connector::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->where('charge_point_number', $model->charge_point_number)->whereNotIn('connector_id', $updated_connector_id_list)
            ->delete();

        self::sendInitPushByKioskNumberList();

        return redirect()->action(
            [self::class, 'showPage'],
            self::getUrlParams($request)
        );
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function rules(?Request $request, ?Model $model): array
    {
        $module_name = self::$module_name;
        $site_number = $model->site_number;
        if ((isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) && blank($model?->charge_point_id)) {
            // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下就直接获取页面传入的场地编号
            $site_number = $request->input('site_number');
        } else if (blank($model?->charge_point_id) && auth()->user()->site_number_list->count() === 1) {
            // 如果是新增并且管理员场地只有一个就直接赋值
            $site_number = auth()->user()->site_number_list->first();
        }

        $rules = array(
            'name' => 'required|max:25',
            'vendor' => 'required|max:45',
            'com_server_number' => 'required|exists:App\Models\Modules\ComServer,com_server_number',
            'charge_point_setting_number' => [
                'required',
                'exists:App\Models\Modules\ChargePointSetting,charge_point_setting_number',
                function ($attr, $value, $fail) {
                    // 判断选择的充电机组是否为当前场地下的
                    $charge_point_setting = ChargePointSetting::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('charge_point_setting_number', $value);
                    if (!filled($charge_point_setting)) $fail(__('common.text_not_found', ['field' => 'Charger Group']));
                },
                function ($attr, $value, $fail) use ($site_number) {
                    // 如果存在site判断是否场地编号一致
                    if (filled($site_number)) {
                        $charge_point_setting = ChargePointSetting::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('charge_point_setting_number', $value);
                        if ($site_number != $charge_point_setting?->site_number) $fail(__('common.site_inconsistent_with_field', ['field' => 'Charger Group']));
                    }
                },
            ],
            'firmware_version' => 'nullable|max:45',
            'model' => 'nullable|max:45',
            'ip_address' => 'nullable|max:45|ip',
            'sort_order' => 'integer|min:0',
            'remark' => 'nullable|max:1000',
            'connector' => 'required|array',
        );
        // 预先给默认值
        $vendor = $model->vendor;
        // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下才需要判断场地编号是否存在
        if (blank($model->charge_point_id)) {
            // 新增时候才考虑供应商
            $vendor = $request->input('vendor', $model->vendor);
            $rules['charge_point_number'] = [
                'required',
                function ($attribute, $value, $fail) use ($vendor, $model, $module_name) {
                    if (ChargePoint::where('charge_point_number', $vendor . self::$delimiter . $value)->exists()) {
                        $fail(__("$module_name.charge_point_number_already"));
                    }
                },
                'max:20',
                'regex:/^[a-zA-Z0-9]+$/', // 只能填写字母和数字
            ];
            if (isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) {
                $rules['site_number'] = [
                    'required',
                    'exists:App\Models\Modules\Site,site_number',
                    function ($attr, $value, $fail) use ($module_name) {
                        // 判断选择的site是否包含在当前角色的场地内
                        $site = Site::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('site_number', $value);
                        if (blank($site)) $fail(__('common.text_not_found', ['field' => __("$module_name.site")]));
                    }
                ];
            };
        }
        // 如果为EVPowerCS时charge_point_cs_number必填
        if (ChargePointVendor::EVPowerCS()->is($vendor)) {
            $rules['charge_point_cs_number'] = [
                'required', 'exists:App\Models\Modules\ChargePointCs,charge_point_cs_number',
                function ($attr, $value, $fail) use ($module_name) {
                    // 判断选择的充电机组是否为当前场地下的
                    $charge_point_cs = ChargePointCS::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('charge_point_cs_number', $value);
                    if (!filled($charge_point_cs)) $fail(__('common.text_not_found', ['field' => __("$module_name.charge_point_cs")]));
                },
                function ($attr, $value, $fail) use ($site_number, $module_name) {
                    // 如果存在site判断是否场地编号一致
                    if (filled($site_number)) {
                        $charge_point_cs = ChargePointCS::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('charge_point_cs_number', $value);
                        if ($site_number != $charge_point_cs?->site_number) $fail(__('common.site_inconsistent_with_field', ['field' => __("$module_name.charge_point_cs")]));
                    }
                }
            ];
        };
        // 充电枪项校验
        $rules['connector.*.connector_number'] = 'required|integer|min:0|max:9|distinct';
        $rules['connector.*.connector_name'] = 'required|max:45';
        $rules['connector.*.connector_setting_number'] = [
            'required',
            'exists:App\Models\Modules\ConnectorSetting,connector_setting_number',
            function ($attr, $value, $fail) use ($site_number, $module_name) {
                // 如果存在site判断是否商户id一致
                if (filled($site_number)) {
                    $connector_setting = ConnectorSetting::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('connector_setting_number', $value);
                    if ($site_number != $connector_setting?->site_number) $fail(__('common.site_inconsistent_with_field', ['field' => __("$module_name.site")]));
                }
            }
        ];
        $rules['connector.*.zone_number'] = [
            'nullable',
            'exists:App\Models\Modules\Zone,zone_number',
            function ($attr, $value, $fail) use ($site_number, $module_name) {
                // 如果存在site,并且选择了zone_number后需判断是否商户id一致
                if (filled($site_number) && filled($value)) {
                    $zone = Zone::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('zone_number', $value);
                    $site_number = Site::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('site_number', $site_number)?->site_number;
                    if ($site_number != $zone?->site_number) $fail(__('common.site_inconsistent_with_field', ['field' => __("$module_name.site")]));
                }
            }
        ];
        $rules['connector.*.remark'] = 'nullable|min:0|max:1000';

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'charge_point_number' => __("$module_name.charge_point_number"),
            'name' => __("$module_name.name"),
            'vendor' => __("$module_name.vendor"),
            'site_number' => __("$module_name.site"),
            'com_server_number' => __("$module_name.com_server"),
            'charge_point_cs_number' => __("$module_name.charge_point_cs"),
            'model' => __("$module_name.model"),
            'firmware_version' => __("$module_name.firmware_version"),
            'ip_address' => __("$module_name.ip_address"),
            'charge_point_setting_number' => __("$module_name.charge_point_setting"),
            'sort_order' => __("$module_name.sort_order"),
            'remark' => __("$module_name.remark"),

            'connector.*.connector_number' => __("$module_name.connector_number"),
            'connector.*.connector_name' => __("$module_name.connector_name"),
            'connector.*.connector_setting_number' => __("$module_name.connector_setting"),
            'connector.*.zone_number' => __("$module_name.zone"),
            'connector.*.remark' => __("$module_name.remark"),
        ];
    }

    /**
     * 返回地址栏参数
     *
     * @param Request|null $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => 'true',
            'charge_point_number_search' => $request->get('charge_point_number_search'),
            'name_search' => $request->get('name_search'),
            'site_search' => $request->get('site_search'),
        );
    }

    /**
     * 获取充电枪
     *
     * @param Request $request
     * @param $id
     * @return JsonResponse
     */
    public function getConnector(Request $request, $number): JsonResponse
    {
        $module_name = self::$module_name;
        $data = Connector::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('connector_number', $number);
        if (!empty($number) && $data) {
            $this->data = array(
                'connector_id' => $data->connector_id, // 充电枪ID
                'charge_point_number' => $data->charge_point_number ?? '—/—', // 充电桩
                'connector_number' => $data->connector_number, // 充电枪编号
                'name' => $data->name, // 显示名称
                'connector_setting_name' => $data->setting->name ?? '—/—', // 充电枪设置名称
                'status' => ConnectorStatus::getDescription($data->status), // 状态
                'gmt_status' => $data->gmt_status ?? '—/—', // 状态时间
                'error_code' => $data->error_code ?? '—/—', // 错误码
                'info' => $data->info ?? '—/—', // 信息
                'vendor_error_code' => $data->vendor_error_code ?? '—/—', // 供应商错误码
                'current_charge_record_number' => $data->current_charge_record_number ?? '—/—', // 当前充电记录编号
                'sort_order' => $data->sort_order, // 排序
                'remark' => $data->remark ?? '—/—', // 备注
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        } else {
            $this->notFoundData(__("$module_name.connector_number"));
        }
        return $this->returnJson();
    }

    /**
     * 上传
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-11-08
     */
    public function uploadChargePoint(Request $request): JsonResponse
    {
        try {
            $result_keys = array(
                'charge_point_number',
                'name',
                'vendor',
                'com_server_number',
                'charge_point_setting_number',
                'charge_point_cs_number',
                'site_number',
                'charge_point_remark',
                'sort_order',
                'connector_number',
                'connector_name',
                'connector_setting_number',
                'zone_number',
                'connector_remark',
            );
            // 获取文件数据
            $format_result = $this->_importDataAndKeys($request, $result_keys);
            if ($format_result['code'] != 200) {
                $this->code = 20001;
                $this->data = null;
                // 提示信息
                $this->message = __('common.import_error_format');
                return $this->returnJson();
            }
            // 获取文件数据结果集
            $format_result_list = $format_result['data'];
            // 移除第一行表头
            array_shift($format_result_list);

            // 是否可上传
            $enable_upload = true;
            // 结果集
            $result = array();
            $vendor = ChargePointVendor::asArray();
            // 当前用户所拥有的场地权限
            $site_array = isSuperAdministrator() ? Site::pluck('site_number')->toArray() : auth()->user()->site_number_list->toArray();
            foreach ($format_result_list as $item) {
                // 校验
                $check_rules = $this->importChargePointRules($item, $result_keys, $vendor, $site_array);
                if (!$check_rules['check_result']) $enable_upload = false;
                $result_item = array(
                    'check_result' => $check_rules['check_result'],
                    'error_message' => $check_rules['error_message'],
                    'charge_point_number' => $item['charge_point_number'] ?? '',
                    'name' => $item['name'] ?? '',
                    'vendor' => $item['vendor'] ?? '',
                    'com_server_number' => $item['com_server_number'] ?? '',
                    'charge_point_setting_number' => $item['charge_point_setting_number'] ?? '',
                    'charge_point_cs_number' => $item['charge_point_cs_number'] ?? '',
                    'site_number' => $item['site_number'] ?? '',
                    'charge_point_remark' => $item['charge_point_remark'] ?? '',
                    'sort_order' => $item['sort_order'] ?? 0,
                    'connector_number' => $item['connector_number'] ?? '',
                    'connector_name' => $item['connector_name'] ?? '',
                    'connector_setting_number' => $item['connector_setting_number'] ?? '',
                    'zone_number' => $item['zone_number'] ?? '',
                    'connector_remark' => $item['connector_remark'] ?? '',
                );
                $result[] = $result_item;
            }

            $this->data = array(
                'result_list' => $result,
                'enable_upload' => $enable_upload,
            );

            return $this->returnJson();
        } catch (Exception) {
            $this->code = 20001;
            $this->data = null;
            // 提示信息
            $this->message = __('common.import_error_format');
            return $this->returnJson();
        }
    }

    /**
     * 导入
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-11-08
     */
    public function importChargePoint(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        // chargePoint列表
        $charge_point_list = $request->input('charge_point_list', array());

        if (empty($charge_point_list)) {
            $this->missingField('Charge Point');
            return $this->returnJson();
        }

        $result_keys = array(
            'charge_point_number',
            'name',
            'vendor',
            'com_server_number',
            'charge_point_setting_number',
            'charge_point_cs_number',
            'site_number',
            'charge_point_remark',
            'sort_order',
            'connector_number',
            'connector_name',
            'connector_setting_number',
            'zone_number',
            'connector_remark',
        );

        $vendor = ChargePointVendor::asArray();
        // 超过最大充电枪个数的充电机编号
        $charge_point_connector_max = array();
        // 没有场地权限的充电机名称数组
        $no_site_charge_point_authority = array();
        // 当前用户所拥有的场地权限
        $site_array = isSuperAdministrator() ? Site::pluck('site_number')->toArray() : auth()->user()->site_number_list->toArray();
        // 当前用户所拥有的场地编号和商户编号，用于后面直接获取对应场地的商户编号而不需要多次查询数据库
        $site_merchant_map = array();
        if (isSuperAdministrator()) {
            $site_merchant_map = Site::pluck('merchant_number', 'site_number');
        } else {
            auth()->user()->loadMissing('roles.site');
            foreach (auth()->user()->roles as $role) {
                foreach ($role->site as $site) {
                    $site_merchant_map[$site->site_number] = $site->merchant_number;
                }
            }
        }

        foreach ($charge_point_list as $item) {
            $check_rules = $this->importChargePointRules($item, $result_keys, $vendor, $site_array);
            if (!$check_rules['check_result']) {
                $this->code = 40001;
                $this->message = __('chargePoint.data_has_modified');
                continue;
            }
            // 存在更新，不存在新增
            $charge_point_number = $item['vendor'] . self::$delimiter . $item['charge_point_number'];
            $charge_point_model = ChargePoint::with('connector')->firstOrNew(['charge_point_number' => $charge_point_number]);
            $is_update_sort_order = false; // 是否更新了充电机的sort_order，如果更新了并且不是新增充电机就要把充电枪的sort_order先更新一遍
            // 如果用firstOrNew的话要用exists，如果用exists()会出现都为true的情况
            if ($charge_point_model->exists) {
                // 如果存在并且充电机场地编号不在权限范围内就不给修改
                if (!in_array($charge_point_model->site_number, $site_array)) {
                    $no_site_charge_point_authority[] = $item['charge_point_number'];
                    continue;
                }
                // 如果存在，并且相关设置被更改为不同场地时就不给修改
                if ($charge_point_model->site_number != $item['site_number']) {
                    $site_no_relevant_setting_data[] = $item['charge_point_number'];
                    continue;
                }
                $is_update_sort_order = $charge_point_model->sort_order != $item['sort_order'] ?? 0;
                // 如果存在充电机就把原先的充电机编号、场地和商户编号赋值
                $merchant_number = $charge_point_model->merchant_number;
                $site_number = $charge_point_model->site_number;
            } else {
                // 如果不存在才可以设置场地和商户编号
                $site_number = $item['site_number'];
                $merchant_number = $site_merchant_map[$item['site_number']];

                $charge_point_model->site_number = $site_number;
                $charge_point_model->merchant_number = $merchant_number;
            }
            if (config('app.is_enable_automatic_encryption_of_license_code')) $charge_point_model->license_code = self::licenseCodeEncryption($charge_point_number);

            $charge_point_model->vendor = $item['vendor'];
            $charge_point_model->charge_point_number = $charge_point_number;
            if (ChargePointVendor::EVPowerCS()->is($charge_point_model->vendor)) $charge_point_model->charge_point_cs_number = $item['charge_point_cs_number'];
            $charge_point_model->name = $item['name'];
            $charge_point_model->com_server_number = $item['com_server_number'];
            $charge_point_model->charge_point_setting_number = $item['charge_point_setting_number'];
            $charge_point_model->remark = $item['charge_point_remark'];
            $charge_point_model->sort_order = $item['sort_order'] ?? 0;
            $charge_point_model->save();

            // 进行排序，无论新增还是编辑都会判断顺序是否在1-3内，保证大体顺序都在1-3范围内
            foreach ($charge_point_model->connector?->sortBy('sort_order')->values() as $index => $connector) {
                $connector_sort_order = $index + 1;
                // 只有按顺序排序不相等和充电机sort_order有改变的情况下才需要保存
                if ($connector_sort_order != $connector->sort_order || $is_update_sort_order) {
                    $connector->sort_order = $charge_point_model->sort_order . $connector_sort_order;
                    $connector->save();
                }
            }

            // 此时查询connector，如果存在覆盖，否则新增
            $connector_number = $charge_point_number . self::$delimiter . $item['connector_number'];
            $connector_model = Connector::firstOrNew([
                'charge_point_number' => $charge_point_model->charge_point_number,
                'connector_number' => $connector_number,
            ]);

            if (!$connector_model->exists) {
                if (($charge_point_model?->connector->count() ?? 0) >= env('CONNECTOR_MAX', 3)) {
                    $charge_point_connector_max[] = $item['charge_point_number'];
                    continue;
                }
                $connector_model->charge_point_number = $charge_point_model->charge_point_number;
                $connector_model->connector_number = $connector_number;
                $connector_model->status = ConnectorStatus::Offline;
                $connector_model->sort_order = $charge_point_model->sort_order . ($charge_point_model?->connector->count() + 1);
                $connector_model->site_number = $site_number;
                $connector_model->merchant_number = $merchant_number;
            }
            $connector_model->name = $item['connector_name'];
            $connector_model->connector_setting_number = $item['connector_setting_number'];
            $connector_model->zone_number = $item['zone_number'];
            $connector_model->remark = $item['connector_remark'];
            $connector_model->save();
        }

        if (!empty($no_site_charge_point_authority)) {
            $this->code = 40002;
            $this->message = __("$module_name.charge_point_number_already_exists") . ': ' . implode(', ', $no_site_charge_point_authority);
        }

        if (!empty($site_no_relevant_setting_data)) {
            $this->code = 40002;
            $this->message = __("$module_name.charge_point_number_no_relevant_setting_data") . ': ' . implode(', ', $site_no_relevant_setting_data);
        }

        if (!empty($charge_point_connector_max)) {
            $this->code = 40002;
            $this->message = __("$module_name.charge_point_connector_max") . ': ' . implode(', ', $charge_point_connector_max);
        }

        self::sendInitPushByKioskNumberList();
        return $this->returnJson();
    }

    public function importChargePointRules($item, $result_keys, $vendor, $site_array): array
    {
        $module_name = self::$module_name;
        if (empty($item) || array_diff($result_keys, array_keys($item))) return array(
            'check_result' => false,
            'error_message' => null,
        );
        // 校验结果
        $check_result = true;
        // 错误信息
        $error_message = null;
        // 如果存在空值不通过 0-4、6索引位置为充电机校验
        $check_charger_item = array_slice($item, 0, 5);
        $check_charger_item[6] = $item['site_number'] ?? null;
        // 9-11索引位置为充电枪校验
        $check_connector_item = array_slice($item, 9, 3);
        // 校验必填
        $check_empty = in_array(null, $check_charger_item, true) ||
            in_array('null', $check_charger_item, true) ||
            in_array(null, $check_connector_item, true) ||
            in_array('null', $check_connector_item, true);
        // 校验是否为一位数字
        $check_numeric = (isset($item['connector_number']) && !empty($item['connector_number']) && !preg_match('/^\d$/', $item['connector_number']));
        // 校验是否为1到20位数字和字母组成的字符串
        $check_charge_point_number = (isset($item['charge_point_number']) && !empty($item['charge_point_number']) && !preg_match('/^[a-zA-Z0-9]{1,20}$/', $item['charge_point_number']));
        // 校验非必填时如果填写是否为数字
        $check_empty_numeric = (!empty($item['sort_order']) && !is_numeric($item['sort_order']));
        // 校验供应商是否为枚举
        $check_enum_vendor = !ChargePointVendor::hasValue($item['vendor']);
        // 如果供应商为EVPowerCS时charge_point_cs_number必填
        $check_vendor_cs = ($item['vendor'] == ChargePointVendor::EVPowerCS && empty($item['charge_point_cs_number']));

        // 判断是否有ComServer
        $check_com_server = (empty($item['com_server_number']) || ComServer::where('com_server_number', $item['com_server_number'])->doesntExist());
        // 判断该场地是否有权限并且存在
        $check_site = !in_array($item['site_number'], $site_array);
        $check_charge_point_setting = $check_connector_setting = $check_zone = false;
        /* 判断该场地下是否存在对应的设置，并且判断仅在有场地编号时才进入判断*/
        if (!$check_site) {
            $check_charge_point_setting = ChargePointSetting::where('site_number', $item['site_number'])->where('charge_point_setting_number', $item['charge_point_setting_number'])->doesntExist();

            $check_connector_setting = (ConnectorSetting::where('site_number', $item['site_number'])->where('connector_setting_number', $item['connector_setting_number'])->doesntExist());

            $check_zone = (filled($item['zone_number']) && Zone::where('site_number', $item['site_number'])->where('zone_number', $item['zone_number'])->doesntExist());
        }
        // 判断当供应商为ChargePointVendor::EVPowerCS时 并且charge_point_cs_number不为空且在拥有的场地权限下数据存在
        $check_charge_point_cs = ($item['vendor'] == ChargePointVendor::EVPowerCS && (empty($item['charge_point_cs_number']) || ChargePointCs::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->where('charge_point_cs_number', $item['charge_point_cs_number'])->doesntExist()));

        // 校验不通过disable
        if (
            $check_empty || $check_numeric || $check_empty_numeric || $check_charge_point_number || $check_enum_vendor || $check_vendor_cs
            || $check_com_server || $check_charge_point_setting || $check_charge_point_cs || $check_site || $check_connector_setting || $check_zone
        ) {
            $check_result = false;
            $error_messages = [];

            if ($check_empty) {
                $error_messages[] = __("$module_name.fill_required_data");
            }
            if ($check_numeric) {
                $error_messages[] = __("$module_name.connector_fill_number_must_one_number");
            }
            if ($check_empty_numeric) {
                $error_messages[] = __("$module_name.fill_number");
            }
            if ($check_charge_point_number) {
                $error_messages[] = __("$module_name.charge_point_number_must_letter_number");
            }
            if ($check_enum_vendor) {
                $error_messages[] = __("$module_name.vendor_does_not_exist");
            }
            if ($check_vendor_cs) {
                $error_messages[] = __("$module_name.vendor_cs_required");
            }
            if ($check_com_server) {
                $error_messages[] = __("$module_name.com_server_does_not_exist");
            }
            if ($check_charge_point_setting) {
                $error_messages[] = __("$module_name.charge_point_setting_does_not_exist");
            }
            if ($check_charge_point_cs) {
                $error_messages[] = __("$module_name.charge_point_cs_does_not_exist");
            }
            if ($check_site) {
                $error_messages[] = __("$module_name.site_does_not_exist");
            }
            if ($check_connector_setting) {
                $error_messages[] = __("$module_name.connector_setting_does_not_exist");
            }
            if ($check_zone) {
                $error_messages[] = __("$module_name.zone_does_not_exist");
            }

            // 将错误消息数组连接成一个字符串，用 <br> 分隔
            $error_message = implode('<br>', $error_messages);
        }

        return array(
            'check_result' => $check_result,
            'error_message' => $error_message
        );
    }

    /**
     * 批量更新license_code
     *
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2023-05-30
     */
    public function updateLicenseCode(): JsonResponse
    {
        if (config('app.is_enable_automatic_encryption_of_license_code')) {
            $model = $this->model;
            $charge_points = $model->all(['charge_point_id', 'charge_point_number'])
                ->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list));
            foreach ($charge_points as $charge_point) {
                $model->where('charge_point_number', $charge_point->charge_point_number)
                    ->update([
                        'license_code' => self::licenseCodeEncryption($charge_point->charge_point_number)
                    ]);
            }
        }

        return $this->returnJson();
    }

    public function sendMessageToChargePoint(Request $request): JsonResponse
    {
        $charge_point_number = $request->input('charge_point_number');
        $message = $request->input('message');

        if (blank($charge_point_number)) {
            $this->missingField('charge_point_number');
            return $this->returnJson();
        }

        $charge_point = ChargePoint::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('charge_point_number', $charge_point_number);
        if (blank($charge_point)) {
            $this->notFoundData('charge_point_number');
            return $this->returnJson();
        }

        $api_url = $charge_point?->comServer?->api_url; // 获取充电桩的API地址

        $params = array(
            'charge_point_number' => $charge_point_number,
            'message' => $message,
        );

        if (filled($api_url)) {
            try {
                $response = $this->curlPost("$api_url/charge/callChargePoint", $params);
                $fmt_response = json_decode(htmlspecialchars_decode($response), true);
                // 失败
                if (empty($fmt_response)) {
                    $this->code = 201;
                    $this->message = $response;
                } else {
                    $this->data = $response;
                }
            } catch (Exception $e) {
                $this->code = 201;
                $this->message = $e->getMessage();
            }
        }

        return $this->returnJson();
    }

    /**
     * 填充充电枪模型
     *
     * @param Connector $connector_model
     * @param array $connector
     * @return Connector
     * @Description
     * @example
     * @date 2023-12-12
     */
    protected static function fillConnectorModel(Connector &$connector_model, array $connector): Connector
    {
        $connector_model->name = $connector['connector_name'] ?? null;
        $connector_model->connector_setting_number = $connector['connector_setting_number'] ?? null;
        $connector_model->zone_number = $connector['zone_number'] ?? null;
        $connector_model->sort_order = $connector['sort_order'] ?? null;
        $connector_model->remark = $connector['remark'] ?? null;

        if (blank($connector_model->connector_id)) {
            $connector_model->status = ConnectorStatus::Offline;
        }

        return $connector_model;
    }
}

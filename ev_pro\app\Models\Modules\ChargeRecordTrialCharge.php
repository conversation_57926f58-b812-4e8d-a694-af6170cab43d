<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;
use App\Models\Traits\Emoji;

class ChargeRecordTrialCharge extends Model
{
    use emoji;

    protected $table = 'charge_record_trial_charge';
    protected $primaryKey = 'charge_record_trial_charge_id';

    const CREATED_AT = 'gmt_create'; //默认是小写的模型类名复数格式，此处为自定义表名
    const UPDATED_AT = 'gmt_modified'; //定义主键，默认为id
    // protected $fillable = [];
    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 类型转化器
     */
    protected $casts = [
        'trial_charge_item_json' => 'array',
    ];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    protected function asJson($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }


    /**
     * 一对一关联充电纪录
     */
    public function chargeRecord()
    {
        return $this->hasOne(ChargeRecord::class, 'charge_record_number', 'charge_record_number');
    }
}

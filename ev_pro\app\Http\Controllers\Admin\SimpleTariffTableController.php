<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Redis;
use Illuminate\Http\{
    JsonResponse,
    RedirectResponse,
    Request,
};
use App\Http\Controllers\Admin\Traits\{
    Edit,
};
use App\Enums\{
    ChargeTariffScheme,
    ChargeValueType,
    IdentityType,
    RemainChargeValueGenerationTrigger,
};
use PHPUnit\Util\Json;
use App\Models\Modules\{
    SimpleTariffTable,
    MemberCardGroup,
    Site,
    UserGroup,
};
use Illuminate\Validation\Rule;

class SimpleTariffTableController extends CommonController
{
    use Edit;

    protected static string $module_name = 'simpleTariffTable'; // 模块名称
    protected static bool $module_check_site = true; // 标记该模块校验site

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new SimpleTariffTable;
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'add_url' => action([self::class, 'add']),
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'charge_tariff_scheme_search' => $request->get('charge_tariff_scheme_search'),
            'site_search' => $request->get('site_search'),
            'name_search' => $request->get('name_search'),
        );

        $data['charge_tariff_scheme_list'] = array();
        foreach (ChargeTariffScheme::asSelectArray() as $value => $name) {
            $data['charge_tariff_scheme_list'][] = array(
                'name' => $name,
                'value' => $value,
            );
        }

        // 场地下拉列表
        $data['site_list'] = $this->getSiteOptionList();

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $length = (int)$request->input('length', 10);
        $charge_tariff_scheme_search = $request->input('charge_tariff_scheme_search');
        $site_search = $request->input('site_search');
        $name_search = $request->input('name_search');

        if ($order == 'site_name') $order = 'site.name_json';

        $data_list = SimpleTariffTable::select('simple_tariff_table.*', 'site.name_json as site_name_json_init')
            ->leftJoin('site', 'simple_tariff_table.site_number', '=', 'site.site_number')
            ->when(filled($charge_tariff_scheme_search), fn ($query) => $query->where('simple_tariff_table.charge_tariff_scheme', $charge_tariff_scheme_search))
            ->when(filled($site_search), fn ($query) => $query->where('simple_tariff_table.site_number', $site_search))
            ->when(filled($name_search), fn ($query) => $query->where('simple_tariff_table.name', 'like', "%$name_search%"))
            ->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('simple_tariff_table.site_number', auth()->user()->site_number_list))
            ->orderBy($order, $sort)
            ->latest('gmt_modified')
            ->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $charge_value_interval = $data->charge_value_interval ?? 0;
            $pre_paid_charge_value_maximum_selection = $data->pre_paid_charge_value_maximum_selection ?? 0;
            $remain_charge_value_minimum_limit = $data->remain_charge_value_minimum_limit ?? 0;
            [$charge_value_interval, $pre_paid_charge_value_maximum_selection, $remain_charge_value_minimum_limit] = match ($data->charge_value_type) {
                ChargeValueType::Time => [
                    round($charge_value_interval / 60) . __('common.unit_mins'),
                    round($pre_paid_charge_value_maximum_selection / 60) . __('common.unit_mins'),
                    round($remain_charge_value_minimum_limit / 60) . __('common.unit_mins'),
                ],
                ChargeValueType::Energy => [
                    (float)bcdiv($charge_value_interval, 1000, 3) . __('common.unit_kwh'),
                    (float)bcdiv($pre_paid_charge_value_maximum_selection, 1000, 3) . __('common.unit_kwh'),
                    (float)bcdiv($remain_charge_value_minimum_limit, 1000, 3), __('common.unit_kwh'),
                ],
                default => [
                    $charge_value_interval,
                    $pre_paid_charge_value_maximum_selection,
                    $remain_charge_value_minimum_limit,
                ],
            };
            $post_paid_identity_type_list = $data->post_paid_identity_type_list ?? '';
            // 将，分隔的字符串转为数组
            $post_paid_identity_type_list = explode(',', $post_paid_identity_type_list);
            // 将数组中的元素转换为对应的描述
            $post_paid_identity_type_list = implode(',', array_map(fn ($item) => IdentityType::getDescription($item), $post_paid_identity_type_list));
            $result[] = array(
                'simple_tariff_table_id' => $data->simple_tariff_table_id, // 簡單收費ID
                'simple_tariff_table_number' => $data->simple_tariff_table_number, // 簡單收費編號
                'name' => $data->name,
                'site_name' => $this->getValueFromLanguageArray($data->site_name_json_init) ?? '—/—', // 場地名称
                'charge_tariff_scheme' => ChargeTariffScheme::getDescription($data->charge_tariff_scheme), // 收费方案
                'charge_value_type' => ChargeValueType::getDescription($data->charge_value_type), // 充电量类型
                'charge_value_interval' => $charge_value_interval, // 充电量间隔
                'rate' => __('common.unit_hk') . (float)bcdiv($data->rate, 100, 1), // 费率
                'pre_paid_charge_value_maximum_selection' => $pre_paid_charge_value_maximum_selection, // 预付充电量最大选择
                'post_paid_identity_type_list' => $post_paid_identity_type_list, // 后付身份类型
                'post_paid_maximum_charge_time' => ($data->post_paid_maximum_charge_time / 60) . __('common.unit_mins'), // 后付最大充电时间
                'is_enable_admin_octopus_card_free_deduct' => $data->is_enable_admin_octopus_card_free_deduct, // 是否开启管理员八达通卡免费扣款
                'is_enable_free_octopus_card' => $data->is_enable_free_octopus_card, // 是否启用免费八达通卡
                'is_enable_top_up' => $data->is_enable_top_up, // 是否启用续充
                'top_up_buffer_limit' => ($data->top_up_buffer_limit ?? 0) . __('common.unit_second'), // 续充缓冲限制
                'is_top_up_need_confirm_identity' => $data->is_top_up_need_confirm_identity, // 是否续充需要确认身份
                'remain_charge_value_generation_trigger' => RemainChargeValueGenerationTrigger::getDescription($data->remain_charge_value_generation_trigger), // 剩余充电量生成触发条件
                'remain_charge_value_validity_period' => ($data->remain_charge_value_validity_period / 60) . __('common.unit_mins'), // 剩余充电量有效期
                'remain_charge_value_minimum_limit' => $remain_charge_value_minimum_limit, // 剩余充电量最小限制
                'is_enable_use_remain_charge_value' => $data->is_enable_use_remain_charge_value, // 是否允许使用剩余充电量
                'is_enable_charge_value_adjust_selected_base_on_remain' => $data->is_enable_charge_value_adjust_selected_base_on_remain, // 是否启用根据剩余充电量调整已选充电量
                'sort_order' => $data->sort_order, // 排序
                'remark' => $data->remark ?? '—/—', // 备注
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );

        return response()->json($json);
    }

    protected function getForm(Request $request, array $data): View|Application|Factory
    {
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'],
            self::getUrlParams($request),
        );
        $data['memberCardGroupNotCheckDataUrl'] = action([ModalController::class, 'memberCardGroupNotCheckCheckbox']);
        $data['userGroupNotCheckDataUrl'] = action([ModalController::class, 'userGroupNotCheckCheckbox']);

        // 新增时才回显收费表编号和场地编号
        if (blank($data['model']->simple_tariff_table_id)) {
            $data['model']->simple_tariff_table_number = $request->old('simple_tariff_table_number', $data['model']->simple_tariff_table_number); // 简单收费表编号
            $site_number = isSuperAdministrator() || auth()->user()->site_number_list->count() > 1
                ? $request->old('site_number', $data['model']->site_number)
                : (auth()->user()->site_number_list->first() ?? null);
            $site_name = $this->getValueFromLanguageArray(Site::firstWhere('site_number', $site_number)?->name_json);
            $data['model']->site_number = $site_number; // 场地编号
            $data['model']->site_name = $site_name; // 场地名称
        }
        $data['model']->name = $request->old('name', $data['model']->name); // 收费表名称
        // 秒轉為分鐘
        $data['model']->charge_tariff_scheme = $request->old('charge_tariff_scheme', $data['model']->charge_tariff_scheme); // 收费方案
        $data['model']->charge_value_type = $request->old('charge_value_type', $data['model']->charge_value_type); // 充电量类型
        $charge_value_interval = $request->old('charge_value_interval');
        $pre_paid_charge_value_maximum_selection = $request->old('pre_paid_charge_value_maximum_selection');
        $remain_charge_value_minimum_limit = $request->old('remain_charge_value_minimum_limit');
        if (empty($request->old())) {
            [$charge_value_interval, $pre_paid_charge_value_maximum_selection, $remain_charge_value_minimum_limit] = match ($data['model']->charge_value_type) {
                ChargeValueType::Time => [
                    round($data['model']->charge_value_interval / 60),
                    round($data['model']->pre_paid_charge_value_maximum_selection / 60),
                    round($data['model']->remain_charge_value_minimum_limit / 60),
                ],
                ChargeValueType::Energy => [
                    (float)bcdiv($data['model']->charge_value_interval, 1000, 3),
                    (float)bcdiv($data['model']->pre_paid_charge_value_maximum_selection, 1000, 3),
                    (float)bcdiv($data['model']->remain_charge_value_minimum_limit, 1000, 3),
                ],
                default => [
                    $data['model']->charge_value_interval,
                    $pre_paid_charge_value_maximum_selection,
                    $data['model']->remain_charge_value_minimum_limit
                ],
            };
        }

        if ($request->old('user_group_item_list')) {
            $data['user_group_item_list'] = [];
            $user_group_item_list = $request->old('user_group_item_list');
            foreach ($user_group_item_list as $index => $user_group_item) {
                if (isset($user_group_item) && !empty($user_group_item)) {
                    $data['user_group_item_list'][$index] = array(
                        'user_group_id' => $user_group_item['user_group_id'] ?? null,
                        'user_group_name' => $user_group_item['user_group_name'] ?? null,
                        'rate' => $user_group_item['rate'] ?? 0,
                    );
                }
            }
        }

        if ($request->old('member_card_group_item_list')) {
            $data['member_card_group_item_list'] = [];
            $member_card_group_item_list = $request->old('member_card_group_item_list');
            foreach ($member_card_group_item_list as $index => $member_card_group_item) {
                if (isset($member_card_group_item) && !empty($member_card_group_item)) {
                    $data['member_card_group_item_list'][$index] = array(
                        'member_card_group_id' => $member_card_group_item['member_card_group_id'] ?? null,
                        'member_card_group_name' => $member_card_group_item['member_card_group_name'] ?? null,
                        'rate' => $member_card_group_item['rate'] ?? 0,
                    );
                }
            }
        } elseif (isset($data['simple_item_list']) && !empty($data['simple_item_list'])) {
            // 循环获取$data['simple_item_list'], 根据是否存在member_card_group_id赋值member_card_group_item_list
            foreach ($data['simple_item_list'] as $item) {
                if (filled($item['member_card_group_id'])) {
                    $data['member_card_group_item_list'] = [$item];
                } elseif (filled($item['user_group_id'])) {
                    $data['user_group_item_list'] = [$item];
                }
            }
            unset($data['simple_item_list']); // 移除掉simple_item_list这样输出页面数据量少一些
        } elseif (filled($data['model']->item)) {
            $member_card_group_index = 0;
            $user_group_index = 0;
            foreach ($data['model']->item()->get() as $item) {
                if(filled($item->member_card_group_id)){
                    $member_card_group_id = $item->member_card_group_id ?? null;
                    $data['member_card_group_item_list'][$member_card_group_index] = array(
                        'member_card_group_id' => $member_card_group_id,
                        'rate' => (float)bcdiv($item->rate, 100, 1) ?? null,
                    );
                    $member_card_group_name = $this->getValueFromLanguageArray(MemberCardGroup::find($member_card_group_id)?->name_json);
                    $data['member_card_group_item_list'][$member_card_group_index]['member_card_group_name'] = $member_card_group_name ?? '—/—';
                    $member_card_group_index++;
                } elseif(filled($item->user_group_id)) {
                    $user_group_id = $item->user_group_id ?? null;
                    $data['user_group_item_list'][$user_group_index] = array(
                        'user_group_id' => $user_group_id,
                        'rate' => (float)bcdiv($item->rate, 100, 1) ?? null,
                    );
                    $user_group_name = $this->getValueFromLanguageArray(UserGroup::find($user_group_id)?->name_json);
                    $data['user_group_item_list'][$user_group_index]['user_group_name'] = $user_group_name ?? '—/—';
                    $user_group_index++;
                }
                //$index++;
            }
        }


        $data['model']->charge_value_interval = $charge_value_interval; // 充电量间隔
        $data['model']->rate = $request->old('rate', (float)bcdiv($data['model']->rate, 100, 1)); // 费率
        $data['model']->pre_paid_charge_value_maximum_selection = $pre_paid_charge_value_maximum_selection; // 预付充电量最大选择
        $data['model']->post_paid_identity_type_list = $request->old('post_paid_identity_type_list', explode(',', $data['model']->post_paid_identity_type_list ?? '')); // 后付身份类型
        $data['model']->post_paid_maximum_charge_time = $request->old('post_paid_maximum_charge_time', $data['model']->post_paid_maximum_charge_time / 60); // 后付最大充电时间
        $data['model']->is_enable_admin_octopus_card_free_deduct = $request->old('is_enable_admin_octopus_card_free_deduct', $data['model']->is_enable_admin_octopus_card_free_deduct); // 是否开启管理员八达通卡免费扣款
        $data['model']->is_enable_free_octopus_card = $request->old('is_enable_free_octopus_card', $data['model']->is_enable_free_octopus_card); // 是否启用免费八达通卡
        $data['model']->is_enable_top_up = $request->old('is_enable_top_up', $data['model']->is_enable_top_up); // 是否启用续充
        $data['model']->top_up_buffer_limit = $request->old('top_up_buffer_limit', $data['model']->top_up_buffer_limit); // 续充缓冲限制
        $data['model']->is_top_up_need_confirm_identity = $request->old('is_top_up_need_confirm_identity', $data['model']->is_top_up_need_confirm_identity); // 是否续充需要确认身份
        $data['model']->remain_charge_value_generation_trigger = $request->old('remain_charge_value_generation_trigger', $data['model']->remain_charge_value_generation_trigger); // 剩余充电量生成触发器
        $data['model']->remain_charge_value_validity_period = $request->old('remain_charge_value_validity_period', $data['model']->remain_charge_value_validity_period / 60); // 剩余充电量有效期
        $data['model']->remain_charge_value_minimum_limit = $remain_charge_value_minimum_limit; // 剩余充电量最小限制
        $data['model']->is_enable_use_remain_charge_value = $request->old('is_enable_use_remain_charge_value', $data['model']->is_enable_use_remain_charge_value); // 是否允许使用剩余充电量
        $data['model']->is_enable_charge_value_adjust_selected_base_on_remain = $request->old('is_enable_charge_value_adjust_selected_base_on_remain', $data['model']->is_enable_charge_value_adjust_selected_base_on_remain); // 是否启用根据剩余充电量调整已选充电量
        $data['model']->remark = $request->old('remark', $data['model']->remark); // 备注
        $data['model']->sort_order = $request->old('sort_order', $data['model']->sort_order); // 排序

        $data['charge_tariff_scheme_list'] = array();
        foreach (ChargeTariffScheme::asSelectArray() as $value => $name) {
            $data['charge_tariff_scheme_list'][] = array(
                'name' => $name,
                'value' => $value,
            );
        }

        $data['post_paid_identity_type_list'] = array();
        foreach (IdentityType::asSelectArray() as $value => $name) {
            $data['post_paid_identity_type_list'][] = array(
                'name' => $name,
                'value' => $value,
            );
        }

        return view("pages.{$data['module_name']}.form", $data);
    }

    public function add(Request $request): Factory|View|Application|RedirectResponse
    {
        $data = array();

        $model = $this->model;
        // 因为post提交表单和get链接都会传入收费表编号参数，所以用下划线标识该编号是copyToAdd携带的
        if (filled($simple_tariff_table_number = $request->input('_simple_tariff_table_number'))) {
            $model = $model->load('item')->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->where('simple_tariff_table_number', $simple_tariff_table_number)
                ->firstOrFail();

            $data['simple_item_list'] = array();
            $member_card_group_item_list = [];
            $user_group_item_list = [];

            foreach ($model->item as $index => $item) {
                // 当前处理的数据数组
                unset($current_item_list); // 因为是引用变量，此处用unset删除变量
                $current_item_list = null; // 初始化当前处理数据
                if (filled($item->member_card_group_id)) {
                    // 会员卡组
                    $current_item_list = &$member_card_group_item_list[$index];
                } elseif (filled($item->user_group_id)) {
                    // 用户组
                    $current_item_list = &$user_group_item_list[$index];
                }

                if (blank($current_item_list)) {
                    $current_item_list = [
                        'member_card_group_id' => $item->member_card_group_id ?? null,
                        'user_group_id' => $item->user_group_id ?? null,
                        'member_card_group_name' => $this->getValueFromLanguageArray(MemberCardGroup::find($item->member_card_group_id)?->name_json) ?? '—/—',
                        'user_group_name' => $this->getValueFromLanguageArray(UserGroup::find($item->user_group_id)?->name_json) ?? '—/—',
                        'rate' => (float)bcdiv($item->rate, 100, 1) ?? null,
                    ];
                    $data['simple_item_list'][$index] = $current_item_list;
                }
            }
            $data['member_card_group_item_list'] = array_values($member_card_group_item_list);
            $data['user_group_item_list'] = array_values($user_group_item_list);
            $model->simple_tariff_table_id = $model->simple_tariff_table_number = $model->name = null;
        }
        if ($request->isMethod('post')) {
            return $this->modelValidateAndSave($request, $model);
        }

        $data['model'] = $model;
        return $this->getForm($request, $data);
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @param SimpleTariffTable $model
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    protected function modelValidateAndSave(Request $request, SimpleTariffTable $model): RedirectResponse
    {
        // 判断是否是新增
        if (blank($model->simple_tariff_table_id)) {
            $model = $this->model;
        }
        $request->validate(self::rules($request, $model), [], self::attributes());
        // 公共方法定义，因为需要先刪除子表数据，防止sql执行错误时可以回滚
        deleteItemAndSaveModel(function () use ($request, $model) {
            // 防止修改编号导致原本的无法被刪除，所以现在最上面关联刪除
            $model->item()->delete();

            // 新增时才保存收费表、场地和商户编号
            if (blank($model->simple_tariff_table_id)) {
                $model->simple_tariff_table_number = $request->input('simple_tariff_table_number');
                $model->site_number = $request->input('site_number');
                if (filled($model->site_number) && filled($site = Site::firstWhere('site_number', $model->site_number))) {
                    $model->merchant_number = $site->merchant_number;
                }
            }
            $model->name = $request->input('name');
            $model->charge_tariff_scheme = $request->input('charge_tariff_scheme');
            $model->charge_value_type = $request->input('charge_value_type');
            $charge_value_interval = $request->input('charge_value_interval', 0);
            $pre_paid_charge_value_maximum_selection = $request->input('pre_paid_charge_value_maximum_selection', 0);
            $remain_charge_value_minimum_limit = $request->input('remain_charge_value_minimum_limit', 0);
            // 根据类型格式化间隔数字
            [$charge_value_interval, $pre_paid_charge_value_maximum_selection, $remain_charge_value_minimum_limit] = match ($model->charge_value_type) {
                ChargeValueType::Time => [
                    $charge_value_interval * 60,
                    $pre_paid_charge_value_maximum_selection * 60,
                    $remain_charge_value_minimum_limit * 60
                ],
                ChargeValueType::Energy => [
                    $charge_value_interval * 1000,
                    $pre_paid_charge_value_maximum_selection * 1000,
                    $remain_charge_value_minimum_limit * 1000
                ],
                default => [$charge_value_interval, $pre_paid_charge_value_maximum_selection]
            };
            $model->charge_value_interval = $charge_value_interval;
            $model->rate = bcmul($request->input('rate', 0), 100);
            $model->pre_paid_charge_value_maximum_selection = $pre_paid_charge_value_maximum_selection;
            $model->post_paid_identity_type_list = $request->input('post_paid_identity_type_list', array()); // 后付身份类型
            // 将数组转换为字符串
            $model->post_paid_identity_type_list = implode(',', $model->post_paid_identity_type_list);
            $model->post_paid_maximum_charge_time = $request->input('post_paid_maximum_charge_time', 0) * 60;
            $model->is_enable_admin_octopus_card_free_deduct = $request->input('is_enable_admin_octopus_card_free_deduct', 0);
            $model->is_enable_free_octopus_card = $request->input('is_enable_free_octopus_card', 0);
            $model->is_enable_top_up = $request->input('is_enable_top_up', 0);
            $model->top_up_buffer_limit = $request->input('top_up_buffer_limit');
            $model->is_top_up_need_confirm_identity = $request->input('is_top_up_need_confirm_identity', 0);
            $model->remain_charge_value_generation_trigger = $request->input('remain_charge_value_generation_trigger');
            $model->remain_charge_value_validity_period = $request->input('remain_charge_value_validity_period', 0) * 60;
            $model->remain_charge_value_minimum_limit = $remain_charge_value_minimum_limit;
            $model->is_enable_use_remain_charge_value = $request->input('is_enable_use_remain_charge_value', 0);
            $model->is_enable_charge_value_adjust_selected_base_on_remain = $request->input('is_enable_charge_value_adjust_selected_base_on_remain', 0);
            $model->remark = $request->input('remark');
            $model->sort_order = $request->input('sort_order', 0);
            if ($model->charge_tariff_scheme == 'PRE_PAID') {
                $model->post_paid_identity_type_list = null;
                $model->post_paid_maximum_charge_time = 0;
                if (!$model->is_enable_top_up) {
                    $model->top_up_buffer_limit = null;
                    $model->is_top_up_need_confirm_identity = false;
                }
            } else if ($model->charge_tariff_scheme == 'POST_PAID') {
                $model->is_enable_top_up = false;
                $model->is_top_up_need_confirm_identity = false;
                $model->pre_paid_charge_value_maximum_selection = 0;
                $model->remain_charge_value_validity_period = null;
                $model->remain_charge_value_minimum_limit = null;
                $model->is_enable_use_remain_charge_value = false;
                $model->top_up_buffer_limit = null;
            }
            // 获取member_card_group列表
            $member_card_group_item_list = $request->input('member_card_group_item_list', array());
            // 获取user_group列表
            $user_group_item_list = $request->input('user_group_item_list', array());
            $item_list = array_merge($member_card_group_item_list, $user_group_item_list);
            // 处理item
            $item_data_list = [];
            foreach ($item_list as $item_group) {
                $item_data_list[] = [
                    'member_card_group_id' => $item_group['member_card_group_id'] ?? null,
                    'user_group_id' => $item_group['user_group_id'] ?? null,
                    'rate' => (int)bcmul($item_group['rate'], 100),
                ];

            }
            // 会员卡组
            $model->member_card_group_simple_tariff_table_item_json = array_values(collect($item_data_list)
                ->whereNotNull('member_card_group_id')
                ->whereNull('user_group_id')
                ->sortBy('member_card_group_id')
                ->values()
                ->toArray());
            // 用户组
            $model->user_group_simple_tariff_table_item_json = array_values(collect($item_data_list)
                ->whereNotNull('user_group_id')
                ->whereNull('member_card_group_id')
                ->sortBy('user_group_id')
                ->values()
                ->toArray());
            $model->save();
            $model->item()->createMany($item_data_list);
        });
        self::delRedis($model->simple_tariff_table_number);
        self::setConnectorTokenByTariffTable($model);
        self::sendInitPushByKioskNumberList();

        return redirect()->action(
            [self::class, 'showPage'],
            self::getUrlParams($request)
        );
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function rules(?Request $request, ?Model $model): array
    {
        $module_name = self::$module_name;
        $rules = array(
            'name' => 'required|max:45',
            'charge_tariff_scheme' => 'required|enum_value:' . ChargeTariffScheme::class,
            'charge_value_type' => 'required|enum_value:' . ChargeValueType::class,
            // 如果charge_value_type是TIME，格式设置为integer，如果是ENERGY，格式设置为numeric,最多三位小数
            'charge_value_interval' => Rule::when(
                $request->charge_value_type === ChargeValueType::Time,
                'required|integer|max:999999|gt:0',
                'required|numeric|max:999999|gt:0|regex:/^\d{1,6}(\.\d{1,3})?$/'
            ),
            // 如果是预付，启动此项规则，在此基础上，如果charge_value_type是TIME，格式设置为integer，如果是ENERGY，格式设置为numeric,最多三位小数
            'pre_paid_charge_value_maximum_selection' => Rule::when(
                $request->charge_tariff_scheme === ChargeTariffScheme::PrePaid,
                $request->charge_value_type === ChargeValueType::Time ?
                    ('required|integer|min:1|max:999999|multiple_of:' . $request->charge_value_interval) :
                    'required|numeric|min:0.001|max:999999.999|regex:/^\d{1,6}(\.\d{1,3})?$/'
            ),
            'post_paid_identity_type_list' => 'exclude_unless:charge_tariff_scheme,' . ChargeTariffScheme::PostPaid . '|required|array',
            'post_paid_maximum_charge_time' => 'exclude_unless:charge_tariff_scheme,' . ChargeTariffScheme::PostPaid . '|required|integer|min:1|max:999999',
            'is_enable_admin_octopus_card_free_deduct' => 'bool',
            'is_enable_free_octopus_card' => 'bool',
            'is_enable_top_up' => 'bool|exclude_unless:charge_tariff_scheme,' . ChargeTariffScheme::PrePaid,
            'top_up_buffer_limit' => 'exclude_unless:charge_tariff_scheme,' . ChargeTariffScheme::PrePaid . '|exclude_unless:is_enable_top_up,"1"|required|integer|min:0|max:999999',
            'is_top_up_need_confirm_identity' => 'bool|exclude_unless:charge_tariff_scheme,' . ChargeTariffScheme::PrePaid,
            'remain_charge_value_generation_trigger' => 'enum_value:' . RemainChargeValueGenerationTrigger::class,
            // remain_charge_value_generation_trigger有值时，下面两项为必填
            'remain_charge_value_validity_period' => 'required_with:remain_charge_value_generation_trigger|nullable|integer|min:0|max:999999',
            // // 如果charge_value_type是TIME，格式设置为integer，如果是ENERGY，格式设置为numeric,最多三位小数
            'remain_charge_value_minimum_limit' => Rule::when(
                $request->charge_value_type === ChargeValueType::Time && $request->charge_tariff_scheme === ChargeTariffScheme::PrePaid,
                'required_with:remain_charge_value_generation_trigger|nullable|integer|min:0|max:999999',
                'required_with:remain_charge_value_generation_trigger|nullable|numeric|min:0|max:999999.999|regex:/^\d{1,6}(\.\d{1,3})?$/'
            ),
            'is_enable_use_remain_charge_value' => 'exclude_unless:charge_tariff_scheme,' . ChargeTariffScheme::PrePaid . '|bool',
            'is_enable_charge_value_adjust_selected_base_on_remain' => 'exclude_unless:charge_tariff_scheme,' . ChargeTariffScheme::PrePaid . '|bool',
            'sort_order' => 'integer|min:0',
            'remark' => 'nullable|max:1000',
        );

        // 只有新增时才校验收费表和场地编号
        if (blank($model->simple_tariff_table_id)) {
            $rules['simple_tariff_table_number'] = [
                'required',
                'max:30',
                'regex:/^[a-zA-Z0-9]+$/', // 只能填写字母和数字
                'unique:App\Models\Modules\SimpleTariffTable,simple_tariff_table_number',
            ];
            if (isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) {
                $rules['site_number'] = [
                    'required',
                    'exists:App\Models\Modules\Site,site_number',
                    function ($attr, $value, $fail) use ($request, $model, $module_name) {
                        // 新增时校验非超级管理员且未拥有该场地提示错误
                        if (!isSuperAdministrator() && !in_array($value, auth()->user()->site_number_list->toArray())) {
                            $fail(__('common.text_not_found', ['field' => __("$module_name.site")]));
                        }
                    },
                ];
            }
        }

        $rules['rate'] = $rules['member_card_group_item_list.*.rate'] = $rules['user_group_item_list.*.rate'] = ['required', 'numeric', 'min:0', 'max:999999', function ($attribute, $value, $fail) use ($module_name) {
            if (!preg_match("/^\d+(\.\d{0,1})?$/", $value)) {
                $message = __("$module_name.one_decimal_places", [
                    'rate' => $value
                ]);
                $fail($message);
            }
        }];

        // 费率规则
        $rate_rule = function ($attribute, $value, $fail) use ($module_name) {
            if (!preg_match("/^\d+(\.\d{0,1})?$/", $value)) {
                $message = __("$module_name.text_most_one_decimal", [
                    'rate' => $value,
                ]);
                $fail($message);
            }
        };
        // 费率规则数组
        $rate_rule_array = ['required', 'numeric', 'min:0', 'max:999999', $rate_rule];

        $rules['member_card_group_item_list.*.member_card_group_id'] = [
            'exists:App\Models\Modules\MemberCardGroup,member_card_group_id',
            function ($attr, $value, $fail) use ($module_name, $request) {
                // 判断选择的Member Card Group是否为当前场地下的
                $member_card_group = MemberCardGroup::when(
                    !isSuperAdministrator(),
                    fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list),
                    fn ($query) => $query->where('site_number', $request->input('site_number'))
                )
                    ->find($value);
                if (blank($member_card_group)) $fail(__("$module_name.no_such_member_card_group"));
            }
        ];

        // 用户组规则
        $user_group_rule = function ($attribute, $value, $fail) use ($module_name, $request) {
            // 判断选择的User Group是否为当前商户下的
            $site = Site::firstWhere('site_number', $request->input('site_number'));
            $user_group = UserGroup::when(
                !isSuperAdministrator(),
                fn ($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list)
            )
            ->where('merchant_number', $site?->merchant_number)
            ->where('user_group_id', $value)
            ->first();
            if (blank($user_group)) $fail(__("$module_name.no_such_user_group"));
        };
        $rules['user_group_item_list.*.user_group_id'] = [
            'nullable',
            'exists:App\Models\Modules\UserGroup,user_group_id',
            $user_group_rule,
        ];

        $rules['member_card_group_item_list.*.rate'] = $rate_rule_array;
        $rules['user_group_item_list.*.rate'] = $rate_rule_array;

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'simple_tariff_table_number' => __("$module_name.simple_tariff_table_number"),
            'name' => __("$module_name.name"),
            'site_number' => __("$module_name.site"),
            'charge_tariff_scheme' => __("$module_name.charge_tariff_scheme"),
            'charge_value_type' => __("$module_name.charge_value_type"),
            'charge_value_interval' => __("$module_name.charge_value_interval"),
            'rate' => __("$module_name.rate"),
            'is_enable_admin_octopus_card_free_deduct' => __("$module_name.is_enable_admin_octopus_card_free_deduct"),
            'is_enable_free_octopus_card' => __("$module_name.is_enable_free_octopus_card"),
            'pre_paid_charge_value_maximum_selection' => __("$module_name.pre_paid_charge_value_maximum_selection"),
            'post_paid_identity_type_list' => __("$module_name.post_paid_identity_type_list"),
            'post_paid_maximum_charge_time' => __("$module_name.post_paid_maximum_charge_time"),
            'is_enable_top_up' => __("$module_name.is_enable_top_up"),
            'top_up_buffer_limit' => __("$module_name.top_up_buffer_limit"),
            'is_top_up_need_confirm_identity' => __("$module_name.is_top_up_need_confirm_identity"),
            'remain_charge_value_generation_trigger' => __("$module_name.remain_charge_value_generation_trigger"),
            'remain_charge_value_validity_period' => __("$module_name.remain_charge_value_validity_period"),
            'remain_charge_value_minimum_limit' => __("$module_name.remain_charge_value_minimum_limit"),
            'is_enable_use_remain_charge_value' => __("$module_name.is_enable_use_remain_charge_value"),
            'is_enable_charge_value_adjust_selected_base_on_remain' => __("$module_name.is_enable_charge_value_adjust_selected_base_on_remain"),
            'sort_order' => __("$module_name.sort_order"),
            'remark' => __("$module_name.remark"),
            'member_card_group_item_list.*.member_card_group_id' => __("$module_name.member_card_group"),
            'member_card_group_item_list.*.rate' => __("$module_name.rate"),
            'user_group_item_list.*.user_group_id' => __("$module_name.user_group"),
            'user_group_item_list.*.rate' => __("$module_name.rate"),
        ];
    }

    /**
     * 返回地址栏参数
     *
     * @param Request|null $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => 'true',
            'charge_tariff_scheme_search' => $request->get('charge_tariff_scheme_search'),
            'site_search' => $request->get('site_search'),
            'name_search' => $request->get('name_search'),
        );
    }

    public function delete(Request $request): JsonResponse
    {
        $simple_tariff_table_number = $request->input('simple_tariff_table_number');
        $module_name = self::$module_name;

        if (
            filled($simple_tariff_table_number) &&
            filled($simple_tariff_table = SimpleTariffTable::with('connectorSetting')
                ->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->firstWhere('simple_tariff_table_number', $simple_tariff_table_number))
        ) {
            if (blank($connector_setting_list = $simple_tariff_table->connectorSetting)) {
                $simple_tariff_table->item()->delete();
                $simple_tariff_table->delete();
                self::delRedis($simple_tariff_table_number);
                self::sendInitPushByKioskNumberList();
            } else {
                $connector_setting_str = '';
                foreach ($connector_setting_list as $connector_setting) {
                    $connector_setting_str .= '<li>' . $connector_setting->name . '</li>';
                }
                $this->code = 201;
                $this->message = __('common.error_has_binding_unable_to_delete_tariff_table', [
                    'connector_setting' => $connector_setting_str
                ]);
            }
        } else {
            $this->notFoundData(__("$module_name.web_title"));
        }

        return $this->returnJson();
    }

    public static function delRedis($simple_tariff_table_number): void
    {
        foreach (['defaultFee', 'memberCardGroupFee', 'userGroupFee'] as $type) {
            foreach (config('languages') as $language_code => $language) {
                Redis::del("simpleTariffTable:$simple_tariff_table_number:$type:$language_code");
            }
            Redis::del("simpleTariffTable:$simple_tariff_table_number:$type:en_US_zh_HK");
        }

    }
}

<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Exception;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Application;
use Illuminate\Http\{
    JsonResponse,
    RedirectResponse,
    Request,
};
use App\Http\Controllers\Admin\Traits\{
    Add,
    Edit,
};
use App\Models\Modules\{
    AppUser,
    Merchant,
    Region,
    RegionDescription,
};
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\App;

class RegionController extends CommonController
{
    use Add, Edit;

    protected static string $module_name = 'region'; // 模块名称

    protected static bool $module_check_merchant = true; // 该模块校验商户

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new Region;
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'add_url' => action([self::class, 'add']),
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'merchant_search' => $request->get('merchant_search'),
            'name_search' => $request->get('name_search'),
            'region_search' => $request->get('region_search'),
        );

        // 商户下拉列表
        $data['merchant_list'] = $this->getMerchantOptionList();
        // 地区下拉列表
        $data['region_list'] = $this->getRegionList();

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $length = (int)$request->input('length', 10);
        $search = $this->_searchList($request);
        $data_list = $search->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $merchant_name = $this->getValueFromLanguageArray($data->merchant?->name_json) ?? '—/—'; // 商户名称
            $name = $this->getValueFromLanguageArray($data->name_json) ?? '—/—'; // 地区名称
            $parent_name = $this->getValueFromLanguageArray($data?->parent?->name_json) ?? '—/—'; // 上级地区名称
            $result[] = array(
                'region_id' => $data->region_id, // 地区ID
                'name' => $name, // 地区名称
                'merchant_name' => $merchant_name, // 商户名称
                'parent_name' => $parent_name, // 上级地区名称
                'sort_order' => $data->sort_order, // 排序
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );

        return response()->json($json);
    }

    protected function getForm(Request $request, array $data): View|Application|Factory
    {
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'],
            self::getUrlParams($request),
        );
        $current_merchant_number_list = auth()->user()->merchant_number_list;

        $merchant_number = $data['model']->merchant_number;
        if ((isSuperAdministrator() || $current_merchant_number_list->count() > 1) && blank($data['model']?->region_id)) {
            // 如果是新增，并且是超级管理员或者管理员的商户大于1的情况下就直接获取页面传入的商户编号
            $merchant_number = $request->old('merchant_number', $data['model']->merchant_number);
        } else if (blank($data['model']?->region_id) && $current_merchant_number_list->count() === 1) {
            // 如果是新增并且管理员商户只有一个就直接赋值
            $merchant_number = $current_merchant_number_list->first();
        }
        $data['model']->merchant_number = $merchant_number; // 商户编号
        $merchant_name = $this->getValueFromLanguageArray(
            Merchant::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('merchant_number', $current_merchant_number_list))
            ->firstWhere('merchant_number', $merchant_number)?->name_json);
        $data['model']->merchant_name = $merchant_name; // 商户名称
        $data['model']->parent_id = $request->old('parent_id', $data['model']->parent_id); // 上级地区ID
        $parent_region_name = $this->getValueFromLanguageArray(
            Region::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('merchant_number', $current_merchant_number_list))
            ->firstWhere('region_id', $data['model']->parent_id)?->name_json);
        $data['model']->parent_region_name = $parent_region_name; // 上级地区名称
        $data['model']->sort_order = $request->old('sort_order', $data['model']->sort_order); // 排序

        foreach (config('languages') as $language_code => $language_name) {
            // 有旧数据
            $region_description_old_list = $request->old('item');
            if (filled($region_description_old_list)) {
                $data['item'][$language_code] = array(
                    'language_name' => $language_name,
                    'language_code' => $language_code,
                    'region_description_id' => $region_description_old_list[$language_code]['region_description_id'] ?? null, // ID
                    'name' => $region_description_old_list[$language_code]['name'] ?? null,
                );
            } else {
                $user_group_description_result = $data['model']->description->where('language_code', $language_code)->first();
                $data['item'][$language_code] = array(
                    'language_name' => $language_name,
                    'language_code' => $language_code,
                    'region_description_id' => $user_group_description_result->region_description_id ?? null, // ID
                    'name' => $user_group_description_result->name ?? null, // 名称
                );
            }
        }


        $data['region_list'] = self::getRegionList($data['model']->merchant_number, $data['model']->region_id); // 地区列表
        return view("pages.{$data['module_name']}.form", $data);
    }

    public function delete(Request $request): JsonResponse
    {
        $id = $request->post('id');
        $module_name = self::$module_name;

        if (blank($id)) {
            $this->missingField('ID');
            return $this->returnJson();
        }

        $region = $this->model::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))->find($id);

        if (blank($region)) {
            $this->notFoundData('ID');
            return $this->returnJson();
        }

        // 判断此地区下是否有子地区
        $is_child_region_exists = Region::where('parent_id', $region->region_id)->exists();
        if ($is_child_region_exists) {
            $this->code = 201;
            $this->message = __("$module_name.error_has_child_region");
            return $this->returnJson();
        }
        // 查询app用户是否使用此地区，并清空
        AppUser::where('region_id', $region->region_id)->update([
            'region_id' => null,
            'region_name_json' => null,
        ]);

        $region->description()->delete();
        $region->delete();

        return $this->returnJson();
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @param Region $model
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    protected function modelValidateAndSave(Request $request, Region $model): RedirectResponse
    {
        $request->validate(self::rules($request, $model), [], self::attributes());
        // 判断是否为新增
        if (blank($model->region_id)) {
            $model = $this->model;
            $merchant_number = $model->merchant_number;

            if ((isSuperAdministrator() || auth()->user()->merchant_number_list->count() > 1)) {
                // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下就直接获取页面传入的场地编号
                $merchant_number = $request->input('merchant_number');
            } else if (auth()->user()->merchant_number_list->count() === 1) {
                // 如果是新增并且管理员场地只有一个就直接赋值
                $merchant_number = auth()->user()->merchant_number_list->first();
            }
            $model->merchant_number = $merchant_number; // 商户编号
        }

        // 获取请求地区描述数据
        $region_description_list = $request->input('item', array());
        $name_json = array();
        foreach ($region_description_list as $region_description) {
            $name_json[$region_description['language_code']] = $region_description['name'];
        }
        if (filled($model->region_id)) {
            // 编辑时更新name_json到app_user
            AppUser::where('region_id', $model->region_id)->update(['region_name_json'=> json($name_json)]);
        }


        $model->name_json = $name_json;
        $model->sort_order = $request->input('sort_order');
        $model->save();

        foreach ($region_description_list as $region_description) {
            if (isset($region_description['region_description_id'])) {
                $region_description_model =
                    RegionDescription::findOr($region_description['region_description_id'], fn () => new RegionDescription);
            } else {
                $region_description_model = new RegionDescription;
            }
            $region_description_model->region_id = $model->region_id;
            $region_description_model->language_code = $region_description['language_code'];
            $region_description_model->name = $region_description['name'];

            $region_description_model->save();
        }

        #TODO: 修改user模块的name_json

        return redirect()->action(
            [self::class, 'showPage'],
            self::getUrlParams($request)
        );
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function rules(?Request $request, ?Model $model): array
    {
        $module_name = self::$module_name;

        $rules = array(
            'parent_id' => [
                'nullable',
                'exists:App\Models\Modules\Region,region_id',
                function ($attr, $value, $fail) use ($request, $module_name) {
                    $region = Region::find($value);
                    if ($region->merchant_number != $request->merchant_number) {
                        $fail(__("$module_name.error_parent_region_merchant_number_inconsistent"));
                    }
                },
            ],
            'sort_order' => 'integer|min:0',
            'item.*.name' => 'required|max:45'
        );

        // 如果是新增，并且是超级管理员或者管理员的商户大于1的情况下才需要判断商户编号是否存在
        if (blank($model?->region_id)) {
            if ((isSuperAdministrator() || auth()->user()->merchant_number_list->count() > 1)) {
                $rules['merchant_number'] = [
                    'required',
                    'exists:App\Models\Modules\Merchant,merchant_number',
                    function ($attr, $value, $fail) use ($module_name, $request) {
                        // 判断选择的merchant是否为当前管理员权限下的
                        $merchant = Merchant::when(!isSuperAdministrator(), fn($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))->firstWhere('merchant_number', $value);
                        if (blank($merchant)) $fail(__('common.text_not_found', ['field' => __("$module_name.merchant")]));
                    },
                ];
            }
        }

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'merchant_number' => __("$module_name.merchant"),
            'parent_id' => __("$module_name.parent_id"),
            'sort_order' => __("$module_name.sort_order"),
            'item.*.name' => __("$module_name.name"),
        ];
    }

    /**
     * 返回地址栏参数
     *
     * @param Request|null $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => 'true',
            'merchant_search' => $request->get('merchant_search'),
            'name_search' => $request->get('name_search'),
            'region_search' => $request->get('region_search'),
        );
    }

    /**
     * 获取搜索数据
     *
     * @param Request $request
     * @return mixed
     * @Description
     * @example
     */
    protected function _searchList(Request $request): mixed
    {
        // dataTable字段
        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $merchant_search = $request->input('merchant_search');
        $name_search = $request->input('name_search');
        $region_search = $request->input('region_search');


        return Region::with(['description', 'merchant', 'parent'])
            ->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))
            ->when(filled($merchant_search), fn ($query) => $query->where('merchant_number', $merchant_search))
            ->when(filled($name_search), fn ($query) => $query->where(function ($query) use ($name_search) {
                return $query->whereHas('description', function ($query) use ($name_search) {
                    return $query->where('name', 'like', "%{$name_search}%");
                });
            }))
            ->when(filled($region_search), fn ($query) => $query->where("parent_id", $region_search))
            ->orderBy($order, $sort)
            ->orderBy('gmt_modified', 'desc');
    }

    /**
     * 获取地区列表，前端插件用
     */
    public static function getRegionList(string $merchant_number = null, $exclude_region_id = null): array
    {
        $region_list = [];
        $is_show_merchant = isSuperAdministrator() || auth()->user()->merchant_number_list->count() > 1;
        $region_list_result = Region::when($is_show_merchant, fn($query) => $query->with('merchant'))
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))
            ->when(filled($merchant_number), fn($query) => $query->where('merchant_number', $merchant_number))
            ->when(filled($exclude_region_id), fn($query) => $query->whereNot('region_id', $exclude_region_id))
            ->whereNull('parent_id')
            ->orderBy('sort_order')
            ->get();

        foreach ($region_list_result as $value) {
            $name = self::getValueFromLanguageArray($value->name_json) ?? '—/—';
            if ($is_show_merchant) {
                $merchant_name = self::getValueFromLanguageArray($value?->merchant?->name_json) ?? '—/—';
                $name .= ' ( ' . $merchant_name . ' )'; // 拼接商户名称
            }
            $region_list[] = array(
                'value' => $value->region_id,
                'name' => $name,
            );
        }

        return $region_list;
    }
}

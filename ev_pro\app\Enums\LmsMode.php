<?php

namespace App\Enums;

use BenSampo\Enum\Enum;
use BenSampo\Enum\Contracts\LocalizedEnum;
use App\Enums\Traits\Tools;

/**
 * @method static NoLms
 * @method static EvenDistribution
 * @method static MaximumOutput
 */
final class LmsMode extends Enum implements LocalizedEnum
{
    use Tools;

    const NoLms = 'NO_LMS';
    const EvenDistribution = 'EVEN_DISTRIBUTION';
    const MaximumOutput = 'MAXIMUM_OUTPUT';
}

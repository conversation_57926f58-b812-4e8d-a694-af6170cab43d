<?php

use Illuminate\Support\Facades\Storage;
use App\Http\Controllers\Common\CommonController;
use Illuminate\Database\Eloquent\Model;


/**
 *把用户输入的文本转义(主要针对特殊符号和emoji表情)
 */
/* function emojiEncode($str)
{
if (!is_string($str)) return $str;
if (!$str || $str == 'undefined') return '';
$text = json_encode($str); //暴露出unicode
$text = preg_replace_callback("/(\\\u[ed][0-9a-f]{3})/i", function ($str) {
return addslashes($str[0]);
}, $text);
return json_decode($text);
} */

/**
 *解码上面的转义
 */
/* function emojiDecode($str)
{
$text = json_encode($str);
$text = preg_replace_callback('/\\\\\\\\/i', function ($str) {
return '\\';
}, $text);
return json_decode($text);
} */

// 数组转json, 中文不编码为unicode
function json($data)
{
    return json_encode($data, JSON_UNESCAPED_UNICODE);
}

/**
 * 获取文件路径
 * @param string $file
 * @param string $type
 * @return string
 */
function getFileUrl($file, $type): ?string
{
    if (empty($file)) {
        if (!$type)
            $type = 'file';
        return existsImage('icon', 'not_select_' . $type . '.png');
    }
    $file_name = explode('/', $file);
    $file_name = end($file_name);
    $file_name_arr = explode('.', $file_name);
    $file_format = end($file_name_arr);
    $file_format = strtolower($file_format); // 獲取文件後綴名

    $format_list = json_decode(file_get_contents(base_path() . '/format.json'), true);

    if (in_array($file_format, $format_list['image'])) { // 圖片格式，返回地址預覽
        $pic = existsImage('public', $file);
        if (!$pic) {
            if (!$type)
                $type = 'image';
            $pic = existsImage('icon', 'not_select_' . $type . '.png');
        }
    } elseif (Storage::disk('icon')->exists($file_format . '.png')) { // 找後綴對應的圖片
        $pic = existsImage('icon', $file_format . '.png');
    } elseif (in_array($file_format, $format_list['video'])) {
        $pic = existsImage('icon', 'video.png');
    } else {
        $pic = existsImage('icon', 'doc.png');
    }

    return $pic;
}

/**
 * 判断图片是否存在并返回地址
 * @param string $disk
 * @param $url
 * @return string|null
 */
function existsImage(string $disk, $url): ?string
{
    if (empty($disk) || empty($url)) return null;
    if (Storage::disk($disk)->exists($url)) {
        return Storage::disk($disk)->url($url);
    }
    return null;
}

/**
 * 将字符串中的emoji转化为HTML代码，下两个函数为此方法需要用到的函数
 * @param mixed $string
 * @return string
 */
function emojiEncode($string)
{
    $stringBuilder = "";
    $offset = 0;

    if (mb_strlen($string) < 1) {
        return "";
    }

    while ($offset >= 0) {
        $decValue = ordutf8($string, $offset);
        $char = unichr($decValue);

        $htmlEntited = htmlentities($char);
        if ($char != $htmlEntited) {
            $stringBuilder .= $htmlEntited;
        } elseif ($decValue >= 128) {
            $stringBuilder .= "&#" . $decValue . ";";
        } else {
            $stringBuilder .= $char;
        }
    }

    return $stringBuilder;
}

// source - http://php.net/manual/en/function.ord.php#109812
function ordutf8($string, &$offset)
{
    $code = ord(substr($string, $offset, 1));
    if ($code >= 128) { //otherwise 0xxxxxxx
        if ($code < 224)
            $bytesnumber = 2; //110xxxxx
        else if ($code < 240)
            $bytesnumber = 3; //1110xxxx
        else if ($code < 248)
            $bytesnumber = 4; //11110xxx
        $codetemp = $code - 192 - ($bytesnumber > 2 ? 32 : 0) - ($bytesnumber > 3 ? 16 : 0);
        for ($i = 2; $i <= $bytesnumber; $i++) {
            $offset++;
            $code2 = ord(substr($string, $offset, 1)) - 128; //10xxxxxx
            $codetemp = $codetemp * 64 + $code2;
        }
        $code = $codetemp;
    }
    $offset += 1;
    if ($offset >= strlen($string))
        $offset = -1;
    return $code;
}

// source - http://php.net/manual/en/function.chr.php#88611
function unichr($u)
{
    return mb_convert_encoding('&#' . intval($u) . ';', 'UTF-8', 'HTML-ENTITIES');
}

// 判断是否是后台超级管理员
function isSuperAdministrator(): bool
{
    return Auth::user()?->hasRole('Super Administrator') ?? false;
}

// 判断是否可以修改场地
function isHasSite(): bool
{
    return isSuperAdministrator() || blank(auth()->user()->site_number);
}

// 判断是否是商家管理员
function isMerchantAdministrator(): bool
{
    return Auth::user()?->hasRole('Merchant Administrator') ?? false;
}

/**
 * @param $query
 * @param string $site_number_field 查询数据库site字段，默认为site_number
 * @param string $merchant_number_field 查询数据库merchant字段，默认为merchant_number
 * @return mixed
 */
function checkAdministratorPermission($query, string $site_number_field = 'site_number', string $merchant_number_field = 'merchant_number'): mixed
{
    $site_number = auth()->user()->site_number;
    $query->when(
        filled($site_number),
        fn ($query) => $query->where($site_number_field, $site_number),
        fn ($query) => $query->where($merchant_number_field, auth()->user()->merchant_number),
    );
    return $query;
}

/**
 * 从根目录的json文件夹获取json文件的内容
 *
 * @param string $filename
 * @param boolean $is_redis_first // 是否先从redis获取
 * @return array|boolean
 * @Description
 * @example
 * @date 2024-01-05
 */
function getArrayFromJsonFile(string $filename, $is_redis_first = true): array|bool
{
    // 优先从redis获取
    if ($is_redis_first) {
        $array = CommonController::getJsonDataFromRedis($filename);
        if ($array) {
            return $array;
        }
    }
    // 从json文件获取
    $json_file_path = base_path('json/' . $filename . '.json');
    if (!file_exists($json_file_path)) {
        return false;
    }
    $json = file_get_contents($json_file_path) ?: '';
    $array = json_decode($json, true) ?: [];
    // 保存到redis
    if ($is_redis_first) CommonController::saveToRedis($filename, $array);

    return $array;
}

/**
 * 数组转json并保存到文件，格式化，不转义中文
 *
 * @param array $array
 * @param string $filename // 纯文件名，不带路径不带后缀
 * @return void
 * @Description
 * @example
 * @date 2024-01-04
 */
function saveArrayToJSONFile(array $array, string $filename, bool $save_to_redis = true): void
{
    $json = json_encode($array, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    // 根目录下的json文件夹
    $file_path = base_path('json/' . $filename . '.json');
    file_put_contents($file_path, $json);
    if ($save_to_redis) {
        CommonController::saveToRedis($filename, $array);
    }
}

/**
 * 生成32位UUID
 * @return string
 * @Description
 * @example
 * @date 2024-03-06
 */
function generateUUID(): string
{
    $uuid = (string)Str::uuid();
    $uuid32 = str_replace('-', '', $uuid);
    return $uuid32;
}

/**
 * 获取唯一的UUID
 *
 * @param [type] $model 验证的模型
 * @param string $field 验证的字段
 * @param string $uuid_prefix UUID前缀
 * @param string $uuid_suffix UUID后缀
 * @return string
 * @Description
 * @example
 * @date 2024-03-06
 */
function getUUID($model, string $field, $uuid_prefix = '', $uuid_suffix = ''): string
{
    return retry(5, function () use ($model, $field, $uuid_prefix, $uuid_suffix) {
        $uuid = $uuid_prefix . generateUUID() . $uuid_suffix;
        if ($model::where($field, $uuid)->doesntExist()) {
            return $uuid;
        }
    }, 100);
}

/**
 * 生成纯数字的Hash Code
 * @param string $str
 * @return int
 */
function generateHashCode($str): int
{
    return abs(crc32($str));
}

/**
 * 生成唯一的Hash Code（预留）
 *
 * @param [type] $model 验证的模型
 * @param string $field 验证的字段
 * @param string $str 需要生成hash code的字符串
 * @param string $hash_code_prefix Hash Code前缀
 * @param string $hash_code_suffix Hash Code后缀
 * @return int
 * @Description
 * @example
 * @date 2024-03-11
 */
function getHashCode($model, string $field, $str, $hash_code_prefix = '', $hash_code_suffix = ''): int
{
    $hash_code = generateHashCode($str);
    $hash_code = $hash_code_prefix . $hash_code . $hash_code_suffix;
    if ($model::where($field, $hash_code)->doesntExist()) {
        return $hash_code;
    }
    // 防止hash code重复
    return retry(5, function () use ($model, $field, $hash_code) {
        // 拼接一个随机数字(0-9)到hash code后面，再次验证是否存在
        $hash_code .= mt_rand(0, 9);
        if ($model::where($field, $hash_code)->doesntExist()) {
            return $hash_code;
        }
    }, 100);
}

/**
 * 当sql执行失败时可以回滚，因为修改为编号关联，但编号可以被修改，所以需要先用原先的编号删除关联子数据后再去执行后面保存
 *
 * @param callable|null $func
 * @Description
 * @example
 * @date 2024-03-13
 */
function deleteItemAndSaveModel(?callable $func = null)
{
    if ($func !== null) {
        DB::transaction($func, 5);
    }
}

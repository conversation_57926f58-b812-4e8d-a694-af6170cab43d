<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;
use Attribute;

class AppUser extends Model
{

    protected $table = 'user';
    protected $primaryKey = 'user_id';

    const CREATED_AT = 'gmt_create'; //默认是小写的模型类名复数格式，此处为自定义表名
    const UPDATED_AT = 'gmt_modified'; //定义主键，默认为id

    // protected $fillable = [];
    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 类型转化器
     */
    protected $casts = [
        'region_name_json' => 'array',
    ];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [
    ];

    // 追加字段
    protected $appends = [
        'is_password_unset',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    protected function asJson($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    // 自定义一个属性，is_password_unset，用于判断用户是否未设置密码（密码和盐都为空）
    public function getIsPasswordUnsetAttribute()
    {
        return blank($this->password) || blank($this->salt);
    }

    /**
     * 一对多关联积分交易
     */
    public function pointsTransaction()
    {
        return $this->hasMany(PointsTransaction::class, 'user_id', 'user_id');
    }

    /**
     * 一对多关联车辆
     */
    public function vehicle()
    {
        return $this->hasMany(Vehicle::class, 'user_id', 'user_id');
    }

    // 一对多关联会员卡
    public function memberCard()
    {
        return $this->hasMany(MemberCard::class, 'user_id', 'user_id');
    }

    // 一对一关联商户
    public function merchant()
    {
        return $this->hasOne(Merchant::class, 'merchant_number', 'merchant_number');
    }

    // 一对一关联用户组
    public function userGroup()
    {
        return $this->hasOne(UserGroup::class, 'user_group_id', 'user_group_id');
    }

    // 一对多关联用户凭证(user_credential_id表中的user_id)
    public function userCredential()
    {
        return $this->hasMany(UserCredential::class, 'user_id', 'user_id');
    }

    // 一对多关联积分钱包
    public function pointsWallet()
    {
        return $this->hasMany(PointsWallet::class,'user_id', 'user_id');
    }
}

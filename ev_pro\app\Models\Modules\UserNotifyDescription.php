<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;
use App\Models\Traits\Emoji;
use Illuminate\Database\Eloquent\Casts\Attribute;

class UserNotifyDescription extends Model
{
    use emoji;

    protected $table = 'user_notify_description';
    protected $primaryKey = 'user_notify_description_id';

    const CREATED_AT = 'gmt_create'; //默认是小写的模型类名复数格式，此处为自定义表名
    const UPDATED_AT = 'gmt_modified'; //定义主键，默认为id

    public $skipMutators = false; // 是否跳过修改器

    public $fillable = [
        'user_notify_description_id',
        'language_code',
        'title',
        'content',
        'image_url'
    ];
    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 类型转化器
     */
    protected $casts = [

    ];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [

    ];

    /**
     * 获取图片路径
     */
    protected function imageUrl(): Attribute
    {
        return Attribute::make(
            get: fn (?string $value) => !$this->skipMutators ? existsImage('public', $value) : $value,
        );
    }

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * 用户通知-用户关联表
     */
    public function userNotify()
    {
        return $this->hasOne(UserNotify::class, 'user_notify_id', 'user_notify_id');
    }
}

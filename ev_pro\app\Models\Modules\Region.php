<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;
use app\Models\Modules\Permission\Role;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Region extends Model
{
    protected $table = 'region'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'region_id'; //定义主键，默认为id
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    // protected $fillable = [];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [
        'sort_order' => 0, // 排序
    ];

    /**
     * 类型转化器
     */
    protected $casts = [
        'name_json' => 'array',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    protected function asJson($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 一对多关联description
     */
    public function description()
    {
        return $this->hasMany(RegionDescription::class, 'region_id', 'region_id');
    }

    /**
     * 一对一关联商户
     */
    public function merchant()
    {
        return $this->hasOne(Merchant::class, 'merchant_number', 'merchant_number');
    }

    /***
     * 一对一查询自己的父级
     */
    public function parent()
    {
        return $this->hasOne(Region::class, 'region_id', 'parent_id');
    }
}

<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;
use App\Models\Traits\Emoji;

class KioskScreenOperationRecord extends Model
{
    use emoji;

    protected $table = 'kiosk_screen_operation_record'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'kiosk_screen_operation_record_id'; //定义主键，默认为id
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    // protected $fillable = [];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];
}

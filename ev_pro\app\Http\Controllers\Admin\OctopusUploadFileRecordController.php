<?php

namespace App\Http\Controllers\Admin;

use App\Enums\{
    OctopusDataExchangeMode,
    OctopusDataExchangeOperationMode,
};
use App\Http\Controllers\Common\CommonController;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use Illuminate\Foundation\Application;
use Illuminate\Http\{
    JsonResponse,
    Request,
};
use App\Models\Modules\OctopusUploadFileRecord;

class OctopusUploadFileRecordController extends CommonController
{
    protected static string $module_name = 'octopusUploadFileRecord'; // 模块名称
    protected OctopusUploadFileRecord $model;

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new OctopusUploadFileRecord;
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'kiosk_name_search' => $request->get('kiosk_name_search'),
            'octopus_device_number_search' => $request->get('octopus_device_number_search'),
        );

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'kiosk_number');
        $sort = $request->input('sort', 'asc');
        $length = (int)$request->input('length', 10);
        $kiosk_name_search = $request->input('kiosk_name_search');
        $octopus_device_number_search = $request->input('octopus_device_number_search');

        $where = array();

        if (filled($kiosk_name_search)) {
            $where[] = ['octopus_upload_file_record.kiosk_name', 'like', "%$kiosk_name_search%"];
        }
        if (filled($octopus_device_number_search)) {
            $where[] = ['octopus_upload_file_record.octopus_device_number', 'like', "%$octopus_device_number_search%"];
        }

        $data_list = OctopusUploadFileRecord::when(!isSuperAdministrator(), function ($query) {
            return $query->whereHas('kiosk', fn ($query_item) => $query_item->whereIn('site_number', auth()->user()->site_number_list));
        })
            ->where($where)
            ->orderBy($order, $sort)
            ->latest('gmt_create')
            ->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $result[] = array(
                'octopus_upload_file_record_id' => $data->octopus_upload_file_record_id, // 八达通上传文件记录ID
                'kiosk_number' => $data->kiosk_number, // Kiosk编号
                'kiosk_name' => $data->kiosk_name, // Kiosk名称
                'octopus_device_number' => $data->octopus_device_number, // 八达通设备编号
                'octopus_data_exchange_mode' => filled($data->octopus_device_number)
                    ? OctopusDataExchangeMode::getDescription($data->octopus_data_exchange_mode)
                    : '—/—', // 八达通数据交换模式
                'octopus_data_exchange_operation_mode' => filled($data->octopus_device_number)
                    ? OctopusDataExchangeOperationMode::getDescription($data->octopus_data_exchange_operation_mode)
                    : '—/—', // 八达通数据交换操作模式
                'file_url' => $data->file_url, // 文件路径
                'file_path' => existsImage('local', $data->file_url), // 拼接文件路径
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );

        return response()->json($json);
    }
}

<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Contracts\View\{
    Factory,
    View
};
use Illuminate\Foundation\Application;
use Illuminate\Http\{
    JsonResponse,
    Request
};
use App\Enums\{
    PaymentVendor,
    PointsOrderStatus,
};
use App\Models\Modules\{
    PointsOrder,
};

class PointsOrderController extends CommonController
{

    protected static string $module_name = 'pointsOrder'; // 模块名称
    protected PointsOrder $model;

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new PointsOrder;
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $data = [
            'module_name' => self::$module_name,
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'nickname_search' => $request->nickname_search,
            'points_order_number_search' => $request->points_order_number_search,
            'merchant_search' => $request->merchant_search,
            'points_order_status_search' => $request->points_order_status_search,
        ];
        // 商户下拉列表
        $data['merchant_list'] = $this->getMerchantOptionList();

        // 积分订单状态下拉列表
        $data['points_order_status_list'] = [];

        foreach (PointsOrderStatus::asSelectArray() as $value => $name) {
            $data['points_order_status_list'][] = [
                'name' => $name,
                'value' => $value,
            ];
        }

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        $draw = (int)$request->input('draw', 1);
        $length = (int)$request->input('length', 10);
        $search = $this->_searchList($request);
        $data_list = $search->paginate($length, ['*'], 'start');

        $result = [];
        foreach ($data_list as $data) {
            $points_order_history = $data?->pointsOrderHistory ?? [];
            foreach ($points_order_history as &$history) {
                $history['previous_points_order_status'] = PointsOrderStatus::getDescription($history['previous_points_order_status'], '');
                $history['latest_points_order_status'] = PointsOrderStatus::getDescription($history['latest_points_order_status'], '');
                $history->makeVisible(['gmt_create']);
            }

            $result[] = [
                'points_order_id' => $data->points_order_id, // 积分订单ID
                'user_id' => $data->user_id, // 用户ID
                'nickname' => $data->nickname ?? $data->user_id, // 用户昵称
                'avatar' => existsImage('avatar', $data->avatar_url) ?: existsImage('icon', 'not_select_image.png'), // 用户头像
                'points_order_number' => $data->points_order_number, // 积分订单编号
                'merchant_number' => $data->merchant_number, // 商户编号
                'merchant_name' => $this->getValueFromLanguageArray($data?->merchant?->name_json) ?? '—/—', // 商户名称
                'payment_vendor' => PaymentVendor::getDescription($data->payment_vendor), // 支付方式
                'price' => (float)bcdiv($data->price, 100, 1), // 价格
                'points' => (float)bcdiv($data->points, 100, 1), // 积分
                'points_order_status' => PointsOrderStatus::getDescription($data->points_order_status), // 积分订单状态
                'gmt_points_order_status' => $data->gmt_points_order_status, // 积分订单状态时间
                'payment_vendor_unique_id' => $data->payment_vendor_unique_id, // 支付方式唯一ID
                'online_payment_link_url' => $data->online_payment_link_url, // 在线支付链接
                'transaction_id' => $data->transaction_id, // 交易ID
                'payment_method_name' => $data->payment_method_name, // 支付方式名称
                'card_number' => $data->card_number, // 卡号
                'trace_no' => $data->trace_no, // 交易追踪号
                'reference_id' => $data->reference_id, // 参考ID
                'gmt_paid' => $data->gmt_paid, // 支付时间
                'gmt_refunded' => $data->gmt_refunded, // 退款时间
                'refund_detail' => $data->refund_detail, // 退款详情
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
                'points_order_history' => $points_order_history, // 积分订单历史
            ];
        }

        $json = [
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result
        ];
        return response()->json($json);
    }

    /**
     * 返回地址栏参数
     *
     * @param Request $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(Request $request): array
    {
        return array(
            'is_page' => 'true',
            'nickname_search' => $request->get('nickname_search'),
            'points_order_number_search' => $request->get('points_order_number_search'),
            'merchant_search' => $request->get('merchant_search'),
            'points_order_status_search' => $request->get('points_order_status_search'),
        );
    }

    /**
     * 获取搜索数据
     *
     * @param Request $request
     * @return mixed
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-11-08
     */
    protected function _searchList(Request $request): mixed
    {
        // dataTable字段

        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $nickname_search = $request->nickname_search;
        $points_order_number_search = $request->points_order_number_search;
        $merchant_search = $request->merchant_search;
        $points_order_status_search = $request->points_order_status_search;

        return PointsOrder::with([
            'merchant',
            'pointsOrderHistory' => function ($query) {
                $query->orderBy('gmt_create', 'asc');
            }
        ])
            ->select('points_order.*','user.nickname as nickname', 'user.avatar_url as avatar_url')
            ->leftJoin('user', 'points_order.user_id', '=', 'user.user_id')
            ->when(filled($nickname_search), function ($query) use ($nickname_search) {
                $query->where('user.nickname', 'like', "%$nickname_search%");
            })
            ->when(filled($points_order_number_search), function ($query) use ($points_order_number_search) {
                $query->where('points_order_number', 'like', "%$points_order_number_search%");
            })
            ->when(filled($merchant_search), function ($query) use ($merchant_search) {
                $query->where('points_order.merchant_number', $merchant_search);
            })
            ->when(filled($points_order_status_search), function ($query) use ($points_order_status_search) {
                $query->where('points_order_status', $points_order_status_search);
            })
            ->orderBy($order, $sort)
            ->latest();
    }
}

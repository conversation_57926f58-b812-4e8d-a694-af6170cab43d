<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;

class ChargePointCs extends Model
{
    protected $table = 'charge_point_cs'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'charge_point_cs_id'; //定义主键，默认为id
    // 表的唯一number
    public static $table_number = 'charge_point_cs_number';
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    // protected $fillable = [];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [
        'sort_order' => 0, // 排序
    ];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * 一对多关联充电机
     */
    public function chargePoint()
    {
        return $this->hasMany(ChargePoint::class, 'charge_point_cs_number', 'charge_point_cs_number');
    }
}

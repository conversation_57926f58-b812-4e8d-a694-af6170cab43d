<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;

class RemainChargeValueRecord extends Model
{
    protected $table = 'remain_charge_value_record'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'remain_charge_value_record_id'; //定义主键，默认为id
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    // protected $fillable = [];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * 一对一关联充电纪录
     */
    public function chargeRecord()
    {
        return $this->hasOne(ChargeRecord::class, 'charge_record_number', 'charge_record_number');
    }

    /**
     * 一对一关联充电纪录(已用)
     */
    public function usedChargeRecord()
    {
        return $this->hasOne(ChargeRecord::class, 'charge_record_number', 'used_charge_record_number');
    }

}

<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;

class AppVersionDescription extends Model
{
    protected $table = 'app_version_description'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'app_version_description_id'; //定义主键，默认为id
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    // protected $fillable = [];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * 获取相关App版本
     */
    public function appVersion()
    {
        return $this->hasOne(AppVersion::class, 'app_version_id', 'app_version_id');
    }

}

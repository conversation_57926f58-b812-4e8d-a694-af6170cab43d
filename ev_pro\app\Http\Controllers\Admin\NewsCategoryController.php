<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use BenSampo\Enum\Rules\EnumValue;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\{
    JsonResponse,
    RedirectResponse,
    Request,
};
use App\Http\Controllers\Admin\Traits\{
    Add,
    Edit,
};
use Illuminate\Foundation\Application;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use App\Models\Modules\{
    Merchant,
    NewsCategory,
    NewsCategoryDescription,
};
use App\Enums\{
    NewsCategoryTheme,
    NewsCategoryCarouselThemeEnum,
    NewsCategoryViewAllTypeEnum,
};
use Illuminate\Support\Str;

class NewsCategoryController extends CommonController
{
    use Add, Edit;

    protected static string $module_name = 'newsCategory'; // 模块名称
    protected static bool $module_check_merchant = true; // 标记该模块校验商户

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new NewsCategory;
    }

    /**
     * 初始页
     *
     * @param Request $request
     * @return View|Application|Factory
     */
    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'list_url' => action([self::class, 'list']),
            'add_url' => action([self::class, 'add']),
            'news_url' => action([NewsController::class, 'showPage']),
            'show_page_url' => action([self::class, 'showPage']),
            'theme_list' => NewsCategoryTheme::asSelectArray(),
            'theme_search' => $request->get('theme_search'),
            'title_search' => $request->get('title_search'),
            'merchant_search' => $request->get('merchant_search'),
        );

        // 商户下拉列表
        $data['merchant_list'] = $this->getMerchantOptionList();

        return view("pages.{$data['module_name']}.list", $data);
    }

    /**
     * 列表接口
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        $draw = (int)$request->input('draw', 1);
        $length = (int)$request->input('length', 10);
        $search = $this->_searchList($request);
        $data_list = $search->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $merchant_name = $this->getValueFromLanguageArray($data->merchant_name_json_init) ?? '—/—';
            $title = '';
            foreach ($data->description->sortBy('language_code') as $description) {
                $title .= ($description->title ?? '—/—') . '<br>';
            }
            // 去除最后四个字符
            $title = Str::of($title)->substr(0, -4);
            $result[] = array(
                'merchant_name' => $merchant_name, // 商户名称
                'news_category_id' => $data->news_category_id, // ID
                'title' => $title, // 标题
                'theme' => NewsCategoryTheme::getDescription($data->theme), // 主题
                'is_main_page_display_text_content' => $data->is_main_page_display_text_content, // 是否首页显示文本内容
                'main_page_display_news_count' => $data->main_page_display_news_count, // 首页显示新闻数量
                'view_all_theme' => NewsCategoryViewAllTypeEnum::getDescription($data->view_all_theme), // 查看全部主题
                'carousel_theme' => NewsCategoryCarouselThemeEnum::getDescription($data->carousel_theme), // 轮播样式
                'is_show_carousel_indicator' => $data->is_show_carousel_indicator, // 是否显示轮播指示器
                'carousel_auto_play_interval' => $data->carousel_auto_play_interval, // 轮播自动播放间隔
                'is_enable' => $data->is_enable, // 是否启用
                'sort_order' => $data->sort_order, // 排序
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );

        return response()->json($json);
    }

    /**
     * 表单页
     *
     * @param Request $request
     * @param array $data
     * @return View|Application|Factory
     */
    protected function getForm(Request $request, array $data): View|Application|Factory
    {
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'], self::getUrlParams($request)
        );

        // 新增时才回显商户编号
        if (blank($data['model']->news_category_id)) {
            // 超级管理员或者多商户能选商户，否则只取所属的单商户
            $merchant_number = isSuperAdministrator() || auth()->user()->merchant_number_list->count() > 1
                ? $request->old('merchant_number', $data['model']->merchant_number)
                : (auth()->user()->merchant_number_list->first() ?? null);
            $merchant_name = $this->getValueFromLanguageArray(Merchant::firstWhere('merchant_number', $merchant_number)?->name_json);
            $data['model']->merchant_number = $merchant_number; // 商户编号
            $data['model']->merchant_name = $merchant_name; // 商户名称
        }
        $data['model']->theme = $request->old('theme', $data['model']->theme); // 主题
        $data['model']->is_main_page_display_text_content = $request->old('is_main_page_display_text_content', $data['model']->is_main_page_display_text_content); // 是否首页显示文本内容
        $data['model']->main_page_display_news_count = $request->old('main_page_display_news_count', $data['model']->main_page_display_news_count ?: 1); // 首页显示新闻数量
        $data['model']->view_all_theme = $request->old('view_all_theme', $data['model']->view_all_theme); // 查看全部主题
        $data['model']->carousel_theme = $request->old('carousel_theme', $data['model']->carousel_theme); // 轮播样式
        $data['model']->is_show_carousel_indicator = $request->old('is_show_carousel_indicator', $data['model']->is_show_carousel_indicator); // 是否显示轮播指示器
        $data['model']->carousel_auto_play_interval = $request->old('carousel_auto_play_interval', $data['model']->carousel_auto_play_interval); // 轮播自动播放间隔
        $data['model']->sort_order = $request->old('sort_order', $data['model']->sort_order); // 排序
        $data['model']->is_enable = $request->old('is_enable', $data['model']->is_enable); // 是否启用

        // 先加载description关联
        if (filled($data['model']->news_category_id)) $data['model']->load(['description']);
        // 处理description
        $data['description_list'] = [];
        // 多语言
        $language_list = config('languages');
        $data['language_list'] = $language_list;
        // old数据
        $old_description_list = $request->old('description_list');
        foreach ($language_list as $language_code => $language_name) {
            // 先处理old数据
            if (filled($old_description_list)) {
                $old_description = $old_description_list[$language_code];
                $description = new NewsCategoryDescription([
                    'news_category_description_id' => $old_description['news_category_description_id'] ?? null,
                    'news_category_id' => $old_description['news_category_id'] ?? null,
                    'language_code' => $language_code,
                    'title' => $old_description['title'] ?? null,
                ]);
                $data['description_list'][$language_code] = $description;
                continue;
            }
            // 再处理数据库中的数据
            $description = $data['model']?->description->firstWhere('language_code', $language_code);
            if (blank($description)) {
                // 如果数据库中没有数据，则创建一个空的
                $description = new NewsCategoryDescription([
                    'news_category_id' => null,
                    'language_code' => $language_code,
                    'title' => null,
                ]);
            }
            $data['description_list'][$language_code] = $description;
        }

        $data['theme_list'] = NewsCategoryTheme::asSelectArray();
        $data['view_all_theme_list'] = NewsCategoryViewAllTypeEnum::asSelectArray();
        $data['carousel_theme_list'] = NewsCategoryCarouselThemeEnum::asSelectArray();

        return view("pages.{$data['module_name']}.form", $data);
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @param NewsCategory $model
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    protected function modelValidateAndSave(Request $request, NewsCategory $model): RedirectResponse
    {
        $request->validate(self::rules($request, $model), [], self::attributes());
        // 新增时才保存商户编号
        if (blank($model->news_category_id)) {
            $model->merchant_number = $request->input('merchant_number');
        }
        $model->theme = $request->theme;
        $model->is_main_page_display_text_content = $request->boolean('is_main_page_display_text_content');
        $model->main_page_display_news_count = $request->main_page_display_news_count;
        $model->view_all_theme = $request->view_all_theme;
        if ($model->theme === NewsCategoryTheme::Carousel) {
            $model->carousel_theme = $request->carousel_theme;
            $model->is_show_carousel_indicator = $request->boolean('is_show_carousel_indicator');
            $model->carousel_auto_play_interval = $request->carousel_auto_play_interval;
        } else {
            $model->carousel_theme = null;
            $model->is_show_carousel_indicator = 0;
            $model->carousel_auto_play_interval = null;
        }
        $model->sort_order = $request->input('sort_order', 0);
        $model->is_enable = $request->boolean('is_enable');
        $model->save();

        foreach ($request->description_list as $description) {
            if (filled($description['news_category_description_id'])) {
                $model->description()
                    ->where('news_category_description_id', $description['news_category_description_id'])
                    ->update($description);
                continue;
            }
            $model->description()->create($description);
        }

        return redirect()->action(
            [self::class, 'showPage'], self::getUrlParams($request)
        );
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function rules(?Request $request, ?Model $model): array
    {
        $module_name = self::$module_name;
        // 多语言
        $language_list = config('languages');

        $rules = array(
            'theme' => [
                'required',
                new EnumValue(NewsCategoryTheme::class)
            ],
            'is_main_page_display_text_content' => 'nullable|boolean',
            'main_page_display_news_count' => 'required|numeric|min:1|max:99',
            'view_all_theme' => [
                'nullable',
                new EnumValue(NewsCategoryViewAllTypeEnum::class)
            ],
            // 如果theme是轮播，则此项必填且在枚举中
            'carousel_theme' => [
                'nullable',
                'required_if:theme,' . NewsCategoryTheme::Carousel,
                new EnumValue(NewsCategoryCarouselThemeEnum::class)
            ],
            'is_show_carousel_indicator' => [
                'nullable',
                'boolean',
            ],
            'carousel_auto_play_interval' => [
                'nullable',
                'required_if:theme,' . NewsCategoryTheme::Carousel,
                'integer',
                'min:1'
            ],
            'sort_order' => 'integer|min:0',
            'is_enable' => 'nullable|boolean',
            'description_list' => 'required|array',
            'description_list.*.language_code' => 'required|string|in:' . implode(',', array_keys($language_list)),
            // title 最大45个字符
            'description_list.*.title' => 'nullable|string|max:45',
        );

        // 只有新增时才校验商户编号
        if (blank($model->news_category_id)) {
            if (isSuperAdministrator() || auth()->user()->merchant_number_list->count() > 1) {
                $rules['merchant_number'] = [
                    'required',
                    'exists:App\Models\Modules\Merchant,merchant_number',
                    function ($attr, $value, $fail) use ($request, $model, $module_name) {
                        // 新增时校验非超级管理员且未拥有该商户提示错误
                        if (!isSuperAdministrator() && !in_array($value, auth()->user()->merchant_number_list->toArray())) {
                            $fail(__('common.text_not_found', ['field' => __("$module_name.merchant")]));
                        }
                    },
                ];
            }
        }

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'merchant_number' => __("$module_name.merchant"),
            'theme' => __("$module_name.theme"),
            'is_main_page_display_text_content' => __("$module_name.is_main_page_display_text_content"),
            'main_page_display_news_count' => __("$module_name.main_page_display_news_count"),
            'view_all_theme' => __("$module_name.view_all_theme"),
            'carousel_theme' => __("$module_name.carousel_theme"),
            'is_show_carousel_indicator' => __("$module_name.is_show_carousel_indicator"),
            'carousel_auto_play_interval' => __("$module_name.carousel_auto_play_interval"),
            'sort_order' => __("$module_name.sort_order"),
            'is_enable' => __("$module_name.is_enable"),
            'description_list' => __('common.text_description'),
            'description_list.*.title' => __("$module_name.title"),
        ];
    }

    public function delete(Request $request): JsonResponse
    {
        $news_category_id = $request->post('news_category_id');
        $module_name = self::$module_name;

        if (blank($news_category_id) ||
            blank($model = $this->model::when(!isSuperAdministrator(), fn($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))
                ->find($news_category_id))
        ) {
            $this->notFoundData(__("$module_name.web_title"));
            return $this->returnJson();
        }

        // 删除和 news 表多对多关联的关系数据
        $model->news()->detach();
        $model->description()->delete();
        $model->delete();

        return $this->returnJson();
    }

    /**
     * 返回地址栏参数
     *
     * @param Request|null $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => 'true',
            'theme_search' => $request->get('theme_search'),
            'title_search' => $request->get('title_search'),
            'merchant_search' => $request->get('merchant_search'),
        );
    }

    /**
     * 获取搜索数据
     *
     * @param Request $request
     * @return mixed
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-11-08
     */
    protected function _searchList(Request $request): mixed
    {
        // dataTable字段
        $order = $request->input('order', 'sort_order');
        $sort = $request->input('sort', 'asc');
        $theme_search = $request->theme_search;
        $title_search = $request->title_search;
        $merchant_search = $request->merchant_search;

        if ($order == 'merchant_name') $order = 'merchant.name_json';

        return NewsCategory::select('news_category.*', 'merchant.name_json as merchant_name_json_init')
            ->leftJoin('merchant', 'news_category.merchant_number', '=', 'merchant.merchant_number')
            ->with(['description'])
            ->when(filled($theme_search), fn($query) => $query->where('theme', $theme_search))
            ->when(filled($title_search), fn($query) => $query->whereHas('description', fn($q) => $q->where('title', 'like', "%$title_search%")))
            ->when(filled($merchant_search), fn($query) => $query->where('merchant.merchant_number', $merchant_search))
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('merchant.merchant_number', auth()->user()->merchant_number_list))
            ->orderBy($order, $sort)
            ->latest('gmt_create')
            ->latest('gmt_modified')
            ->latest('news_category_id');
    }
}

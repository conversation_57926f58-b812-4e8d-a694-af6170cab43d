<?php

namespace App\Http\Controllers\Admin;

use BenSampo\Enum\Rules\EnumValue;
use App\Http\Controllers\Common\CommonController;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\{
    JsonResponse,
    RedirectResponse,
    Request,
};
use App\Http\Controllers\Admin\Traits\{
    Add,
    Edit,
};
use Illuminate\Foundation\Application;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use App\Models\Modules\{
    News,
    NewsDescription,
    NewsCategory,
    Merchant,
};
use App\Enums\{
    ContentType,
};
use Illuminate\Support\Str;

class NewsController extends CommonController
{
    use Add, Edit;

    protected static string $module_name = 'news'; // 模块名称
    protected static bool $module_check_merchant = true; // 标记该模块校验商户

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new News;
    }

    /**
     * 初始页
     *
     * @param Request $request
     * @return View|Application|Factory
     */
    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'list_url' => action([self::class, 'list']),
            'add_url' => action([self::class, 'add']),
            'show_page_url' => action([self::class, 'showPage']),
            'news_category_list' => $this->getNewsCategoryList(),
            'news_category_search' => $request->news_category_search,
            'gmt_release_search' => $request->gmt_release_search,
            'title_search' => $request->title_search,
            'content_search' => $request->content_search,
        );
        // 多语言
        $language_list = config('languages');
        $data['language_list'] = $language_list;

        return view("pages.{$data['module_name']}.list", $data);
    }

    /**
     * 列表接口
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        $draw = (int)$request->input('draw', 1);
        $length = (int)$request->input('length', 10);
        $search = $this->_searchList($request);
        $data_list = $search->paginate($length, ['*'], 'start');

        // 获取当前语言值，获取语言值下的name
        $language_code = app()->getLocale();

        $result = array();
        foreach ($data_list as $data) {
            $current_language_description = $data->description->firstWhere('language_code', $language_code);
            $merchant_name = $this->getValueFromLanguageArray($data->merchant_name_json_init) ?? '—/—';
            $description_list = [];
            foreach ($data->description as $description) {
                $description_list[$description->language_code] = $description;
            }
            $category = '';
            foreach ($data->category as $news_category) {
                $category .= ($news_category->firstDescription->title ?? '—/—') . '<br>';
            }

            // 去除最后的<br>
            $category = Str::of($category)->substr(0, -4);
            $result[] = array(
                'merchant_name' => $merchant_name, // 商户名称
                'content_type' => ContentType::getDescription($data->content_type), // 内容类型
                'news_id' => $data->news_id, // ID
                'category' => $category, // 分类
                'title' => $current_language_description->title ?? '—/—', // 标题
                'image_url' => $current_language_description?->image_url, // 图片地址
                'content' => $current_language_description->content ?? '—/—', // 内容
                'is_main_page_display_text_content' => $data->is_main_page_display_text_content, // 是否首页显示文本内容
                'is_enable' => $data->is_enable, // 是否启用
                'view_count' => $data->view_count, // 浏览量
                'gmt_release' => $data->gmt_release, // 发布时间
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
                'description_list' => $description_list, // 描述列表
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );

        return response()->json($json);
    }

    /**
     * 表单页
     *
     * @param Request $request
     * @param array $data
     * @return View|Application|Factory
     */
    protected function getForm(Request $request, array $data): View|Application|Factory
    {
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'], self::getUrlParams($request)
        );

        // 新增时才回显商户编号
        if (blank($data['model']->news_id)) {
            // 超级管理员或者多商户能选商户，否则只取所属的单商户
            $merchant_number = isSuperAdministrator() || auth()->user()->merchant_number_list->count() > 1
                ? $request->old('merchant_number', $data['model']->merchant_number)
                : (auth()->user()->merchant_number_list->first() ?? null);
            $merchant_name = $this->getValueFromLanguageArray(Merchant::firstWhere('merchant_number', $merchant_number)?->name_json);
            $data['model']->merchant_number = $merchant_number; // 商户编号
            $data['model']->merchant_name = $merchant_name; // 商户名称
        }
        $data['model']->content_type = $request->old('content_type', $data['model']->content_type); // 内容类型
        $data['model']->is_main_page_display_text_content = $request->old('is_main_page_display_text_content', $data['model']->is_main_page_display_text_content); // 是否首页显示文本内容
        $data['model']->is_enable = $request->old('is_enable', $data['model']->is_enable); // 是否启用
        $data['model']->gmt_release = $request->old('gmt_release', $data['model']->gmt_release); // 发布时间

        // 先加载description关联
        if (filled($data['model']->news_id)) $data['model']->load(['description']);
        // 处理description
        $data['description_list'] = [];
        // 多语言
        $language_list = config('languages');
        $data['language_list'] = $language_list;
        // old数据
        $old_description_list = $request->old('description_list');
        foreach ($language_list as $language_code => $language_name) {
            // 先处理old数据
            if (filled($old_description_list)) {
                $old_description = $old_description_list[$language_code];
                $description = array(
                    'news_description_id' => $old_description['news_description_id'] ?? null,
                    'language_code' => $language_code,
                    'title' => $old_description['title'] ?? null,
                    ContentType::Text => $old_description[ContentType::Text] ?? null,
                    ContentType::Html => $old_description[ContentType::Html] ?? null,
                    ContentType::Markdown => $old_description[ContentType::Markdown] ? htmlspecialchars($old_description[ContentType::Markdown], ENT_QUOTES | ENT_HTML5, 'UTF-8', true) : null,
                    ContentType::InAppLink => $old_description[ContentType::InAppLink] ?? null,
                    ContentType::ExternalLink => $old_description[ContentType::ExternalLink] ?? null,
                    'image_url' => $old_description['image_url'] ?? null,
                );
                $data['description_list'][$language_code] = $description;
                continue;
            }
            // 再处理数据库中的数据
            $description = $data['model']?->description->firstWhere('language_code', $language_code);
            if (blank($description)) {
                $description = array(
                    'news_description_id' => null,
                    'language_code' => $language_code,
                    'title' => null,
                    ContentType::Text => null,
                    ContentType::Html => null,
                    ContentType::Markdown => null,
                    ContentType::InAppLink => null,
                    ContentType::ExternalLink => null,
                    'image_url' => null,
                );
            } else {
                $description->skipMutators = true; // 关闭模型的修改器
                $content = $description->content;
                $description = array(
                    'news_description_id' => $description->news_description_id,
                    'language_code' => $language_code,
                    'title' => $description->title,
                    ContentType::Text => null,
                    ContentType::Html => null,
                    ContentType::Markdown => null,
                    ContentType::InAppLink => null,
                    ContentType::ExternalLink => null,
                    'image_url' => $description->image_url,
                );
                $description[$data['model']->content_type] = $content;
            }
            $data['description_list'][$language_code] = $description;
        }

        // 处理category
        // 获取old数据或中间表的news_category_id、sort_order
        $data['category_list'] = $request->old('category_list', $data['model']->category()->select('news_category_to_news.news_category_id', 'news_category_to_news.sort_order')->get()->toArray());
        // 查询分类名称
        $category_list = NewsCategory::with([
            'description' => function ($query) {
                $query->where('language_code', app()->getLocale());
            }
        ])
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))
            // 只查询category_list中有的news_category_id
            ->whereIn('news_category_id', array_column($data['category_list'], 'news_category_id'))
            ->get();
        foreach ($data['category_list'] as &$category) {
            $category_model = $category_list->firstWhere('news_category_id', $category['news_category_id']);
            $category['label'] = $category_model->firstDescription->title ?? '—/—';
            if (isset($category['pivot'])) unset($category['pivot']);
        }

        $data['news_category_url'] = action([self::class, 'getCategoryList']);

        return view("pages.{$data['module_name']}.form", $data);
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @param News $model
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    protected function modelValidateAndSave(Request $request, News $model): RedirectResponse
    {
        $request->validate(self::rules($request, $model), [], self::attributes());
        // 新增时才保存商户编号
        if (blank($model->news_id)) {
            $model->merchant_number = $request->input('merchant_number');
        }
        $model->is_main_page_display_text_content = $request->boolean('is_main_page_display_text_content');
        $model->is_enable = $request->boolean('is_enable');
        $model->content_type = $request->content_type;
        $model->gmt_release = $request->gmt_release;
        $model->save();

        foreach ($request->input('description_list', []) as $description) {
            $content = match ($model->content_type) {
                ContentType::Text => $description[ContentType::Text] ?? null,
                ContentType::Html => $description[ContentType::Html] ?? null,
                ContentType::Markdown => $description[ContentType::Markdown] ?? null,
                ContentType::InAppLink => $description[ContentType::InAppLink] ?? null,
                ContentType::ExternalLink => $description[ContentType::ExternalLink] ?? null,
                default => null,
            };

            $new_description = array(
                'language_code' => $description['language_code'] ?? null,
                'title' => $description['title'] ?? null,
                'content' => $content,
                'image_url' => $description['image_url'] ?? null,
            );
            if (filled($description['news_description_id'])) {
                $model->description()->where('news_description_id', $description['news_description_id'])->update($new_description);
                continue;
            }
            $model->description()->create($new_description);
        }

        $category_list = [];
        foreach ($request->input('category_list', []) as $category) {
            $category_list[$category['news_category_id']] = ['sort_order' => $category['sort_order'] ?? 0];
        }

        $model->category()->sync($category_list);

        return redirect()->action(
            [self::class, 'showPage'], self::getUrlParams($request)
        );
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function rules(?Request $request, ?Model $model): array
    {
        // 多语言
        $language_list = config('languages');
        $module_name = self::$module_name;

        $rules = array(
            'content_type' => [
                'required',
                new EnumValue(ContentType::class)
            ],
            'is_main_page_display_text_content' => 'nullable|boolean',
            'is_enable' => 'nullable|boolean',
            'gmt_release' => 'required|date',
            'description_list' => 'required|array',
            'description_list.*.' . ContentType::InAppLink => 'exclude_unless:content_type,' . ContentType::InAppLink . '|nullable|url',
            'description_list.*.' . ContentType::ExternalLink => 'exclude_unless:content_type,' . ContentType::ExternalLink . '|nullable|url',
            'description_list.*.language_code' => 'required|string|in:' . implode(',', array_keys($language_list)),
            // title 最大45个字符
            'description_list.*.title' => 'nullable|string|max:45',
            'category_list' => 'nullable|array',
            'category_list.*.news_category_id' => 'required|integer|exists:news_category,news_category_id',
            'category_list.*.sort_order' => 'nullable|integer',
        );

        // 只有新增时才校验商户编号
        if (blank($model->news_id)) {
            if (isSuperAdministrator() || auth()->user()->merchant_number_list->count() > 1) {
                $rules['merchant_number'] = [
                    'required',
                    'exists:App\Models\Modules\Merchant,merchant_number',
                    function ($attr, $value, $fail) use ($request, $model, $module_name) {
                        // 新增时校验非超级管理员且未拥有该商户提示错误
                        if (!isSuperAdministrator() && !in_array($value, auth()->user()->merchant_number_list->toArray())) {
                            $fail(__('common.text_not_found', ['field' => __("$module_name.merchant")]));
                        }
                    },
                ];
            }
        }

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'merchant_number' => __("$module_name.merchant"),
            'content_type' => __("$module_name.content_type"),
            'is_main_page_display_text_content' => __("$module_name.is_main_page_display_text_content"),
            'is_enable' => __("$module_name.is_enable"),
            'gmt_release' => __("$module_name.gmt_release"),
            'description_list' => __('common.text_description'),
            'description_list.*.title' => __("$module_name.title"),
            'description_list.*.app_link' => __("$module_name.app_link"),
            'description_list.*.external_link' => __("$module_name.external_link"),
        ];
    }

    public function delete(Request $request): JsonResponse
    {
        $news_id = $request->post('news_id');
        $module_name = self::$module_name;

        if (blank($news_id) ||
            blank($model = $this->model::when(!isSuperAdministrator(), fn($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))
                ->find($news_id))
        ) {
            $this->notFoundData(__("$module_name.web_title"));
            return $this->returnJson();
        }

        // 删除和 category 表多对多关联的关系数据
        $model->category()->detach();
        $model->description()->delete();
        $model->delete();

        return $this->returnJson();
    }

    public function getCategoryList(Request $request): JsonResponse
    {
        $name = $request->name;
        $merchant_number = $request->merchant_number;

        $category_list = NewsCategory::select('news_category_id', 'title')
            ->leftJoinSub(
                fn($query) => $query->select('news_category_id AS d_news_category_id', 'title', 'language_code')->from('news_category_description'),
                'news_category_description',
                fn($join) => $join->on('news_category.news_category_id', '=', 'news_category_description.d_news_category_id'),
            )
            ->when(filled($name), fn($query) => $query->where('title', 'like', "%$name%"))
            ->when(filled($merchant_number), fn($query) => $query->where('news_category.merchant_number', $merchant_number))
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))
            ->where('language_code', app()->getLocale())
            ->orderBy('title')
            ->get();

        $this->data = $category_list;

        return $this->returnJson();
    }

    /**
     * 返回地址栏参数
     *
     * @param Request|null $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => 'true',
            'news_category_search' => $request->get('news_category_search'),
            'gmt_release_search' => $request->get('gmt_release_search'),
            'title_search' => $request->get('title_search'),
            'content_search' => $request->get('content_search'),
        );
    }

    protected function getNewsCategoryList(): array
    {
        $news_category_list = [];

        $news_category_list_model = NewsCategory::with([
            'description' => function ($query) {
                $query->where('language_code', app()->getLocale());
            }
        ])
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))
            ->get();
        foreach ($news_category_list_model as $news_category_model) {
            $news_category_list[] = array(
                'news_category_id' => $news_category_model->news_category_id,
                'title' => $news_category_model->firstDescription->title ?? '—/—',
            );
        }
        // 按照title排序
        array_multisort(array_column($news_category_list, 'title'), SORT_ASC, $news_category_list);

        return $news_category_list;
    }

    /**
     * 获取搜索数据
     *
     * @param Request $request
     * @return mixed
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-11-08
     */
    protected function _searchList(Request $request): mixed
    {
        // dataTable字段

        $order = $request->input('order', 'gmt_modified');
        $sort = $request->input('sort', 'desc');
        $news_category_id = $request->news_category_search;
        $gmt_release = $request->gmt_release_search;
        $title = $request->title_search;
        $content = $request->content_search;
        $gmt_release_list = self::getRangeDateTimeArray($gmt_release ?: '') ?: null;

        if ($order == 'merchant_name') $order = 'merchant.name_json';

        return News::select('news.*', 'merchant.name_json as merchant_name_json_init')
            ->leftJoin('merchant', 'news.merchant_number', '=', 'merchant.merchant_number')
            ->with(['description', 'category', 'category.description' => function ($query) {
                $query->where('language_code', app()->getLocale());
            }])
            ->when(filled($news_category_id), function ($query) use ($news_category_id) {
                return $query->whereHas('category', function ($query) use ($news_category_id) {
                    return $query->where('news_category.news_category_id', $news_category_id);
                });
            })
            ->when(filled($gmt_release_list), function ($query) use ($gmt_release_list) {
                return $query->whereBetween('gmt_release', $gmt_release_list);
            })
            ->when(filled($title), function ($query) use ($title) {
                return $query->whereHas('description', function ($query) use ($title) {
                    return $query->where('title', 'like', "%$title%");
                });
            })
            ->when(filled($content), function ($query) use ($content) {
                return $query->whereHas('description', function ($query) use ($content) {
                    return $query->where('content', 'like', "%$content%");
                });
            })
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('merchant.merchant_number', auth()->user()->merchant_number_list))
            ->orderBy($order, $sort)
            ->latest('gmt_modified')
            ->latest('news_id');
    }
}

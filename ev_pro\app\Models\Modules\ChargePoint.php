<?php

namespace App\Models\Modules;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Model;

class ChargePoint extends Model
{
    protected $table = 'charge_point'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'charge_point_id'; //定义主键，默认为id
    // 表的唯一number
    public static $table_number = 'charge_point_number';
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    // protected $fillable = [];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [
        'sort_order' => 0, // 排序
    ];

    /**
     * 类型转化器
     */
    protected $casts = [];

    /**
     * 为 array / JSON 序列化准备日期格式
     *
     * @param DateTimeInterface $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    /**
     * 一对多关联充电桩
     */
    public function connector()
    {
        return $this->hasMany(Connector::class, 'charge_point_number', 'charge_point_number');
    }

    /**
     * 获取与用户相关的电话记录
     */
    public function chargePointSetting()
    {
        return $this->hasOne(ChargePointSetting::class, 'charge_point_setting_number', 'charge_point_setting_number');
    }

    /**
     * 获取与用户相关的电话记录
     */
    public function chargePointCs()
    {
        return $this->hasOne(ChargePointCs::class, 'charge_point_cs_number', 'charge_point_cs_number');
    }

    /**
     * Charge Point 关联 Kiosk
     */
    public function kiosk()
    {
        return $this->belongsToMany(Kiosk::class, 'kiosk_to_charge_point', 'charge_point_number', 'kiosk_number', 'charge_point_number', 'kiosk_number')->withTimestamps();
    }

    /**
     * Charge Point 关联 Com Server
     */
    public function comServer()
    {
        return $this->hasOne(ComServer::class, 'com_server_number', 'com_server_number');
    }

    /**
     * Charge Point 关联 site
     */
    public function site()
    {
        return $this->hasOne(Site::class, 'site_number', 'site_number');
    }

    /**
     * 一对一关联 关联 Viosk
     */
    public function viosk()
    {
        return $this->hasOne(Viosk::class, 'charge_point_number', 'charge_point_number');
    }

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];
}

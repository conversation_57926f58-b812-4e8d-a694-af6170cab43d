<?php

namespace App\Http\Controllers\Admin;

use App\Models\Modules\OctopusDataExchangeProgramRecord;
use App\Enums\{OctopusDataExchangeOperationMode, OctopusDataExchangeType};
use App\Http\Controllers\Common\CommonController;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use Illuminate\Foundation\Application;
use Illuminate\Http\{
    JsonResponse,
    Request,
};

class OctopusDataExchangeProgramRecordController extends CommonController
{
    protected static string $module_name = 'octopusDataExchangeProgramRecord'; // 模块名称
    protected OctopusDataExchangeProgramRecord $model;

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new OctopusDataExchangeProgramRecord;
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'octopus_data_exchange_type_search' => $request->get('octopus_data_exchange_type_search'),
            'octopus_data_exchange_operation_mode_search' => $request->get('octopus_data_exchange_operation_mode_search'),
        );

        foreach (OctopusDataExchangeType::asSelectArray() as $value => $name) {
            $data['octopus_data_exchange_type_list'][] = array(
                'name' => $name,
                'value' => $value,
            );
        }

        foreach (OctopusDataExchangeOperationMode::asSelectArray() as $value => $name) {
            $data['octopus_data_exchange_operation_mode_list'][] = array(
                'name' => $name,
                'value' => $value,
            );
        }

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'kiosk_number');
        $sort = $request->input('sort', 'asc');
        $length = (int)$request->input('length', 10);
        $octopus_data_exchange_type_search = $request->input('octopus_data_exchange_type_search');
        $octopus_data_exchange_operation_mode_search = $request->input('octopus_data_exchange_operation_mode_search');

        $where = array();

        if (filled($octopus_data_exchange_type_search)) {
            $where[] = ['octopus_data_exchange_type', '=', "$octopus_data_exchange_type_search"];
        }
        if (filled($octopus_data_exchange_operation_mode_search)) {
            $where[] = ['octopus_data_exchange_operation_mode', '=', "$octopus_data_exchange_operation_mode_search"];
        }

        $data_list = OctopusDataExchangeProgramRecord::where($where)
            ->orderBy($order, $sort)
            ->latest('gmt_create')
            ->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $result[] = array(
                'octopus_data_exchange_program_record_id' => $data->octopus_data_exchange_program_record_id, // 八达通上传文件记录ID
                'octopus_data_exchange_type' => filled($data->octopus_data_exchange_type)
                    ? OctopusDataExchangeType::getDescription($data->octopus_data_exchange_type)
                    : '—/—',// 八达通数据交换类型
                'octopus_data_exchange_operation_mode' => filled($data->octopus_data_exchange_operation_mode)
                    ? OctopusDataExchangeOperationMode::getDescription($data->octopus_data_exchange_operation_mode)
                    : '—/—',// 八达通数据交换操作模式
                'execute_log' => $data->execute_log,// 执行日志
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );

        return response()->json($json);
    }

}

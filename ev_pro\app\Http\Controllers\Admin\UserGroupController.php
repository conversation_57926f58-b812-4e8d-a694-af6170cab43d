<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use Illuminate\Database\Eloquent\Model;
use Illuminate\Validation\Rule;
use Illuminate\Foundation\Application;
use Illuminate\Http\{
    JsonResponse,
    RedirectResponse,
    Request,
};
use App\Http\Controllers\Admin\Traits\{
    Add,
    Edit,
};
use App\Models\Modules\{
    Merchant,
    UserGroup,
    UserGroupDescription,
    AppUser,
};

class UserGroupController extends CommonController
{
    use Add, Edit;

    protected static string $module_name = 'userGroup'; // 模块名称
    protected static bool $module_check_merchant = true; // 标记该模块校验商户

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new UserGroup;
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'add_url' => action([self::class, 'add']),
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'merchant_search' => $request->get('merchant_search'),
            'name_search' => $request->get('name_search'),
        );

        // 商户下拉列表
        $data['merchant_list'] = $this->getMerchantOptionList();

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $length = (int)$request->input('length', 10);
        $search = $this->_searchList($request);
        $data_list = $search->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $merchant_name = $this->getValueFromLanguageArray($data->merchant?->name_json) ?? '—/—';

            $result[] = array(
                'user_group_id' => $data->user_group_id, // 用户组ID
                'name' => $this->getValueFromLanguageArray($data->name_json) ?? '—/—', // 名称
                'merchant_number' => $data->merchant_number, // 商户编号
                'merchant_name' => $merchant_name, // 商户名称
                'is_enable' => $data->is_enable, // 是否启用
                'sort_order' => $data->sort_order, // 排序
                'remark' => $data->remark ?? '—/—', // 备注
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );
        return response()->json($json);
    }

    protected function getForm(Request $request, array $data): View|Application|Factory
    {
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'],
            self::getUrlParams($request)
        );
        $merchant_number = $data['model']->merchant_number;
        if ((isSuperAdministrator() || auth()->user()->merchant_number_list->count() > 1) && blank($data['model']?->user_group_id)) {
            // 如果是新增，并且是超级管理员或者管理员的商户大于1的情况下就直接获取页面传入的商户编号
            $merchant_number = $request->old('merchant_number', $data['model']->merchant_number);
        } else if (blank($data['model']?->user_group_id) && auth()->user()->merchant_number_list->count() === 1) {
            // 如果是新增并且管理员商户只有一个就直接赋值
            $merchant_number = auth()->user()->merchant_number_list->first();
        }
        $data['model']->merchant_number = $merchant_number; // 商户编号
        $merchant_name = $this->getValueFromLanguageArray(
            Merchant::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))
            ->firstWhere('merchant_number', $merchant_number)?->name_json);
        $data['model']->merchant_name = $merchant_name; // 商户名称
        $data['model']->is_enable = $request->old('is_enable', $data['model']->is_enable);
        $data['model']->sort_order = $request->old('sort_order', $data['model']->sort_order);

        foreach (config('languages') as $language_code => $language_name) {
            // 有旧数据
            $user_group_description_old_list = $request->old('item');
            if (filled($user_group_description_old_list)) {
                $data['item'][$language_code] = array(
                    'language_name' => $language_name,
                    'language_code' => $language_code,
                    'user_group_description_id' => $user_group_description_old_list[$language_code]['user_group_description_id'] ?? null, // ID
                    'name' => $user_group_description_old_list[$language_code]['name'] ?? null,
                );
            } else {
                $user_group_description_result = $data['model']->description->where('language_code', $language_code)->first();
                $data['item'][$language_code] = array(
                    'language_name' => $language_name,
                    'language_code' => $language_code,
                    'user_group_description_id' => $user_group_description_result->user_group_description_id ?? null, // ID
                    'name' => $user_group_description_result->name ?? null, // 名称
                );
            }
        }

        return view("pages.{$data['module_name']}.form", $data);
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @param UserGroup $model
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    protected function modelValidateAndSave(Request $request, UserGroup $model): RedirectResponse
    {
        $request->validate(self::rules($request, $model), [], self::attributes());
        // 判断是否为新增
        if (blank($model->user_group_id)) {
            $model = $this->model;
            $merchant_number = $model->merchant_number;

            if ((isSuperAdministrator() || auth()->user()->merchant_number_list->count() > 1)) {
                // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下就直接获取页面传入的场地编号
                $merchant_number = $request->input('merchant_number');
            } else if (auth()->user()->merchant_number_list->count() === 1) {
                // 如果是新增并且管理员场地只有一个就直接赋值
                $merchant_number = auth()->user()->merchant_number_list->first();
            }
            $model->merchant_number = $merchant_number; // 商户编号
        }

        // 获取请求用户组描述数据
        $user_group_description_list = $request->input('item', array());
        $name_json = array();
        foreach ($user_group_description_list as $user_group_description) {
            $name_json[$user_group_description['language_code']] = $user_group_description['name'];
        }


        $model->name_json = $name_json;
        $model->is_enable = $request->input('is_enable', 0);
        $model->sort_order = $request->input('sort_order');
        $model->remark = $request->input('remark');
        $model->save();

        foreach ($user_group_description_list as $user_group_description) {
            if (isset($user_group_description['user_group_description_id'])) {
                $user_group_description_model =
                    UserGroupDescription::findOr($user_group_description['user_group_description_id'], fn () => new UserGroupDescription);
            } else {
                $user_group_description_model = new UserGroupDescription;
            }
            $user_group_description_model->user_group_id = $model->user_group_id;
            $user_group_description_model->language_code = $user_group_description['language_code'];
            $user_group_description_model->name = $user_group_description['name'];

            $user_group_description_model->save();
        }

        return redirect()->action(
            [self::class, 'showPage'],
            self::getUrlParams($request)
        );
    }

    /**
     * 删除
     *
     * @param Request $request
     * @return JsonResponse
     * @Description 删除对应数据并且删除对应头像
     * @example
     * <AUTHOR>
     * @date 2023-10-24
     */
    public function delete(Request $request): JsonResponse
    {
        $id = $request->post('id');
        $module_name = self::$module_name;

        if (blank($id)) {
            $this->missingField('ID');
            return $this->returnJson();
        }

        $user_group = $this->model::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))->find($id);

        if (blank($user_group)) {
            $this->notFoundData('ID');
            return $this->returnJson();
        }

        // 判断是否有绑定用户
        if (filled($app_user_list = $user_group->user)) {
            $app_user_str = '';
            foreach ($app_user_list as $app_user) {
                $app_user_str .= '<li>' . ($app_user->email ?? ($app_user->nickname ?? $app_user->user_id)) . '</li>';
            }
            $this->code = 201;
            $this->message = __('common.error_has_binding_unable_to_delete_user_group', ['user' => $app_user_str]);
            return $this->returnJson();
        }

        // 判断是否有绑定收费表
        $tariff_tables = [
            'simple_tariff_table' => 'simpleTariffTableItem',
            'time_tariff_table' => 'timeTariffTableItem',
            'energy_tariff_table' => 'energyTariffTableItem',
            'idling_penalty_tariff_table' => 'idlingPenaltyTariffTableItem',
        ];

        $tariff_scheme_str = '';

        foreach ($tariff_tables as $name => $method) {
            if ($user_group->$method()->exists()) {
                $tariff_scheme_str .= '<li>' . __("$module_name.$name") . '</li>';
            }
        }

        if (!empty($tariff_scheme_str)) {
            $this->code = 201;
            $this->message = __("$module_name.error_has_binding_tariff_table_unable_to_delete_user_group", ['tariff_scheme' => $tariff_scheme_str]);
            return $this->returnJson();
        }

        $user_group->description()->delete();
        $user_group->delete();

        return $this->returnJson();
    }

    // 获取已绑定用户列表
    protected function getUserGroupAppUserList(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $length = (int)$request->input('length', 10);
        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $user_group_id = $request->input('user_group_id');
        $user_email_search = $request->input('user_email_search');
        $user_telephone_search = $request->input('user_telephone_search');

        $data_list = AppUser::with(['merchant', 'userGroup'])
            ->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))
            ->when(filled($user_email_search), fn ($query) => $query->where('email', 'like', "%$user_email_search%"))
            ->when(filled($user_telephone_search), fn ($query) => $query->where('telephone', 'like', "%$user_telephone_search%"))
            ->when(
                filled($user_group_id),
                fn ($query) => $query->where('user_group_id', $user_group_id),
                fn ($query) => $query->whereRaw('1 = 2')
            )
            ->orderBy($order, $sort)
            ->orderBy('gmt_modified', 'desc')
            ->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $merchant_name = $this->getValueFromLanguageArray($data->merchant?->name_json) ?? '—/—';
            $user_group_name = $this->getValueFromLanguageArray($data->userGroup?->name_json) ?? '—/—';
            $result[] = array(
                'is_checked' => $data->user_group_id == $user_group_id ? true : false, // 判断是否有id来确认选中
                'user_id' => $data->user_id, // 用户ID
                'merchant_name' => $merchant_name, // 商户名称
                'nickname' => $data->nickname, // 昵称
                'avatar_url' => existsImage('public', $data->avatar_url) ?: existsImage('icon', 'not_select_image.png'), // 头像
                'telephone' => $data->telephone ?? '—/—', // 电话
                'email' => $data->email ?? '—/—', // 电邮
                'user_group_id' => $data->user_group_id, // 用户组ID
                'user_group_name' => $user_group_name, // 用户组名称
                'points_balance' => (float)bcdiv($data->points_balance, 100, 1), // 积分余额
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );
        return response()->json($json);
    }

    // 保存用户组绑定关系
    public function saveUserGroupToAppUser(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        $user_id_list = $request->input('user_id_list', array());
        $user_group_id = $request->input('user_group_id');

        if (blank($user_group_id)) {
            $this->missingField(__("$module_name.user_group"));
            return $this->returnJson();
        }
        $user_group = UserGroup::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))->find($user_group_id);
        if (blank($user_group)) {
            $this->missingField(__("$module_name.user_group"));
            return $this->returnJson();
        }

        // 批量更新user表的user_group_id
        AppUser::where('merchant_number', $user_group->merchant_number)
            ->whereIn('user_id', $user_id_list)
            ->update(['user_group_id' => $user_group_id]);

        return $this->returnJson();
    }

    // 删除用户组绑定关系
    public function deleteUserGroupToAppUser(Request $request): JsonResponse
    {
        $user_id = $request->input('user_id');

        if (blank($user_id)) {
            $this->missingField('User');
            return $this->returnJson();
        }
        $app_user = AppUser::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))->find($user_id);
        if (blank($app_user)) {
            $this->notFoundData('User');
            return $this->returnJson();
        }

        $app_user->update(['user_group_id' => null]);

        return $this->returnJson();
    }

    // 批量删除用户组绑定关系
    public function batchDeleteUserGroupToAppUser(Request $request): JsonResponse
    {
        $user_id_list = $request->input('user_id_list', array());

        if (blank($user_id_list)) {
            $this->missingField('User');
            return $this->returnJson();
        }

        AppUser::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))
            ->whereIn('user_id', $user_id_list)
            ->update(['user_group_id' => null]);

        return $this->returnJson();
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function rules(?Request $request, ?Model $model): array
    {
        $module_name = self::$module_name;

        $rules = array(
            'item.*.name' => 'required|max:45'
        );

        // 如果是新增，并且是超级管理员或者管理员的商户大于1的情况下才需要判断商户编号是否存在
        if (blank($model?->user_group_id)) {
            if ((isSuperAdministrator() || auth()->user()->merchant_number_list->count() > 1)) {
                $rules['merchant_number'] = [
                    'required',
                    'exists:App\Models\Modules\Merchant,merchant_number',
                    function ($attr, $value, $fail) use ($module_name, $request) {
                        // 判断选择的merchant是否为当前管理员权限下的
                        $merchant = Merchant::when(!isSuperAdministrator(), fn($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))->firstWhere('merchant_number', $value);
                        if (blank($merchant)) $fail(__('common.text_not_found', ['field' => __("$module_name.merchant")]));
                    },
                ];
            }
        }

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'name' => __("$module_name.name"),
            'merchant_number' => __("$module_name.merchant"),
            'item.*.name' => __("$module_name.description_name"),
        ];
    }

    /**
     * 返回地址栏参数
     *
     * @param Request|null $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => 'true',
            'merchant_search' => $request->get('merchant_search'),
            'name_search' => $request->get('name_search'),
        );
    }

    /**
     * 搜索数据
     *
     * @param Request $request
     * @return mixed
     */
    protected function _searchList(Request $request): mixed
    {
        // dataTable字段
        $order = $request->input('order', 'gmt_modified');
        $sort = $request->input('sort', 'asc');
        $merchant_search = $request->input('merchant_search');
        $name_search = $request->input('name_search');

        if ($order == 'name') $order = 'name_json';

        $current_language_code = app()->getLocale() ?? 'en_US';
        $user_group_list = UserGroup::with(['merchant'])
            ->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))
            ->when(filled($name_search), function ($query) use ($name_search, $current_language_code) {
                // 如果搜索名称，才加载description关联，然后搜索description当前语言的名称
                return $query->with('description')->whereHas('description', function ($query) use ($name_search, $current_language_code) {
                    return $query->where('language_code', $current_language_code)->where('name', 'like', "%$name_search%");
                });
            })
            ->when(filled($merchant_search), fn ($query) => $query->where('merchant_number', $merchant_search))
            ->orderBy($order, $sort)
            ->latest('gmt_modified');

        return $user_group_list;
    }
}

<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;
use App\Models\Traits\Emoji;

class MemberCardGroupDescription extends Model
{
    use emoji;

    protected $table = 'member_card_group_description'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'member_card_group_description_id'; //定义主键，默认为id
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    // protected $fillable = [];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * 获取区域
     */
    public function memberCardGroup()
    {
        return $this->hasOne(MemberCardGroup::class, 'member_card_group_id', 'member_card_group_id');
    }

}

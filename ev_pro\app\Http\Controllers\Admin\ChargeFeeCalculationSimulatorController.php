<?php

namespace App\Http\Controllers\Admin;

use App\Enums\TariffTableType;
use App\Enums\WebsocketType;
use App\Http\Controllers\Common\CommonController;
use Illuminate\Http\{
    JsonResponse,
    Request,
};
use App\Models\Modules\{
    ConnectorSetting,
    EnergyTariffTable,
    IdlingPenaltyTariffTable,
    PeakTimeTable,
    PublicHoliday,
    SimpleTariffTable,
    TimeTariffTable,
};
use Illuminate\Foundation\Application;
use Illuminate\Contracts\View\{
    Factory,
    View,
};

class ChargeFeeCalculationSimulatorController extends CommonController
{

    protected static string $module_name = 'chargeFeeCalculationSimulator'; // 模块名称

    /**
     * 初始页
     *
     * @param Request $request
     * @return View|Application|Factory|JsonResponse
     */
    protected function showPage(Request $request): View|Application|Factory|JsonResponse
    {
        if ($request->isMethod('post')) {
            return $this->send($request);
        }

        $data = array(
            'module_name' => self::$module_name,
            'send_url' => action([self::class, 'send']),
            'get_kiosk_url' => action([self::class, 'getKiosk']),
            'websocket_type_push' => WebsocketType::ChargeFeeCalculationNotification,
        );

        return view("pages.{$data['module_name']}.form", $data);
    }

    /**
     * 发送消息
     *
     * @param Request $request
     * @return JsonResponse
     */
    protected function send(Request $request): JsonResponse
    {
        $request->validate(self::rules(), [], self::attributes());

        $kiosk_id = $request->input('kiosk_id');
        $connector_setting_id = $request->input('connector_setting_id');
        $charged_energy_list = $request->input('charged_energy_list', []);
        $client_id = $request->post('client_id');

        if (empty($kiosk_id)) {
            $this->code = 404;
            $this->message = 'Kiosk ' . __('common.text_not_found');
            return $this->returnJson();
        }

        $charge_fee_calculation = array(
            'lms_mode' => $request->input('lms_mode'),
            'gmt_start' => $request->input('gmt_start'),
            'gmt_stop' => $request->input('gmt_stop'),
            'gmt_unlocked' => $request->input('gmt_unlocked'),
            'public_holiday_json' => json(PublicHoliday::get()->toArray()),
            'charged_energy_record_list' => $charged_energy_list,
        );

        if (!empty($connector_setting_id) && filled($connector_setting_model = ConnectorSetting::find($connector_setting_id))) {
            // 收费表类型
            $charge_fee_calculation['tariff_table_type'] = $connector_setting_model->tariff_table_type;
            // 对应收费表
            switch ($connector_setting_model->tariff_table_type) {
                case TariffTableType::SimpleTariffTable:
                    // 获取简单收费表并转成json
                    if (filled($connector_setting_model->simple_tariff_table_id) &&
                        filled($simple_tariff_table_model = SimpleTariffTable::find($connector_setting_model->simple_tariff_table_id))) {
                        $charge_fee_calculation['$simple_tariff_table_json'] = json_encode($simple_tariff_table_model);
                    }
                    // 获取闲时、高峰收费表并转成json
                    if (filled($connector_setting_model->idling_penalty_tariff_table_id)
                        && filled($idling_penalty_tariff_table_model = IdlingPenaltyTariffTable::find($connector_setting_model->idling_penalty_tariff_table_id))) {
                        $charge_fee_calculation['idling_penalty_tariff_table_item_json'] = json($idling_penalty_tariff_table_model->idling_penalty_tariff_table_item_json);
                    }
                    if (filled($connector_setting_model->peak_time_table_id)
                        && filled($peak_time_table_model = PeakTimeTable::find($connector_setting_model->peak_time_table_id))) {
                        $charge_fee_calculation['peak_time_table_item_json'] = json($peak_time_table_model->peak_time_table_item_json);
                    }
                    break;
                case TariffTableType::ComplexTimeTariffTable:
                case TariffTableType::ComplexEnergyTariffTable:
                    if ($connector_setting_model->tariff_table_type == TariffTableType::ComplexTimeTariffTable) {
                        // 获取时间收费表并转成json
                        if (filled($connector_setting_model->time_tariff_table_id)
                            && filled($time_tariff_table_model = TimeTariffTable::find($connector_setting_model->time_tariff_table_id))) {
                            $charge_fee_calculation['time_tariff_table_item_json'] = json($time_tariff_table_model->time_tariff_table_item_json);
                        }
                    } else if ($connector_setting_model->tariff_table_type == TariffTableType::ComplexEnergyTariffTable) {
                        // 获取电量收费表并转成json
                        if (filled($connector_setting_model->energy_tariff_table_id)
                            && filled($energy_tariff_table_model = EnergyTariffTable::find($connector_setting_model->energy_tariff_table_id))) {
                            $charge_fee_calculation['energy_tariff_table_item_json'] = json($energy_tariff_table_model->energy_tariff_table_item_json);
                        }
                    }
                    // 获取闲时、高峰收费表并转成json
                    if (filled($connector_setting_model->idling_penalty_tariff_table_id)
                        && filled($idling_penalty_tariff_table_model = IdlingPenaltyTariffTable::find($connector_setting_model->idling_penalty_tariff_table_id))) {
                        $charge_fee_calculation['idling_penalty_tariff_table_item_json'] = json($idling_penalty_tariff_table_model->idling_penalty_tariff_table_item_json);
                    }
                    if (filled($connector_setting_model->peak_time_table_id)
                        && filled($peak_time_table_model = PeakTimeTable::find($connector_setting_model->peak_time_table_id))) {
                        $charge_fee_calculation['peak_time_table_item_json'] = json($peak_time_table_model->peak_time_table_item_json);
                    }
                    break;
            }
        }

        self::websocketPush(WebsocketType::ChargeFeeCalculationNotification, $charge_fee_calculation, $kiosk_id, $client_id);

        return $this->returnJson();
    }

    /**
     * 获取kiosk状态
     *
     * @param Request $request
     * @return JsonResponse
     */
    protected function getKiosk(Request $request): JsonResponse
    {
        $kiosk_id = $request->input('kiosk_id');

        if (empty($kiosk_id)) {
            $this->code = 404;
            $this->message = 'Kiosk ' . __('common.text_not_found');
            return $this->returnJson();
        }

        // 无kiosk在线
        $this->data = !empty($kiosk_list = $this->websocketAllUid()) && in_array($kiosk_id, $kiosk_list);

        return $this->returnJson();
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @return array
     */
    protected static function rules(): array
    {
        $rules = array(
            'kiosk_id' => 'required',
            'connector_setting_id' => 'required',
            'lms_mode' => 'required',
            'gmt_start' => 'required|max:45',
            'gmt_stop' => 'required|max:45',
            'gmt_unlocked' => 'required|max:45',
        );

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'kiosk_id' => __("$module_name.kiosk_id"),
            'connector_setting_id' => __("$module_name.connector_setting_id"),
            'lms_mode' => __("$module_name.lms_mode"),
            'gmt_start' => __("$module_name.gmt_start"),
            'gmt_stop' => __("$module_name.gmt_stop"),
            'gmt_unlocked' => __("$module_name.gmt_unlocked"),
        ];
    }

    /**
     * 返回地址栏参数
     *
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(): array
    {
        return array();
    }
}

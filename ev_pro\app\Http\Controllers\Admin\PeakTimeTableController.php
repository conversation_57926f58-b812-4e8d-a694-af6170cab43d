<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use Illuminate\Support\Facades\Redis;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Application;
use Illuminate\Http\{
    JsonResponse,
    RedirectResponse,
    Request,
};
use App\Models\Modules\{
    ConnectorSetting,
    PeakTimeTable,
    Site,
};
use App\Enums\{
    DayType,
};

class PeakTimeTableController extends CommonController
{
    protected static string $module_name = 'peakTimeTable'; // 模块名称

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new PeakTimeTable;
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'name_search' => $request->get('name_search'),
            'add_url' => action([self::class, 'add']),
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'site_search' => $request->get('site_search'),
        );

        // 场地下拉列表
        $data['site_list'] = $this->getSiteOptionList();

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $name_search = $request->input('name_search');
        $site_search = $request->input('site_search');
        $length = (int)$request->input('length', 10);

        if ($order == 'site_name') $order = 'site.name_json';

        $data_list = PeakTimeTable::select('peak_time_table.*', 'site.name_json as site_name_json_init')
            ->leftJoin('site', 'peak_time_table.site_number', '=', 'site.site_number')
            ->when(filled($name_search), fn($query) => $query->where('peak_time_table.name', 'like', "%$name_search%"))
            ->when(filled($site_search), fn($query) => $query->where('peak_time_table.site_number', $site_search))
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('peak_time_table.site_number', auth()->user()->site_number_list))
            ->orderBy($order, $sort)
            ->latest('gmt_modified')
            ->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $result[] = array(
                'peak_time_table_id' => $data->peak_time_table_id,
                'peak_time_table_number' => $data->peak_time_table_number,
                'name' => $data->name, // 名称
                'site_name' => $this->getValueFromLanguageArray($data->site_name_json_init) ?? '—/—', // 場地名称
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
                'remark' => $data->remark ?? '—/—', // 备注
                'sort_order' => $data->sort_order,// 排序
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );

        return response()->json($json);
    }

    public function add(Request $request): View|Application|Factory|RedirectResponse
    {
        $data = array();

        $module_name = self::$module_name;
        $model = $this->model;

        if ($request->isMethod('post')) {
            return $this->modelValidateAndSave($request, $model);
        }

        $data['item'] = array();
        date_default_timezone_set('GMT');
        // 因为post提交表单和get链接都会传入收费表编号参数，所以用下划线标识该编号是copyToAdd携带的
        if (filled($peak_time_table_number = $request->input('_peak_time_table_number'))) {
            $model = $model->load('item')->when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->where('peak_time_table_number', $peak_time_table_number)
                ->firstOrFail();
            $item_list = $model->item()
                ->orderByRaw("CASE
                    WHEN day_type IS NULL THEN 0
                    WHEN day_type = '" . DayType::Monday . "' THEN 1
                    WHEN day_type = '" . DayType::Tuesday . "' THEN 2
                    WHEN day_type = '" . DayType::Wednesday . "' THEN 3
                    WHEN day_type = '" . DayType::Thursday . "' THEN 4
                    WHEN day_type = '" . DayType::Friday . "' THEN 5
                    WHEN day_type = '" . DayType::Saturday . "' THEN 6
                    WHEN day_type = '" . DayType::Sunday . "' THEN 7
                    WHEN day_type = '" . DayType::PublicHoliday . "' THEN 8
                    ELSE 9
                    END")
                ->oldest('start_time')
                ->get();
            foreach ($item_list as $item) {
                $is_shut_peak_time = ($item->start_time == $item->end_time && $item->start_time == 0);
                if (!$is_shut_peak_time) {
                    $data['item']['time'][$item->day_type ?? 'DEFAULT']['data'][] = array(
                        'day_type' => $item->day_type,
                        'start_time' => date('H:i', $item->start_time),
                        'end_time' => date('H:i', $item->end_time),
                    );
                }
                // 判断是否开启无高峰时间模式（当开始时间和结束时间都为0的时候就开启无高峰时间模式）
                $data['item']['time'][$item->day_type ?? 'DEFAULT']['is_shut_peak_time'] = $is_shut_peak_time;
                $data['item']['day_type'][$item->day_type ?? 'DEFAULT'] = DayType::hasValue($item->day_type) ? DayType::getDescription($item->day_type) : __("$module_name.default_day");
            }
            $model->peak_time_table_id = $model->peak_time_table_number = $model->name = null;
        } else {
            $data['item']['day_type']['DEFAULT'] = __("$module_name.default_day");
            $data['item']['time']['DEFAULT']['data'] = array();
            $data['item']['time']['DEFAULT']['is_shut_peak_time'] = false;
        }
        date_default_timezone_set(config('app.timezone'));

        $data['model'] = $model;

        return $this->getForm($request, $data);
    }

    public function edit(Request $request, $peak_time_table_number): Factory|Application|View|RedirectResponse
    {
        $data = array();

        $module_name = self::$module_name;
        $model = PeakTimeTable::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->where('peak_time_table_number', $peak_time_table_number)
            ->firstOrFail();

        if ($request->isMethod('post')) {
            return $this->modelValidateAndSave($request, $model);
        }

        $data['item'] = array();

        date_default_timezone_set('GMT');
        // 查询并自定义排序
        $item_list = $model->item()
            ->orderByRaw("CASE
            WHEN day_type IS NULL THEN 0
            WHEN day_type = '" . DayType::Monday . "' THEN 1
            WHEN day_type = '" . DayType::Tuesday . "' THEN 2
            WHEN day_type = '" . DayType::Wednesday . "' THEN 3
            WHEN day_type = '" . DayType::Thursday . "' THEN 4
            WHEN day_type = '" . DayType::Friday . "' THEN 5
            WHEN day_type = '" . DayType::Saturday . "' THEN 6
            WHEN day_type = '" . DayType::Sunday . "' THEN 7
            WHEN day_type = '" . DayType::PublicHoliday . "' THEN 8
            ELSE 9
            END")
            ->oldest('start_time')
            ->get();
        foreach ($item_list as $item) {
            $is_shut_peak_time = ($item->start_time == $item->end_time && $item->start_time == 0);
            if (!$is_shut_peak_time) {
                $data['item']['time'][$item->day_type ?? 'DEFAULT']['data'][] = array(
                    'day_type' => $item->day_type,
                    'start_time' => date('H:i', $item->start_time),
                    'end_time' => date('H:i', $item->end_time),
                );
            }
            // 判断是否开启无高峰时间模式（当开始时间和结束时间都为0的时候就开启无高峰时间模式）
            $data['item']['time'][$item->day_type ?? 'DEFAULT']['is_shut_peak_time'] = $is_shut_peak_time;
            $data['item']['day_type'][$item->day_type ?? 'DEFAULT'] = DayType::hasValue($item->day_type) ? DayType::getDescription($item->day_type) : __("$module_name.default_day");
        }

        // 防止默认值不存在
        if (!isset($data['item']['day_type']['DEFAULT'])) {
            $data['item']['day_type']['DEFAULT'] = __("$module_name.default_day");
            $data['item']['time']['DEFAULT']['data'] = array();
            $data['item']['time']['DEFAULT']['is_shut_peak_time'] = false;
        }
        date_default_timezone_set(config('app.timezone'));

        $data['model'] = $model;

        return $this->getForm($request, $data);
    }

    protected function getForm(Request $request, array $data): View|Application|Factory
    {
        $module_name = $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'],
            self::getUrlParams($request)
        );

        // 新增时才回显收费表编号和场地编号
        if (blank($data['model']->peak_time_table_id)) {
            $data['model']->peak_time_table_number = $request->old('peak_time_table_number', $data['model']->peak_time_table_number); // 高峰收费表编号
            $site_number = isSuperAdministrator() || auth()->user()->site_number_list->count() > 1
                ? $request->old('site_number', $data['model']->site_number)
                : (auth()->user()->site_number_list->first() ?? null);
            $site_name = $this->getValueFromLanguageArray(Site::firstWhere('site_number', $site_number)?->name_json);
            $data['model']->site_number = $site_number; // 场地编号
            $data['model']->site_name = $site_name; // 场地名称
        }
        $data['model']->name = $request->old('name', $data['model']->name);  // 名称
        $data['model']->remark = $request->old('remark', $data['model']->remark); // 备注
        $data['model']->sort_order = $request->old('sort_order', $data['model']->sort_order); // 排序
        if ($request->old('item')) {
            $data['item'] = array();
            $item_list = $request->old('item');
            // 初始化默认值
            $default_values = [
                'time' => ['DEFAULT' => ['data' => [], 'is_shut_peak_time' => false]],
                'day_type' => ['DEFAULT' => __("$module_name.default_day")],
            ];
            foreach ($item_list['day_type'] as $day_type => $day) {
                // 防止默认值不存在
                if (!isset($item_list['time']) || !array_key_exists('DEFAULT', $item_list['time'])) $data['item']['time'] = $default_values;
                $data['item']['time'][$day_type ?? 'DEFAULT']['data'] = $item_list['time'][$day_type ?? 'DEFAULT']['data'] ?? [];
                $data['item']['time'][$day_type ?? 'DEFAULT']['is_shut_peak_time'] = isset($item_list['time'][$day_type ?? 'DEFAULT']['is_shut_peak_time']) ? (bool)$item_list['time'][$day_type ?? 'DEFAULT']['is_shut_peak_time'] : false;
                $data['item']['day_type'][$day_type ?? 'DEFAULT'] = DayType::hasValue($day_type) ? DayType::getDescription($day_type) : __("$module_name.default_day");
            }

            if (empty($data['item']) || !isset($data['item'])) {
                $data['item']['time'] = $default_values;
            }
        }
        $data['day_type_enum'] = $data['day_description'] = $data['day_type_all_list'] = array();
        $data['day_description']['DEFAULT'] = $data['day_type_all_list']['DEFAULT'] = __("$module_name.default_day");
        foreach (DayType::asSelectArray() as $value => $name) {
            $data['day_type_all_list'][$value] = $name;
            $data['day_type_enum'][$value] = $name;
            $data['day_description'][$value] = $name;
        }

        return view("pages.{$data['module_name']}.form", $data);
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @param PeakTimeTable $model
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    protected function modelValidateAndSave(Request $request, PeakTimeTable $model): RedirectResponse
    {
        // 判断是否是新增
        if (blank($model->peak_time_table_id)) {
            $model = $this->model;
        }
        $request->validate(self::rules($request, $model), [], self::attributes($request));
        // 公共方法定义，因为需要先刪除子表数据，防止sql执行错误时可以回滚
        deleteItemAndSaveModel(function () use ($request, $model) {
            $model->item()->delete();

            // 新增时才保存收费表、场地和商户编号
            if (blank($model->peak_time_table_id)) {
                $model->peak_time_table_number = $request->input('peak_time_table_number');
                $model->site_number = $request->input('site_number');
                if (filled($model->site_number) && filled($site = Site::firstWhere('site_number', $model->site_number))) {
                    $model->merchant_number = $site->merchant_number;
                }
            }
            $model->name = $request->input('name'); // 名称
            $model->remark = $request->input('remark'); // 备注
            $model->sort_order = $request->input('sort_order', 0); // 排序
            $item_list = $request->input('item', array());

            date_default_timezone_set('GMT');
            $peak_time_table_item_list = array();
            foreach ($item_list['time'] as $key => $item) {
                if (isset($item['is_shut_peak_time']) && (bool)$item['is_shut_peak_time']) {
                    $peak_time_table_item_list[] = array(
                        'day_type' => DayType::hasValue($key) ? $key : null,
                        'start_time' => 0,
                        'end_time' => 0
                    );
                    continue;
                }
                if (isset($item['data'])) {
                    foreach ($item['data'] as $peak_time_table_item) {
                        $start = explode(':', $peak_time_table_item['start_time']);
                        $start_time = $start[0] * 3600 + $start[1] * 60; // 将开始时间 H:i:s 转为秒
                        $end = explode(':', $peak_time_table_item['end_time']);
                        $end_time = $end[0] * 3600 + $end[1] * 60; // 将结束时间 H:i:s 转为秒
                        $peak_time_table_item_list[] = array(
                            'day_type' => DayType::hasValue($key) ? $key : null,
                            'start_time' => $start_time,
                            'end_time' => $end_time
                        );
                    }
                }
            }
            date_default_timezone_set(config('app.timezone'));
            // 最后排序一次防止顺序错乱
            $day_type_order = [
                null, DayType::Monday, DayType::Tuesday, DayType::Wednesday, DayType::Thursday, DayType::Friday, DayType::Saturday, DayType::Sunday, DayType::PublicHoliday
            ];
            $model->peak_time_table_item_json = array_values(collect($peak_time_table_item_list)
                ->sortBy('start_time')
                ->sortBy(function ($item) use ($day_type_order) {
                    return array_search($item['day_type'], $day_type_order);
                })
                ->values()
                ->toArray());

            $model->save();
            $model->item()->createMany($model->peak_time_table_item_json);
        });
        self::delRedis($model->peak_time_table_number);
        self::setConnectorTokenByTariffTable($model);
        self::sendInitPushByKioskNumberList();

        return redirect()->action(
            [self::class, 'showPage'],
            self::getUrlParams($request)
        );
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function rules(?Request $request, ?Model $model): array
    {
        $module_name = self::$module_name;
        $rules = array(
            'name' => 'required|string|max:45',
            'sort_order' => 'integer|min:0',
            'remark' => 'nullable|max:1000',
        );

        // 只有新增时才校验收费表和场地编号
        if (blank($model->peak_time_table_id)) {
            $rules['peak_time_table_number'] = [
                'required',
                'max:30',
                'regex:/^[a-zA-Z0-9]+$/', // 只能填写字母和数字
                'unique:App\Models\Modules\PeakTimeTable,peak_time_table_number',
            ];
            if (isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) {
                $rules['site_number'] = [
                    'required',
                    'exists:App\Models\Modules\Site,site_number',
                    function ($attr, $value, $fail) use ($request, $model, $module_name) {
                        // 新增时校验非超级管理员且未拥有该场地提示错误
                        if (!isSuperAdministrator() && !in_array($value, auth()->user()->site_number_list->toArray())) {
                            $fail(__('common.text_not_found', ['field' => __("$module_name.site")]));
                        }
                    },
                ];
            }
        }

        $item_list = $request->input('item', array());
        $rules['item.time'] = 'required|array';

        foreach ($item_list['day_type'] as $day_type => $item) {
            if (isset($item_list['time'][$day_type]['is_shut_peak_time']) && (bool)$item_list['time'][$day_type]['is_shut_peak_time']) continue;
            $rules['item.time.' . $day_type . '.data.*.start_time'] = 'required|date_format:H:i';
            $rules['item.time.' . $day_type . '.data.*.end_time'] = 'required|date_format:H:i|after:item.time.' . $day_type . '.data.*.start_time';
            $rules['item.time.' . $day_type . '.data'] = ['required', 'array', function ($attribute, $value, $fail) use ($module_name) {
                // 判断时间是否合法 前面有验证格式的规则了，但这里会用到如果填写不为H:i的格式会报错，所以需要这样处理直接跳出当前循环，前面会返回对应的错误信息
                $pattern = '/^(?:2[0-3]|[01][0-9]):[0-5][0-9]$/';
                date_default_timezone_set('GMT');
                foreach ($value as $v) {
                    if (!isset($v['start_time']) || !preg_match($pattern, $v['start_time']) || !isset($v['end_time']) || !preg_match($pattern, $v['end_time'])) return;
                    // 时间转换成秒
                    $start = explode(':', $v['start_time']);
                    $end = explode(':', $v['end_time']);
                    $start_time = $start[0] * 3600 + $start[1] * 60;
                    $end_time = $end[0] * 3600 + $end[1] * 60;
                    // 更新进新列表
                    $array[] = array(
                        'start_s' => $start_time,
                        'end_s' => $end_time
                    );
                }

                foreach ($array as $k => $v) {
                    foreach ($array as $kk => $vv) {
                        if ($kk != $k) {
                            if (self::isTimeCross($v['start_s'], $v['end_s'], $vv['start_s'], $vv['end_s'])) {
                                $message = __("$module_name.validate_time_repetition", [
                                    'start_time' => date('H:i', $v['start_s']),
                                    'end_time' => date('H:i', $v['end_s']),
                                    'start_time2' => date('H:i', $vv['start_s']),
                                    'end_time2' => date('H:i', $vv['end_s'])
                                ]);
                                $fail($message);
                                return;
                            }
                        }
                    }
                }
                date_default_timezone_set(config('app.timezone'));
            }];
        }
        // 如果不存在或者布尔值不为true就给验证规则必须有对应数据
        if (!isset($item_list['time']['DEFAULT']) || !isset($item_list['time']['DEFAULT']['is_shut_peak_time']) || !(bool)$item_list['time']['DEFAULT']['is_shut_peak_time']) {
            $rules['item.time.DEFAULT.data'] = 'required|array';
        }
        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(?Request $request): array
    {
        $module_name = self::$module_name;
        $attributes = [
            'peak_time_table_number' => __("$module_name.peak_time_table_number"),
            'name' => __("$module_name.name"),
            'site_number' => __("$module_name.site"),
            'sort_order' => __("$module_name.sort_order"),
            'remark' => __("$module_name.remark"),
            'item' => __("$module_name.item"),
        ];
        $item_list = $request->input('item', array());
        foreach ($item_list['day_type'] as $day_type => $day) {
            $attributes['item.time.' . $day_type . '.data.*.start_time'] = __("$module_name.start_time");
            $attributes['item.time.' . $day_type . '.data.*.end_time'] = __("$module_name.end_time");
            $attributes['item.time.' . $day_type . '.data'] = DayType::hasValue($day_type) ? DayType::getDescription($day_type) : __("$module_name.default_day");
        }
        $attributes['item.time.DEFAULT.data'] = __("$module_name.default_day");
        return $attributes;
    }

    /**
     * 返回地址栏参数
     *
     * @param Request $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(Request $request): array
    {
        return array(
            'is_page' => 'true',
            'name_search' => $request->get('name_search'),
            'site_search' => $request->get('site_search'),
        );
    }

    /**
     * PHP计算两个时间段是否有交集（边界重叠不算）
     *
     * @param string $beginTime1 开始时间1
     * @param string $endTime1 结束时间1
     * @param string $beginTime2 开始时间2
     * @param string $endTime2 结束时间2
     * @return bool
     */
    protected static function isTimeCross(string $beginTime1 = '', string $endTime1 = '', string $beginTime2 = '', string $endTime2 = ''): bool
    {
        $status = $beginTime2 - $beginTime1;
        if ($status > 0) {
            $status2 = $beginTime2 - $endTime1;
            if ($status2 >= 0) {
                return false;
            } else {
                return true;
            }
        } else {
            $status2 = $endTime2 - $beginTime1;
            if ($status2 > 0) {
                return true;
            } else {
                return false;
            }
        }
    }

    public function delete(Request $request): JsonResponse
    {
        $peak_time_table_number = $request->input('peak_time_table_number');
        $module_name = self::$module_name;

        if (filled($peak_time_table_number) &&
            filled($peak_time_table = PeakTimeTable::with('connectorSetting')
                ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->firstWhere('peak_time_table_number', $peak_time_table_number))
        ) {
            if (blank($connector_setting_list = $peak_time_table->connectorSetting)) {
                $peak_time_table->delete();
                self::delRedis($peak_time_table_number);
                self::sendInitPushByKioskNumberList();
            } else {
                $connector_setting_str = '';
                foreach ($connector_setting_list as $connector_setting) {
                    $connector_setting_str .= '<li>' . $connector_setting->name . '</li>';
                }
                $this->code = 201;
                $this->message = __('common.error_has_binding_unable_to_delete_tariff_table', [
                    'connector_setting' => $connector_setting_str
                ]);
            }

        } else {
            $this->notFoundData(__("$module_name.web_title"));
        }

        return $this->returnJson();
    }

    public static function delRedis($peak_time_table_number)
    {
        Redis::del("peakTimeTable:$peak_time_table_number");
    }

}

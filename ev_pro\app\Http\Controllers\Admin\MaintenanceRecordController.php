<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Contracts\View\{
    Factory,
    View
};
use Illuminate\Foundation\Application;
use Illuminate\Http\{
    JsonResponse,
    RedirectResponse
};
use Illuminate\Http\Request;
use App\Http\Controllers\Admin\Traits\{
    Add,
    Edit
};
use App\Models\Modules\{
    MaintenanceRecord,
    Kiosk,
    Site,
};
use App\Enums\{
    WebsocketType,
};
use Illuminate\Database\Eloquent\Model;

class MaintenanceRecordController extends CommonController
{
    use Add, Edit;

    protected static string $module_name = 'maintenanceRecord'; // 模块名称

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new MaintenanceRecord;
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'add_url' => action([self::class, 'add']),
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'start_search' => $request->input('start_search'),
            'end_search' => $request->input('end_search'),
        );

        $data['buttonShow'] = false;/*filled(MaintenanceRecord::whereNull('gmt_end')->first());*/

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $length = (int)$request->input('length', 10);
        $start_search = $request->input('start_search');
        $end_search = $request->input('end_search');

        $start_search_list = self::getRangeDateTimeArray($start_search ?: '') ?: null;
        $end_search_list = self::getRangeDateTimeArray($end_search ?: '') ?: null;

        // 通过当前管理员的site_number_list拼接find_in_set语句
        $find_in_set_sql = '';
        foreach (auth()->user()->site_number_list as $index => $site_number) {
            if ($index == 0) {
                $find_in_set_sql .= "find_in_set('$site_number', site_number_list)";
                continue;
            }
            $find_in_set_sql .= " or find_in_set('$site_number', site_number_list)";
        }

        $data_list = MaintenanceRecord::when(filled($start_search_list), function ($query) use ($start_search_list) {
            return $query->whereBetween('gmt_start', $start_search_list);
        })
            ->when(filled($end_search_list), function ($query) use ($end_search_list) {
                return $query->whereBetween('gmt_end', $end_search_list);
            })
            ->when(!isSuperAdministrator(), fn($query) => $query->whereRaw('IF(site_number_list IS NOT NULL, ' . $find_in_set_sql . ', TRUE)'))
            ->orderBy($order, $sort)
            ->orderBy('gmt_modified', 'desc')
            ->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            // 获取对应kiosk_number_list的kiosk name
            $kiosk_name_list = array();
            if (!empty($data->kiosk_number_list)) {
                $kiosk_name_list = Kiosk::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                    ->whereIn('kiosk_number', (explode(',', $data->kiosk_number_list)) ?? array())
                    ->pluck('name')
                    ->toArray();
            }
            // 获取对应site_number_list的site name
            $site_name_json_list = $site_name_list = array();
            if (!empty($data->site_number_list)) {
                $site_name_json_list = Site::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                    ->whereIn('site_number', (explode(',', $data->site_number_list)) ?? array())
                    ->pluck('name_json')
                    ->toArray();
            }
            foreach ($site_name_json_list as $name_json) {
                $site_name_list[] = $this->getValueFromLanguageArray($name_json) ?? '—/—';
            }
            $result[] = array(
                'maintenance_record_id' => $data->maintenance_record_id, // 维修记录ID
                'site_number_list' => $site_name_list, // Site名称
                'kiosk_number_list' => $kiosk_name_list, // Kiosk名称
                'gmt_start' => $data->gmt_start, // 开始日期
                'gmt_end' => $data->gmt_end ?? '—/—', // 结束日期
                'is_bypass_mode' => $data->is_bypass_mode, // 是否为手动操作充电机模式
                'remark' => $data->remark ?? '—/—', // 备注
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result
        );
        return response()->json($json);
    }

    protected function getForm(Request $request, array $data): View|Application|Factory
    {
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'], self::getUrlParams($request)
        );

        $data['model']->site_number_list = $request->old('site_number_list', $data['model']->site_number_list);
        // 获取对应site_number_list的site name
        $site_name_json_list = $site_name_list = array();
        if (!empty($data['model']->site_number_list)) {
            $site_name_json_list = Site::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->whereIn('site_number', (explode(',', $data['model']->site_number_list)) ?? array())
                ->pluck('name_json')
                ->toArray();
        }
        foreach ($site_name_json_list as $name_json) {
            $site_name_list[] = $this->getValueFromLanguageArray($name_json) ?? '—/—';
        }

        $data['model']->kiosk_number_list = $request->old('kiosk_number_list', $data['model']->kiosk_number_list);
        // 获取对应kiosk_number_list的kiosk name
        $kiosk_name_list = array();
        if (!empty($data['model']->kiosk_number_list)) {
            $kiosk_name_list = Kiosk::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->whereIn('kiosk_number', (explode(',', $data['model']->kiosk_number_list)) ?? array())
                ->pluck('name')
                ->toArray();
        }
        $data['model']->site_name = !empty($site_name_list) ? implode(' | ', $site_name_list) : ''; // Site名称
        $data['model']->kiosk_name = !empty($kiosk_name_list) ? implode(' | ', $kiosk_name_list) : ''; // Kiosk名称
        $data['model']->gmt_start = $request->old('gmt_start', $data['model']->gmt_start);
        $data['model']->gmt_end = $request->old('gmt_end', $data['model']->gmt_end);
        $data['model']->is_bypass_mode = $request->old('is_bypass_mode', $data['model']->is_bypass_mode);
        $data['model']->remark = $request->old('remark', $data['model']->remark);

        return view("pages.{$data['module_name']}.form", $data);
    }

    // 快速创建一条新的维修数据
    public function emergency(Request $request, MaintenanceRecord $model)
    {
        $request->validate(self::isBypassModeRule(), [], self::attributes());
        $model->gmt_start = date("Y-m-d H:i:s");
        $model->gmt_end = null;
        $model->site_number_list = $request->input('site_number_list');
        $model->kiosk_number_list = $request->input('kiosk_number_list');
        $model->is_bypass_mode = $request->input('is_bypass_mode', 0);
        $model->remark = $request->input('remark');
        $model->save();

        if (!filled($model->kiosk_number_list)) {
            // 将ids拆分
            $kiosk_number_list = explode(',', $model->kiosk_number_list) ?? array();

            foreach ($kiosk_number_list as $kiosk_number) {
                if (filled(Kiosk::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('kiosk_number', $kiosk_number))) {
                    self::websocketPush(WebsocketType::MaintenanceNotification, null, $kiosk_number);
                }
            }
        } else {
            $this->notFoundData('KIOSK');
        }

        return $this->returnJson();
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @return array
     */
    protected static function isBypassModeRule(): array
    {
        $rules = array(
            'is_bypass_mode' => 'boolean',
            'site_number_list' => 'nullable|exists:App\Models\Modules\Site,site_number',
            'kiosk_number_list' => 'nullable|exists:App\Models\Modules\Kiosk,kiosk_number',
            'remark' => 'nullable|max:1000',
        );

        return $rules;
    }

    // 找到最新的一条未停止的数据停止
    public function latest()
    {
        $model = MaintenanceRecord::whereNull('gmt_end')
            ->orderBy('gmt_create', 'desc')
            ->first();

        if (filled($model)) {
            if (filled($model->kiosk_number_list)) {
                // 将ids拆分
                $kiosk_number_list = explode(',', $model->kiosk_number_list) ?? array();

                foreach ($kiosk_number_list as $kiosk_number) {
                    if (filled(Kiosk::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('kiosk_number', $kiosk_number))) {
                        self::websocketPush(WebsocketType::MaintenanceNotification, null, $kiosk_number);
                    }
                }
                $model->gmt_end = date("Y-m-d H:i:s");
                $model->save();
            } else {
                $this->notFoundData('KIOSK');
            }
        } else {
            $this->notFoundData('End Time null');
        }

        return $this->returnJson();
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @param MaintenanceRecord $model
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    protected function modelValidateAndSave(Request $request, MaintenanceRecord $model): RedirectResponse
    {
        $request->validate(self::rules($request, $model), [], self::attributes());
        $model->gmt_start = $request->input('gmt_start');
        $model->gmt_end = $request->input('gmt_end');
        $model->site_number_list = $request->input('site_number_list');
        $model->kiosk_number_list = $request->input('kiosk_number_list');
        $model->is_bypass_mode = $request->input('is_bypass_mode', 0);
        $model->remark = $request->input('remark');
        $model->save();

        // Websocket Push
        if (filled($model->site_number_list)) {
            // 将ids拆分
            $site_number_list = explode(',', $model->site_number_list) ?? array();
            foreach ($site_number_list as $site_number) {
                if (filled($kiosk_list = Kiosk::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->where('site_number', $site_number)->get())) {
                    foreach ($kiosk_list as $kiosk) {
                        self::websocketPush(WebsocketType::MaintenanceNotification, null, $kiosk->kiosk_number);
                    }
                }
            }
        }

        // Websocket Push
        if (filled($model->kiosk_number_list)) {
            // 将ids拆分
            $kiosk_number_list = explode(',', $model->kiosk_number_list) ?? array();
            foreach ($kiosk_number_list as $kiosk_number) {
                if (filled(Kiosk::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('kiosk_number', $kiosk_number))) {
                    self::websocketPush(WebsocketType::MaintenanceNotification, null, $kiosk_number);
                }
            }
        }

        if (!filled($model->kiosk_number_list) && !filled($model->site_id_list)) {
            // 如果没有kiosk id并且没有选中Site就是所有kiosk都在维修直接查询全部id
            $kiosk_model = Kiosk::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->get();
            foreach ($kiosk_model as $kiosk_item) {
                $kiosk_number = $kiosk_item->kiosk_number;
                self::websocketPush(WebsocketType::MaintenanceNotification, null, $kiosk_number);
            }
        }
        return redirect()->action(
            [self::class, 'showPage'], self::getUrlParams($request)
        );
    }

    public function delete(Request $request): JsonResponse
    {
        $id = $request->post('id');

        if (!empty($id)) {
            $model = $this->model::find($id);
            if (filled($model)) {
                $model->delete();

                self::sendInitPushByKioskNumberList();
            } else {
                $this->notFoundData('ID');
            }
        } else {
            $this->notFoundData('ID');
        }

        return $this->returnJson();
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @return array
     */
    protected static function rules(?Request $request, ?Model $model): array
    {
        $module_name = self::$module_name;
        $rules = array(
            'gmt_start' => 'required|date_format:Y-m-d H:i:s',
            'gmt_end' => 'nullable|date_format:Y-m-d H:i:s|after:gmt_start',
            'remark' => 'nullable|max:1000',
            'is_bypass_mode' => 'bool',
        );

        $rules['site_number_list'] = ['nullable', function ($attribute, $value, $fail) use ($module_name) {
            $site_number_list = explode(',', $value) ?? array();
            foreach ($site_number_list as $site_number) {
                // 如果选中项有不存在的返回错误
                if (!filled(Site::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('site_number', $site_number))) {
                    $fail(__("$module_name.select_site_not_found"));
                }
            }
        }];

        $rules['kiosk_number_list'] = ['nullable', function ($attribute, $value, $fail) use ($module_name) {
            $kiosk_number_list = explode(',', $value) ?? array();
            foreach ($kiosk_number_list as $kiosk_number) {
                // 如果选中项有不存在的返回错误
                if (!filled(Kiosk::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('kiosk_number', $kiosk_number))) {
                    $fail(__("$module_name.select_kiosk_not_found"));
                }
            }
        }];

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'gmt_start' => __("$module_name.gmt_start"),
            'gmt_end' => __("$module_name.gmt_end"),
            'site_number_list' => __("$module_name.site"),
            'kiosk_number_list' => __("$module_name.kiosk"),
            'is_bypass_mode' => __("$module_name.is_bypass_mode"),
            'remark' => __("$module_name.remark"),
        ];
    }

    /**
     * 返回地址栏参数
     *
     * @param Request $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => 'true',
            'start_search' => $request->get('start_search'),
            'end_search' => $request->get('end_search'),
        );
    }

    /**
     * 用于判断前端按钮是否显示，如果gmt_end都有值就不显示
     */
    public function buttonShow()
    {
//        $model = MaintenanceRecord::whereNull('gmt_end')
//            ->first();
//        if (filled($model)) {
//            return 1;
//        } else {
//            return 0;
//        }
        return 0;
    }

    /**
     * 停止维修时间
     */
    public function stopMaintenance(Request $request, $id): JsonResponse
    {
        if (!empty($id) && filled($model = MaintenanceRecord::whereNull('gmt_end')->where('maintenance_record_id', $id)->first())) {
            $date_time = date("Y-m-d H:i:s");
            if ($date_time < $model->gmt_start) {
                $this->modelSaveFail();
                return $this->returnJson();
            }
            if (filled($model->site_number_list)) {
                // 将ids拆分
                $site_number_list = explode(',', $model->site_number_list) ?? array();

                foreach ($site_number_list as $site_number) {
                    if (filled($kiosk_list = Kiosk::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->where('site_number', $site_number)->get())) {
                        foreach ($kiosk_list as $kiosk) {
                            self::websocketPush(WebsocketType::MaintenanceNotification, null, $kiosk->kiosk_number);
                        }
                    }
                }
            }

            if (filled($model->kiosk_number_list)) {
                // 将ids拆分
                $kiosk_number_list = explode(',', $model->kiosk_number_list) ?? array();

                foreach ($kiosk_number_list as $kiosk_number) {
                    if (filled(Kiosk::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('kiosk_number', $kiosk_number))) {
                        self::websocketPush(WebsocketType::MaintenanceNotification, null, $kiosk_number);
                    }
                }
            }
            // 如果都没有选择就所有Kiosk解锁
            if (!filled($model->kiosk_number_list) && !filled($model->site_id_list)) {
                // 如果没有kiosk id就是所有kiosk都在维修直接查询全部id
                if (filled($model = MaintenanceRecord::whereNull('gmt_end')->where('maintenance_record_id', $id)->first())) {
                    $kiosk_model = Kiosk::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->get();
                    foreach ($kiosk_model as $kiosk_item) {
                        $kiosk_number = $kiosk_item->kiosk_number;
                        self::websocketPush(WebsocketType::MaintenanceNotification, null, $kiosk_number);
                    }
                }
            }
            $model->gmt_end = date("Y-m-d H:i:s");
            $model->save();
        } else {
            $this->notFoundData('ID');
        }

        return $this->returnJson();
    }
}

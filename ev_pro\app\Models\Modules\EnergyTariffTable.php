<?php

namespace App\Models\Modules;

use App\Enums\TariffTableType;
use Illuminate\Database\Eloquent\Model;
use App\Models\Traits\Emoji;

class EnergyTariffTable extends Model
{
    use emoji;

    protected $table = 'energy_tariff_table'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'energy_tariff_table_id'; //定义主键，默认为id
    // 表的唯一number
    public static $table_number = 'energy_tariff_table_number';
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    // protected $fillable = [];

    /**
     * 类型转化器
     */
    protected $casts = [
        'energy_tariff_table_item_json' => 'array',
        'is_enable_admin_octopus_card_free_deduct' => 'bool',
        'is_enable_free_octopus_card' => 'bool',
        'is_enable_round_up_tail_charge_value_calculation' => 'bool',
        'member_card_group_energy_tariff_table_item_json' => 'array',
        'user_group_energy_tariff_table_item_json' => 'array',
    ];

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'post_paid_maximum_charge_time' => 0,
        'sort_order' => 0,
        'is_enable_admin_octopus_card_free_deduct' => false, // 是否开启管理员八达通卡免费扣款
        'is_enable_free_octopus_card' => false, // 是否启用免费八达通卡
        'is_enable_round_up_tail_charge_value_calculation' => false, // 是否启用向上取整尾部充电量计算
    ];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * 一对多关联EnergyTariffTableItem
     */
    public function item()
    {
        return $this->hasMany(EnergyTariffTableItem::class, 'energy_tariff_table_number', 'energy_tariff_table_number');
    }


    // deleting event
    public static function boot()
    {
        parent::boot();

        static::deleting(function ($model) {
            $model->item()->delete();
        });

        static::updating(function ($model) {
            $model->item()->delete();
        });
    }

    /**
     * 查找多个connector setting
     */
    public function connectorSetting()
    {
        return $this->hasMany(ConnectorSetting::class, 'energy_tariff_table_number', 'energy_tariff_table_number')
            ->where('tariff_table_type', '=', TariffTableType::ComplexEnergyTariffTable);
    }
}

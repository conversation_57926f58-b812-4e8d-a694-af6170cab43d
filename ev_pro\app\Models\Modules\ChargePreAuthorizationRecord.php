<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;

class ChargePreAuthorizationRecord extends Model
{

    protected $table = 'charge_pre_authorization_record';
    protected $primaryKey = 'charge_pre_authorization_record_id';

    const CREATED_AT = 'gmt_create'; //默认是小写的模型类名复数格式，此处为自定义表名
    const UPDATED_AT = 'gmt_modified'; //定义主键，默认为id


    // protected $fillable = [];
    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 类型转化器
     */
    protected $casts = [
        'is_admin_octopus_card' => 'bool',
        'is_free_octopus_card' => 'bool',
    ];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [

    ];

    /**
     * 全部字段
     *
     * @var array
     */
    public array $columns = [
        'charge_pre_authorization_record_id',
        'charge_pre_authorization_record_number',
        'merchant_number',
        'site_number',
        'payment_device',
        'charge_pre_authorization_status',
        'gmt_charge_pre_authorization_status',
        'pre_authorization_amount',
        'deduct_amount',
        'refund_amount',
        'actual_refund_amount',
        'identity_type',
        'identity_number',
        'connector_number',
        'connector_name',
        'kiosk_number',
        'kiosk_name',
        'octopus_transaction_id',
        'octopus_receipt_number',
        'gmt_octopus_deduct',
        'octopus_device_number',
        'octopus_device_number',
        'octopus_card_type',
        'octopus_card_number',
        'octopus_raw_card_number',
        'octopus_balance',
        'octopus_last_added_value_type',
        'octopus_last_added_value_date',
        'octopus_response_json',
        'is_admin_octopus_card',
        'is_free_octopus_card',
        'pos_vendor',
        'pos_company_id',
        'pos_pre_authorization_id',
        'pos_transaction_id',
        'pos_payment_method_name',
        'pos_card_number',
        'pos_trace_no',
        'pos_reference_id',
        'gmt_pos_create',
        'pos_create_response_json',
        'gmt_pos_deduct',
        'pos_deduct_response_json',
        'gmt_pos_cancel',
        'pos_cancel_response_json',
        'status_log',
        'remark',
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    // 一对一关联充电枪
    public function connector()
    {
        return $this->hasOne(Connector::class, 'connector_number', 'connector_number');
    }

    // 一对一反向关联充电记录
    public function chargeRecord()
    {
        return $this->belongsTo(ChargeRecord::class, 'charge_pre_authorization_record_number', 'charge_pre_authorization_record_number');
    }

    // 一对多关联预授权退款记录
    public function chargePreAuthorizationRefundRecord()
    {
        return $this->hasMany(ChargePreAuthorizationRefundRecord::class, 'charge_pre_authorization_record_number', 'charge_pre_authorization_record_number');
    }
}

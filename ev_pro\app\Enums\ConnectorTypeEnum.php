<?php

namespace App\Enums;

use BenSampo\Enum\Enum;
use BenSampo\Enum\Contracts\LocalizedEnum;
use App\Enums\Traits\Tools;
use Lang;

/**
 * @method static Type1
 * @method static Type2
 * @method static AC_GBT
 * @method static CCS1
 * @method static CCS2
 * @method static CHAdeMO
 * @method static DC_GBT
 */
final class ConnectorTypeEnum extends Enum implements LocalizedEnum
{
    use Tools;

    const Type1 = 'TYPE_1';
    const Type2 = 'TYPE_2';
    const AC_GBT = 'AC_GBT';
    const CCS1 = 'CCS_1';
    const CCS2 = 'CCS_2';
    const CHAdeMO = 'CHADEMO';
    const DC_GBT = 'DC_GBT';

}

<?php

namespace App\Http\Controllers\Admin;

use App\Enums\ChargePointVendor;
use App\Http\Controllers\Common\CommonController;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use Illuminate\Foundation\Application;
use Illuminate\Http\{
    RedirectResponse,
    Request,
};
use Validator;

class ChargePointVendorNumberController extends CommonController
{
    protected static string $module_name = 'chargePointVendorNumber'; // 模块名称

    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    public function showPage(?Request $request): View|Application|Factory|RedirectResponse
    {
        if ($request->isMethod('post')) {
            return $this->modelValidateAndSave($request);
        }

        // 充电机供应商编号数据
        $vendor_number_list =
            $request->old('vendor', getArrayFromJsonFile('charge_point_vendor_number', false) ?: array());

        $data = array(
            'module_name' => self::$module_name,
            'vendor_option_list' => ChargePointVendor::getValues(),
            'vendor_list' => collect($vendor_number_list)->map(function ($item, $key) {
                $item['key'] = $key;
                return $item;
            })->toArray(),
        );

        return view("pages.{$data['module_name']}.view", $data);
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    protected function modelValidateAndSave(Request $request): RedirectResponse
    {
        $request->validate(self::rules($request), [], self::attributes());
        $vendor_number_list = $request->input('vendor', array());

        $vendor_number_list = collect($vendor_number_list)->map(function ($item) {
            unset($item['key']);
            return $item;
        })->values()->toArray();

        // 保存数据至json文件
        saveArrayToJSONFile($vendor_number_list, 'charge_point_vendor_number');
        // 设置缓存数据，刷新后自动清除
        session()->flash('is_charge_vendor_number_saved');

        return redirect()->action([self::class, 'showPage']);
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @return array
     */
    protected static function rules(?Request $request): array
    {
        $rules = array();

        $rules['vendor.*.vendor_name'] = 'required|max:45|distinct|enum_value:' . ChargePointVendor::class;
        $rules['vendor.*.vendor_number'] = 'required|numeric|digits:3|distinct';

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'vendor.*.vendor_name' => __("$module_name.vendor_name"),
            'vendor.*.vendor_number' => __("$module_name.vendor_number"),
        ];
    }

}

<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Exception;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Application;
use Illuminate\Http\{
    JsonResponse,
    RedirectResponse,
    Request,
};
use App\Http\Controllers\Admin\Traits\{
    Add,
    Edit,
};
use App\Models\Modules\{
    ChargePoint,
    ChargePointCs,
    Connector,
    EventLog,
    Merchant,
    MerchantCurrency,
    Site,
    SiteDescription,
    SiteImage,
    Region,
};
use App\Enums\{
    ConnectorStatus,
    EventTypeEnum,
    LmsMode,
};

class SiteController extends CommonController
{
    use Add, Edit;

    protected static string $module_name = 'site'; // 模块名称

    protected static bool $module_check_merchant = true; // 该模块校验商户

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new Site;
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'add_url' => action([self::class, 'add']),
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'name_search' => $request->get('name_search'),
            'lms_mode_search' => $request->get('lms_mode_search'),
            'merchant_search' => $request->get('merchant_search'),
        );

        $data['lms_mode_list'] = array();
        foreach (LmsMode::asSelectArray() as $value => $name) {
            $data['lms_mode_list'][] = array(
                'name' => $name,
                'value' => $value,
            );
        }

        // 商户下拉列表
        $data['merchant_list'] = $this->getMerchantOptionList();

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'sort_order');
        $sort = $request->input('sort', 'asc');
        $length = (int)$request->input('length', 10);
        $name_search = $request->input('name_search');
        $lms_mode_search = $request->input('lms_mode_search');
        $merchant_search = $request->input('merchant_search');

        if ($order == 'name') $order = 'site.name_json';
        if ($order == 'merchant_name') $order = 'merchant.name_json';

        $data_list = Site::with(['description', 'merchant', 'region'])
            ->when(filled($lms_mode_search), fn ($query) => $query->where('site.lms_mode', $lms_mode_search))
            ->when(filled($name_search), function ($query) use ($name_search) {
                return $query->whereHas('description', function ($query) use ($name_search) {
                    return $query->where('name', 'like', "%{$name_search}%");
                });
            })
            ->when(filled($merchant_search), fn ($query) => $query->where('site.merchant_number', $merchant_search))
            ->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site.site_number', auth()->user()->site_number_list))
            ->orderBy($order, $sort)
            ->latest('gmt_modified')
            ->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $merchant_name = $this->getValueFromLanguageArray($data->merchant?->name_json) ?? '—/—'; // 商户名称
            $region_name = $this->getValueFromLanguageArray($data->region?->name_json) ?? '—/—'; // 地区名称
            $result[] = array(
                'site_id' => $data->site_id, // 场地ID
                'site_number' => $data->site_number,   // 场地编号
                'name' => $this->getValueFromLanguageArray($data->name_json), // 名称
                'acronym' => $data->acronym ?? '—/—',   // 首字母缩写
                'is_public_site' => $data->is_public_site, // 是否公开
                'merchant_number' => $data->merchant_number,   // 商户编号
                'merchant_name' => $merchant_name, // 商户名称
                'region_name' => $region_name, // 地区名称
                'lms_mode' => LmsMode::getDescription($data->lms_mode), // LMS模式
                'maximum_charging_connector_count' => $data->maximum_charging_connector_count, // 最大充电中充电枪数量
                'main_image_url' => existsImage('public', $data->main_image_url) ?: existsImage('icon', 'not_select_image.png'), // 主图路径
                'telephone' => $data->telephone ?? '—/—', // 电话
                'email' => $data->email ?? '—/—', // 电邮
                'longitude' => $data->longitude ?? '—/—', // 经度
                'latitude' => $data->latitude ?? '—/—', // 纬度
                'sort_order' => $data->sort_order, // 排序
                'remark' => $data->remark ?? '—/—', //备注
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );

        return response()->json($json);
    }

    protected function getForm(Request $request, array $data): View|Application|Factory
    {
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'],
            self::getUrlParams($request),
        );


        // 新增时才回显场地编号和商户编号
        if (blank($data['model']->site_id)) {
            $data['model']->site_number = $request->old('site_number', $data['model']->site_number); // 场地编号
            // 超级管理员或者多商户能选商户，否则只取所属的单商户
            $merchant_number = isSuperAdministrator() || auth()->user()->merchant_number_list->count() > 1
                ? $request->old('merchant_number', $data['model']->merchant_number)
                : (auth()->user()->merchant_number_list->first() ?? null);
            $merchant_name = $this->getValueFromLanguageArray(Merchant::firstWhere('merchant_number', $merchant_number)?->name_json);
            $data['model']->merchant_number = $merchant_number; // 商户编号
            $data['model']->merchant_name = $merchant_name; // 商户名称
        }

        $data['model']->region_id = $request->old('region_id', $data['model']->region_id); // 地区ID
        if (filled($data['model']->region_id)) {
            $region = Region::when(!isSuperAdministrator(), function ($query) {
                return $query->where('merchant__number', auth()->user()->merchant_number_list);
            })->find($data['model']->region_id);
            $data['model']->region_name = $this->getValueFromLanguageArray($region->name_json) ?? '—/—'; // 地区名称
        }
        $data['model']->currency_code = $request->old('currency_code', $data['model']->currency_code); // 货币代码
        $data['model']->acronym = $request->old('acronym', $data['model']->acronym); // 首字母缩写
        $data['model']->is_public_site = $request->old('is_public_site', $data['model']->is_public_site); // 是否公开
        $data['model']->lms_mode = $request->old('lms_mode', $data['model']->lms_mode); // LMS模式
        $data['model']->main_image_url = $request->old('main_image_url', $data['model']->main_image_url); // 主图路径
        $data['model']->telephone = $request->old('telephone', $data['model']->telephone); // 电话
        $data['model']->email = $request->old('email', $data['model']->email); // 电邮
        $data['model']->longitude = $request->old('longitude', $data['model']->longitude); // 经度
        $data['model']->latitude = $request->old('latitude', $data['model']->latitude); // 纬度
        $data['model']->maximum_charging_connector_count = $request->old('maximum_charging_connector_count', $data['model']->maximum_charging_connector_count); // 最大充电中充电枪数量
        $data['model']->remark = $request->old('remark', $data['model']->remark); // 备注
        $data['model']->sort_order = $request->old('sort_order', $data['model']->sort_order); // 排序

        $data['lms_mode_list'] = array();
        foreach (LmsMode::asSelectArray() as $value => $name) {
            $data['lms_mode_list'][] = array(
                'name' => $name,
                'value' => $value,
            );
        }

        // 读取系统语言并查询出对应KioskSettingDescription数据
        $data['item'] = array();

        $config_languages = config('languages');
        foreach ($config_languages as $language_code => $language_name) {
            // 有旧数据
            $site_description_old_list = $request->old('item');
            if (filled($site_description_old_list)) {
                $data['item'][$language_code] = array(
                    'language_name' => $language_name,
                    'language_code' => $language_code,
                    'name' => $site_description_old_list[$language_code]['name'] ?? null,
                    'site_description_id' => $site_description_old_list[$language_code]['site_description_id'] ?? null, // ID
                    'address' => $site_description_old_list[$language_code]['address'] ?? null,
                    'google_map_url' => $site_description_old_list[$language_code]['google_map_url'] ?? null,
                    'apple_map_url' => $site_description_old_list[$language_code]['apple_map_url'] ?? null,
                    'amap_map_url' => $site_description_old_list[$language_code]['amap_map_url'] ?? null,
                    'opening_hours' => $site_description_old_list[$language_code]['opening_hours'] ?? null,
                    'fee' => $site_description_old_list[$language_code]['fee'] ?? null,
                    'description' => $site_description_old_list[$language_code]['description'] ?? null,
                );
            } else {
                $site_description_result = $data['model']->description->where('language_code', $language_code)->first();
                $data['item'][$language_code] = array(
                    'language_name' => $language_name,
                    'language_code' => $language_code,
                    'name' => $site_description_result->name ?? null,
                    'site_description_id' => $site_description_result->site_description_id ?? null, // ID
                    'address' => $site_description_result->address ?? null, // 地址
                    'google_map_url' => $site_description_result->google_map_url ?? null, // 谷歌地图地址
                    'apple_map_url' => $site_description_result->apple_map_url ?? null, // 苹果地图地址
                    'amap_map_url' => $site_description_result->amap_map_url ?? null, // 高德地图地址
                    'opening_hours' => $site_description_result->opening_hours ?? null, // 营业时间
                    'fee' => $site_description_result->fee ?? null, // 收費
                    'description' => $site_description_result->description ?? null, // 描述
                );
            }
        }

        // 获取当前site_image数据
        $data['site_image_list'] = array();
        // 有旧数据
        $site_image_old_list = $request->old('site_image_list');
        if ($site_image_old_list && filled($site_image_old_list)) {
            foreach ($site_image_old_list as $rows => $site_image_old_list_item) {
                $data['site_image_list'][$rows] = array(
                    'image_url' => existsImage('public', $site_image_old_list_item['image_url'] ?? null), // 拼接文件路径
                    'image_url_name' => $site_image_old_list_item['image_url'] ?? null,
                    'sort_order' => $site_image_old_list_item['sort_order'] ?? 0,
                );
                // 旧数据可能存在site_image_id
                if (isset($site_image_old_list_item['site_image_id']) && filled($site_image_old_list_item['site_image_id'])) $data['site_image_list'][$rows]['site_image_id'] = $site_image_old_list_item['site_image_id'];
            }
        } else {
            $site_image_result = $data['model']->image->toArray();
            foreach ($site_image_result as $site_image_result_item) {
                $data['site_image_list'][] = array(
                    'site_image_id' => $site_image_result_item['site_image_id'],
                    'image_url' => existsImage('public', $site_image_result_item['image_url'] ?? null), // 拼接文件路径
                    'image_url_name' => $site_image_result_item['image_url'],
                    'sort_order' => $site_image_result_item['sort_order'] ?? 0, // 排序
                );
            }
        }

        // 商户货币列表
        $merchant_currency_list = [];
        if (filled($data['model']->merchant_number)) {
            $merchant_currency_list = MerchantCurrency::where('merchant_number', $data['model']->merchant_number)
            ->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))
            ->get();
        }
        $data['merchant_currency_list'] = $merchant_currency_list;

        return view("pages.{$data['module_name']}.form", $data);
    }

    public function delete(Request $request): JsonResponse
    {
        $site_number = $request->post('site_number');
        $module_name = self::$module_name;

        if (
            filled($site_number) &&
            filled($site = Site::with(['kiosk', 'chargePoint'])
                ->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->firstWhere('site_number', $site_number))
        ) {
            // 如果没有Kiosk和ChargePoint绑定才可以删除
            if ($site->kiosk->count() == 0 && $site->chargePoint->count() == 0) {
                // 删除site和中间表
                $site->role()->detach();
                $site->delete();
            } else {
                $this->code = 40001;
                $this->message = __("$module_name.error_site_mismatching");
            }
        } else {
            $this->notFoundData(__("$module_name.web_title"));
        }

        return $this->returnJson();
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @param Site $model
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    protected function modelValidateAndSave(Request $request, Site $model): RedirectResponse
    {
        $request->validate(self::rules($request, $model), [], self::attributes());
        // 新增时才保存场地和商户编号
        if (blank($model->site_id)) {
            $model->site_number = $request->input('site_number');
            $model->merchant_number = $request->input('merchant_number');
        }
        $model->region_id = $request->input('region_id');
        $model->acronym = $request->input('acronym');
        $model->is_public_site = $request->input('is_public_site', 0);
        $model->lms_mode = $request->input('lms_mode');
        $model->maximum_charging_connector_count = $request->input('maximum_charging_connector_count', 0);
        $model->main_image_url = $request->input('main_image_url');
        $model->telephone = $request->input('telephone');
        $model->email = $request->input('email');
        $model->longitude = $request->input('longitude');
        $model->latitude = $request->input('latitude');
        $model->remark = $request->input('remark');
        $model->sort_order = $request->input('sort_order', 0);
        // 获取请求场地描述数据
        $site_description_list = $request->input('item', array());
        // 获取请求场地图片数据
        $site_image_list = $request->input('site_image_list', array());
        $name_json = array();
        foreach ($site_description_list as $site_description) {
            $name_json[$site_description['language_code']] = $site_description['name'];
        }
        $model->name_json = $name_json;
        $model->save();

        // 保存Site描述后再保存并关联Site描述
        foreach ($site_description_list as $site_description) {
            if (isset($site_description['site_description_id'])) {
                $site_description_model = SiteDescription::findOr($site_description['site_description_id'], function () {
                    // 获取不到即新增
                    return new SiteDescription;
                });
            } else {
                $site_description_model = new SiteDescription;
            }
            $site_description_model->site_number = $model->site_number;
            $site_description_model->language_code = $site_description['language_code'];
            $site_description_model->name = $site_description['name'];
            $site_description_model->address = $site_description['address'] ?? null;
            $site_description_model->google_map_url = $site_description['google_map_url'] ?? null;
            $site_description_model->apple_map_url = $site_description['apple_map_url'] ?? null;
            $site_description_model->amap_map_url = $site_description['amap_map_url'] ?? null;
            $site_description_model->opening_hours = $site_description['opening_hours'] ?? null;
            $site_description_model->fee = $site_description['fee'] ?? null;
            $site_description_model->description = $site_description['description'] ?? null;

            $site_description_model->save();
        }
        $new_site_image_id_list = array_column($site_image_list, 'site_image_id');
        $old_site_image_id_list = SiteImage::where('site_number', $model->site_number)->pluck('site_image_id')->toArray();
        $del_id_list = array_diff($old_site_image_id_list, $new_site_image_id_list);
        if (!empty($del_id_list)) {
            SiteImage::whereIn('site_image_id', $del_id_list)->delete();
        }
        // 保存Site图片后再保存并关联Site图片
        foreach ($site_image_list as $site_image) {
            if (isset($site_image['site_image_id'])) {
                $site_image_model = SiteImage::findOr($site_image['site_image_id'], function () {
                    // 获取不到即新增
                    return new SiteImage;
                });
            } else {
                $site_image_model = new SiteImage;
            }
            $site_image_model->site_number = $model->site_number;
            $site_image_model->image_url = $site_image['image_url'];
            $site_image_model->sort_order = $site_image['sort_order'] ?? 0;

            $site_image_model->save();
        }

        self::delAllRedisTariffTable();

        return redirect()->action(
            [self::class, 'showPage'],
            self::getUrlParams($request)
        );
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function rules(?Request $request, ?Model $model): array
    {
        // 用户
        $user = auth()->user();
        $module_name = self::$module_name;

        $rules = array(
            'region_id' => [
                'nullable',
                'exists:App\Models\Modules\Region,region_id',
                function ($attr, $value, $fail) use ($module_name, $request) {
                    // 查询region
                    $region = Region::find($value);
                    // region的商户是否在当前管理员的商户列表中
                    if (filled($region) && !isSuperAdministrator() && !in_array($region->merchant_number, auth()->user()->merchant_number_list->toArray())) {
                        $fail(__('common.text_not_found', ['field' => __("$module_name.region")]));
                    }
                    // region的商户与site的商户一致
                    if (filled($region) && $region->merchant_number != $request->merchant_number) {
                        $fail(__("$module_name.error_region_merchant_must_be_belong_to_site_merchant"));
                    }
                },
            ],
            'acronym' => 'nullable|max:45',
            'is_public_site' => 'bool',
            'maximum_charging_connector_count' => 'required|integer|min:0|max:999999',
            'telephone' => 'nullable|max:100',
            'email' => 'nullable|email:rfc|max:100',
            'longitude' => 'nullable|numeric|between:-180,180|required_unless:latitude,null',
            'latitude' => 'nullable|numeric|between:-90,90|required_unless:longitude,null',
            'sort_order' => 'integer|min:0',
            'remark' => 'nullable|max:1000',
        );

        // 只有新增时才校验场地和商户编号
        if (blank($model->site_id)) {
            $rules['site_number'] = [
                'required',
                'max:10',
                'regex:/^[a-zA-Z0-9]+$/', // 只能填写字母和数字
                'unique:App\Models\Modules\Site,site_number',
            ];
            if (isSuperAdministrator() || auth()->user()->merchant_number_list->count() > 1) {
                $rules['merchant_number'] = [
                    'required',
                    'exists:App\Models\Modules\Merchant,merchant_number',
                    function ($attr, $value, $fail) use ($request, $model, $module_name) {
                        // 新增时校验非超级管理员且未拥有该商户提示错误
                        if (!isSuperAdministrator() && !in_array($value, auth()->user()->merchant_number_list->toArray())) {
                            $fail(__('common.text_not_found', ['field' => __("$module_name.merchant")]));
                        }
                    },
                ];
            }
        }

        $rules['item.*.name'] = 'required|max:45';
        $rules['item.*.address'] = 'nullable|max:1000';
        $rules['item.*.google_map_url'] = 'nullable|max:1000';
        $rules['item.*.apple_map_url'] = 'nullable|max:1000';
        $rules['item.*.amap_map_url'] = 'nullable|max:1000';
        $rules['item.*.opening_hours'] = 'nullable|max:1000';
        $rules['item.*.description'] = 'nullable|max:1000';

        $module_name = self::$module_name;

        $allow_format = array();
        $format_list = json_decode(file_get_contents(base_path() . '/format.json'), true);

        $rules['site_image_list.*.image_url'] = [
            'required',
            function ($attribute, $value, $fail) use ($format_list, $allow_format, $module_name) {
                // 获取后缀名文件格式然后转小写
                $value_lower = strtolower(pathinfo($value, PATHINFO_EXTENSION));
                if ($allow_format && !in_array($value_lower, $allow_format)) {
                    $message = __("$module_name.image_must_be_for") . implode(',', $format_list['image']);
                    $fail($message);
                    return;
                }
            },
        ];
        $rules['site_image_list.*.sort_order'] = 'integer|min:0';

        $rules['lms_mode'] = ['required', 'max:45', 'enum_value:' . LmsMode::class, function ($attribute, $value, $fail) use ($model, $user) {
            // 场地编辑且修改lms_mode时需要调用chargePointCS的接口，新增不处理
            if (filled($value) && filled($model->site_id)) {
                // 返回错误message数组
                $response_message_list = array();
                // lms mode文字
                $mode = match ($value) {
                    LmsMode::NoLms => 'None',
                    LmsMode::EvenDistribution => 'EvenlyDistributed',
                    LmsMode::MaximumOutput => 'Queue',
                    default => '',
                };

                // 获取结果
                $charge_point_result_list = ChargePoint::select('charge_point_cs_number')
                    ->where('site_number', $model->site_number)
                    ->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', $user->site_number_list))
                    ->whereNotNull('charge_point_cs_number')
                    ->groupBy('charge_point_cs_number')
                    ->pluck('charge_point_cs_number');

                foreach ($charge_point_result_list as $charge_point_cs_number) {
                    // 获取charge_point_cs
                    if (
                        filled($charge_point_cs_model = ChargePointCs::firstWhere('charge_point_cs_number', $charge_point_cs_number)) &&
                        filled($charge_point_cs_model->api_url) &&
                        filled($charge_point_cs_model->api_token)
                    ) {
                        try {
                            $params = array(
                                'key' => $charge_point_cs_model->api_token,
                                'mode' => $mode,
                            );

                            // 获取返回信息
                            $response = self::curlPostJson(
                                $charge_point_cs_model->api_url . '/command/setlmsmode',
                                json_encode($params),
                                array('Content-Type: application/json; charset=utf-8'),
                            );

                            $format_result = json_decode(htmlspecialchars_decode($response), true);

                            // 失败时保存EventLog
                            if ((isset($format_result['status']) && $format_result['status'] != 200) ||
                                (isset($format_result['response']) && strtolower($format_result['response']) != 'success')
                            ) {
                                $response_message_list[] = ($charge_point_cs_model->name ?? $charge_point_cs_number) . ': ' . $format_result['response'];

                                $event_log_model = new EventLog;
                                $event_log_model->target = 'CMS';
                                $event_log_model->target_number = $user->administrator_id;
                                $event_log_model->target_name = $charge_point_cs_model->name;
                                $event_log_model->event_type = EventTypeEnum::PostChargePointCSLMSFailure;
                                $event_log_model->description = 'Site CMS Change LMS Mode Error:' . $response;

                                self::saveEventLogOrRules($event_log_model, true);
                            }
                        } catch (Exception $e) {
                            $response_message_list[] = ($charge_point_cs_model->name ?? $charge_point_cs_number) . ': ' . $e->getMessage();

                            $event_log_model = new EventLog;
                            $event_log_model->target = 'CMS';
                            $event_log_model->target_number = $user->administrator_id;
                            $event_log_model->target_name = $charge_point_cs_model->name;
                            $event_log_model->event_type = EventTypeEnum::PostChargePointCSLMSFailure;
                            $event_log_model->description = 'Site CMS Change LMS Mode Error:' . $e->getMessage();

                            self::saveEventLogOrRules($event_log_model, true);
                        }
                    }
                }

                // 如果有错误提示错误
                if (!empty($response_message_list)) {
                    $fail(implode('<br>', $response_message_list));
                }
            }
        }];

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'region_id' => __("$module_name.region"),
            'acronym' => __("$module_name.acronym"),
            'is_public_site' => __("$module_name.is_public_site"),
            'merchant_number' => __("$module_name.merchant"),
            'site_number' => __("$module_name.site_number"),
            'lms_mode' => __("$module_name.lms_mode"),
            'maximum_charging_connector_count' => __("$module_name.maximum_charging_connector_count"),
            'sort_order' => __("$module_name.sort_order"),
            'remark' => __("$module_name.remark"),
            'item.*.name' => __("$module_name.name"),
            'item.*.address' => __("$module_name.address"),
            'item.*.google_map_url' => __("$module_name.google_map_url"),
            'item.*.apple_map_url' => __("$module_name.apple_map_url"),
            'item.*.amap_map_url' => __("$module_name.amap_map_url"),
            'item.*.opening_hours' => __("$module_name.opening_hours"),
            'item.*.description' => __("$module_name.description"),
            'site_image_list.*.image_url' => __("$module_name.image_url"),
            'site_image_list.*.sort_order' => __("$module_name.sort_order"),
        ];
    }

    /**
     * 返回地址栏参数
     *
     * @param Request|null $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => 'true',
            'name_search' => $request->get('name_search'),
            'lms_mode_search' => $request->get('lms_mode_search'),
            'merchant_search' => $request->get('merchant_search'),
        );
    }

    /**
     * 获取商户货币列表
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getMerchantCurrencyList(Request $request)
    {
        $merchant_currency_list = [];
        $merchant_number = $request->input('merchant_number');
        $merchant_currency_list = MerchantCurrency::where('merchant_number', $merchant_number)
            ->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))
            ->get();
        if (filled($merchant_currency_list)) {
            $merchant_currency_list = $merchant_currency_list->map(function ($item) {
                return [
                    'currency_code' => $item->currency_code,
                    'currency_symbol' => $item->currency_symbol,
                ];
            })->all();
        }

        $this->data = $merchant_currency_list;
        return $this->returnJson();
    }
}

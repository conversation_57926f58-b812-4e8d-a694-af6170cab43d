<?php

namespace App\Enums;

use BenSampo\Enum\Enum;
use BenSampo\Enum\Contracts\LocalizedEnum;
use App\Enums\Traits\Tools;

/**
 * @method static SimpleTariffTable
 * @method static ComplexTimeTariffTable
 * @method static ComplexEnergyTariffTable
 */
final class TariffTableType extends Enum implements LocalizedEnum
{
    use Tools;

    // 简单收费表
    const SimpleTariffTable = 'SIMPLE_TARIFF_TABLE';

    // 复杂时间收费表
    const ComplexTimeTariffTable = 'COMPLEX_TIME_TARIFF_TABLE';

    // 复杂电量收费表
    const ComplexEnergyTariffTable = 'COMPLEX_ENERGY_TARIFF_TABLE';

}

<?php

namespace App\Http\Controllers\Admin;

use App\Models\Modules\OctopusDownloadFileRecord;
use App\Enums\{
    OctopusDataExchangeMode,
    OctopusDataExchangeOperationMode,
};
use App\Http\Controllers\Common\CommonController;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use Illuminate\Foundation\Application;
use Illuminate\Http\{
    JsonResponse,
    Request,
};

class OctopusDownloadFileRecordController extends CommonController
{
    protected static string $module_name = 'octopusDownloadFileRecord'; // 模块名称
    protected OctopusDownloadFileRecord $model;

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new OctopusDownloadFileRecord;
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'kiosk_name_search' => $request->get('kiosk_name_search'),
            'octopus_device_number_search' => $request->get('octopus_device_number_search'),
        );

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'kiosk_number');
        $sort = $request->input('sort', 'asc');
        $length = (int)$request->input('length', 10);
        $kiosk_name_search = $request->input('kiosk_name_search');
        $octopus_device_number_search = $request->input('octopus_device_number_search');

        $where = array();

        if (filled($kiosk_name_search)) {
            $where[] = ['octopus_download_file_record.kiosk_name', 'like', "%$kiosk_name_search%"];
        }
        if (filled($octopus_device_number_search)) {
            $where[] = ['octopus_download_file_record.octopus_device_number', 'like', "%$octopus_device_number_search%"];
        }

        $data_list = OctopusDownloadFileRecord::when(!isSuperAdministrator(), function ($query) {
            return $query->whereHas('kiosk', fn($query_item) => $query_item->whereIn('site_number', auth()->user()->site_number_list));
        })
            ->where($where)
            ->orderBy($order, $sort)
            ->latest('gmt_create')
            ->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $result[] = array(
                'octopus_download_file_record_id' => $data->octopus_download_file_record_id, // 八达通上传文件记录ID
                'kiosk_number' => $data->kiosk_number, // Kiosk编号
                'kiosk_name' => $data->kiosk_name,// Kiosk名称
                'octopus_device_number' => $data->octopus_device_number,// 八达通设备编号
                'octopus_data_exchange_mode' => filled($data->octopus_data_exchange_mode)
                    ? OctopusDataExchangeMode::getDescription($data->octopus_data_exchange_mode)
                    : '—/—',// 八达通数据交换模式
                'octopus_data_exchange_operation_mode' => filled($data->octopus_data_exchange_operation_mode)
                    ? OctopusDataExchangeOperationMode::getDescription($data->octopus_data_exchange_operation_mode)
                    : '—/—',// 八达通数据交换操作模式
                'download_file_list' => $data->download_file_list,// 下载文件列表
                'downloaded_file_list' => $data->downloaded_file_list,// 已下载文件列表

                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );

        return response()->json($json);
    }

    public function getDownloadFileList(Request $request): JsonResponse
    {
        $id = $request->post('id');

        if (filled($id) && filled($download_file_list_result = OctopusDownloadFileRecord::when(!isSuperAdministrator(), function ($query) {
                return $query->whereHas('kiosk', fn($query) => $query->whereIn('kiosk.site_number', auth()->user()->site_number_list));
            })->find($id)?->download_file_list)) {
            $download_file_list = array();
            $download_file_list_result = explode(",", $download_file_list_result);
            foreach ($download_file_list_result as $download_file_list_data) {
                $download_file_list[] = $download_file_list_data;
            }
            $this->data = $download_file_list;
        }

        return $this->returnJson();
    }

    public function getDownloadedFileList(Request $request): JsonResponse
    {
        $id = $request->post('id');

        if (!empty($id) && filled($downloaded_file_list_result = OctopusDownloadFileRecord::when(!isSuperAdministrator(), function ($query) {
                return $query->whereHas('kiosk', fn($query) => $query->whereIn('kiosk.site_number', auth()->user()->site_number_list));
            })->find($id)?->downloaded_file_list)) {
            $downloaded_file_list = array();
            $downloaded_file_list_result = explode(",", $downloaded_file_list_result);
            foreach ($downloaded_file_list_result as $downloaded_file_list_data) {
                $downloaded_file_list[] = $downloaded_file_list_data;
            }
            $this->data = $downloaded_file_list;
        }

        return $this->returnJson();
    }
}

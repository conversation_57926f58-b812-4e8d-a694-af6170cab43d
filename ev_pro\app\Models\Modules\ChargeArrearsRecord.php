<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;

class ChargeArrearsRecord extends Model
{

    protected $table = 'charge_arrears_record';
    protected $primaryKey = 'charge_arrears_record_id';

    const CREATED_AT = 'gmt_create'; //默认是小写的模型类名复数格式，此处为自定义表名
    const UPDATED_AT = 'gmt_modified'; //定义主键，默认为id


    // protected $fillable = [];
    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 类型转化器
     */
    protected $casts = [

    ];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [

    ];

    /**
     * 全部字段
     *
     * @var array
     */
    public array $columns = [
        'charge_arrears_record_id',
        'charge_arrears_record_number',
        'merchant_number',
        'site_number',
        'charge_record_number',
        'charge_arrears_status',
        'gmt_charge_arrears_status',
        'connector_number',
        'connector_name',
        'kiosk_number',
        'kiosk_name',
        'identity_type',
        'identity_number',
        'status_log',
        'remark',
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    // 一对一关联充电枪
    public function connector()
    {
        return $this->hasOne(Connector::class, 'connector_number', 'connector_number');
    }

    // 一对一关联充电记录
    public function chargeRecord()
    {
        return $this->hasOne(ChargeRecord::class, 'charge_record_number', 'charge_record_number');
    }

}

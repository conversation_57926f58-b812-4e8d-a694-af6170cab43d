<?php

namespace App\Enums;

use BenSampo\Enum\{
    Enum,
    Contracts\LocalizedEnum
};
use App\Enums\Traits\Tools;

/**
 * @method static Pending
 * @method static Processing
 * @method static Unpaid
 * @method static NoArrears
 * @method static Completed
 */
final class ChargeArrearsStatus extends Enum implements LocalizedEnum

{
    use Tools;

    const Pending = 'PENDING'; // 准备中
    const Processing = 'PROCESSING'; // 进行中
    const Unpaid = 'UNPAID'; // 未支付
    const NoArrears = 'NO_ARREARS'; // 无欠款
    const Completed = 'COMPLETED'; // 已完成
    const CalculationFailure = 'CALCULATION_FAILURE'; // 计算失败

}

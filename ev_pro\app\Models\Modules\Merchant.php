<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;

class Merchant extends Model
{

    protected $table = 'merchant'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'merchant_id'; //定义主键，默认为id
    // 表的唯一number
    public static $table_number = 'merchant_number';
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    // protected $fillable = [];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [];

    /**
     * 类型转化器
     */
    protected $casts = [
        'name_json' => 'array',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    protected function asJson($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    // creating event
    public static function boot()
    {
        parent::boot();

        static::deleting(function ($model) {
            // 并且删除关联的
            $model->description()->delete();
        });
    }

    /**
     * 一对多关联description
     */
    public function description()
    {
        return $this->hasMany(MerchantDescription::class, 'merchant_number', 'merchant_number');
    }

    /**
     * 一对多关联site
     */
    public function site()
    {
        return $this->hasMany(Site::class, 'merchant_number', 'merchant_number');
    }
}

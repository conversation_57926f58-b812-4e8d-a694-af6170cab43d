<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Contracts\View\{
    Factory,
    View
};
use Illuminate\Database\Eloquent\Model;
use Illuminate\Validation\Rule;
use Illuminate\Foundation\Application;
use Illuminate\Support\Arr;
use Illuminate\Http\{
    JsonResponse,
    RedirectResponse
};
use Illuminate\Http\Request;
use App\Http\Controllers\Admin\Traits\{
    Edit,
};
use App\Models\Modules\{
    KioskSetting,
    KioskSettingDescription,
    KioskSettingScreenTime,
    Site,
    KioskSettingScreensaverResourceDescription,
    Kiosk,
};
use App\Enums\{
    KioskSettingScreenTimeKeyEnum,
    OctopusDataExchangeMode,
    ScreensaverModeEnum,
    IdentityType,
};

class KioskSettingController extends CommonController
{
    use Edit;

    protected static string $module_name = 'kioskSetting'; // 模块名称
    protected static bool $module_check_site = true; // 标记该模块校验场地

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new KioskSetting;
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'add_url' => action([self::class, 'add']),
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'site_search' => $request->get('site_search'),
            'name_search' => $request->get('name_search'),
            'default_language_code_search' => $request->get('default_language_code_search'),
            'kiosk_setting_number_search' => $request->get('kiosk_setting_number_search'),
            'site_list' => $this->getSiteOptionList(),
        );

        $config_languages = config('languages');
        $config_languages['en_US_zh_HK'] = __('common.en_US_zh_HK');
        $data['config_languages'] = $config_languages;

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'sort_order');
        $sort = $request->input('sort', 'asc');
        $length = (int)$request->input('length', 10);
        $name_search = $request->input('name_search');
        $site_search = $request->input('site_search');
        $default_language_code_search = $request->input('default_language_code_search');
        $kiosk_setting_number_search = $request->input('kiosk_setting_number_search');

        if ($order == 'site_name') $order = 'site.name_json';

        $data_list = KioskSetting::query()
            ->select('kiosk_setting.*', 'site.name_json as site_name_json_init')
            ->leftJoin('site', 'kiosk_setting.site_number', '=', 'site.site_number')
            ->when(filled($name_search), fn($query) => $query->where('kiosk_setting.name', 'like', "%$name_search%"))
            ->when(filled($site_search), fn($query) => $query->where('kiosk_setting.site_number', $site_search))
            ->when(filled($default_language_code_search), fn($query) => $query->where('default_language_code', '=', $default_language_code_search))
            ->when(filled($kiosk_setting_number_search), fn($query) => $query->where('kiosk_setting.kiosk_setting_number', 'like', "%$kiosk_setting_number_search%"))
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('kiosk_setting.site_number', auth()->user()->site_number_list))
            ->orderBy($order, $sort)
            ->orderBy('gmt_modified', 'desc')
            ->paginate($length, ['*'], 'start');

        // 单独加入双语语言，只有kiosk需要显示双语，cms不需要双语
        $config_languages = config('languages');
        $config_languages['en_US_zh_HK'] = __('common.en_US_zh_HK');
        $result = array();
        foreach ($data_list as $data) {
            $site_name = $this->getValueFromLanguageArray($data->site_name_json_init) ?? '—/—';
            // 打印历史收据身份类型数组
            $print_history_receipt_identity_type_list = $data->print_history_receipt_identity_type_list ?? '';
            // 将，分隔的字符串转为数组
            $print_history_receipt_identity_type_list = explode(',', $print_history_receipt_identity_type_list);
            // 将数组中的元素转换为对应的描述
            $print_history_receipt_identity_type_list = implode(',', array_map(fn ($item) => IdentityType::getDescription($item), $print_history_receipt_identity_type_list));
            $result[] = array(
                'kiosk_setting_id' => $data->kiosk_setting_id, // Kiosk设置ID
                'site_name' => $site_name, // 场地名称
                'kiosk_setting_number' => $data->kiosk_setting_number, // Kiosk设置编号
                'name' => $data->name, // 名称
                'default_language_code' => $config_languages[$data->default_language_code] ?? '—/—', // 默认语言
                'is_display_idle_remain_time' => $data->is_display_idle_remain_time, // 是否显示闲置剩余时间
                'is_enable_user_charge_record_bill_payment' => $data->is_enable_user_charge_record_bill_payment, // 是否启用用户充电记录账单支付
                'is_display_fee_page' => $data->is_display_fee_page, // 是否显示收费页面
                'is_display_support_page' => $data->is_display_support_page, // 是否显示支援页面
                'is_enable_settle_charge_arrears' => $data->is_enable_settle_charge_arrears, // 是否启用结算充电欠款
                'is_enable_charge_pre_authorization_refund' => $data->is_enable_charge_pre_authorization_refund, // 是否启用预授权退款
                'print_history_receipt_identity_type_list' => $print_history_receipt_identity_type_list, // 打印历史收据身份类型数组
                'print_history_receipt_query_limit' => $data->print_history_receipt_query_limit ?? '—/—', // 打印历史收据查询限制
                'print_history_receipt_query_validity_period' => filled($data->print_history_receipt_query_validity_period) ? (($data->print_history_receipt_query_validity_period / 86400) . __('common.unit_day')) : '—/—', // 打印历史收据查询有效期（秒转天）
                'print_history_receipt_print_count_limit' => $data->print_history_receipt_print_count_limit ?? '—/—', // 打印历史收据打印次数限制
                'is_enable_print_receipt_required_count_update' => $data->is_enable_print_receipt_required_count_update, // 是否启用打印收据必须计数更新
                'is_display_available_payment_method' => $data->is_display_available_payment_method, // 是否显示可用支付方式
                'is_enable_zone_selection' => $data->is_enable_zone_selection, // 是否启用区域选择
                'connector_menu_row_count' => $data->connector_menu_row_count, // 充电机菜单行数
                'connector_menu_column_count' => $data->connector_menu_column_count, // 充电机菜单列数
                'connector_menu_preparing_row_count' => $data->connector_menu_preparing_row_count, // 充电机菜单准备中行数
                'connector_menu_preparing_column_count' => $data->connector_menu_preparing_column_count, // 充电机菜单准备中列数
                'is_display_connector_tag' => $data->is_display_connector_tag, // 是否显示充电枪标签
                'websocket_check_alive_interval' => $data->websocket_check_alive_interval . __('common.unit_s'), // WebSocket检查保活间隔
                'websocket_alive_timeout' => $data->websocket_alive_timeout . __('common.unit_s'), // WebSocket活跃超时
                'heartbeat_interval' => $data->heartbeat_interval . __('common.unit_s'), // 心跳间隔
                'self_check_interval' => $data->self_check_interval . __('common.unit_s'), // 自检间隔
                'pos_check_health_interval' => $data->pos_check_health_interval . __('common.unit_s'), // POS检测健康间隔
                'clear_cache_charge_record_buffer_time' => $data->clear_cache_charge_record_buffer_time . __('common.unit_s'), // 清除缓存充电记录缓冲时间
                'charge_pre_authorization_confirm_retry_count' => $data->charge_pre_authorization_confirm_retry_count ?? '—/—', // 充电预授权确认重试次数
                'is_enable_record_screen_operation' => $data->is_enable_record_screen_operation, // 是否启用记录屏幕操作
                'is_enable_enter_receiver' => $data->is_enable_enter_receiver, // 是否启用输入接收者
                'is_enable_print_receipt' => $data->is_enable_print_receipt, // 是否启用打印收据
                'is_enable_cms_calculate_user_charge_record_bill_total_amount' => $data->is_enable_cms_calculate_user_charge_record_bill_total_amount, // 是否启用CMS计算用户充电记录账单金额
                'is_enable_octopus_cancel' => $data->is_enable_octopus_cancel, // 是否启用八达通取消
                'is_enable_synchronize_system_date_time' => $data->is_enable_synchronize_system_date_time, // 是否启用同步系统日期时间
                'synchronize_system_date_time_offset' => $data->synchronize_system_date_time_offset . __('common.unit_s'), // 同步系统日期时间偏移量
                'screensaver_waiting_time' => $data->screensaver_waiting_time . __('common.unit_s'), // 屏保等待时间
                'screensaver_mode' => filled($data->screensaver_mode) ? ScreensaverModeEnum::getDescription($data->screensaver_mode) : '—/—', // 屏保模式
                'screensaver_image_auto_run_interval' => $data->screensaver_image_auto_run_interval . __('common.unit_s'), // 屏保图片自动轮播间隔
                'is_screensaver_top_priority' => $data->is_screensaver_top_priority, // 是否屏保最优先
                'check_octopus_data_exchange_interval' => $data->check_octopus_data_exchange_interval . __('common.unit_s'), // 检查八达通数据交换间隔
                'octopus_data_exchange_mode' => filled($data->octopus_data_exchange_mode) ? OctopusDataExchangeMode::getDescription($data->octopus_data_exchange_mode) : '—/—', // 八达通上数模式
                'octopus_upload_time_list' => self::secondsStringToTimeArray($data->octopus_upload_time_list), // 八达通上传时间
                'octopus_download_time_list' => self::secondsStringToTimeArray($data->octopus_download_time_list), // 八达通下载时间
                'sort_order' => $data->sort_order, // 排序
                'remark' => $data->remark ?? '—/—', // 备注
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );
        return response()->json($json);
    }

    protected function getForm(Request $request, array $data): View|Application|Factory
    {
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'], self::getUrlParams($request)
        );

        // 获取数据库和表单提交的模式
        $old_screensaver_mode = $request->old('screensaver_mode');
        $model_screensaver_mode = $data['model']->screensaver_mode;

        if (blank($data['model']->kiosk_setting_id)) $data['model']->kiosk_setting_number = $request->old('kiosk_setting_number', $data['model']->kiosk_setting_number); // Kiosk设置编码

        $site_number = $data['model']->site_number;
        if ((isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) && blank($data['model']?->kiosk_setting_id)) {
            // 如果是新增，并且是超级管理员或者管理员的商户大于1的情况下就直接获取页面传入的商户编号
            $site_number = $request->old('site_number', $data['model']->site_number);
        } else if (blank($data['model']?->kiosk_setting_id) && auth()->user()->site_number_list->count() === 1) {
            // 如果是新增并且管理员商户只有一个就直接赋值
            $site_number = auth()->user()->site_number_list->first();
        }

        $data['model']->site_number = $site_number; // 场地编号
        $site_name = $this->getValueFromLanguageArray(
            Site::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->firstWhere('site_number', $site_number)?->name_json
        );
        $data['model']->site_name = $site_name; // 场地名称
        $data['model']->name = $request->old('name', $data['model']->name); // 名称
        $data['model']->default_language_code = $request->old('default_language_code', $data['model']->default_language_code); // 默认语言
        $data['model']->is_display_idle_remain_time = $request->old('is_display_idle_remain_time', $data['model']->is_display_idle_remain_time); // 是否显示闲置剩余时间
        $data['model']->is_enable_user_charge_record_bill_payment = $request->old('is_enable_user_charge_record_bill_payment', $data['model']->is_enable_user_charge_record_bill_payment); // 是否启用用户充电记录账单支付
        $data['model']->is_display_fee_page = $request->old('is_display_fee_page', $data['model']->is_display_fee_page); // 是否显示收费页面
        $data['model']->is_display_support_page = $request->old('is_display_support_page', $data['model']->is_display_support_page); // 是否显示支援页面
        $data['model']->is_enable_settle_charge_arrears = $request->old('is_enable_settle_charge_arrears', $data['model']->is_enable_settle_charge_arrears); // 是否启用结算充电欠款
        $data['model']->is_enable_charge_pre_authorization_refund = $request->old('is_enable_charge_pre_authorization_refund', $data['model']->is_enable_charge_pre_authorization_refund); // 是否启用预授权退款
        $data['model']->print_history_receipt_identity_type_list = $request->old('print_history_receipt_identity_type_list', explode(',', $data['model']->print_history_receipt_identity_type_list ?? '')); // 打印历史收据身份类型数组
        $data['model']->print_history_receipt_query_limit = $request->old('print_history_receipt_query_limit', $data['model']->print_history_receipt_query_limit); // 打印历史收据查询限制
        $data['model']->print_history_receipt_query_validity_period = $request->old('print_history_receipt_query_validity_period', filled($data['model']->print_history_receipt_query_validity_period) ? ($data['model']->print_history_receipt_query_validity_period / 86400) : ''); // 打印历史收据查询有效期（秒转天）
        $data['model']->print_history_receipt_print_count_limit = $request->old('print_history_receipt_print_count_limit', $data['model']->print_history_receipt_print_count_limit); // 打印历史收据打印次数限制
        $data['model']->is_enable_print_receipt_required_count_update = $request->old('is_enable_print_receipt_required_count_update', $data['model']->is_enable_print_receipt_required_count_update); // 是否启用打印收据必须计数更新
        $data['model']->is_display_available_payment_method = $request->old('is_display_available_payment_method', $data['model']->is_display_available_payment_method); // 是否显示可用支付方式
        $data['model']->is_enable_zone_selection = $request->old('is_enable_zone_selection', $data['model']->is_enable_zone_selection); // 是否启用区域选择
        $data['model']->connector_menu_row_count = $request->old('connector_menu_row_count', $data['model']->connector_menu_row_count); // 充电机菜单行数
        $data['model']->connector_menu_column_count = $request->old('connector_menu_column_count', $data['model']->connector_menu_column_count); // 充电机菜单列数
        $data['model']->connector_menu_preparing_row_count = $request->old('connector_menu_preparing_row_count', $data['model']->connector_menu_preparing_row_count); // 充电机菜单准备中行数
        $data['model']->connector_menu_preparing_column_count = $request->old('connector_menu_preparing_column_count', $data['model']->connector_menu_preparing_column_count); // 充电机菜单准备中列数
        $data['model']->is_display_connector_tag = $request->old('is_display_connector_tag', $data['model']->is_display_connector_tag); // 是否显示充电枪标签
        $data['model']->websocket_check_alive_interval = $request->old('websocket_check_alive_interval', $data['model']->websocket_check_alive_interval); // WebSocket检查保活间隔
        $data['model']->websocket_alive_timeout = $request->old('websocket_alive_timeout', $data['model']->websocket_alive_timeout); // WebSocket活跃超时
        $data['model']->heartbeat_interval = $request->old('heartbeat_interval', $data['model']->heartbeat_interval); // 心跳间隔
        $data['model']->pos_check_health_interval = $request->old('pos_check_health_interval', $data['model']->pos_check_health_interval); // POS检测健康间隔
        $data['model']->self_check_interval = $request->old('self_check_interval', $data['model']->self_check_interval); // 自检间隔
        $data['model']->clear_cache_charge_record_buffer_time = $request->old('clear_cache_charge_record_buffer_time', $data['model']->clear_cache_charge_record_buffer_time); // 清除缓存充电记录缓冲时间
        $data['model']->charge_pre_authorization_confirm_retry_count = $request->old('charge_pre_authorization_confirm_retry_count', $data['model']->charge_pre_authorization_confirm_retry_count); // 充电预授权确认重试次数
        $data['model']->is_enable_record_screen_operation = $request->old('is_enable_record_screen_operation', $data['model']->is_enable_record_screen_operation); // 是否启用记录屏幕操作
        $data['model']->is_enable_enter_receiver = $request->old('is_enable_enter_receiver', $data['model']->is_enable_enter_receiver); // 是否启用输入接收者
        $data['model']->is_enable_print_receipt = $request->old('is_enable_print_receipt', $data['model']->is_enable_print_receipt); // 是否启用打印收据
        $data['model']->is_enable_cms_calculate_user_charge_record_bill_total_amount = $request->old('is_enable_cms_calculate_user_charge_record_bill_total_amount', $data['model']->is_enable_cms_calculate_user_charge_record_bill_total_amount); // 是否启用CMS计算用户充电记录账单金额
        $data['model']->is_enable_octopus_cancel = $request->old('is_enable_octopus_cancel', $data['model']->is_enable_octopus_cancel); // 是否启用八达通取消
        $data['model']->is_modified_screen_time = $request->old('is_modified_screen_time', 0); // 是否编辑屏幕时间
        $data['model']->is_enable_synchronize_system_date_time = $request->old('is_enable_synchronize_system_date_time', $data['model']->is_enable_synchronize_system_date_time); // 是否启用同步系统日期时间
        $data['model']->synchronize_system_date_time_offset = $request->old('synchronize_system_date_time_offset', $data['model']->synchronize_system_date_time_offset); // 同步系统日期时间偏移量
        $data['model']->screensaver_waiting_time = $request->old('screensaver_waiting_time', $data['model']->screensaver_waiting_time); // 屏保等待时间
        $data['model']->screensaver_mode = $request->old('screensaver_mode', $data['model']->screensaver_mode); // 屏保模式
        $data['model']->screensaver_image_auto_run_interval = $request->old('screensaver_image_auto_run_interval', $data['model']->screensaver_image_auto_run_interval); // 屏保图片自动轮播间隔
        $data['model']->is_screensaver_top_priority = $request->old('is_screensaver_top_priority', $data['model']->is_screensaver_top_priority); // 是否屏保最优先
        $data['model']->check_octopus_data_exchange_interval = $request->old('check_octopus_data_exchange_interval', $data['model']->check_octopus_data_exchange_interval); // 检查八达通数据交换间隔
        $data['model']->octopus_data_exchange_mode = $request->old('octopus_data_exchange_mode', $data['model']->octopus_data_exchange_mode); // 八达通上数模式
        $data['model']->octopus_upload_time_list = $request->old('octopus_upload_time_list', self::secondsStringToTimeArray($data['model']->octopus_upload_time_list)); // 八达通上传时间
        $data['model']->octopus_download_time_list = $request->old('octopus_download_time_list', self::secondsStringToTimeArray($data['model']->octopus_download_time_list)); // 八达通下载时间
        $data['model']->remark = $request->old('remark', $data['model']->remark); // 备注
        $data['model']->sort_order = $request->old('sort_order', $data['model']->sort_order); // 排序

        // 读取系统语言并查询出对应KioskSettingDescription数据
        $data['item'] = array();
        $config_languages = config('languages');
        // 单独加入双语语言，只有kiosk需要显示双语，cms不需要双语
        $config_languages['en_US_zh_HK'] = __('common.en_US_zh_HK');
        foreach ($config_languages as $language_code => $language_name) {
            // 有旧数据
            $kiosk_setting_description_old_list = $request->old('item');
            if ($kiosk_setting_description_old_list && filled($kiosk_setting_description_old_list)) {
                $data['item'][$language_code] = array(
                    'language_name' => $language_name,
                    'language_code' => $language_code,
                    'kiosk_setting_description_id' => $kiosk_setting_description_old_list[$language_code]['kiosk_setting_description_id'] ?? null, // ID
                    'main_logo_image_url' => $kiosk_setting_description_old_list[$language_code]['main_logo_image_url'] ?? null,
                    'home_page_image_url' => $kiosk_setting_description_old_list[$language_code]['home_page_image_url'] ?? null,
                    'main_header_message' => $kiosk_setting_description_old_list[$language_code]['main_header_message'] ?? null,
                    'receipt_message' => $kiosk_setting_description_old_list[$language_code]['receipt_message'] ?? null,
                    'disclaimer_html' => $kiosk_setting_description_old_list[$language_code]['disclaimer_html'] ?? null,
                );
            } else {
                $kiosk_setting_description_result = $data['model']->description->where('language_code', $language_code)->first();
                $data['item'][$language_code] = array(
                    'language_name' => $language_name,
                    'language_code' => $language_code,
                    'kiosk_setting_description_id' => $kiosk_setting_description_result->kiosk_setting_description_id ?? '', // ID
                    'main_logo_image_url' => $kiosk_setting_description_result->main_logo_image_url ?? null, // 主图标图片URL
                    'home_page_image_url' => $kiosk_setting_description_result->home_page_image_url ?? null, // 首页图片URL
                    'main_header_message' => $kiosk_setting_description_result->main_header_message ?? '', // 主页头部消息
                    'receipt_message' => $kiosk_setting_description_result->receipt_message ?? null, // 收据消息
                    'disclaimer_html' => $kiosk_setting_description_result->disclaimer_html ?? '', // 条款HTML
                );
            }
        }

        // 是否更改屏幕时间
        $is_modified_screen_time = $data['model']->is_modified_screen_time;
        /* 获取当前屏幕时间数据 根据是否更改屏幕时间来判断需要旧数据还是模型里的数据，
        防止编辑时屏幕时间原本有数据，然后清空屏幕时间并且有些字段没过验证的情况下回填数据时把屏幕时间模型里的数据回填了 */
        if (filled($data['model']->kiosk_setting_id) || filled($request->input('_kiosk_setting_number'))) {
            // 编辑的情况下 和 复制到新增的情况下
            $screen_time_list = $is_modified_screen_time ? $request->old('screen', []) : $data['model']->screenTime;
        } else {
            // 新增的情况下
            $screen_time_list = $request->old('screen', []);
        }
        // 防止在复制到新增的时候因为is_modified_screen_time第一次进去默认为false导致数据无法新增进去，所以如果是复制到新增的话就直接为true，为true就是已修改
        if (filled($request->input('_kiosk_setting_number'))) $data['model']->is_modified_screen_time = 1;

        // 获取当前screen time数据
        $data['screen'] = array();

        foreach ($screen_time_list as $screen_time) {
            $name = KioskSettingScreenTimeKeyEnum::getDescription($screen_time['key']);
            if ($request->old('screen')) {
                $data['screen'][] = array(
                    'key' => $screen_time['key'],
                    'name' => $name,
                    'time' => $screen_time['time'],
                );
            } else {
                $data['screen'][] = array(
                    'kiosk_setting_screen_time_id' => $screen_time->kiosk_setting_screen_time_id,
                    'key' => $screen_time->key,
                    'name' => $name,
                    'time' => $screen_time->time,
                    'gmt_create' => $screen_time->gmt_create,
                    'gmt_modified' => $screen_time->gmt_modified,
                );
            }
        }

        // 屏幕时间枚举
        $data['screen_time_list'] = array();
        foreach (KioskSettingScreenTimeKeyEnum::asSelectArray() as $value => $description) {
            $data['screen_time_list'][] = array(
                'name' => $description,
                'value' => $value,
            );
        }
        // 获取当前screensaver数据
        $data['screensaver_list'] = array();
        // 有旧数据
        $screensaver_old_list = $request->old('screensaver_list');
        if ($screensaver_old_list && filled($screensaver_old_list)) {
            foreach ($screensaver_old_list as $key => $screensaver_old_list_item) {
                foreach ($screensaver_old_list_item as $rows => $item) {
                    $data['screensaver_list'][$key][$rows] = array(
                        'resource_url' => existsImage('public', $item['resource_url'] ?? null), // 拼接文件路径
                        'resource_url_name' => $item['resource_url'] ?? null,
                        'sort_order' => $item['sort_order'] ?? null,
                    );
                }
            }
        } else {
            // 判断数据库和表单提交的模式是否相等，不相等就不输入数据
            if ((empty($old_screensaver_mode) || $old_screensaver_mode == $model_screensaver_mode) && $old_screensaver_mode != 'NO_MODE') {
                foreach ($config_languages as $language_code => $language_name) {
                    $screensaver_result = Arr::where($data['model']->screensaver->toArray(), function ($value) use ($language_code) {
                        return $value['language_code'] === $language_code;
                    });
                    foreach ($screensaver_result as $screensaver_result_item) {
                        $data['screensaver_list'][$language_code][] = array(
                            'kiosk_setting_screensaver_resource_description_id' => $screensaver_result_item['kiosk_setting_screensaver_resource_description_id'],
                            'sort_order' => $screensaver_result_item['sort_order'] ?? 0, // 排序
                            'resource_url' => existsImage('public', $screensaver_result_item['resource_url'] ?? null), // 拼接文件路径
                            'resource_url_name' => $screensaver_result_item['resource_url'],
                        );
                    }
                }
            }

        }
        $data['image_not_select_path'] = existsImage('icon', 'not_select_image.png');

        $data['language_code_list'] = $data['language_code_value'] = array();
        foreach ($config_languages as $language_code => $language_name) {
            $data['language_code_list'][$language_code] = $language_name;
            $data['language_code_value'][] = $language_code;
        }

        $data['image_value'] = false;
        $data['video_value'] = false;
        $data['tag_name_value'] = false;
        // 判断file manager插件上传的是视频还是图片还是禁用
        switch ($data['model']->screensaver_mode) {
            case ScreensaverModeEnum::Image:
                $data['image_value'] = true;
                $data['video_value'] = false;
                $data['tag_name_value'] = false;
                break;
            case ScreensaverModeEnum::Video:
                $data['image_value'] = false;
                $data['video_value'] = true;
                $data['tag_name_value'] = true;
                break;
            default:
                break;
        }

        $data['print_history_receipt_identity_type_list'] = array();
        foreach (IdentityType::asSelectArray() as $value => $name) {
            $data['print_history_receipt_identity_type_list'][] = array(
                'name' => $name,
                'value' => $value,
            );
        }

        return view("pages.{$data['module_name']}.form", $data);
    }

    public function add(Request $request): Application|View|Factory|RedirectResponse
    {
        $data = array();

        $model = $this->model;

        if ($request->isMethod('post')) {
            return $this->modelValidateAndSave($request, $model);
        }

        // 如果有kiosk_setting_number的存在就是复制除了kiosk_setting_number和name以外的收费表数据输出到新增页面
        $kiosk_setting_number = $request->input('_kiosk_setting_number');
        if (filled($kiosk_setting_number)) {
            $old_data_model = $model->with(['description', 'screenTime', 'screensaver' => fn($query) => $query->oldest('sort_order')])
                ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('kiosk_setting.site_number', auth()->user()->site_number_list))
                ->where('kiosk_setting_number', $kiosk_setting_number)
                ->firstOrFail();
            $model = $old_data_model->replicate(['name', 'kiosk_setting_number']);

            foreach ($model->description as $description) {
                $description->kiosk_setting_description_id = $description->kiosk_setting_number = null;
            }
            foreach ($model->screenTime as $screen_time) {
                $screen_time->kiosk_setting_screen_time_id = $screen_time->kiosk_setting_number = null;
            }
            foreach ($model->screensaver as $screensaver) {
                $screensaver->kiosk_setting_screensaver_resource_description_id = $screensaver->kiosk_setting_number = null;
            }
        }

        $data['model'] = $model;

        return $this->getForm($request, $data);
    }

    public function delete(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        $kiosk_setting_number = $request->post('kiosk_setting_number');

        $kiosk_setting = KioskSetting::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('kiosk_setting_number', $kiosk_setting_number);
        if (!empty($kiosk_setting_number) && filled($kiosk_setting)) {
            if (blank($kiosk_setting_list = $kiosk_setting->kiosk()->get())) {
                // 并且删除关联的
                $kiosk_setting->description()->delete();
                $kiosk_setting->screenTime()->delete();
                $kiosk_setting->screensaver()->delete();

                // 删除kiosk setting
                $kiosk_setting->delete();

                self::sendInitPushByKioskNumberList();
            } else {
                $kiosk_str = '';
                foreach ($kiosk_setting_list as $kiosk) {
                    $kiosk_str .= '<li>' . $kiosk->name . '</li>';
                }
                $this->code = 201;
                $this->message = __('common.error_has_binding_unable_to_delete_kiosk_setting', [
                    'kiosk' => $kiosk_str
                ]);
            }
        } else {
            $this->code = 404;
            $this->message = __('common.text_not_found', ['field' => __("$module_name.kiosk_setting_number")]);
        }

        return $this->returnJson();
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @param KioskSetting $model
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    protected function modelValidateAndSave(Request $request, KioskSetting $model): RedirectResponse
    {
        $request->validate(self::rules($request, $model), [], self::attributes());
        // 判断是否是新增
        if (blank($model->kiosk_setting_id)) {
            $model = $this->model;
            $model->kiosk_setting_number = $request->input('kiosk_setting_number');
            $site_number = $model->site_number;

            if (isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) {
                // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下就直接获取页面传入的场地编号
                $site_number = $request->input('site_number');
            } else if (auth()->user()->site_number_list->count() === 1) {
                // 如果是新增并且管理员场地只有一个就直接赋值
                $site_number = auth()->user()->site_number_list->first();
            }
            $model->site_number = $site_number;
            $model->merchant_number = Site::firstWhere('site_number', $model->site_number)?->merchant_number;
        }
        $model->name = $request->input('name');
        $model->default_language_code = $request->input('default_language_code');
        $model->is_display_idle_remain_time = $request->input('is_display_idle_remain_time', 0);
        $model->is_enable_user_charge_record_bill_payment = $request->input('is_enable_user_charge_record_bill_payment', 0);
        $model->is_display_fee_page = $request->input('is_display_fee_page', 0);
        $model->is_display_support_page = $request->input('is_display_support_page', 0);
        $model->is_enable_settle_charge_arrears = $request->input('is_enable_settle_charge_arrears', 0);
        $model->is_enable_charge_pre_authorization_refund = $request->input('is_enable_charge_pre_authorization_refund', 0);
        $model->is_display_available_payment_method = $request->input('is_display_available_payment_method', 0);
        $model->print_history_receipt_identity_type_list = $request->input('print_history_receipt_identity_type_list', array()); // 打印历史收据身份类型数组
        // 将数组转换为字符串
        $model->print_history_receipt_identity_type_list = implode(',', $model->print_history_receipt_identity_type_list);
        $model->print_history_receipt_query_limit = $request->input('print_history_receipt_query_limit');
        $model->print_history_receipt_query_validity_period = $request->input('print_history_receipt_query_validity_period');
        // 将天转换为秒存储
        $model->print_history_receipt_query_validity_period *= 86400;
        $model->print_history_receipt_print_count_limit = $request->input('print_history_receipt_print_count_limit');
        $model->is_enable_print_receipt_required_count_update = $request->input('is_enable_print_receipt_required_count_update', 0);
        $model->is_enable_zone_selection = $request->input('is_enable_zone_selection', 0);
        $model->connector_menu_row_count = $request->input('connector_menu_row_count', 0);
        $model->connector_menu_column_count = $request->input('connector_menu_column_count', 0);
        $model->connector_menu_preparing_row_count = $request->input('connector_menu_preparing_row_count', 0);
        $model->connector_menu_preparing_column_count = $request->input('connector_menu_preparing_column_count', 0);
        $model->is_display_connector_tag = $request->input('is_display_connector_tag', 0);
        $model->websocket_check_alive_interval = $request->input('websocket_check_alive_interval', 0);
        $model->websocket_alive_timeout = $request->input('websocket_alive_timeout', 0);
        $model->heartbeat_interval = $request->input('heartbeat_interval', 0);
        $model->pos_check_health_interval = $request->input('pos_check_health_interval', 0);
        $model->self_check_interval = $request->input('self_check_interval', 0);
        $model->clear_cache_charge_record_buffer_time = $request->input('clear_cache_charge_record_buffer_time', 0);
        $model->charge_pre_authorization_confirm_retry_count = $request->input('charge_pre_authorization_confirm_retry_count');
        $model->is_enable_record_screen_operation = $request->input('is_enable_record_screen_operation', 0);
        $model->is_enable_enter_receiver = $request->input('is_enable_enter_receiver', 0);
        $model->is_enable_print_receipt = $request->input('is_enable_print_receipt', 0);
        $model->is_enable_cms_calculate_user_charge_record_bill_total_amount = $request->input('is_enable_cms_calculate_user_charge_record_bill_total_amount', 0);
        $model->is_enable_octopus_cancel = $request->input('is_enable_octopus_cancel', 0);
        $model->is_enable_synchronize_system_date_time = $request->input('is_enable_synchronize_system_date_time', 0);
        $model->synchronize_system_date_time_offset = $request->input('synchronize_system_date_time_offset', -28800);
        $model->screensaver_waiting_time = $request->input('screensaver_waiting_time', 0);
        $model->check_octopus_data_exchange_interval = $request->input('check_octopus_data_exchange_interval', 0);
        $model->octopus_data_exchange_mode = $request->input('octopus_data_exchange_mode');

        if (isset($request['screensaver_mode']) && $request['screensaver_mode'] != ScreensaverModeEnum::Image && $request['screensaver_mode'] != ScreensaverModeEnum::Video) {
            $model->screensaver_mode = null;
        } else {
            $model->screensaver_mode = $request->input('screensaver_mode');
        }

        $model->screensaver_image_auto_run_interval = $request->input('screensaver_image_auto_run_interval', 0);
        $model->is_screensaver_top_priority = $request->input('is_screensaver_top_priority', 0);
        $model->remark = $request->input('remark');
        $model->sort_order = $request->input('sort_order', 0);
        // 是否更改数据
        $is_modified_screen_time = $request->input('is_modified_screen_time', 0);
        // 获取请求充电枪数据
        $kiosk_setting_description_list = $request->input('item', array());
        // 获取请求屏幕时间数据
        $kiosk_setting_screen_time_list = $request->input('screen', array());
        $screensaver_list = $request->input('screensaver_list', array());
        // 八达通上传时间
        $octopus_upload_time_list = $request->input('octopus_upload_time_list', array());
        // 将时间数组转成以逗号分隔的秒字符串
        $octopus_upload_time_list_str = self::timeArrayStrToSecondsArrayStr($octopus_upload_time_list);
        $model->octopus_upload_time_list = $octopus_upload_time_list_str;
        // 八达通下载时间
        $octopus_download_time_list = $request->input('octopus_download_time_list', array());
        // 过滤掉空字符串和 null 值
        $octopus_download_time_list_str = self::timeArrayStrToSecondsArrayStr($octopus_download_time_list);
        $model->octopus_download_time_list = $octopus_download_time_list_str;

        $model->save();

        // 保存Kiosk设置后再保存并关联Kiosk设置
        foreach ($kiosk_setting_description_list as $kiosk_setting_description) {
            if (!isset($kiosk_setting_description['kiosk_setting_description_id']) ||
                blank($kiosk_setting_description_model = KioskSettingDescription::find($kiosk_setting_description['kiosk_setting_description_id']))) {
                $kiosk_setting_description_model = new KioskSettingDescription;
            }
            $kiosk_setting_description_model->kiosk_setting_number = $model->kiosk_setting_number;
            $kiosk_setting_description_model->language_code = $kiosk_setting_description['language_code'];
            $kiosk_setting_description_model->main_header_message = $kiosk_setting_description['main_header_message'] ?? null;
            $kiosk_setting_description_model->receipt_message = $kiosk_setting_description['receipt_message'] ?? null;
            $kiosk_setting_description_model->main_logo_image_url = $kiosk_setting_description['main_logo_image_url'] ?? null;
            $kiosk_setting_description_model->home_page_image_url = $kiosk_setting_description['home_page_image_url'] ?? null;
            $kiosk_setting_description_model->disclaimer_html = $kiosk_setting_description['disclaimer_html'] ?? null;

            $kiosk_setting_description_model->save();
        }

        // 是否更改数据
        if ($is_modified_screen_time == 1) {
            // 移除该kiosk设置下的屏幕时间数据
            $model->screenTime()->delete();
            foreach ($kiosk_setting_screen_time_list as $screen_time) {
                // 再次校验key是否为枚举值
                if (isset($screen_time['key']) && KioskSettingScreenTimeKeyEnum::hasValue($screen_time['key'], false)) {
                    $kiosk_setting_screen_time_model = new KioskSettingScreenTime;
                    $kiosk_setting_screen_time_model->kiosk_setting_number = $model->kiosk_setting_number;
                    $kiosk_setting_screen_time_model->key = $screen_time['key'];
                    $kiosk_setting_screen_time_model->time = $screen_time['time'] ?? 0;

                    $kiosk_setting_screen_time_model->save();
                }
            }
        }

        // 保存Kiosk设置后再保存并关联Kiosk设置
        if (filled($screensaver_list) && filled($model->screensaver_mode)) {
            $screensaver_id_list = $new_screensaver_id_list = $old_screensaver_id_list = $del_id_list = array();

            foreach ($screensaver_list as $screensaver) {
                $screensaver_id_list[] = array_filter(array_column($screensaver, 'kiosk_setting_screensaver_resource_description_id'));
            }
            // foreach循环三个语言
            foreach ($screensaver_id_list as $screensaver_id_new_language_code) {
                // 把每个语言内的id都获取到
                foreach ($screensaver_id_new_language_code as $screensaver_id_new) {
                    $new_screensaver_id_list[] = $screensaver_id_new;
                }
            }

            $old_screensaver_id_list = KioskSettingScreensaverResourceDescription::where('kiosk_setting_number', $model->kiosk_setting_number)
                ->pluck('kiosk_setting_screensaver_resource_description_id')
                ->toArray();
            $del_id_list = array_diff($old_screensaver_id_list, $new_screensaver_id_list);
            if (!empty($del_id_list)) {
                KioskSettingScreensaverResourceDescription::whereIn('kiosk_setting_screensaver_resource_description_id', $del_id_list)->delete();
            }
            foreach ($screensaver_list as $key => $screensaver) {
                foreach ($screensaver as $screensaver_item) {
                    if (empty($screensaver_item['kiosk_setting_screensaver_resource_description_id']) ||
                        blank($screensaver_model = KioskSettingScreensaverResourceDescription::find($screensaver_item['kiosk_setting_screensaver_resource_description_id']))) {
                        $screensaver_model = new KioskSettingScreensaverResourceDescription;
                    }
                    $screensaver_model->kiosk_setting_number = $model->kiosk_setting_number;
                    $screensaver_model->language_code = $key;
                    $screensaver_model->resource_url = $screensaver_item['resource_url'] ?? null;
                    $screensaver_model->sort_order = $screensaver_item['sort_order'] ?? 0;

                    $screensaver_model->save();
                }
            }
        } else {
            $model->screensaver()->delete();
        }

        self::sendInitPushByKioskNumberList();

        return redirect()->action(
            [self::class, 'showPage'], self::getUrlParams($request)
        );
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function rules(?Request $request, ?Model $model): array
    {
        $module_name = self::$module_name;

        $config_languages = config('languages');
        // 单独加入双语语言，只有kiosk需要显示双语，cms不需要双语
        $config_languages['en_US_zh_HK'] = __('common.en_US_zh_HK');

        $config_languages_keys = array_keys($config_languages);
        $rules = array(
            'name' => 'required|string|max:45',
            'default_language_code' => 'required|string|max:20|in:' . implode(",", $config_languages_keys),
            'is_display_idle_remain_time' => 'bool',
            'is_enable_user_charge_record_bill_payment' => 'bool',
            'is_display_fee_page' => 'bool',
            'is_display_support_page' => 'bool',
            'is_enable_settle_charge_arrears' => 'bool',
            'is_enable_charge_pre_authorization_refund' => 'bool',
            'print_history_receipt_identity_type_list' => 'nullable|array',
            'print_history_receipt_query_limit' => 'nullable|integer|min:0|max:999999',
            'print_history_receipt_query_validity_period' => 'nullable|integer|min:0|max:999999',
            'print_history_receipt_print_count_limit' => 'nullable|integer|min:0|max:999999',
            'is_enable_print_receipt_required_count_update' => 'bool',
            'is_display_available_payment_method' => 'bool',
            'is_enable_zone_selection' => 'bool',
            'connector_menu_row_count' => 'required|integer|min:0|max:10',
            'connector_menu_column_count' => 'required|integer|min:0|max:5',
            'connector_menu_preparing_row_count' => 'required|integer|min:0|max:2',
            'connector_menu_preparing_column_count' => 'required|integer|min:0|max:5',
            'is_display_connector_tag' => 'bool',
            'websocket_check_alive_interval' => [
                'required',
                'integer',
                'min:0',
                'max:999999',
                function ($attribute, $value, $fail) use ($module_name, $request) {
                    if ($value < $request->input('heartbeat_interval')) {
                        $fail(__("$module_name.websocket_check_alive_interval_heartbeat_interval_error"));
                    }
                },
            ],
            'websocket_alive_timeout' => [
                'required',
                'integer',
                'min:0',
                'max:999999',
                function ($attribute, $value, $fail) use ($module_name, $request) {
                    if ($value < $request->input('heartbeat_interval')) {
                        $fail(__("$module_name.websocket_alive_timeout_heartbeat_interval_error"));
                    }
                },
            ],
            'heartbeat_interval' => 'required|integer|min:0|max:999999',
            'pos_check_health_interval' => 'required|integer|min:0|max:999999',
            'self_check_interval' => 'required|integer|min:0|max:999999',
            'clear_cache_charge_record_buffer_time' => 'required|integer|min:0|max:999999',
            'charge_pre_authorization_confirm_retry_count' => 'nullable|integer|min:0|max:999999',
            'is_enable_record_screen_operation' => 'bool',
            'is_enable_enter_receiver' => 'bool',
            'is_enable_print_receipt' => 'bool',
            'is_enable_cms_calculate_user_charge_record_bill_total_amount' => 'bool',
            'is_enable_octopus_cancel' => 'bool',
            'is_enable_synchronize_system_date_time' => 'bool',
            'synchronize_system_date_time_offset' => 'required|integer|min:-999999|max:999999',
            'screensaver_waiting_time' => 'required|integer|min:0',
            'screensaver_mode' => 'nullable|string',
            'screensaver_image_auto_run_interval' => 'required|integer|min:0',
            'is_screensaver_top_priority' => 'bool',
            'check_octopus_data_exchange_interval' => 'required|integer|min:0|max:999999',
            'octopus_data_exchange_mode' => 'nullable|enum_value:' . OctopusDataExchangeMode::class,
            'octopus_upload_time_list' => [
                'nullable',
                'array',
                function ($attribute, $value, $fail) use ($module_name) {
                    // 如果存在空值或者重复值提示错误
                    if (count(Arr::where($value, fn($time) => blank($time))) > 0 ||
                        count($value) != count(array_unique($value))) {
                        $fail(__("$module_name.octopus_time_item_error"));
                    }
                },
            ],
            'octopus_download_time_list' => [
                'nullable',
                'array',
                function ($attribute, $value, $fail) use ($module_name) {
                    // 如果存在空值或者重复值提示错误
                    if (count(Arr::where($value, fn($time) => blank($time))) > 0 ||
                        count($value) != count(array_unique($value))) {
                        $fail(__("$module_name.octopus_time_item_error"));
                    }
                },
            ],
            'remark' => 'nullable|string',
            'sort_order' => 'integer|min:0',
        );

        $language_codes = array_keys($config_languages);
        $rules['item.*.language_code'] = Rule::in($language_codes);

        $rules['item.*.main_header_message'] = 'nullable|max:1000';
        /*$rules['item.*.disclaimer_html'] = 'required';*/

        // 验证枚举值且唯一
        $rules['screen.*.key'] = 'required|distinct|enum_value:' . KioskSettingScreenTimeKeyEnum::class;
        $rules['screen.*.time'] = 'required|integer|min:0|max:999';

        $resource_url_rules = array();
        if (filled($request->input('screensaver_mode')) && $request->input('screensaver_mode') != 'NO_MODE') {
            foreach ($config_languages as $language_code => $language_name) {
                $rules['screensaver_list.' . $language_code] = 'required';
            }
        }

        $allow_format = array();
        $format_list = json_decode(file_get_contents(base_path() . '/format.json'), true);

        switch ($request->input('screensaver_mode')) {
            case ScreensaverModeEnum::Image:
                $allow_format = array_merge($allow_format, $format_list['image']);
                $resource_url_rules = [
                    'required',
                    function ($attribute, $value, $fail) use ($format_list, $allow_format, $module_name) {
                        // 获取后缀名文件格式然后转小写
                        $value_lower = strtolower(pathinfo($value, PATHINFO_EXTENSION));
                        if ($allow_format && !in_array($value_lower, $allow_format)) {
                            $message = __("$module_name.image_must_be_for") . implode(',', $format_list['image']);
                            $fail($message);
                            return;
                        }
                    },
                ];
                break;
            case ScreensaverModeEnum::Video:
                $allow_format = array_merge($allow_format, $format_list['video']);
                $resource_url_rules = [
                    'required',
                    function ($attribute, $value, $fail) use ($format_list, $allow_format, $module_name) {
                        // 获取后缀名文件格式然后转小写
                        $value_lower = strtolower(pathinfo($value, PATHINFO_EXTENSION));
                        if ($allow_format && !in_array($value_lower, $allow_format)) {
                            $message = __("$module_name.video_must_be_for") . implode(',', $format_list['video']);
                            $fail($message);
                            return;
                        }
                    },
                ];
                break;
            default:
                $resource_url_rules = 'required';
                $allow_format[] = $request->input('screensaver_mode');
                break;
        }
        $rules['screensaver_list.*.*.resource_url'] = $resource_url_rules;
        $rules['screensaver_list.*.*.sort_order'] = 'integer|min:0|max:999999';

        // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下才需要判断场地编号是否存在
        if (blank($model->kiosk_setting_id)) {
            $rules['kiosk_setting_number'] = [
                'required',
                'unique:App\Models\Modules\KioskSetting,kiosk_setting_number',
                'max:30',
                'regex:/^[a-zA-Z0-9]+$/', // 只能填写字母和数字
            ];
            if (isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) {
                $rules['site_number'] = [
                    'required',
                    'exists:App\Models\Modules\Site,site_number',
                    function ($attr, $value, $fail) use ($module_name) {
                        // 判断选择的site是否包含在当前角色的场地内
                        $site = Site::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('site_number', $value);
                        if (blank($site)) $fail(__('common.text_not_found', ['field' => __("$module_name.site")]));
                    }
                ];
            }
        }

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        $errors = [
            'site_number' => __("$module_name.site"),
            'kiosk_setting_number' => __("$module_name.kiosk_setting_number"),
            'name' => __("$module_name.name"),
            'default_language_code' => __("$module_name.default_language_code"),
            'is_display_idle_remain_time' => __("$module_name.is_display_idle_remain_time"),
            'is_enable_user_charge_record_bill_payment' => __("$module_name.is_enable_user_charge_record_bill_payment"),
            'is_display_fee_page' => __("$module_name.is_display_fee_page"),
            'is_display_support_page' => __("$module_name.is_display_support_page"),
            'is_enable_settle_charge_arrears' => __("$module_name.is_enable_settle_charge_arrears"),
            'is_enable_charge_pre_authorization_refund' => __("$module_name.is_enable_charge_pre_authorization_refund"),
            'print_history_receipt_identity_type_list' => __("$module_name.print_history_receipt_identity_type_list"),
            'print_history_receipt_query_limit' => __("$module_name.print_history_receipt_query_limit"),
            'print_history_receipt_query_validity_period' => __("$module_name.print_history_receipt_query_validity_period"),
            'print_history_receipt_print_count_limit' => __("$module_name.print_history_receipt_print_count_limit"),
            'is_enable_print_receipt_required_count_update' => __("$module_name.is_enable_print_receipt_required_count_update"),
            'is_display_available_payment_method' => __("$module_name.is_display_available_payment_method"),
            'is_enable_zone_selection' => __("$module_name.is_enable_zone_selection"),
            'connector_menu_row_count' => __("$module_name.connector_menu_row_count"),
            'connector_menu_column_count' => __("$module_name.connector_menu_column_count"),
            'connector_menu_preparing_row_count' => __("$module_name.connector_menu_preparing_row_count"),
            'connector_menu_preparing_column_count' => __("$module_name.connector_menu_preparing_column_count"),
            'is_display_connector_tag' => __("$module_name.is_display_connector_tag"),
            'websocket_check_alive_interval' => __("$module_name.websocket_check_alive_interval"),
            'websocket_alive_timeout' => __("$module_name.websocket_alive_timeout"),
            'heartbeat_interval' => __("$module_name.heartbeat_interval"),
            'self_check_interval' => __("$module_name.self_check_interval"),
            'pos_check_health_interval' => __("$module_name.pos_check_health_interval"),
            'clear_cache_charge_record_buffer_time' => __("$module_name.clear_cache_charge_record_buffer_time"),
            'charge_pre_authorization_confirm_retry_count' => __("$module_name.charge_pre_authorization_confirm_retry_count"),
            'is_enable_record_screen_operation' => __("$module_name.is_enable_record_screen_operation"),
            'is_enable_enter_receiver' => __("$module_name.is_enable_enter_receiver"),
            'is_enable_print_receipt' => __("$module_name.is_enable_print_receipt"),
            'is_enable_cms_calculate_user_charge_record_bill_total_amount' => __("$module_name.is_enable_cms_calculate_user_charge_record_bill_total_amount"),
            'is_enable_octopus_cancel' => __("$module_name.is_enable_octopus_cancel"),
            'check_octopus_data_exchange_interval' => __("$module_name.check_octopus_data_exchange_interval"),
            'octopus_data_exchange_mode' => __("$module_name.octopus_data_exchange_mode"),
            'octopus_upload_time_list' => __("$module_name.octopus_upload_time_list"),
            'octopus_download_time_list' => __("$module_name.octopus_download_time_list"),
            'is_enable_synchronize_system_date_time' => __("$module_name.is_enable_synchronize_system_date_time"),
            'synchronize_system_date_time_offset' => __("$module_name.synchronize_system_date_time_offset"),
            'screensaver_waiting_time' => __("$module_name.screensaver_waiting_time"),
            'screensaver_mode' => __("$module_name.screensaver_mode"),
            'screensaver_image_auto_run_interval' => __("$module_name.screensaver_image_auto_run_interval"),
            'is_screensaver_top_priority' => __("$module_name.is_screensaver_top_priority"),
            'sort_order' => __("$module_name.sort_order"),
            'remark' => __("$module_name.remark"),

            'item.*.main_header_message' => __("$module_name.main_header_message"),
            'item.*.disclaimer_html' => __("$module_name.disclaimer_html"),
            'item.*.language_code' => __("$module_name.language_code"),

            'screen.*.key' => __("$module_name.screen_time_key"),
            'screen.*.time' => __("$module_name.screen_time_time"),
            'screensaver_list.*.*.resource_url' => __("$module_name.resource_url"),
            'screensaver_list.*.*.sort_order' => __("$module_name.sort_order"),
        ];
        $config_languages = config('languages');
        // 单独加入双语语言，只有kiosk需要显示双语，cms不需要双语
        $config_languages['en_US_zh_HK'] = __('common.en_US_zh_HK');
        foreach ($config_languages as $language_code => $language_name) {
            $errors['screensaver_list.' . $language_code] = $language_name . __("$module_name.follow_field");
        }

        return $errors;
    }

    /**
     * 返回地址栏参数
     *
     * @param Request $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => 'true',
            'site_search' => $request->get('site_search'),
            'name_search' => $request->get('name_search'),
            'default_language_code_search' => $request->get('default_language_code_search'),
            'kiosk_setting_number_search' => $request->get('kiosk_setting_number_search'),
        );
    }

    public function getKioskNumberListByKioskSettingNumber(Request $request): JsonResponse
    {
        $kiosk_setting_number = $request->input('kiosk_setting_number');
        if (blank($kiosk_setting_number)) {
            $this->missingField('kiosk_setting_number');
            return $this->returnJson();
        }
        $kiosk_number_list = Kiosk::where('kiosk_setting_number', $kiosk_setting_number)->pluck('kiosk_number')->toArray();
        $this->data = $kiosk_number_list ?? [];

        return $this->returnJson();
    }

    /**
     * 去除数组中的null或''
     * @param array $array
     * @return array
     * <AUTHOR>
     * @date 2024-05-20
     */
    protected static function arrayFilterNullOrEmpty(array $array): array
    {
        return array_filter($array, function ($value) {
            return !is_null($value) && $value !== '';
        });
    }

    /**
     * 将时间转换为秒
     * @param string $time
     * @return int
     * <AUTHOR>
     * @date 2024-05-20
     */
    protected static function timeToSeconds(string $time): int
    {
        // 判断传入的值是否为空
        // 使用正则表达式验证时间格式是否为 HH:mm
        if (is_null($time) || '' == trim($time) || !preg_match('/^([01]\d|2[0-3]):([0-5]\d)$/', $time)) return -1;
        list($hours, $minutes) = explode(':', $time);
        return $hours * 3600 + $minutes * 60;
    }

    /**
     * 将秒数转换为HH:mm格式的时间字符串
     * @param int $seconds
     * @return string
     */
    protected static function secondsToTime(int $seconds): string
    {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        return sprintf('%02d:%02d', $hours, $minutes);
    }

    /**
     * 将时间字符串数组转成秒数组,并返回指定分隔符分隔的秒数组字符串
     * @param array $time_array
     * @return string
     * <AUTHOR>
     * @date 2024-05-20
     */
    protected static function timeArrayStrToSecondsArrayStr(array $time_array)
    {
        if (blank($time_array)) return '';
        // 去除重复值
        $unique_time_array = array_unique($time_array);
        // 过滤掉空字符串和 null 值
        $filtered_time_array = self::arrayFilterNullOrEmpty($unique_time_array);
        // 检查过滤后的数组是否为空，如果为空直接返回空字符串
        if (blank($filtered_time_array)) return '';
        // 将过滤后的时间数组转换为秒的数组
        $seconds_array = array_map([self::class, 'timeToSeconds'], $filtered_time_array);
        // 过滤掉转换结果为 -1 的元素
        $filter_second_array = array_filter($seconds_array, function ($seconds) {
            return $seconds >= 0;
        });
        // 对秒的数组进行排序
        sort($filter_second_array);
        // 将排序后的秒数组转换为以逗号分隔的字符串
        $seconds_array_str = implode(',', $filter_second_array);
        return $seconds_array_str;
    }

    /**
     * 将以逗号分隔的秒数字符串转换为HH:mm时间数组
     * @param string $secondsString
     * @return array
     */
    protected static function secondsStringToTimeArray(?string $secondsString): array
    {
        if (blank($secondsString)) {
            return [];
        }

        // 将逗号分隔的字符串转换为数组
        $secondsArray = explode(',', $secondsString);

        // 去除数组中的null或空字符串
        // $filteredSecondsArray = self::arrayFilterNullOrEmpty($secondsArray);

        // 将每个秒数转换为HH:mm格式的时间
        $timeArray = array_map(function ($seconds) {
            return self::secondsToTime((int)$seconds);
        }, $secondsArray);
        // }, $filteredSecondsArray);
        return $timeArray;
    }

}


<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;
use App\Models\Traits\Emoji;

class EventLog extends Model
{
    use emoji;

    protected $table = 'event_log'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'event_log_id'; //定义主键，默认为id
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    // protected $fillable = [];

    /**
     * 类型转化器
     */
    protected $casts = [];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];
}

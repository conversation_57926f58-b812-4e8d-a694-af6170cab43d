<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Enums\{LmsMode,
    TariffTableType,
    ChargeTariffScheme,
    ChargeValueType,
    IdentityType,
};
use App\Models\Modules\{
    ComServer,
    ChargePointSetting,
    Merchant,
    MemberCardGroup,
    UserGroup,
    Site,
    VioskSetting,
    Zone,
    ConnectorSetting,
    KioskSetting,
    ChargePoint,
    ChargePointCs,
    TimeTariffTable,
    IdlingPenaltyTariffTable,
    PeakTimeTable,
    SimpleTariffTable,
    EnergyTariffTable,
    Kiosk,
    AppUser,
};

class ModalController extends CommonController
{

    /**
     * Com Server最新数据
     * @param Request $request
     * @return JsonResponse
     */
    public function comServer(Request $request): JsonResponse
    {
        $data = array(
            'comServerList' => ComServer::orderBy('sort_order')->get(),
        );

        $this->data = view('components.data.com-server-manager-modal', $data)->render();

        return $this->returnJson();
    }

    /**
     * Charge Point Setting最新数据
     * @param Request $request
     * @return JsonResponse
     */
    public function chargePointSetting(Request $request): JsonResponse
    {
        $data = array(
            'chargePointSettingList' => ChargePointSetting::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->orderBy('sort_order')
                ->get(),
        );

        $this->data = view('components.data.charge-point-setting-manager-modal', $data)->render();

        return $this->returnJson();
    }

    /**
     * Site最新数据
     * @param Request $request
     * @return JsonResponse
     */
    public function site(Request $request): JsonResponse
    {
        $data = array(
            'siteList' => Site::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->orderBy('sort_order')
                ->get(),
        );

        foreach ($data['siteList'] as $site) {
            $site->name = $this->getValueFromLanguageArray($site->name_json);
            $site->lms_mode = LmsMode::getDescription($site->lms_mode);
        }

        $this->data = view('components.data.site-manager-modal', $data)->render();

        return $this->returnJson();
    }

    /**
     * Zone最新数据
     * @param Request $request
     * @return JsonResponse
     */
    public function zone(Request $request): JsonResponse
    {
        $data = array(
            'zoneList' => Zone::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->orderBy('sort_order')
                ->get(),
        );

        foreach ($data['zoneList'] as $zone) {
            $zone->name = $this->getValueFromLanguageArray($zone->name_json) ?? '—/—';
        }

        $this->data = view('components.data.zone-manager-modal', $data)->render();

        return $this->returnJson();
    }

    /**
     * Connector Setting最新数据
     * @param Request $request
     * @return JsonResponse
     */
    public function connectorSetting(Request $request): JsonResponse
    {
        $data = array(
            'connectorSettingList' => ConnectorSetting::with(['simpleTariffTable', 'timeTariffTable', 'energyTariffTable', 'idlingPenaltyTariffTable', 'peakTimeTable'])
                ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->orderBy('sort_order')
                ->get(),
        );

        foreach ($data['connectorSettingList'] as $connector_setting) {
            $connector_setting->tariff_table_type = TariffTableType::getDescription($connector_setting->tariff_table_type);
        }

        $this->data = view('components.data.connector-setting-manager-modal', $data)->render();

        return $this->returnJson();
    }

    /**
     * Kiosk Setting最新数据
     * @param Request $request
     * @return JsonResponse
     */
    public function kioskSetting(Request $request): JsonResponse
    {
        $data = array(
            'kioskSettingList' => KioskSetting::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->orderBy('sort_order')
                ->get(),
        );
        $default_language_codes = config('languages');
        $default_language_codes['en_US_zh_HK'] = __('common.en_US_zh_HK');
        foreach ($data['kioskSettingList'] as $kiosk_setting) {
            $kiosk_setting->default_language_code = ($default_language_codes[$kiosk_setting->default_language_code]) ?? $kiosk_setting->default_language_code;
        }

        $this->data = view('components.data.kiosk-setting-manager-modal', $data)->render();

        return $this->returnJson();
    }

    /**
     * Charge Point最新数据
     * @param Request $request
     * @return JsonResponse
     */
    public function chargePoint(Request $request): JsonResponse
    {
        $data = array(
            'chargePointList' => ChargePoint::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->orderBy('sort_order')
                ->get(),
        );
        foreach ($data['chargePointList'] as $charge_point) {
            $charge_point->com_server_number = filled($charge_point->com_server_number)
                ? (ComServer::firstWhere('com_server_number', $charge_point->com_server_number)?->name ?? '')
                : $charge_point->com_server_number;
            $charge_point->charge_point_cs_number = filled($charge_point->charge_point_cs_number)
                ? (ChargePointCs::firstWhere('charge_point_cs_number', $charge_point->charge_point_cs_number)?->name ?? '')
                : $charge_point->charge_point_cs_number;
            $charge_point->site_number = filled($charge_point->site_number)
                ? ($this->getValueFromLanguageArray(
                    Site::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                        ->firstWhere('site_number', $charge_point->site_number)?->name_json) ?? '')
                : $charge_point->site_number;
            $charge_point->charge_point_setting_number = filled($charge_point->charge_point_setting_number)
                ? (ChargePointSetting::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                    ->firstWhere('charge_point_setting_number', $charge_point->charge_point_setting_number)?->name ?? '')
                : $charge_point->charge_point_setting_number;
        }

        $this->data = view('components.data.charge-point-manager-modal', $data)->render();

        return $this->returnJson();
    }

    /**
     * Time Tariff Table最新数据
     * @param Request $request
     * @return JsonResponse
     */
    public function timeTariffTable(Request $request): JsonResponse
    {
        $data = array(
            'timeTariffTableList' => TimeTariffTable::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->orderBy('sort_order')
                ->get(),
            'timeId' => null,
        );
        foreach ($data['timeTariffTableList'] as &$time_tariff_table) {
            $time_tariff_table->charge_tariff_scheme = ChargeTariffScheme::getDescription($time_tariff_table->charge_tariff_scheme);
            $time_tariff_table->post_paid_identity_type = IdentityType::getDescription($time_tariff_table->post_paid_identity_type);
        }

        $this->data = view('components.data.time-tariff-table-manager-modal', $data)->render();

        return $this->returnJson();
    }

    /**
     * Idling Penalty Tariff Table最新数据
     * @param Request $request
     * @return JsonResponse
     */
    public function idlingPenaltyTariffTable(Request $request): JsonResponse
    {
        $data = array(
            'idlingPenaltyTariffTableList' => IdlingPenaltyTariffTable::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->orderBy('sort_order')
                ->get(),
            'idlingId' => null,
        );

        $this->data = view('components.data.idling-penalty-tariff-table-manager-modal', $data)->render();

        return $this->returnJson();
    }

    /**
     * Peak Time Table最新数据
     * @param Request $request
     * @return JsonResponse
     */
    public function peakTimeTable(Request $request): JsonResponse
    {
        $data = array(
            'peakTimeTableList' => PeakTimeTable::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->orderBy('sort_order')
                ->get(),
            'peakId' => null,
        );

        $this->data = view('components.data.peak-time-table-manager-modal', $data)->render();

        return $this->returnJson();
    }

    /**
     * Simple Tariff Table最新数据
     * @param Request $request
     * @return JsonResponse
     */
    public function simpleTariffTable(Request $request): JsonResponse
    {
        $data = array(
            'simpleTariffTableList' => SimpleTariffTable::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->orderBy('sort_order')
                ->get(),
            'simpleId' => null,
        );
        foreach ($data['simpleTariffTableList'] as &$simple_tariff_table) {
            $simple_tariff_table->charge_tariff_scheme = ChargeTariffScheme::getDescription($simple_tariff_table->charge_tariff_scheme);
            $simple_tariff_table->post_paid_identity_type = IdentityType::getDescription($simple_tariff_table->post_paid_identity_type);
            switch ($simple_tariff_table->charge_value_type) {
                case ChargeValueType::Time:
                    $simple_tariff_table->charge_value_interval = round($simple_tariff_table->charge_value_interval / 60, 2) . __('common.unit_mins');
                    $simple_tariff_table->pre_paid_charge_value_maximum_selection = round($simple_tariff_table->pre_paid_charge_value_maximum_selection / 60, 2) . __('common.unit_mins');
                    break;
                case ChargeValueType::Energy:
                    $simple_tariff_table->charge_value_interval = round($simple_tariff_table->charge_value_interval / 1000, 3) . __('common.unit_kwh');
                    $simple_tariff_table->pre_paid_charge_value_maximum_selection = round($simple_tariff_table->pre_paid_charge_value_maximum_selection / 1000, 3) . __('common.unit_kwh');
                    break;
                default:
                    break;
            }
        }
        $this->data = view('components.data.simple-tariff-table-manager-modal', $data)->render();

        return $this->returnJson();
    }

    /**
     * Energy Tariff Table最新数据
     * @param Request $request
     * @return JsonResponse
     */
    public function energyTariffTable(Request $request): JsonResponse
    {
        $data = array(
            'energyTariffTableList' => EnergyTariffTable::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->orderBy('sort_order')
                ->get(),
            'energyId' => null,
        );
        foreach ($data['energyTariffTableList'] as $energy_tariff_table) {
            $energy_tariff_table->post_paid_identity_type = IdentityType::getDescription($energy_tariff_table->post_paid_identity_type);
        }

        $this->data = view('components.data.energy-tariff-table-manager-modal', $data)->render();

        return $this->returnJson();
    }

    /**
     * Charge Point Cs最新数据
     * @param Request $request
     * @return JsonResponse
     */
    public function chargePointCs(Request $request): JsonResponse
    {
        $data = array(
            'chargePointCsManagerList' => ChargePointCs::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->orderBy('sort_order')->get(),
        );

        $this->data = view('components.data.charge-point-cs-manager-modal', $data)->render();

        return $this->returnJson();
    }

    /**
     * Kiosk最新数据
     * @param Request $request
     * @return JsonResponse
     */
    public function kiosk(Request $request): JsonResponse
    {
        $site = Site::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->pluck('site_number')
            ->toArray();
        $data = array(
            'kioskList' => Kiosk::whereIn('site_number', $site)->orderBy('sort_order')->get(),
        );
        foreach ($data['kioskList'] as $kiosk) {
            $kiosk_setting_name = KioskSetting::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->firstWhere('kiosk_setting_number', $kiosk['kiosk_setting_number'])?->name; // Kiosk设置名称
            $site_name = $this->getValueFromLanguageArray(
                Site::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                    ->firstWhere('site_number', $kiosk['site_number'])?->name_json); // 场地名称
            $kiosk->kiosk_setting_name = $kiosk_setting_name;
            $kiosk->site_name = $site_name;
        }

        $this->data = view('components.data.kiosk-manager-modal', $data)->render();

        return $this->returnJson();
    }

    /**
     * Kiosk Checkbox最新数据
     * @param Request $request
     * @return JsonResponse
     */
    public function kioskCheckbox(Request $request): JsonResponse
    {
        $octopus_data_exchange_mode = $request->octopus_data_exchange_mode;
        $enableCheckOnline = (bool)$request->enableCheckOnline;
        $kiosk_list = new Kiosk;
        if (filled($octopus_data_exchange_mode)) {
            $kiosk_list = $kiosk_list->whereHas('kioskSetting', function ($query) use ($octopus_data_exchange_mode) {
                $query->where('octopus_data_exchange_mode', $octopus_data_exchange_mode);
            });
        }
        $site = Site::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->pluck('site_number')
            ->toArray();
        $data = array(
            'kioskList' => $kiosk_list->whereIn('site_number', $site)->orderBy('sort_order')->get(),
        );

        // 是否校验在线
        if (filled($data['kioskList']) && $enableCheckOnline) {
            $data['kioskList'] = CommonController::websocketIsUidOnlineByKioskList($data['kioskList']);
        }

        foreach ($data['kioskList'] as $kiosk) {
            $kiosk_setting_name = KioskSetting::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->firstWhere('kiosk_setting_number', $kiosk['kiosk_setting_number'])?->name; // Kiosk设置名称
            $site_name = $this->getValueFromLanguageArray(
                Site::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                    ->firstWhere('site_number', $kiosk['site_number'])?->name_json); // 场地名称
            $kiosk->kiosk_setting_name = $kiosk_setting_name;
            $kiosk->site_name = $site_name;
        }

        $this->data = view('components.data.kiosk-manager-checkbox-modal', $data)->render();

        return $this->returnJson();
    }

    /**
     * Site Checkbox最新数据
     * @return JsonResponse
     */
    public function siteCheckbox(): JsonResponse
    {
        $data = array(
            'siteList' => Site::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->orderBy('sort_order')
                ->get(),
        );
        foreach ($data['siteList'] as $site) {
            $site->name = $this->getValueFromLanguageArray($site->name_json);
            $site->lms_mode = LmsMode::getDescription($site->lms_mode);
        }

        $this->data = view('components.data.site-manager-checkbox-modal', $data)->render();

        return $this->returnJson();
    }

    /**
     * 商戶最新数据
     * @return JsonResponse
     */
    public function merchant(): JsonResponse
    {
        $data = array(
            'merchantList' => Merchant::when(!isSuperAdministrator(), fn($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))->get(),
        );
        foreach ($data['merchantList'] as $merchant) {
            $merchant->name = $this->getValueFromLanguageArray($merchant->name_json) ?? '';
        }
        $this->data = view('components.data.merchant-manager-modal', $data)->render();

        return $this->returnJson();
    }

    /**
     * 商戶最新数据
     * @return JsonResponse
     */
    public function memberCardGroup(): JsonResponse
    {
        $data = array(
            'memberCardGroupList' => MemberCardGroup::with('site')->when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->get(),
        );
        foreach ($data['memberCardGroupList'] as &$memberCardGroup) {
            $memberCardGroup->name = $this->getValueFromLanguageArray($memberCardGroup->name_json) ?? '';
            $memberCardGroup->site_name = $this->getValueFromLanguageArray($memberCardGroup->site?->name_json) ?? '';
        }
        $this->data = view('components.data.member-card-group-manager-modal', $data)->render();

        return $this->returnJson();
    }

    /**
     * 商戶最新数据
     * @return JsonResponse
     */
    public function memberCardGroupNotCheckCheckbox(Request $request): JsonResponse
    {
        $member_card_group_id_list = $request->member_card_group_id_list;
        $data = array(
            'memberCardGroupList' => MemberCardGroup::with('site')->when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->when(filled($member_card_group_id_list), fn($query) => $query->whereNotIn('member_card_group_id', $member_card_group_id_list))
                ->get(),
        );

        foreach ($data['memberCardGroupList'] as &$memberCardGroup) {
            $memberCardGroup->name = $this->getValueFromLanguageArray($memberCardGroup->name_json) ?? '';
            $memberCardGroup->site_name = $this->getValueFromLanguageArray($memberCardGroup->site?->name_json) ?? '';
        }
        $this->data = view('components.data.member-card-group-manager-checkbox-modal', $data)->render();

        return $this->returnJson();
    }

    /**
     * 用户组最新数据
     * @return JsonResponse
     */
    public function userGroupNotCheckCheckbox(Request $request): JsonResponse
    {
        $user_group_id_list = $request->user_group_id_list;
        $data = array(
            'userGroupList' => UserGroup::with('merchant')->when(!isSuperAdministrator(), fn($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))
                ->when(filled($user_group_id_list), fn($query) => $query->whereNotIn('user_group_id', $user_group_id_list))
                ->get(),
        );

        foreach ($data['userGroupList'] as &$userGroup) {
            $userGroup->name = $this->getValueFromLanguageArray($userGroup->name_json) ?? '';
            $userGroup->merchant_name = $this->getValueFromLanguageArray($userGroup->merchant?->name_json) ?? '';
        }
        $this->data = view('components.data.user-group-manager-checkbox-modal', $data)->render();

        return $this->returnJson();
    }

    /**
     * 商戶最新数据
     * @return JsonResponse
     */
    public function appUser(): JsonResponse
    {
        $data = array(
            'appUserList' => AppUser::get(),
        );

        $this->data = view('components.data.app-user-manager-modal', $data)->render();

        return $this->returnJson();
    }

    /**
     * Viosk Setting最新数据
     * @param Request $request
     * @return JsonResponse
     */
    public function vioskSetting(Request $request): JsonResponse
    {
        $data = array(
            'vioskSettingList' => VioskSetting::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->orderBy('sort_order')
                ->get(),
        );
        $default_language_codes = config('languages');
        $default_language_codes['en_US_zh_HK'] = __('common.en_US_zh_HK');
        foreach ($data['vioskSettingList'] as $viosk_setting) {
            $viosk_setting->default_language_code = ($default_language_codes[$viosk_setting->default_language_code]) ?? $viosk_setting->default_language_code;
        }

        $this->data = view('components.data.viosk-setting-manager-modal', $data)->render();

        return $this->returnJson();
    }
}

<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;

class MemberCard extends Model
{
    protected $table = 'member_card';
    protected $primaryKey = 'member_card_id';

    const CREATED_AT = 'gmt_create'; //默认是小写的模型类名复数格式，此处为自定义表名
    const UPDATED_AT = 'gmt_modified'; //定义主键，默认为id

    // protected $fillable = [];
    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 类型转化器
     */
    protected $casts = [
        'is_enable' => 'bool',
    ];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [
        'is_enable' => true,
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    // 一对一关联商户
    public function merchant()
    {
        return $this->hasOne(Merchant::class, 'merchant_number', 'merchant_number');
    }

    // 一对一关联场地
    public function site()
    {
        return $this->hasOne(Site::class, 'site_number', 'site_number');
    }

    // 一对一关联用户User
    public function user()
    {
        return $this->hasOne(AppUser::class, 'user_id', 'user_id');
    }

    // 一对一关联会员卡组
    public function memberCardGroup()
    {
        return $this->hasOne(MemberCardGroup::class, 'member_card_group_id', 'member_card_group_id');
    }

}

<?php

namespace App\Enums;

use BenSampo\Enum\Enum;
use BenSampo\Enum\Contracts\LocalizedEnum;
use App\Enums\Traits\Tools;

/**
 * @method static YedpayOnlinePayment
 * @method static SpectraOnlinePayment
 */
final class PaymentVendor extends Enum implements LocalizedEnum
{
    use Tools;

    const YedpayOnlinePayment = 'YEDPAY_ONLINE_PAYMENT';
    const SpectraOnlinePayment = 'SPECTRA_ONLINE_PAYMENT';
}

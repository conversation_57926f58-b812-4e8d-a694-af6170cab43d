<?php

namespace App\Http\Controllers\Admin;

use App;
use App\Http\Controllers\Common\CommonController;
use Illuminate\Http\Request;
use Illuminate\Contracts\View\{
    Factory,
    View
};
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Lang;

class ManualController extends CommonController
{
    protected static string $module_name = 'manual'; // 模块名称

    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $loader = Lang::getLoader(); // 获取加载器
        $locale = Lang::getLocale(); // 获取当前本地化语言
        $translations = $loader->load($locale, 'locale'); // 加载语言文件

        $data = array(
            'module_name' => self::$module_name,
            'current_language' => App::currentLocale(),
            'manual_translations' => $translations,
        );

        return view("pages.{$data['module_name']}.view", $data);
    }

}

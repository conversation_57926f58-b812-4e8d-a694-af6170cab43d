<?php

namespace App\Enums;

use BenSampo\Enum\Enum;
use BenSampo\Enum\Contracts\LocalizedEnum;
use App\Enums\Traits\Tools;

/**
 * @tags 验证码类型
 * @description 验证码类型
 * @method static static Login()
 * @method static static Registration()
 * @method static static ResetPassword()
 * @method static static UpdateTelephone()
 * @method static static UpdateEmail()
 */
final class VerificationCodeTypeEnum extends Enum implements LocalizedEnum
{
    use Tools;

    const Login = 'LOGIN';
    const Registration = 'REGISTRATION';
    const ResetPassword = 'RESET_PASSWORD';
    const UpdateTelephone = 'UPDATE_TELEPHONE';
    const UpdateEmail = 'UPDATE_EMAIL';
}

<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use App\Jobs\ScheduleSendEmailJob;
use App\Jobs\ScheduleSendSMSJob;
use App\Jobs\SendEmailJob;
use App\Models\Modules\EmailSendRecord;
use App\Models\Modules\SmsSendRecord;
use DateTime;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Foundation\Application;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\{
    DB,
    Redis,
};

class ServerDeploymentTestController extends CommonController
{
    protected static string $module_name = 'serverDeploymentTest'; // 模块名称

    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $db_status = 'OK';
        $redis_status = 'OK';

        // 检查数据库连接
        try {
            DB::connection()->getPdo();
        } catch (\Exception $e) {
            $db_status = 'ERROR: ' . $e->getMessage();
        }

        // 检查 Redis 连接
        try {
            Redis::ping();
        } catch (\Exception $e) {
            $redis_status = 'ERROR: ' . $e->getMessage();
        }

        $data = array(
            'module_name' => self::$module_name,
            'db_status' => $db_status,
            'redis_status' => $redis_status,
        );

        return view("pages.{$data['module_name']}.view", $data);
    }

    /**
     * 测试Kiosk Web Socket是否在线
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function checkKiosk(Request $request): JsonResponse
    {
        $kiosk_id = $request->input('kiosk_id');

        $wb_status = 'Error';
        $host = env('WEBSOCKET_HOST', '127.0.0.1');
        $port = env('WEBSOCKET_KIOSK_PORT', '8282');

        $result = array();

        $fp = @fsockopen($host, $port);

        if ($fp) {
            $wb_status = 'OK';
        }

        if (!empty($kiosk_id)) {
            $kiosk_status = 'Offline';
            try {
                if (self::websocketIsUidOnline($kiosk_id)) {
                    $kiosk_status = 'Online';
                }
            } catch (\Exception $e) {
                $kiosk_status = 'Kiosk Error: ' . $e->getMessage();
            }
            $result['kiosk_status'] = $kiosk_status;
        }

        $result['wb_status'] = $wb_status;

        $this->data = $result;

        return $this->returnJson();
    }

    /**
     * 测试CMS Web Socket是否在线
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function checkCMS(Request $request): JsonResponse
    {
        $wb_status = 'Error';
        $host = env('WEBSOCKET_HOST', '127.0.0.1');
        $port = env('WEBSOCKET_WEB_PORT', '8283');

        $fp = @fsockopen($host, $port);

        if ($fp) {
            $wb_status = 'OK';
        }

        $this->data = array(
            'wb_status' => $wb_status,
        );

        return $this->returnJson();
    }

    /**
     * 获取服务器时间及时区
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getServerTime(Request $request): JsonResponse
    {
        $server_time = new DateTime();

        $this->data = array(
            'server_time' => $server_time->format('Y-m-d H:i:s') . ' | ' . $server_time->getTimezone()->getName(),
        );

        return $this->returnJson();
    }

    /**
     * 发送邮件
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function sendEmail1(Request $request): JsonResponse
    {
        $email = $request->input('email', '');

        if (!empty($email)) {
            $send_title = 'Server Deployment Test Email';
            $send_content = 'Server Deployment Test Email Message…';

            $email_send_record = new EmailSendRecord;
            $email_send_record->recipient = $email;
            $email_send_record->title = $send_title;
            $email_send_record->message = $send_content;
            $email_send_record->gmt_expiry = date('Y-m-d H:i:s', strtotime('+5 minute'));
            $email_send_record->save();

            // 将发送邮件任务分发给队列
            ScheduleSendEmailJob::dispatch($email_send_record);
        }

        return $this->returnJson();
    }

    /**
     * 发送短信
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function sendSMS1(Request $request): JsonResponse
    {
        $telephone = $request->input('telephone', '');

        if (!empty($telephone)) {
            $send_content = 'Server Deployment Test SMS Message…';

            $sms_send_record = new SmsSendRecord;
            $sms_send_record->recipient = $telephone;
            $sms_send_record->message = $send_content;
            $sms_send_record->gmt_expiry = date('Y-m-d H:i:s', strtotime('+5 minute'));
            $sms_send_record->save();

            // 将发送短信任务分发给队列
            ScheduleSendSMSJob::dispatch($sms_send_record);
        }

        return $this->returnJson();
    }
}

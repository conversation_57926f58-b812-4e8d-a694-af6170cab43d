<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Application;
use Illuminate\Validation\Rule;
use Illuminate\Http\{
    Request,
    JsonResponse,
    RedirectResponse,
};
use App\Http\Controllers\Admin\Traits\{
    Add,
    Edit,
};
use App\Models\Modules\{
    MemberCard,
    MemberCardGroup,
    AppUser,
    Merchant,
    Site,
};
use Illuminate\Support\Str;

class MemberCardController extends CommonController
{
    use Add;

    protected static string $module_name = 'memberCard'; // 模块名称
    protected static bool $module_check_site = true; // 标记该模块校验场地

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new MemberCard;
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'add_url' => action([self::class, 'add']),
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'download_template_url' => existsImage('template', 'member_card_template.xlsx'),
            'import_member_card_url' => action([self::class, 'importMemberCard']),
            'member_card_key_search' => $request->input('member_card_key_search'),
            'octopus_card_number_search' => $request->input('octopus_card_number_search'),
            'rfid_card_number_search' => $request->input('rfid_card_number_search'),
            'user_email_search' => $request->input('user_email_search'),
        );

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        $draw = (int)$request->input('draw', 1);
        $length = (int)$request->input('length', 10);
        $search = $this->_searchList($request);
        $data_list = $search->paginate($length, ['*'], 'start');
        $member_card_group_id = $request->input('member_card_group_id');

        $result = array();
        foreach ($data_list as $data) {
            $site_name = $this->getValueFromLanguageArray($data->site?->name_json) ?? '—/—';
            $member_card_group_name = $this->getValueFromLanguageArray($data->memberCardGroup?->name_json) ?? '—/—';
            $result[] = array(
                'is_checked' => $data->member_card_group_id == $member_card_group_id ? true : false, // 判断是否有编号来确认选中
                'member_card_id' => $data->member_card_id, // 会员卡ID
                'site_name' => $site_name, // 站点名称
                'member_card_key' => $data->member_card_key, // 会员卡号
                'user_email' => $data->user?->email ?? '—/—', // 用户名称
                'member_card_group_name' => $member_card_group_name, // 会员组名称
                'member_name' => $data->member_name ?? '—/—', // 会员名称
                'member_email' => $data->member_email ?? '—/—', // 会员电邮
                'octopus_card_number' => $data->octopus_card_number ?? '—/—', // 会员电邮
                'rfid_card_number' => $data->rfid_card_number ?? '—/—', // 会员电邮
                'is_enable' => $data->is_enable, // 是否启用
                'remark' => $data->remark ?? '—/—', // 备注
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );
        return response()->json($json);
    }

    // 获取未绑定App用户的会员卡
    public function getUnbindUserMemberCardList(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $length = (int)$request->input('length', 10);
        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $member_card_group_id = $request->input('member_card_group_id');
        $member_card_key_search = $request->input('member_card_key_search');
        $site_search = $request->input('site_search');

        $data_list = MemberCard::with(['site', 'memberCardGroup'])
            ->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->when(filled($member_card_key_search), fn ($query) => $query->where('member_card_key', 'like', "%$member_card_key_search%"))
            ->when(filled($site_search), fn ($query) => $query->where('site_number', $site_search))
            ->when(filled($member_card_group_id), fn ($query) => $query->where(function ($query) use ($member_card_group_id) {
                $query->where('member_card_group_id', $member_card_group_id)->orWhereNull('member_card_group_id');
            }))
            ->whereNull('user_id')
            ->orderBy($order, $sort)
            ->orderBy('gmt_modified', 'desc')
            ->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $site_name = $this->getValueFromLanguageArray($data->site?->name_json) ?? '—/—';
            $member_card_group_name = $this->getValueFromLanguageArray($data->memberCardGroup?->name_json) ?? '—/—';
            $result[] = array(
                'is_checked' => $data->member_card_group_id == $member_card_group_id, // 判断是否有ID来确认选中
                'member_card_id' => $data->member_card_id, // 会员卡ID
                'site_name' => $site_name, // 站点名称
                'member_card_key' => $data->member_card_key, // 会员卡号
                'member_card_group_name' => $member_card_group_name, // 会员组名称
                'member_name' => $data->member_name ?? '—/—', // 会员名称
                'member_email' => $data->member_email ?? '—/—', // 会员电邮
                'octopus_card_number' => $data->octopus_card_number ?? '—/—', // 会员电邮
                'rfid_card_number' => $data->rfid_card_number ?? '—/—', // 会员电邮
                'is_enable' => $data->is_enable, // 是否启用
                'remark' => $data->remark ?? '—/—', // 备注
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );

        return response()->json($json);
    }

    public function edit(Request $request, int $member_card_id): View|Application|Factory|RedirectResponse|null
    {
        $data = array();

        $model = $this->model->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->findOrFail($member_card_id);

        if ($request->isMethod('post')) {
            return $this->modelValidateAndSave($request, $model);
        }

        $data['model'] = $model;

        return $this->getForm($request, $data);
    }

    protected function getForm(Request $request, array $data): View|Application|Factory
    {
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'],
            self::getUrlParams($request)
        );
        $site_number = $data['model']->site_number;
        if ((isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) && blank($data['model']?->member_card_id)) {
            // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下就直接获取页面传入的场地编号
            $site_number = $request->old('site_number', $data['model']->site_number);
        } else if (blank($data['model']?->member_card_id) && auth()->user()->site_number_list->count() === 1) {
            // 如果是新增并且管理员场地只有一个就直接赋值
            $site_number = auth()->user()->site_number_list->first();
        }
        $data['model']->site_number = $site_number;
        $site_name = $this->getValueFromLanguageArray(
            Site::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->firstWhere('site_number', $site_number)?->name_json
        );
        $data['model']->site_name = $site_name;

        // 当前存在以get窗口的方式打开form，判断是否有user_id，否则读取旧数据等等
        $user_id = $request->get('user_id', $request->old('user_id', $data['model']->user_id));
        $user_email = AppUser::find($user_id)?->email;
        $data['model']->member_card_key = $request->old('member_card_key', $data['model']->member_card_key);
        $data['model']->user_id = $user_id;
        $data['model']->user_email = $user_email;

        $member_card_group_id = $request->old('member_card_group_id', $data['model']->member_card_group_id);
        $member_card_group_name = $this->getValueFromLanguageArray(
            MemberCardGroup::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->find($member_card_group_id)?->name_json
        );
        $data['model']->member_card_group_id = $member_card_group_id;
        $data['model']->member_card_group_name = $member_card_group_name;
        $data['model']->member_name = $request->old('member_name', $data['model']->member_name);
        $data['model']->member_email = $request->old('member_email', $data['model']->member_email);
        $data['model']->octopus_card_number = $request->old('octopus_card_number', $data['model']->octopus_card_number);
        $data['model']->rfid_card_number = $request->old('rfid_card_number', $data['model']->rfid_card_number);
        $data['model']->is_enable = $request->old('is_enable', $data['model']->is_enable);
        $data['model']->remark = $request->old('remark', $data['model']->remark);

        return view("pages.{$data['module_name']}.form", $data);
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @param MemberCard $model
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    protected function modelValidateAndSave(Request $request, MemberCard $model): RedirectResponse
    {
        $module_name = self::$module_name;
        $is_add = blank($model?->member_card_id);
        $is_edit = !$is_add;
        $merchant_number = $model->merchant_number;
        if ($is_add) {
            $model = $this->model;
            $site_number = $model->site_number;
            if ((isSuperAdministrator() || auth()->user()->site_number_list->count() > 1)) {
                // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下就直接获取页面传入的场地编号
                $site_number = $request->input('site_number');
            } else if (auth()->user()->site_number_list->count() === 1) {
                // 如果是新增并且管理员场地只有一个就直接赋值
                $site_number = auth()->user()->site_number_list->first();
            }
            $model->site_number = $site_number;
            $merchant_number = Site::firstWhere('site_number', $site_number)?->merchant_number;
        }

        // 只有超级管理员才可以选择用户
        if (isSuperAdministrator()) {
            $model->user_id = $request->input('user_id');
        }

        // 验证规则
        $request->validate(self::rules($request, $model), [], self::attributes());
        // 如果是新增会员卡或者编辑修改了商户编号 (改到下面来写是为了防止merchant_number不存在导致报错，验证规则可以先排除掉不存在的merchant_number)
        if ($is_add || ($is_edit && $model?->merchant_number != $merchant_number)){
            // 获取最大会员卡数量是否填写值，判断该商户最大会员卡数量是否小于等于该商户下已存在的会员卡数量
            $maximum_member_card_count = Merchant::firstWhere('merchant_number', $merchant_number)?->maximum_member_card_count;
            if (filled($maximum_member_card_count) && $maximum_member_card_count <= MemberCard::where('merchant_number', $merchant_number)->count()) {
                return redirect()->back()->withInput()->withErrors(['maximum_member_card_count' => __("$module_name.maximum_member_card_count_error")]);
            }
        }

        $model->merchant_number = $merchant_number;
        $model->member_card_key = $request->input('member_card_key');
        $model->member_card_group_id = $request->input('member_card_group_id');
        $model->member_name = $request->input('member_name');
        $model->member_email = $request->input('member_email');
        $model->octopus_card_number = $request->input('octopus_card_number');
        $model->rfid_card_number = $request->input('rfid_card_number');
        $model->is_enable = $request->input('is_enable', 0);
        $model->remark = $request->input('remark');
        $model->save();

        return redirect()->action(
            [self::class, 'showPage'],
            self::getUrlParams($request)
        );
    }

    public function delete(Request $request): JsonResponse
    {
        $id = $request->post('id');

        if (blank($id)) {
            $this->notFoundData('ID');
            return $this->returnJson();
        }

        $model = $this->model::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->find($id);

        if (blank($model)) {
            $this->notFoundData('ID');
            return $this->returnJson();
        }

        // 删除关联数据
        $model->delete();

        return $this->returnJson();
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function rules(?Request $request, ?Model $model): array
    {
        $module_name = self::$module_name;

        $site_number = $model->site_number;
        if ((isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) && blank($model?->member_card_id)) {
            // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下就直接获取页面传入的场地编号
            $site_number = $request->input('site_number');
        } else if (blank($model?->member_card_id) && auth()->user()->site_number_list->count() === 1) {
            // 如果是新增并且管理员场地只有一个就直接赋值
            $site_number = auth()->user()->site_number_list->first();
        }

        $rules = array(
            'member_card_key' => [
                'required',
                'max:45',
                'unique:App\Models\Modules\MemberCard,member_card_key,' . $model->member_card_id . ',member_card_id',
                'regex:/^[0-9a-zA-Z]{0,45}$/',
            ],
            'member_card_group_id' => [
                'nullable',
                'exists:App\Models\Modules\MemberCardGroup,member_card_group_id',
                function ($attr, $value, $fail) use ($module_name, $site_number) {
                    // 判断会员卡组的商户id与当前商户id是否一致
                    $member_card_group = MemberCardGroup::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->find($value);
                    if ($site_number != $member_card_group?->site_number) {
                        $fail(__('common.merchant_inconsistent_with_field', ['field' => __("$module_name.merchant")]));
                    }
                },
            ],
            'member_name' => 'nullable|max:45',
            'member_email' => 'nullable|email:rfc',
            'octopus_card_number' => [
                'nullable',
                'numeric',
                'regex:/^[0-9]{0,20}$/',
                // 相同场地唯一
                Rule::unique('App\Models\Modules\MemberCard', 'octopus_card_number')
                    ->where(fn ($query) => $query->where('site_number', $site_number))
                    ->where(fn ($query) => $query->where('member_card_id', '!=', $model?->member_card_id)),
            ],
            'rfid_card_number' => 'nullable|max:255',
            'is_enable' => 'boolean',
            'remark' => 'nullable|max:1000',
        );

        // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下才需要判断场地编号是否存在
        if ((isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) && blank($model?->member_card_id)) {
            $rules['site_number'] = [
                'required',
                'exists:App\Models\Modules\Site,site_number',
                function ($attr, $value, $fail) use ($module_name, $request) {
                    // 判断选择的site是否为当前管理员权限下的
                    if (!isSuperAdministrator() && !in_array($value, auth()->user()->site_number_list->toArray())) $fail(__('common.text_not_found', ['field' => __("$module_name.site")]));
                },
            ];
        }

        // 只有超级管理员才可以添加、修改用户
        if (isSuperAdministrator()) {
            $rules['user_id'] = [
                'nullable',
                'exists:App\Models\Modules\AppUser,user_id',
                function ($attr, $value, $fail) use ($module_name, $model, $site_number) {
                    // 同一用户同一场地有且只有一张会员卡
                    if (MemberCard::where('site_number', $site_number)
                        ->where('user_id', $value)
                        ->whereNot('member_card_id', $model?->member_card_id)
                        ->exists()
                    ) {
                        $fail(__("$module_name.cannot_repeatedly_bind"));
                    }
                },
            ];
        }

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'merchant_number' => __("$module_name.merchant"),
            'member_card_key' => __("$module_name.member_card_key"),
            'site_number' => __("$module_name.site"),
            'user_id' => __("$module_name.user"),
            'member_card_group_id' => __("$module_name.member_card_group"),
            'member_name' => __("$module_name.member_name"),
            'member_email' => __("$module_name.member_email"),
            'octopus_card_number' => __("$module_name.octopus_card_number"),
            'rfid_card_number' => __("$module_name.rfid_card_number"),
            'is_enable' => __("$module_name.is_enable"),
            'remark' => __("$module_name.remark"),
        ];
    }

    /**
     * 返回地址栏参数
     *
     * @param Request|null $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => 'true',
            'site_search' => $request->get('site_search'),
            'member_card_key_search' => $request->get('member_card_key_search'),
            'octopus_card_number_search' => $request->get('octopus_card_number_search'),
            'rfid_card_number_search' => $request->get('rfid_card_number_search'),
            'user_email_search' => $request->get('user_email_search'),
        );
    }

    /**
     * 获取搜索数据
     *
     * @param Request $request
     * @return mixed
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-11-08
     */
    protected function _searchList(Request $request): mixed
    {
        // dataTable字段
        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $site_search = $request->input('site_search');
        $member_card_group_id = $request->input('member_card_group_id');
        $member_card_key_search = $request->input('member_card_key_search');
        $octopus_card_number_search = $request->input('octopus_card_number_search');
        $rfid_card_number_search = $request->input('rfid_card_number_search');
        $user_id = $request->input('user_id');
        $user_email_search = $request->input('user_email_search');

        return MemberCard::with(['site', 'memberCardGroup', 'user'])
            ->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->when(filled($site_search), fn ($query) => $query->where('site_number', $site_search))
            ->when(filled($member_card_group_id), fn ($query) => $query->where(function ($query) use ($member_card_group_id) {
                // 获取该会员卡组的场地编号
                $site_number = MemberCardGroup::find($member_card_group_id)?->site_number;
                $query->where('member_card.site_number', $site_number)
                    ->where(fn ($where) => $where->where('member_card_group_id', $member_card_group_id)->orWhereNull('member_card_group_id'));
            }))
            ->when(filled($member_card_key_search), fn ($query) => $query->where('member_card_key', 'like', "%$member_card_key_search%"))
            ->when(filled($octopus_card_number_search), fn ($query) => $query->where('octopus_card_number', 'like', "%$octopus_card_number_search%"))
            ->when(filled($rfid_card_number_search), fn ($query) => $query->where('rfid_card_number', 'like', "%$rfid_card_number_search%"))
            ->when(filled($user_id), fn ($query) => $query->where('user_id', $user_id))
            ->when(filled($user_email_search), fn ($query) => $query->whereHas('user', function ($query) use ($user_email_search) {
                $query->where('email', 'like', "%$user_email_search%");
            }))
            ->orderBy($order, $sort)
            ->orderBy('gmt_modified', 'desc');
    }

    // 更新会员卡App用户绑定关系
    public function updateBindingAppUser(Request $request): JsonResponse
    {
        $member_card_id = $request->post('member_card_id');   // 单个或ids
        $user_id = $request->post('user_id');
        $module_name = self::$module_name;

        $member_card_id_list = explode(',', $member_card_id);
        $member_card_list = MemberCard::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->when(filled($user_id), fn ($query) => $query->whereNull('user_id'), fn ($query) => $query->whereNotNull('user_id')) // 查询已绑定user或未绑定user的数据
            ->whereIn('member_card_id', $member_card_id_list);
        // 未找到会员卡
        if ($member_card_list->doesntExist()) {
            $this->notFoundData('Member Card');
            return $this->returnJson();
        }

        // 当用户存在时
        if (filled($user_id)) {
            $select_merchant_member_card_result = MemberCard::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->whereIn('member_card_id', $member_card_id_list)
                ->pluck('site_number');

            // 同一商户只能绑定一张会员卡
            if ($select_merchant_member_card_result->unique()->count() < count($member_card_id_list)) {
                $this->code = 201;
                $this->message = __("$module_name.cannot_repeatedly_bind_by_merchant");
                return $this->returnJson();
            }

            // 获取已选择会员卡的所有场地编号
            $select_merchant_member_card_result_list = $select_merchant_member_card_result->toArray();
            // 获取该用户已绑定会员卡的场地编号
            $select_merchant_member_card_bound_app_user_result = MemberCard::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->where('user_id', $user_id)
                ->pluck('site_number')
                ->toArray();
            // 查询该用户已绑定会员卡以及选择的会员卡是否存在相同的场地编号
            if (!empty(array_intersect($select_merchant_member_card_result_list, $select_merchant_member_card_bound_app_user_result))) {
                $this->code = 201;
                $this->message = __("$module_name.member_card_for_the_same_site_already_exists");
                return $this->returnJson();
            }
        }

        // 更新绑定关系
        $update_result = $member_card_list->update(['user_id' => $user_id]);
        if ($update_result === false) {
            $this->code = 201;
            $this->message = __("$module_name.update_binding_app_user_error");
            return $this->returnJson();
        }

        return $this->returnJson();
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function importExcelRules(?Request $request): array
    {
        $module_name = self::$module_name;

        $rules = array(
            'member_card_group_id' => [
                'nullable',
                'exists:App\Models\Modules\MemberCardGroup,member_card_group_id',
                function ($attr, $value, $fail) use ($module_name) {
                    $member_card_group = MemberCardGroup::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                        ->find($value);
                    // 判断会员卡组是否存在且有权限
                    if (blank($member_card_group)) {
                        $fail(__('common.text_not_found', ['field' => __("$module_name.member_card_group")]));
                    }
                },
            ],
            'member_name' => 'nullable|max:45',
            'member_email' => 'nullable|email:rfc',
            'octopus_card_number' => [
                'required', // 导入时必填
                'numeric',
                'regex:/^[0-9]{0,20}$/',
            ]
        );

        return $rules;
    }

    public function importMemberCard(Request $request): JsonResponse
    {
        // 验证规则
        $request->validate(self::importExcelRules($request), [], self::attributes());
        $module_name = self::$module_name;
        $member_card_group_id = $request->input('member_card_group_id');
        $member_name = $request->input('member_name');
        $member_email = $request->input('member_email');
        $octopus_card_number = $request->input('octopus_card_number');

        $member_card_group = MemberCardGroup::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->find($member_card_group_id);
        if (blank($member_card_group)) {
            $this->notFoundData(__("$module_name.member_card_group"));
            return $this->returnJson();
        }


        // 如果八达通卡号不足8位，则补足8位
        if (strlen($octopus_card_number) < 8) {
            $octopus_card_number = str_pad($octopus_card_number, 8, '0', STR_PAD_LEFT);
        }

        $member_card = MemberCard::where('octopus_card_number', $octopus_card_number)
            ->where('site_number', $member_card_group->site_number)
            ->first();
        if (blank($member_card)) {
            $member_card = new MemberCard();
            // 新增生成会员卡号
            $member_card_key = $this->generateMemberCardKey();
            if ($member_card_key === false) {
                $this->code = 201;
                $this->message = __("$module_name.generate_member_card_key_error");
                return $this->returnJson();
            }
            $member_card->member_card_key = $member_card_key;
            $member_card->merchant_number = $member_card_group->merchant_number;
            $member_card->site_number = $member_card_group->site_number;
        }
        $member_card->member_card_group_id = $member_card_group_id;
        $member_card->member_name = $member_name;
        $member_card->member_email = $member_email;
        $member_card->octopus_card_number = $octopus_card_number;
        $member_card->is_enable = 1;
        $this->data = $member_card->save();

        return $this->returnJson();
    }

    /**
     * 生成会员卡key
     *
     * @return string|false
     */
    protected function generateMemberCardKey(): string|false
    {
        $member_card_key = retry(5, function () {
            // 随机生成20个字符（数字+字母）
            $member_card_key = Str::random(20);
            $member_card_key_exists = MemberCard::where('member_card_key', $member_card_key)->exists();
            if ($member_card_key_exists) {
                throw new \Exception('member_card_key already exists');
            }
            return $member_card_key;
        }, 100);

        if (blank($member_card_key)) {
            return false;
        }

        return $member_card_key;
    }
}

<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use App\Models\Modules\Merchant;
use App\Models\Modules\Permission\Permission;
use App\Models\Modules\Permission\Role;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Foundation\Application;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Validation\Rule;
use App\Models\Modules\Site;

class RoleController extends CommonController
{
    protected static string $module_name = 'role'; // 模块名称
    protected Role $model;

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new Role;
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'list_url' => action([self::class, 'listAdministrator']),
            'show_page_url' => action([self::class, 'showPage']),
            'administrator_name_search' => $request->input('administrator_name_search', ''),
            'administrator_email_search' => $request->input('administrator_email_search', ''),
            'administrator_role_search' => $request->input('administrator_role_search', ''),
            'site_search' => $request->input('site_search'),
        );

        // 非超级管理员管理员不显示超级管理员组
        // 非超级管理员不显示自己的角色
        $role_result_list = Role::with([
            'site',
            'permissions'
        ])
            ->withCount('users')
            ->whereNot('role.name', 'Super Administrator')
            ->when(!isSuperAdministrator(), function ($query) {
                // 非超级管理员只显示自己有的场地的角色(角色不能拥有其他场地的权限)
                $query->whereDoesntHave('site', fn ($query) => $query->whereNotIn('site.site_number', auth()->user()->site_number_list))
                    // 筛选掉没有场地的角色
                    ->whereHas('site', fn ($query) => $query->whereIn('site.site_number', auth()->user()->site_number_list));
            })
            ->oldest()
            ->oldest('role_id')
            ->get();
        // 获取每个角色的前五个用户
        foreach ($role_result_list as $role) {
            $role->load(['users' => function ($query) {
                $query->limit(5);
            }]);
        }
        // 获取角色列表
        $role_list = array();
        foreach ($role_result_list as $role) {
            $administrator_list = $role_permission_list = array();
            foreach ($role->permissions as $permission) {
                $role_permission_list[] = array(
                    'name' => $permission->name,
                    'value' => $permission->path,
                );
            }
            // 获取对应角色下的管理员，为了显示头像组最高为5
            foreach ($role->users as $administrator) {
                $administrator_list[] = array(
                    'administrator_id' => $administrator->administrator_id,
                    'name' => $administrator->name,
                    'avatar_url' => existsImage('avatar', $administrator->avatar_url) ?: existsImage('icon', 'not_select_image.png'),
                    'email' => $administrator->email,
                    'remark' => $administrator->remark ?? '—/—',
                    'gmt_create' => $administrator->gmt_create->toDateTimeString(),
                );
            }

            $site_name = [];
            foreach ($role?->site ?? [] as $site) {
                $site_name[] = self::getValueFromLanguageArray($site->name_json);
            }
            $role_list[] = array(
                'no_role' => !auth()->user()->hasRole($role->role_id), // 判断当前用户是否有该角色如果有就进显示，不可编辑
                'role_id' => $role->role_id,
                'site_name' => implode(', ', $site_name),
                'site_number_list' => $role->site->pluck('site_number')->toArray(),
                'name' => $role->name,
                'remark' => $role->remark,
                'administrator_list_total' => $role->users_count,
                'administrator_list' => $administrator_list,
                'permission_list' => $role_permission_list,
            );
        }
        $data['role_list'] = $role_list;
        $role_has_permission = isSuperAdministrator()
            ? Permission::oldest('name')->oldest('path')->get()
            : (auth()->user()->getAllPermissions()->sortBy('name')->sortBy('path') ?: collect());
        // 获取权限列表
        $permission_list = array();
        // 忽略权限列表
        $ignore_permission_list = [
            'Permission',
        ];

        foreach ($role_has_permission as $permission) {
            if (in_array($permission->name, $ignore_permission_list)) {
                continue;
            }
            // 将path拆分
            $permission_arr = explode('/', $permission->path);
            if (blank($permission_arr)) {
                continue;
            }

            // 如果没有主权限
            if (!isset($permission_list[$permission_arr[0]]) || blank($permission_list[$permission_arr[0]])) {
                // 添加一个主权限
                $permission_list[$permission_arr[0]] = array(
                    'name' => $permission->name ?: $permission->path,
                    'value' => $permission->path,
                );
            }

            // 如果有子权限
            if (count($permission_arr) > 1 && isset($permission_arr[1]) && filled($permission_arr[1])) {
                $permission_list[$permission_arr[0]]['children'][] = array(
                    'name' => $permission_arr[1] ?: $permission->path,
                    'value' => $permission->path,
                );
            } else {
                // 默认生成一个list权限存入子权限
                $permission_list[$permission_arr[0]]['children'][] = array(
                    'name' => 'list',
                    'value' => $permission->path,
                );
            }
        }
        $data['permission_list'] = $permission_list;

        // 场地下拉列表
        $data['site_list'] = self::getSiteListGroupByMerchant();

        return view("pages.{$data['module_name']}.list", $data);
    }

    /**
     * 根据id获取role
     */
    public function getRole(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        $role_id = $request->input('role_id');

        if (!empty($role_id)) {
            $role = Role::with(['permissions', 'site'])
                ->find($role_id);
            if (filled($role)) {
                $site_number_list = $role->site->pluck('site_number')->toArray();
                if (!isSuperAdministrator() && filled(array_diff($site_number_list, auth()->user()->site_number_list->toArray()))) {
                    // 无法获取site_number_list中有不属于自己的场地
                    $this->code = 201;
                    $this->message = __("$module_name.error_no_site_permission");
                }
                $role_permission_list = array();
                if (filled($role->permissions)) {
                    foreach ($role->permissions as $permission) {
                        $role_permission_list[] = array(
                            'name' => $permission->name,
                            'value' => $permission->path,
                        );
                    }
                }
                $this->data = array(
                    'role_id' => $role->role_id,
                    'name' => $role->name,
                    'site_number_list' => $role->site->pluck('site_number')->toArray(),
                    'remark' => $role->remark,
                    'permission_list' => $role_permission_list,
                );
            } else {
                $this->code = 201;
                $this->message = __("$module_name.error_get_role");
            }
        } else {
            $this->code = 404;
            $this->message = __('common.text_not_found', ['field' => 'ID']);
        }

        return $this->returnJson();
    }

    /**
     * 管理员列表
     */
    public function listAdministrator(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'name');
        $sort = $request->input('sort', 'asc');
        $length = (int)$request->input('length', 10);
        $site_search = $request->input('site_search');
        $name_search = $request->input('administrator_name_search', '');
        $email_search = $request->input('administrator_email_search', '');
        $role_search = $request->input('administrator_role_search', '');

        $data_list = User::with(['roles.site'])
            ->when(filled($name_search), fn ($query) => $query->where('name', 'like', '%' . $name_search . '%'))
            ->when(filled($email_search), fn ($query) => $query->where('email', 'like', '%' . $email_search . '%'))
            ->when(filled($role_search), function ($query) use ($role_search) {
                $query->whereHas('roles', fn ($query) => $query->where('role.role_id', $role_search));
            })
            ->when(filled($site_search), function ($query) use ($site_search) {
                $query->whereHas('roles.site', fn ($query) => $query->where('site.site_number', $site_search));
            })
            ->when(!isSuperAdministrator(), function ($query) {
                $query->whereDoesntHave('roles.site', fn ($query) => $query->whereNotIn('site.site_number', auth()->user()->site_number_list))
                    ->whereHas('roles.site', fn ($query) => $query->whereNotNull('site.site_number'))
                    ->whereHas('roles', fn ($query) => $query->whereNot('role.name', 'Super Administrator'));
            })
            ->orderBy($order, $sort)
            ->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $site_name = [];
            foreach ($data->roles as $role) {
                foreach ($role->site as $site) {
                    $site_name[] = self::getValueFromLanguageArray($site->name_json);
                }
            }
            $site_name = filled($site_name) ? implode('<br>', $site_name) : '—/—';
            $result[] = array(
                'administrator_id' => $data->administrator_id,
                'site_name' => $site_name,
                'name' => $data->name,
                'avatar_url' => existsImage('avatar', $data->avatar_url) ?: existsImage('icon', 'not_select_image.png'),
                'email' => $data->email,
                'remark' => $data->remark ?? '—/—',
                'role' => $data->roles,
                'gmt_create' => $data->gmt_create->toDateTimeString(),
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );

        return response()->json($json);
    }

    public function delete(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        $role_id = $request->input('role_id');

        $role_model = Role::find($role_id);
        $site_number_list = $role_model->site->pluck('site_number')->toArray();
        if (!empty($role_id) && filled($role_model)) {
            if ($role_model->role_id === 1) {
                // 超级管理员不能被删
                $this->code = 201;
                $this->message = __("$module_name.error_role_del");
            } elseif (auth()->user()->hasRole($role_model->role_id)) {
                // 不能删除自己
                $this->code = 201;
                $this->message = __("$module_name.error_role_del_self");
            } elseif (!isSuperAdministrator() && filled(array_diff($site_number_list, auth()->user()->site_number_list->toArray()))) {
                // 无法删除site_number_list中有不属于自己的场地
                $this->code = 201;
                $this->message = __("$module_name.error_no_site_permission");
            } elseif ($role_model->users->count() > 0) {
                // role已绑定管理员
                $this->code = 202;
                $this->message = __("$module_name.role_is_bound");
            } else {
                // 刪除中间表关联
                $role_model->site()->detach();
                $role_model->delete();

                $this->clearPermissionCache();
            }
        } else {
            $this->notFoundData('Role');
        }

        return $this->returnJson();
    }

    public function add(Request $request): JsonResponse
    {
        $request->validate(self::rules($request), [], self::attributes());
        $site_number_list = $request->input('site_number_list');
        $name = $request->input('name');
        $remark = $request->input('remark');
        $permissions = $request->input('permissions', array());

        $this->clearPermissionCache();

        $model = new Role;
        $model->name = $name;
        $model->remark = $remark;
        $model->save();
        // 多对多关联site同步关联
        $model->site()->sync($site_number_list);

        // 设置权限
        $model->syncPermissions($permissions);

        $role_permission_list = array();
        if (!empty($model->permissions->toArray())) {
            foreach ($model->permissions as $permission) {
                $role_permission_list[] = array(
                    'name' => $permission->name,
                    'value' => $permission->path,
                );
            }
        }
        $this->data = array(
            'role_id' => $model->role_id,
            'site_number_list' => $model->site->pluck('site_number')->toArray(),
            'name' => $model->name,
            'remark' => $model->remark,
            'administrator_list' => $model->users()->get(),
            'permission_list' => $role_permission_list,
        );

        return $this->returnJson();
    }

    public function edit(Request $request): JsonResponse
    {
        // role_id需要转为int类型才可以匹配当前用户是否有该角色
        if ($request->input('role_id') == 1 || auth()->user()->hasRole(intval($request->input('role_id')))) {
            abort(403, __('common.no_permission'));
        }
        $request->validate(self::rules($request), [], self::attributes());
        $role_id = $request->input('role_id');
        $site_number_list = $request->input('site_number_list');
        $name = $request->input('name');
        $remark = $request->input('remark');
        $permissions = $request->input('permissions', array());

        $this->clearPermissionCache();

        $model = Role::find($role_id);
        if (!empty($role_id) && filled($model)) {
            $model->name = $name;
            $model->remark = $remark;
            $model->save();
            // 多对多关联site同步关联
            $model->site()->sync($site_number_list);

            // 设置权限
            $model->syncPermissions($permissions);

            $role_permission_list = array();
            if (filled($model->permissions)) {
                foreach ($model->permissions as $permission) {
                    $role_permission_list[] = array(
                        'name' => $permission->name,
                        'value' => $permission->path,
                    );
                }
            }
            $this->data = array(
                'role_id' => $model->role_id,
                'site_number_list' => $model->site->pluck('site_number')->toArray(),
                'name' => $model->name,
                'remark' => $model->remark,
                'administrator_list' => $model->users()->get(),
                'permission_list' => $role_permission_list,
            );
        } else {
            $this->notFoundData('Role');
        }

        return $this->returnJson();
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param $role
     * @return array
     */
    protected function rules(?Request $role): array
    {
        $module_name = self::$module_name;
        $rules = array(
            'name' => [
                'required',
                Rule::unique('role', 'name')->ignore($role->role_id, 'role_id')
            ],
            'site_number_list' => [
                'required',
                'array',
                // 验证传入的site_number都属于自己的site权限内
                function ($attribute, $value, $fail) use ($module_name) {
                    if (isSuperAdministrator()) {
                        return;
                    }
                    $diff = array_diff($value, auth()->user()->site_number_list->toArray());
                    if (filled($diff)) {
                        $fail(__("$module_name.error_no_site_permission"));
                    }
                }
            ],
        );

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'site_number_list' => __("$module_name.site"),
            'name' => __("$module_name.name"),
        ];
    }

    public static function getUrlParams(Request $request): array
    {
        return array(
            'is_page' => 'true',
            'administrator_name_search' => $request->get('administrator_name_search', ''),
            'administrator_email_search' => $request->get('administrator_email_search', ''),
            'administrator_role_search' => $request->get('administrator_role_search', ''),
            'site_search' => $request->get('site_search'),
        );
    }
}

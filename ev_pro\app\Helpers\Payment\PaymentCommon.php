<?php

namespace App\Helpers\Payment;

use Exception;
use DB;
use App\Models\Modules\{
    ChargeRecord,
    AppUser,
    PointsTransaction,
    UserNotify,
};
use App\Enums\{
    UserNotifyCategory,
};

class PaymentCommon
{
    public static function beforePayment(ChargeRecord $charge_record, AppUser $user, array $parameter): array
    {
        $check_result = self::checkChargeRecordBillParameter($charge_record, $user, $parameter); // 验证结果
        $return = [
            'result' => $check_result['result'],
            'total_amount' => $check_result['total_amount'],
            'selected_points' => $check_result['selected_points'],
            'message' => $check_result['message'],
        ];

        // 校验失败
        if (!$check_result['result']) {
            return $return;
        }
        #TODO 暂时将使用的积分存至charge record的use_points字段，后续会新加字段存
        $charge_record->use_points = $check_result['selected_points'];
        $charge_record->save();

        return $return;
    }

    public static function checkoutErrorPage($error)
    {
        echo $error;
    }

    public static function afterPaymentReturnPage()
    {
        echo 'success';
    }

    /**
     * 验证传入的支付参数
     *
     * @param ChargeRecord $charge_record
     * @param AppUser $user
     * @param array $parameter
     * @return array
     * @Description
     * @example
     * @date 2023-11-28
     */
    public static function checkChargeRecordBillParameter(ChargeRecord $charge_record, AppUser $user, array $parameter): array
    {
        $return = [
            'result' => true,
            'total_amount' => null,
            'selected_points' => null,
            'message' => null,
        ];
        $selected_points_post = $parameter['selected_points_post'] ?? null; // 传入的积分
        $total_amount_post = $parameter['total_amount_post'] ?? null; // 传入的总金额
        $selected_payment_method = $parameter['selected_payment_method'] ?? null; // 传入的支付方式

        // 总金额
        $total_amount = null;
        foreach ($charge_record->chargePaymentRecord as $charge_payment_record) {
            $total_amount += $charge_payment_record->total_amount;
        }

        // 积分
        // 传入了参数才需要抵扣积分
        if (filled($selected_points_post)) {
            $selected_points_post = (int)$selected_points_post;
            $points_balance = $user->points_balance;
            // 默认抵扣所有积分
            $selected_points = $points_balance;
            // 如果积分比总金额大，那么只抵扣总金额对应的积分
            if ($points_balance > $total_amount) {
                $selected_points = $total_amount;
            }
            // 抵扣积分和传入的积分不一致，验证失败
            if ($selected_points_post !== $selected_points) {
                $return['result'] = false;
                $return['message'] = 'selected_points';
                return $return;
            }
            $return['selected_points'] = $selected_points;
        }

        // 总金额 = 总金额 - 积分抵扣
        $total_amount -= $selected_points_post;
        // 总金额和传入的总金额不一致，验证失败
        if ($total_amount !== $total_amount_post) {
            $return['result'] = false;
            $return['message'] = 'total_amount';
            return $return;
        }

        // 总金额，防止负数
        $return['total_amount'] = max($total_amount, 0);

        // 支付方式
        // 如果未传入抵扣积分或者总金额大于0，且未传入支付方式，验证失败
        if (blank($selected_points_post) || $total_amount > 0) {
            if (blank($selected_payment_method)) {
                $return['result'] = false;
                $return['message'] = 'payment_method';
                return $return;
            }
        }

        return $return;
    }

    /**
     * 添加用户通知
     *
     * @param integer $use_points
     * @param integer $actual_payment_amount
     * @param AppUser $user
     * @return void
     * @Description
     * @example
     * @date 2023-11-30
     */
    public static function addUserNotifyAfterPaySuccess(int $use_points, int $actual_payment_amount, AppUser $user): void
    {
        try {
            DB::transaction(function () use ($use_points, $actual_payment_amount, $user) {
                $user_notify = new UserNotify();
                $user_notify->user_id = $user->user_id;
                $user_notify->category = UserNotifyCategory::Points;
                $user_notify->save();

                $user_notify_description = [];
                foreach (config('languages') as $language_code => $language) {
                    $user_notify_description[] = [
                        'language_code' => $language_code,
                        'title' => __('userNotify.template_payment_transaction_title', [], $language_code),
                        'content' => __('userNotify.template_payment_transaction_content', [
                            'use_points' => (float)bcdiv($use_points, 100, 1),
                            'points_balance' => (float)bcdiv($user->points_balance, 100, 1),
                            // 格式化金额
                            'actual_payment_amount' => __('common.unit_hk', [], $language_code) . (float)bcdiv($actual_payment_amount, 100, 1),
                        ], $language_code),
                    ];
                }
                $user_notify->description()->createMany($user_notify_description);
            }, 3);
        } catch (Exception $e) {
            report($e);
        }
    }

    /**
     * 生成积分交易编号
     *
     * @return string
     * @Description
     * @example
     * @date 2024-01-09
     */
    public static function generatePointsTransactionNumber(): string
    {
        // 生成规则：年月日时分秒+6位随机数+毫秒时间戳后4位
        return retry(5, function () {
            $now = date('YmdHis');
            $random_number = mt_rand(100000, 999999);
            $micro_time = substr(microtime(true), -4);
            $points_transaction_number = $now . $random_number . $micro_time;
            if (PointsTransaction::where('transaction_number', $points_transaction_number)->exists()) {
                throw new Exception('Transaction Number Exists');
            }
            return $points_transaction_number;
        }, 100);
    }
}

<?php

namespace App\Enums;

use BenSampo\Enum\{
    Enum,
    Contracts\LocalizedEnum
};
use App\Enums\Traits\Tools;

/**
 * 負載均衡算法枚舉
 */
final class LoadBalanceAlgorithmEnum extends Enum implements LocalizedEnum
{
    use Tools;

    /**
     * 平均分配
     */
    const EVEN_DISTRIBUTION = 'EVEN_DISTRIBUTION';

    /**
     * 先到先得
     */
    const FIRST_COME_FIRST_SERVED = 'FIRST_COME_FIRST_SERVED';
}

<?php

namespace App\Http\Controllers\Admin;

use Exception;
use App\Http\Controllers\Common\CommonController;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\{
    JsonResponse,
    RedirectResponse,
    Request,
};
use App\Http\Controllers\Admin\Traits\{
    Add,
    Edit,
};
use Illuminate\Foundation\Application;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use App\Models\Modules\{
    ComServer,
};

class ComServerController extends CommonController
{
    use Add, Edit;

    protected static string $module_name = 'comServer'; // 模块名称

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new ComServer;
    }

    /**
     * 初始页
     *
     * @param Request $request
     * @return View|Application|Factory
     */
    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'add_url' => action([self::class, 'add']),
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'send_message_url' => action([self::class, 'sendMessage']),
        );

        return view("pages.{$data['module_name']}.list", $data);
    }

    /**
     * 列表接口
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $length = (int)$request->input('length', 10);

        $data_list = ComServer::orderBy($order, $sort)
            ->latest('gmt_modified')
            ->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $result[] = array(
                'com_server_id' => $data->com_server_id, // 中央服务器ID
                'com_server_number' => $data->com_server_number, // 中央服务器编码
                'name' => $data->name, // 名称
                'websocket_url' => $data->websocket_url, // WebSocket 地址
                'api_url' => $data->api_url, // API 地址
                'gmt_last_boot' => $data->gmt_last_boot ?? '—/—', // 最后启动时间
                'sort_order' => $data->sort_order, // 排序
                'remark' => $data->remark ?? '—/—', // 备注
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result
        );

        return response()->json($json);
    }

    /**
     * 表单页
     *
     * @param Request $request
     * @param array $data
     * @return View|Application|Factory
     */
    protected function getForm(Request $request, array $data): View|Application|Factory
    {
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'], self::getUrlParams($request)
        );

        // 新增时才回显中央伺服器编号
        if (blank($data['model']->com_server_id)) {
            $data['model']->com_server_number = $request->old('com_server_number', $data['model']->com_server_number); // 中央伺服器编号
        }
        $data['model']->name = $request->old('name', $data['model']->name); // 名称
        $data['model']->mac_address = $request->old('mac_address'); // MAC地址
        $data['model']->websocket_url = $request->old('websocket_url', $data['model']->websocket_url); // Websocket 地址
        $data['model']->api_url = $request->old('api_url', $data['model']->api_url); // API 地址
        $data['model']->sort_order = $request->old('sort_order', $data['model']->sort_order); // 排序
        $data['model']->remark = $request->old('remark', $data['model']->remark); // 备注

        return view("pages.{$data['module_name']}.form", $data);
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @param ComServer $model
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    protected function modelValidateAndSave(Request $request, ComServer $model): RedirectResponse
    {
        $request->validate(self::rules($request, $model), [], self::attributes());
        // 新增时才保存中央伺服器编号
        if (blank($model->com_server_id)) {
            $model->com_server_number = $request->input('com_server_number');
        }
        $model->name = $request->input('name');
        $model->websocket_url = $request->input('websocket_url');
        $model->api_url = $request->input('api_url');
        $model->sort_order = $request->input('sort_order', 0);
        $model->remark = $request->input('remark');

        // MAC地址加密生成license_code
        $mac_address = $request->input('mac_address');
        if (filled($mac_address) && config('app.is_enable_automatic_encryption_of_license_code')) {
            $model->license_code = self::licenseCodeEncryption($mac_address);
        }

        $model->save();

        self::sendInitPushByKioskNumberList();

        return redirect()->action(
            [self::class, 'showPage'], self::getUrlParams($request)
        );
    }

    /**
     * 删除
     *
     * @param Request $request
     * @return JsonResponse
     * @Description 删除对应数据
     * @example
     * <AUTHOR>
     * @date 2024-03-28
     */
    public function delete(Request $request): JsonResponse
    {
        $com_server_number = $request->post('com_server_number');
        $module_name = self::$module_name;

        if (filled($com_server_number) &&
            filled($model = $this->model::with('chargePoint')->firstWhere('com_server_number', $com_server_number))
        ) {
            if ($model->chargePoint->count() == 0) {
                $model->delete();

                self::sendInitPushByKioskNumberList();
            } else {
                $this->code = 40001;
                $this->message = __('common.error_has_binding_unable_to_modify_some_one', ['field' => __("$module_name.web_title")]);
            }
        } else {
            $this->notFoundData(__("$module_name.web_title"));
        }

        return $this->returnJson();
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function rules(?Request $request, ?Model $model): array
    {
        $module_name = self::$module_name;

        $rules = array(
            'name' => 'required|max:45',
            'mac_address' => ['nullable', function ($attr, $value, $fail) use ($module_name, $model) {
                if (filled($value)) {
                    $com_server = ComServer::where('license_code', self::licenseCodeEncryption($value))->first();
                    if (filled($com_server) && $com_server->com_server_id != $model->com_server_id) {
                        $fail(__("$module_name.error_mac_address_exists"));
                    }
                }
            }],
            'websocket_url' => 'required|max:1000',
            'api_url' => 'required|max:1000',
            'remark' => 'nullable|max:1000',
            'sort_order' => 'integer|min:0|max:999999',
        );

        // 只有新增时才校验中央伺服器编号
        if (blank($model->com_server_id)) {
            $rules['com_server_number'] = 'required|unique:App\Models\Modules\ComServer,com_server_number';
        }

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'name' => __("$module_name.name"),
            'mac_address' => __("$module_name.mac_address"),
            'com_server_number' => __("$module_name.com_server_number"),
            'websocket_url' => __("$module_name.websocket_url"),
            'api_url' => __("$module_name.api_url"),
            'sort_order' => __("$module_name.sort_order"),
            'remark' => __("$module_name.remark"),
        ];
    }

    /**
     * 返回地址栏参数
     *
     * @param Request|null $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => '',
        );
    }

    public function sendMessage(Request $request): JsonResponse
    {
        $com_server_number = $request->com_server_number;
        $charge_point_serial_number = $request->charge_point_serial_number; // 充电机序列号
        $message = $request->message;
        $module_name = self::$module_name;

        if (blank($com_server_number) ||
            blank($com_server = ComServer::firstWhere('com_server_number', $com_server_number))
        ) {
            $this->missingField(__("$module_name.web_title"));
            return $this->returnJson();
        }

        $api_url = $com_server->api_url; // 获取充电桩的API地址

        $params = array(
            'charge_point_serial_number' => $charge_point_serial_number,
            'message' => $message,
        );

        if (filled($api_url)) {
            try {
                $this->data = $this->curlPost("$api_url/charge/callSimulator", $params);
            } catch (Exception $e) {
                $this->code = 201;
                $this->message = $e->getMessage();
            }
        }

        return $this->returnJson();
    }

}

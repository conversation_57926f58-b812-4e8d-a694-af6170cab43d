<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Http\{
    JsonResponse,
    Request,
};
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\DB;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use App\Models\Modules\{
    EventLog,
    Merchant,
    Site,
};
use App\Enums\{
    EventTypeEnum,
};


class EventLogController extends CommonController
{

    protected static string $module_name = 'eventLog'; // 模块名称
    protected EventLog $model;

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new EventLog;
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $default_time_end = date('Y-m-d 23:59:59');
        $default_time_start = date('Y-m-d 00:00:00', strtotime('-7 days'));
        $data = array(
            'module_name' => self::$module_name,
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'site_number_search' => $request->input('site_number_search'),
            'target_number_search' => $request->input('target_number_search'),
            'event_type_search' => $request->input('event_type_search'),
            'time_frame_search' => $request->input('time_frame_search', $default_time_start . ' - ' . $default_time_end), // 默认最近7天
            'buttonShow' => EventLog::exists(),
        );

        $data['event_type_enum_list'] = array();
        foreach (EventTypeEnum::asSelectArray() as $value => $name) {
            $data['event_type_enum_list'][] = array(
                'name' => $name,
                'value' => $value,
            );
        }

        $data['site_list'] = $this->getSiteOptionList();
        $data['merchant_list'] = $this->getMerchantOptionList();
        $data['set_merchant_and_site_url'] = action([self::class, 'setAllEventLogMerchantAndSite']);

        return view("pages.{$data['module_name']}.list", $data);
    }

    /**
     * 列表接口
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        $draw = (int)$request->input('draw', 1);
        $length = (int)$request->input('length', 10);
        $search = $this->_searchList($request);
        $data_list = $search->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $merchant_name = $this->getValueFromLanguageArray($data->merchant_name) ?? '—/—';
            $site_name = $this->getValueFromLanguageArray($data->site_name) ?? '—/—';
            $result[] = array(
                'event_log_id' => $data->event_log_id, // 事件记录ID
                'merchant_name' => $merchant_name, // 商户名称
                'site_name' => $site_name, // 场地名称
                'target' => $data->target, // 目标
                'target_number' => $data->target_number, // 目标编号
                'target_name' => $data->target_name, // 目标名称
                'event_type' => EventTypeEnum::getDescription($data->event_type), // 事件类型
                'event_code' => $data->event_code ?? '—/—', // 事件编码
                'description' => $data->description ?? '—/—', // 描述
                'exception' => $data->exception ?? '—/—', // 异常
                'charge_record_number' => $data->charge_record_number ?? '—/—', // 充电记录编号
                'remark' => $data->remark ?? '—/—', // 备注
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result
        );
        return response()->json($json);
    }

    /**
     * 清除所有数据
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function deleteAllData(Request $request): JsonResponse
    {
        if (isSuperAdministrator()) {
            // 超级管理员直接重置表
            EventLog::truncate();
        } else {
            // 如果不是超级管理员，只删除自己的数据
            EventLog::whereIn('site_number', auth()->user()->site_number_list)->delete();
        }

        return $this->returnJson();
    }

    /**
     * 返回地址栏参数
     *
     * @param Request $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(Request $request): array
    {
        return array(
            'is_page' => 'true',
            'site_number_search' => $request->get('site_number_search'),
            'target_number_search' => $request->get('target_number_search'),
            'event_type_search' => $request->get('event_type_search'),
            'time_frame_search' => $request->get('time_frame_search'),
        );
    }

    /**
     * 获取搜索数据
     *
     * @param Request $request
     * @return mixed
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-11-08
     */
    protected function _searchList(Request $request): mixed
    {
        // dataTable字段
        $order = $request->input('order', 'gmt_modified');
        $sort = $request->input('sort', 'desc');
        $site_number_search = $request->input('site_number_search');
        $target_number_search = $request->input('target_number_search');
        $event_type_search = $request->input('event_type_search');
        $time_frame_search = $request->input('time_frame_search');

        $where = array();

        if (filled($site_number_search)) {
            $where[] = ['site_number', 'like', "%$site_number_search%"];
        }

        if (filled($target_number_search)) {
            $where[] = ['target_number', 'like', "%$target_number_search%"];
        }
        if (filled($event_type_search)) {
            $where[] = ['event_type', 'like', "%$event_type_search%"];
        }
        if (filled($time_frame_search)) {
            $time_frame_search_array = explode(' - ', $time_frame_search);
            // 判断是否是时间类型且为开始时间结束时间类型
            if (count($time_frame_search_array) == 2 && strtotime($time_frame_search_array[0]) && strtotime($time_frame_search_array[1]) && (strtotime($time_frame_search_array[0]) <= strtotime($time_frame_search_array[1]))) {
                $where[] = ['gmt_modified', '>=', "$time_frame_search_array[0]"];
                $where[] = ['gmt_modified', '<=', "$time_frame_search_array[1]"];
            }
        }

        return EventLog::where($where)
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->orderBy($order, $sort)
            ->latest();
    }

    public function excelExport(Request $request): void
    {
        ini_set('memory_limit', '-1');
        $site_number_search = $request->input('site_number_search');
        $target_number_search = $request->input('target_number_search');
        $event_type_search = $request->input('event_type_search');
        $time_frame_search = $request->input('time_frame_search');
        // 获取数据
        $search = $this->_searchList($request);
        // 如果没有搜索条件，默认获取最近1000条
        if (blank($site_number_search) && blank($target_number_search) && blank($event_type_search) && blank($time_frame_search)) {
            $data_list = $search->limit(1000)->get();
        } else {
            $data_list = $search->get();
        }
        $module_name = self::$module_name;

        $result = array();
        foreach ($data_list as $data) {
            $merchant_name = $this->getValueFromLanguageArray($data->merchant_name) ?? '—/—';
            $site_name = $this->getValueFromLanguageArray($data->site_name) ?? '—/—';
            $result[] = array(
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 创建时间
                'merchant_name' => $merchant_name, // 商户名称
                'site_name' => $site_name, // 场地名称
                'target' => $data->target, // 目标
                'target_number' => $data->target_number, // 目标编号
                'target_name' => $data->target_name, // 目标名称
                'event_type' => EventTypeEnum::getDescription($data->event_type), // 事件类型
                'remark' => $data->remark ?? '—/—', // 备注
                'description' => $data->description ?? '—/—', // 描述
                'exception' => $data->exception ?? '—/—', // 异常
                'charge_record_number' => $data->charge_record_number ?? '—/—', // 充电记录编号
                'event_code' => $data->event_code ?? '—/—', // 事件编码
            );
        }

        // 先把语言赋值才可以在后面array出来表头，不然会输出未编译前的字符串
        $gmt_modified = __("$module_name.gmt_modified");
        $merchant_name = __("$module_name.merchant_name");
        $site_name = __("$module_name.site_name");
        $target = __("$module_name.target");
        $target_number = __("$module_name.target_number");
        $target_name = __("$module_name.target_name");
        $event_type = __("$module_name.event_type");
        $remark = __("$module_name.remark");
        $description = __("$module_name.description");
        $exception = __("$module_name.exception");
        $charge_record_number = __("$module_name.charge_record_number");
        $event_code = __("$module_name.event_code");
        $web_title = __("$module_name.web_title");

        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet(); //实例化 Spreadsheet 对象
        $worksheet = $spreadsheet->getActiveSheet();

        // 工作簿名称为 "Event Log"
        $worksheet->setTitle($web_title);

        $styleArray = [
            'font' => [
                'bold' => false
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
        ];

        // 计算数组长度用于动态补充表格样式
        $len = count($result);
        // 因为前面两格表头样式占了一行，所以要加1用于设置单元格样式
        $total_rows = $len + 1;
        $styleArrayBody = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['argb' => '666666'],
                ],
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
        ];

        // 指定表头字段
        $excel_data = [
            [
                $gmt_modified,
                $merchant_name,
                $site_name,
                $target,
                $target_number,
                $target_name,
                $event_type,
                $remark,
                $description,
                $exception,
                $charge_record_number,
                $event_code,
            ],
        ];

        // 设置单元格样式
        // 将数组长度转换为 Excel 列名
        $last_column = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex(count($excel_data[0]));
        $worksheet->getStyle("A1:{$last_column}$total_rows")->applyFromArray($styleArray)->getFont()->setName('Times New Roman')->setSize(11);
        $worksheet->getStyle("A1:{$last_column}1")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('c0c0c0');
        $worksheet->getStyle("A1:{$last_column}$total_rows")->applyFromArray($styleArrayBody);

        foreach ($result as $event_log) {
            $excel_row = array(
                $gmt_modified => $event_log['gmt_modified'],
                $merchant_name => $event_log['merchant_name'],
                $site_name => $event_log['site_name'],
                $target => $event_log['target'],
                $target_number => $event_log['target_number'],
                $target_name => $event_log['target_name'],
                $event_type => $event_log['event_type'],
                $remark => $event_log['remark'],
                $description => $event_log['description'],
                $exception => $event_log['exception'],
                $charge_record_number => $event_log['charge_record_number'],
                $event_code => $event_log['event_code'],
            );

            $excel_data[] = $excel_row;
        }

        $worksheet->fromArray($excel_data);
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $file_name = $web_title . ' ' . date('Y-m-d H:i:s') . '.xlsx';
        //设置单元格的宽度
        // 固定宽度列
        $fixed_width_column_list = [
            'H' => 20, // remark
            'I' => 30, // description
            'J' => 30, // exception
        ];
        foreach ($spreadsheet->getActiveSheet()->getColumnIterator() as $column) {
            $column_index = $column->getColumnIndex();
            // remark和description列宽度设置为20
            if (isset($fixed_width_column_list[$column_index])) {
                $spreadsheet->getActiveSheet()->getColumnDimension($column_index)->setWidth($fixed_width_column_list[$column_index]);
                continue;
            }

            $spreadsheet->getActiveSheet()->getColumnDimension($column_index)->setAutoSize(true);
        }

        // 客户端文件下载
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $file_name . '"');
        header('Cache-Control: max-age=0');
        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
        exit;
    }

    // 设置所有的事件日志的商户和场地
    public function setAllEventLogMerchantAndSite(Request $request): JsonResponse
    {
        $site_number = $request->input('site_number');
        $merchant_number = $request->input('merchant_number');
        // 如果不是超级管理员，不执行
        if (!isSuperAdministrator()) {
            $this->code = 403;
            $this->message = 'No permission';
            return $this->returnJson();
        }

        // 查询merchant和site表的数据，获取merchant_number和name_json
        $merchant = Merchant::where('merchant_number', $merchant_number)->first();
        $site = Site::where('site_number', $site_number)->first();

        if (blank($merchant) || blank($site)) {
            $this->code = 500;
            $this->message = 'Merchant or Site not found';
            return $this->returnJson();
        }


        // 锁表，然后更新EventLog的merchant_number、merchant_name和site_number、site_name
        DB::beginTransaction();
        try {
            EventLog::withoutTimestamps(function () use ($merchant, $site) {
                EventLog::lockForUpdate()->update(['merchant_number' => $merchant->merchant_number, 'merchant_name' => $merchant->name_json, 'site_number' => $site->site_number, 'site_name' => $site->name_json]);
            });
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->code = 500;
            $this->message = 'Failed to update EventLog';
            return $this->returnJson();
        }

        return $this->returnJson();
    }
}


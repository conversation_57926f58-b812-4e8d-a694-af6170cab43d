<?php

namespace App\Enums;

use BenSampo\Enum\Contracts\LocalizedEnum;
use BenSampo\Enum\Enum;

final class ErpPaymentAccountEnum extends Enum implements LocalizedEnum
{
    const VISA = 365;
    const VISA_OVERSEAS = 1280;
    const MASTER = 364;
    const MASTER_OVERSEAS = 1279;
//    const AE = 3;
//
//    const UNIONPAY = 4;

    const ALIPAY_HK = 367;

    const ALIPAY_CHINA = 368;

    const WECHAT_HK = 369;

    const WECHAT_CHINA = 370;

    // const APPLE_PAY = 1207;

    // const GOOGLE_PAY = 1206;

    const OCTOPUS = 361;

    const ELV_CHARGING = 1101;

    const DISCOUNT = 1132;

//    const FPS = 12;// 转数快


    public static function getValue(string $key): int
    {
        return match ($key) {
            'VISA' => self::VISA,
            'VISA_OVERSEAS' => self::VISA_OVERSEAS,
            'MASTER', 'Mastercard' => self::MASTER,
            'MASTER_OVERSEAS' => self::MASTER_OVERSEAS,
//            'AE' => self::AE,
//            'UNIONPAY' => self::UNIONPAY,
            'ALIPAY', 'Alipay', 'ALIPAY_HK' => self::ALIPAY_HK,
            'ALIPAY_CHINA' => self::ALIPAY_CHINA,
            'WECHAT', 'WechatPay', 'Wechatpay', 'WECHAT_HK' => self::WECHAT_HK,
            'WECHAT_CHINA' => self::WECHAT_CHINA,
            // 'APPLE_PAY' => self::APPLE_PAY,
            // 'GOOGLE_PAY' => self::GOOGLE_PAY,
            'OCTOPUS', 'Octopus' => self::OCTOPUS,
//            'FPS' => self::FPS,
            default => 0, // 如果找不到匹配的字符串，返回0
        };
    }
}

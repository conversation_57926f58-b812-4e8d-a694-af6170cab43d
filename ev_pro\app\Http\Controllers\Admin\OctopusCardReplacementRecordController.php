<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Foundation\Application;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Models\Modules\{
    OctopusCardReplacementRecord,
};

class OctopusCardReplacementRecordController extends CommonController
{
    protected static string $module_name = 'octopusCardReplacementRecord'; // 模块名称
    protected OctopusCardReplacementRecord $model;

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new OctopusCardReplacementRecord;
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'kiosk_name_search' => $request->get('kiosk_name_search'),
            'octopus_device_number_search' => $request->get('octopus_device_number_search'),
            'old_card_number_search' => $request->get('old_card_number_search'),
            'new_card_number_search' => $request->get('new_card_number_search'),
        );

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'kiosk_number');
        $sort = $request->input('sort', 'asc');
        $length = (int)$request->input('length', 10);
        $kiosk_name_search = $request->input('kiosk_name_search');
        $octopus_device_number_search = $request->input('octopus_device_number_search');
        $old_card_number_search = $request->input('old_card_number_search');
        $new_card_number_search = $request->input('new_card_number_search');

        $where = array();

        if (filled($kiosk_name_search)) {
            $where[] = ['octopus_card_replacement_record.kiosk_name', 'like', "%$kiosk_name_search%"];
        }
        if (filled($octopus_device_number_search)) {
            $where[] = ['octopus_card_replacement_record.octopus_device_number', 'like', "%$octopus_device_number_search%"];
        }
        if (filled($old_card_number_search)) {
            $where[] = ['old_card_number', 'like', "%$old_card_number_search%"];
        }
        if (filled($new_card_number_search)) {
            $where[] = ['new_card_number', 'like', "%$new_card_number_search%"];
        }

        $data_list = OctopusCardReplacementRecord::when(!isSuperAdministrator(), function ($query) {
            return $query->whereHas('kiosk', fn($query_item) => $query_item->whereIn('kiosk.site_number', auth()->user()->site_number_list));
        })
            ->where($where)
            ->orderBy($order, $sort)
            ->latest('gmt_create')
            ->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $result[] = array(
                'octopus_card_replacement_record_id' => $data->octopus_card_replacement_record_id, // 八达通换卡记录ID
                'kiosk_number' => $data->kiosk_number, // Kiosk编号
                'kiosk_name' => $data->kiosk_name,// Kiosk名称
                'octopus_device_number' => $data->octopus_device_number,// 八达通设备编号
                'old_card_number' => $data->old_card_number,// 旧卡号
                'new_card_number' => $data->new_card_number,// 新卡号
                'description' => $data->description ?? '—/—', // 描述
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );

        return response()->json($json);
    }

}

<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Support\Facades\DB;
use Exception;
use App\Helpers\Payment\PaymentCommon;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Illuminate\Http\{
    Request,
    JsonResponse,
};
use App\Enums\{
    TransactionType,
    TransactionCategory,
    UserCredentialType,
};
use App\Models\Modules\{
    AppUser,
    MemberCard,
    PointsTransaction,
    PointsWallet,
    Vehicle,
    UserNotify,
    Site,
    UserGroup,
    Region,
};

class AppUserController extends CommonController
{
    protected static string $module_name = 'appUser'; // 模块名称
    protected AppUser $model;

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new AppUser;
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'list_url' => action([self::class, 'list']),
            'telephone_search' => $request->telephone_search,
            'email_search' => $request->email_search,
            'default_avatar' => existsImage('icon', 'not_select_image.png'),
            'merchant_search' => $request->merchant_search,
            'merchant_list' => self::getMerchantOptionList(),
            'user_group_search' => $request->user_group_search,
        );

        $data['user_group_list'] = $this->getUserGroupOptionList();

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        $draw = (int)$request->input('draw', 1);
        $length = (int)$request->input('length', 10);
        $search = $this->_searchList($request);
        $data_list = $search->paginate($length, ['*'], 'start');

        // 积分钱包处理函数
        $point_wallet_function = function ($point_wallet_list) {
            $point_balance_text = '';
            if (blank($point_wallet_list)) {
                return $point_balance_text;
            }
            $default_currency_symbol = config('app.default_currency_symbol');
            foreach ($point_wallet_list as $point_wallet) {
                $point_balance_text .= ($point_wallet->currency_symbol ?: $default_currency_symbol) . ' ' . (float) bcdiv($point_wallet->points_balance, 100, 1) . '<br>';
            }
            $point_balance_text = substr($point_balance_text, 0, -4); // 去掉最后的<br>
            return $point_balance_text;
        };

        $result = array();
        foreach ($data_list as $data) {
            // 获取对应site_number_list的site name
            $site_name_json_list = $site_name_list = array();
            if (filled($data->site_number_list)) {
                $site_name_json_list = Site::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                    ->whereIn('site_number', (explode(',', $data->site_number_list)) ?? array())
                    ->pluck('name_json')
                    ->toArray();
            }
            foreach ($site_name_json_list as $name_json) {
                $site_name_list[] = $this->getValueFromLanguageArray($name_json) ?? '—/—';
            }
            $result[] = array(
                'user_id' => $data->user_id, // 用户ID
                'merchant_name' => $this->getValueFromLanguageArray($data?->merchant?->name_json) ?? '—/—', // 商户名称
                'user_group_name' => $this->getValueFromLanguageArray($data?->userGroup?->name_json) ?? '—/—', // 用户组名称
                'nickname' => $data->nickname, // 昵称
                'avatar_url' => existsImage('public', $data->avatar_url) ?: existsImage('icon', 'not_select_image.png'), // 头像
                'telephone' => $data->telephone ?? '—/—', // 电话
                'email' => $data->email ?? '—/—', // 电邮
                'region_name' => $this->getValueFromLanguageArray($data?->region_name_json) ?? '—/—', // 地区名称
                // 'points_balance' => (float)bcdiv($data->points_balance, 100, 1), // 积分余额
                'points_balance' => $point_wallet_function($data->pointsWallet),
                'site_number_list' => $site_name_list, // Site名称
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );
        return response()->json($json);
    }

    /**
     * 视图页
     *
     * @param Request $request
     * @param $user_id
     * @return View|Application|Factory
     */
    public function view(Request $request, $user_id): View|Application|Factory
    {
        // dataTable字段
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'],
            self::getUrlParams($request)
        );
        $data['user_credential_list_url'] = action([UserCredentialController::class, 'list'], ['user_search' => $user_id]);
        $data['user_credential_add_url'] = action([UserCredentialController::class, 'add'], ['user_search' => $user_id]);
        $data['user_credential_edit_url'] = '/userCredential/edit/';
        $data['member_card_list_url'] = action([MemberCardController::class, 'list'], ['user_search' => $user_id]);
        $data['member_card_add_url'] = action([MemberCardController::class, 'add'], ['user_id' => $user_id]);
        $data['points_transaction_list_url'] = action([self::class, 'pointsTransactionList']);
        $data['vehicle_list_url'] = action([self::class, 'vehicleList']);
        $data['notify_list_url'] = action([self::class, 'notifyList']);
        $model = AppUser::where('user_id', $user_id)
            ->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))
            ->firstOrFail();

        // 通过当前管理员的site_number_list拼接find_in_set语句
        // 获取对应site_number_list的site name
        $site_name_json_list = $site_name_list = array();
        if (filled($model->site_number_list)) {
            $site_name_json_list = Site::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->whereIn('site_number', (explode(',', $model->site_number_list)) ?? [])
                ->pluck('name_json')
                ->toArray();
        }
        foreach ($site_name_json_list as $name_json) {
            $site_name_list[] = $this->getValueFromLanguageArray($name_json) ?? '—/—';
        }

        // 积分钱包处理函数
        $point_wallet_function = function ($point_wallet_list) {
            $default_currency_code = config('app.default_currency_code');
            $default_currency_symbol = config('app.default_currency_symbol');
            $points_wallet_list = [];
            if (filled($point_wallet_list)) {
                foreach ($point_wallet_list as $points_wallet) {
                    $points_wallet_list[] = [
                        'points_wallet_id' => $points_wallet->points_wallet_id,
                        'currency_code' => $points_wallet->currency_code ?: $default_currency_code,
                        'currency_symbol' => $points_wallet->currency_symbol ?: $default_currency_symbol,
                        'points_balance' => (float) bcdiv($points_wallet->points_balance, 100, 1),
                    ];
                }
            }
            return $points_wallet_list;
        };

        $data['model'] = array(
            'merchant_number' => $model->merchant_number,
            'merchant_name' => $this->getValueFromLanguageArray($model?->merchant?->name_json) ?? '—/—', // 商户名称
            'user_group_id' => $model->user_group_id,
            'user_group_name' => $this->getValueFromLanguageArray($model?->userGroup?->name_json) ?? '—/—', // 用户组名称
            'user_id' => $model->user_id,
            'nickname' => $model->nickname,
            'avatar_url' => existsImage('public', $model->avatar_url) ?: existsImage('icon', 'not_select_image.png'),
            'telephone' => $model->telephone,
            'email' => $model->email,
            'region_id' => $model->region_id,
            'region_name' => $this->getValueFromLanguageArray($model->region_name_json) ?? '—/—',
            'points_wallet_list' => $point_wallet_function($model->pointsWallet),
            'site_number_list' => $model->site_number_list, // 场地编号数组
            'site_name' => filled($site_name_list) ? implode(' | ', $site_name_list) : null, // 场地名称
            'gmt_create' => $model->gmt_create->toDateTimeString(),
        );

        $data['model']['user_credential_type_list'] = [];
        $data['model']['transaction_type_list'] = [];
        $data['model']['transaction_category_list'] = [];

        foreach (UserCredentialType::asSelectArray() as $value => $name) {
            $data['model']['user_credential_type_list'][] = [
                'name' => $name,
                'value' => $value,
            ];
        }

        foreach (TransactionType::asSelectArray() as $value => $name) {
            $data['model']['transaction_type_list'][] = array(
                'name' => $name,
                'value' => $value,
            );
        }

        foreach (TransactionCategory::asSelectArray() as $value => $name) {
            $data['model']['transaction_category_list'][] = array(
                'name' => $name,
                'value' => $value,
            );
        }
        // 获取配置值
        $data['language_list'] = config('languages');
        $data['merchant_list'] = $this->getMerchantOptionList();
        $data['user_group_list'] = $this->getUserGroupOptionList();
        return view("pages.{$data['module_name']}.view", $data);
    }

    /**
     * pointsTransaction列表接口
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function pointsTransactionList(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $length = (int)$request->input('length', 10);
        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $user_id = $request->input('user_id', 0);
        $points_wallet_id = $request->input('points_wallet_id', 0);
        $transaction_number_search = $request->transaction_number_search;
        $transaction_type_search = $request->transaction_type_search;
        $transaction_category_search = $request->transaction_category_search;

        // 找不到对应商户下的用户，直接返回
        $user = AppUser::where('user_id', $user_id)
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))
            ->first();
        if (blank($user)) {
            $json = array(
                'draw' => $draw,
                'recordsTotal' => 0,
                'recordsFiltered' => 0,
                "data" => [],
            );
            return response()->json($json);
        }

        $data_list = PointsTransaction::where('user_id', $user_id)
            ->where('points_wallet_id', $points_wallet_id)
            ->when(filled($transaction_number_search), function ($query) use ($transaction_number_search) {
                $query->where('transaction_number', 'like', "%$transaction_number_search%");
            })
            ->when(filled($transaction_type_search), function ($query) use ($transaction_type_search) {
                $query->where('transaction_type', $transaction_type_search);
            })
            ->when(filled($transaction_category_search), function ($query) use ($transaction_category_search) {
                $query->where('transaction_category', $transaction_category_search);
            })
            ->orderBy($order, $sort)
            ->paginate($length, ['*'], 'start');

        $default_currency_symbol = config('app.default_currency_symbol');
        $result = array();
        foreach ($data_list as $points_transaction) {
            $result[] = array(
                'transaction_number' => $points_transaction->transaction_number,
                'transaction_type' => TransactionType::getDescription($points_transaction->transaction_type), // 交易类型
                'transaction_category' => TransactionCategory::getDescription($points_transaction->transaction_category), // 交易类别
                'amount' => ($points_transaction->currency_symbol ?: $default_currency_symbol) . ' ' . (float)bcdiv($points_transaction->amount, 100, 1), // 金额
                'points_balance' => ($points_transaction->currency_symbol ?: $default_currency_symbol) . ' ' . (float)bcdiv($points_transaction->points_balance, 100, 1), // 积分余额
                'gmt_create' => $points_transaction->gmt_create->toDateTimeString(),
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );

        return response()->json($json);
    }

    /**
     * pointsTransaction系統增值接口
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function systemTopUp(Request $request, AppUser $model): JsonResponse
    {
        $module_name = self::$module_name;
        // dataTable字段
        $request->validate(self::systemRules($request, $model, true), [], self::attributes());
        $user_id = $request->input('user_id', 0);
        $points_balance = $request->input('points_balance', 0);
        $points_wallet_id = $request->input('points_wallet_id',0);

        try {
            DB::transaction(function () use ($module_name, $user_id, $points_balance, $points_wallet_id) {
                // 获取悲观锁
                $points_wallet = PointsWallet::where('user_id', $user_id)
                    ->where('points_wallet_id', $points_wallet_id)
                    ->lockForUpdate()
                    ->first();
                if (blank($points_wallet)) {
                    $this->notFoundData(__("$module_name.points_wallet"));
                    $this->message = __("$module_name.error_points_wallet_not_exist");
                    return $this->returnJson();
                }
                // 新加积分交易记录
                $points_transaction = new PointsTransaction;
                $points_transaction->user_id = $user_id;
                $points_transaction->points_wallet_id = $points_wallet_id;
                $points_transaction->currency_code = $points_wallet->currency_code;
                $points_transaction->currency_symbol = $points_wallet->currency_symbol;
                $points_transaction->transaction_number = PaymentCommon::generatePointsTransactionNumber();
                $points_transaction->transaction_type = TransactionType::Income;
                $points_transaction->transaction_category = TransactionCategory::SystemTopUp;
                $points_transaction->amount = bcmul($points_balance, 100);
                $points_transaction->save();
                // 回填积分钱包余额
                $points_wallet->increment('points_balance', $points_transaction->amount); // 自增
                // 回填积分交易记录余额
                $points_transaction->points_balance = $points_wallet->fresh()->points_balance;
                $points_transaction->save();
                $this->data = (float) (bcdiv($points_wallet->points_balance, 100, 1));
            }, 3); // 死锁时重试3次
        } catch (\Throwable $th) {
            logger($th->getTraceAsString());
            $this->code = 500;
            $this->message = $th->getMessage();
        }

        return $this->returnJson();
    }

    /**
     * pointsTransaction系統扣除接口
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function systemDeduct(Request $request, AppUser $model): JsonResponse
    {
        $module_name = self::$module_name;
        // dataTable字段
        $request->validate(self::systemRules($request, $model, false), [], self::attributes());
        $user_id = $request->input('user_id', 0);
        $points_balance = $request->input('points_balance', 0);
        $points_wallet_id = $request->input('points_wallet_id',0);

        try {
            DB::transaction(function () use ($module_name, $user_id, $points_balance, $points_wallet_id) {
                // 获取悲观锁
                $points_wallet = PointsWallet::where('user_id', $user_id)
                    ->where('points_wallet_id', $points_wallet_id)
                    ->lockForUpdate()
                    ->first();
                if (blank($points_wallet)) {
                    $this->notFoundData(__("$module_name.points_wallet"));
                    $this->message = __("$module_name.error_points_wallet_not_exist");
                    return $this->returnJson();
                }
                // 新加积分交易记录
                $points_transaction = new PointsTransaction;
                $points_transaction->user_id = $user_id;
                $points_transaction->points_wallet_id = $points_wallet_id;
                $points_transaction->currency_code = $points_wallet->currency_code;
                $points_transaction->currency_symbol = $points_wallet->currency_symbol;
                $points_transaction->transaction_number = PaymentCommon::generatePointsTransactionNumber();
                $points_transaction->transaction_type = TransactionType::Expense;
                $points_transaction->transaction_category = TransactionCategory::SystemDeduct;
                $points_transaction->amount = bcmul($points_balance, 100);
                $points_transaction->save();
                // 回填积分钱包余额
                $points_wallet->decrement('points_balance', $points_transaction->amount); // 自减
                // 回填积分交易记录余额
                $points_transaction->points_balance = $points_wallet->fresh()->points_balance;
                $points_transaction->save();
                $this->data = (float) (bcdiv($points_wallet->points_balance, 100, 1));
            }, 3); // 死锁时重试3次
        } catch (\Throwable $th) {
            logger($th->getTraceAsString());
            $this->code = 500;
            $this->message = $th->getMessage();
        }

        return $this->returnJson();
    }

    /**
     * 获取用户最新积分钱包余额
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getUserLatestPointsWalletBalance(Request $request)
    {
        $module_name = self::$module_name;
        $user_id = $request->input('user_id', 0);

        if (blank($user_id)) {
            $this->missingField(__("$module_name.user"));
            return $this->returnJson();
        }
        $model = AppUser::where('user_id', $user_id)
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))
            ->first();
        if (blank($model)) {
            $this->notFound(__("$module_name.user"));
            return $this->returnJson();
        }

        $default_currency_code = config('app.default_currency_code');
        $default_currency_symbol = config('app.default_currency_symbol');
        $points_waller_list = PointsWallet::where('user_id', $user_id)->get();
        if (filled($points_waller_list)) {
            $points_waller_list = $points_waller_list?->map(function ($item) use ($default_currency_code, $default_currency_symbol) {
                $item->currency_code = $item->currency_code ?: $default_currency_code;
                $item->currency_symbol = $item->currency_symbol ?: $default_currency_symbol;
                $item->points_balance = (float) bcdiv($item->points_balance, 100, 1);
                return $item;
            })->all();
        }

        $this->data = $points_waller_list;

        return $this->returnJson();
    }

    /**
     * vehicle列表接口
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function vehicleList(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $length = (int)$request->input('length', 10);
        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $user_id = $request->input('user_id', 0);
        $plate_number_search = $request->plate_number_search;

        // 找不到对应商户下的用户，直接返回
        $user = AppUser::where('user_id', $user_id)
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))
            ->first();
        if (blank($user)) {
            $json = array(
                'draw' => $draw,
                'recordsTotal' => 0,
                'recordsFiltered' => 0,
                "data" => [],
            );
            return response()->json($json);
        }

        $data_list = Vehicle::where('user_id', $user_id)
            ->when(filled($plate_number_search), function ($query) use ($plate_number_search) {
                $query->where('plate_number', 'like', "%$plate_number_search%");
            })
            ->orderBy($order, $sort)
            ->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $vehicle) {
            $result[] = array(
                'plate_number' => $vehicle->plate_number,
                'is_common' => $vehicle->is_common,
                'gmt_create' => $vehicle->gmt_create->toDateTimeString(),
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );

        return response()->json($json);
    }

    /**
     * vehicle列表接口
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function notifyList(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $length = (int)$request->input('length', 10);
        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $user_id = $request->input('user_id', 0);
        $gmt_release = $request->gmt_release_search;
        $title = $request->title_search;
        $content = $request->content_search;

        $gmt_release_list = self::getRangeDateTimeArray($gmt_release ?: '') ?: null;

        // 找不到对应商户下的用户，直接返回
        $user = AppUser::where('user_id', $user_id)
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))
            ->first();
        if (blank($user)) {
            $json = array(
                'draw' => $draw,
                'recordsTotal' => 0,
                'recordsFiltered' => 0,
                "data" => [],
            );
            return response()->json($json);
        }

        $data_list = UserNotify::with(['description'])
            ->where('user_id', $user_id)
            ->when(filled($gmt_release_list), function ($query) use ($gmt_release_list) {
                return $query->whereBetween('gmt_release', $gmt_release_list);
            })
            ->when(filled($title), function ($query) use ($title) {
                return $query->whereHas('description', function ($query) use ($title) {
                    return $query->where('title', 'like', "%{$title}%");
                });
            })
            ->when(filled($content), function ($query) use ($content) {
                return $query->whereHas('description', function ($query) use ($content) {
                    return $query->where('content', 'like', "%{$content}%");
                });
            })
            // 排序，关联user_notify_to_user表
            ->leftJoinSub(function ($query) use ($user_id) {
                $query->select('user_notify_id AS n2u_user_notify_id', 'gmt_read', 'gmt_delete')
                    ->from('user_notify_to_user')
                    ->where('user_id', $user_id);
            }, 'user_notify_to_user', function ($join) {
                $join->on('user_notify_id', '=', 'n2u_user_notify_id');
            })
            ->whereNull('user_notify_to_user.gmt_delete')
            ->orderBy($order, $sort)
            ->latest('gmt_modified')
            ->latest('user_notify_id')
            ->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $current_language_description = $data->description->firstWhere('language_code', app()->getLocale());
            $description_list = [];
            foreach ($data->description as $description) {
                $description_list[$description->language_code] = $description;
            }
            $result[] = array(
                'user_notify_id' => $data->user_notify_id, // 用户通知ID
                'gmt_release' => $data->gmt_release, // 发布时间
                'image_url' => $current_language_description?->image_url, // 图片地址
                'title' => $current_language_description->title ?? '—/—', // 标题
                'content' => $current_language_description->content ?? '—/—', // 内容
                'description_list' => $description_list, // 描述列表
                'gmt_read' => $data->gmt_read, // 已读时间
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );

        return response()->json($json);
    }

    /**
     * 添加
     *
     * @param Request $request
     * @param AppUser $model
     * @return JsonResponse
     */
    public function add(Request $request, AppUser $model): JsonResponse
    {
        $request->validate(self::rules($request, $model), [], self::attributes());
        $module_name = self::$module_name;

        $merchant_number = $request->merchant_number;
        $user_group_id = $request->user_group_id;
        $password = $request->password;
        $nickname = $request->nickname ?? 'user_' . Str::random(10);
        $telephone = $request->telephone;
        $email = $request->email;
        $region_id = $request->region_id;
        $site_number_list = $request->site_number_list;
        $is_bind_member_card = $request->boolean('is_bind_member_card');
        $is_auto_generate_member_card = $request->boolean('is_auto_generate_member_card');
        $member_card_ids = $request->member_card_id;

        try {
            DB::transaction(function () use ($request, $model, $merchant_number, $user_group_id, $password, $nickname, $telephone, $email, $site_number_list, $is_bind_member_card, $is_auto_generate_member_card, $member_card_ids, $region_id) {
                $model = new AppUser;
                if (filled($password)) {
                    $salt = Str::random(10);
                    $model->salt = $salt;
                    $model->password = hash('sha512', $password . $salt);
                }
                if ($request->hasFile('avatar_url')) {
                    $file = $request->file('avatar_url');
                    $format_list = json_decode(file_get_contents(base_path() . '/format.json'), true);

                    if (!empty($format_list['image']) && in_array(strtolower($file->extension()), $format_list['image'])) {
                        $file_name = $file->store('/avatar/app_user', 'public');
                        $model->avatar_url = $file_name;
                    }
                }

                $model->site_number_list = filled($site_number_list) ? implode(',', $site_number_list) : null;

                $model->merchant_number = $merchant_number;
                $model->user_group_id = $user_group_id;
                $model->nickname = $nickname;
                $model->telephone = $telephone;
                $model->email = $email;
                $model->region_id = $region_id;
                $region = Region::find($region_id);
                $model->region_name_json = $region->name_json;

                $model->save();

                // 自动生成会员卡再绑定
                /* if ($is_auto_generate_member_card === true) {
                    $merchant_number = strtoupper(env('APP_NAME'));
                    // 创建平台会员卡
                    $member_card = MemberCard::create([
                        'user_id' => $model->user_id,
                        'member_card_key' => self::getRandomString(20, function ($member_card_key) {
                            return MemberCard::where('member_card_key', $member_card_key)->exists();
                        }),
                        'merchant_number' => $merchant_number,
                        'is_enable' => 1,
                    ]);

                    if (blank($member_card)) throw new Exception('Member card is blank');
                }
                // 绑定已有会员卡
                if ($is_bind_member_card === true && !empty($member_card_ids = explode(',', $member_card_ids))) {
                    // 如果需要绑定会员卡且会员卡不为空时绑定
                    $update_result = MemberCard::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))
                        ->whereIn('member_card_id', $member_card_ids)
                        ->update(['user_id' => $model->user_id]);

                    if ($update_result === false) throw new Exception('Member card bind error');
                } */

                $this->data = $model;
                return $this->returnJson();
            });
        } catch (Exception $e) {
            report($e);
            $this->modelSaveFail();
            $this->message = __("$module_name.app_user_create_error");
        }

        return $this->returnJson();
    }

    /**
     * 编辑
     *
     * @param Request $request
     * @param AppUser $model
     * @return JsonResponse
     */
    public function edit(Request $request, AppUser $model): JsonResponse
    {
        $module_name = self::$module_name;
        $request->validate(self::rules($request, $model), [], self::attributes());
        $user_id = $request->input('user_id', 0);
        $merchant_number = $request->merchant_number;
        $user_group_id = $request->user_group_id;
        $nickname = $request->nickname;
        $telephone = $request->telephone;
        $email = $request->email;
        $region_id = $request->region_id;
        $new_site_number_list = $request->input('site_number_list', '');

        if (blank($user_id)) {
            $this->missingField(__("$module_name.user"));
            return $this->returnJson();
        }
        $model = AppUser::where('user_id', $user_id)
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))
            ->first();
        if (blank($model)) {
            $this->notFound(__("$module_name.user"));
            return $this->returnJson();
        }

        $model->nickname = $nickname;
        $model->merchant_number = $merchant_number;
        $model->user_group_id = $user_group_id;
        $model->telephone = $telephone;
        $model->email = $email;
        $model->region_id = $region_id;
        $region = Region::find($region_id);
        $model->region_name_json = $region?->name_json;
        // 保存场地
        $new_site_number_list = filled($new_site_number_list) ? explode(',', $new_site_number_list) : []; // 新场地编号
        $old_site_number_list = filled($model->site_number_list) ? explode(',', $model->site_number_list) : []; // 旧场地编号
        $admin_site_number_list = auth()->user()->site_number_list->toArray(); // 管理员可管理的场地编号
        $new_site_number_list = self::onlyModifyMySiteNumberList($admin_site_number_list, $new_site_number_list, $old_site_number_list); // 仅修改管理员可管理的场地编号
        $model->site_number_list = filled($new_site_number_list) ? implode(',', $new_site_number_list) : null;
        $model->save();

        $this->data = $model;

        return $this->returnJson();
    }

    /**
     * 修改密码
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function changePassword(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        $request->validate(self::passwordRules(), [], self::attributes());
        $user_id = $request->input('user_id', 0);
        $password = $request->password;

        if (blank($user_id)) {
            $this->missingField(__("$module_name.user"));
            return $this->returnJson();
        }
        $model = AppUser::where('user_id', $user_id)
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))
            ->first();
        if (blank($model)) {
            $this->notFound(__("$module_name.user"));
            return $this->returnJson();
        }

        $salt = Str::random(10);
        $model->salt = $salt;
        $model->password = hash('sha512', $password . $salt);
        $model->save();

        $this->data = $model;

        return $this->returnJson();
    }

    /**
     * 修改头像
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function upload(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        $user_id = $request->input('user_id', 0);

        if (blank($user_id)) {
            $this->missingField(__("$module_name.user"));
            return $this->returnJson();
        }
        $model = AppUser::where('user_id', $user_id)
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))
            ->first();
        if (blank($model)) {
            $this->notFound(__("$module_name.user"));
            return $this->returnJson();
        }
        if (!$request->hasFile('avatar_url')) {
            $this->missingField(__("$module_name.avatar_url"));
            return $this->returnJson();
        }

        $file = $request->file('avatar_url');

        $format_list = json_decode(file_get_contents(base_path() . '/format.json'), true);

        if (filled($format_list['image']) && !in_array($file->getClientOriginalExtension(), $format_list['image'])) {
            $this->message = __("$module_name.app_user_avatar_upload_error_format");
            $this->code = 201;
            return $this->returnJson();
        }

        // 判断是否存在文件夹app_user，没有则创建此文件夹
        if (!Storage::disk('public')->exists('/avatar/app_user')) {
            Storage::disk('public')->makeDirectory('/avatar/app_user');
        }

        $file_name = $file->store('/avatar/app_user', 'public');
        $model->avatar_url = $file_name;
        if (filled($model->getOriginal('avatar_url'))) {
            Storage::disk('public')->delete($model->getOriginal('avatar_url'));
        }
        $model->save();
        $this->data = $model;

        return $this->returnJson();
    }

    /**
     * 删除
     *
     * @param Request $request
     * @param AppUser $model
     * @return JsonResponse
     * @Description 删除对应数据并且删除对应头像
     * @example
     * <AUTHOR>
     * @date 2023-10-24
     */
    public function delete(Request $request): JsonResponse
    {
        $id = $request->post('id');

        if (blank($id)) {
            $this->missingField('ID');
            return $this->returnJson();
        }

        $model = AppUser::where('user_id', $id)
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))
            ->first();

        if (blank($model)) {
            $this->notFoundData('ID');
            return $this->returnJson();
        }

        if (filled($model->avatar_url)) {
            Storage::disk('avatar')->delete($model->avatar_url);
        }

        // 移除对应信息
        $model->memberCard()->delete();
        $model->vehicle()->delete();
        $model->userCredential()->delete();
        $model->pointsWallet()->delete();
        $model->delete();

        return $this->returnJson();
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function rules(?Request $request, ?Model $model): array
    {
        $module_name = self::$module_name;
        $format_list = json_decode(file_get_contents(base_path() . '/format.json'), true);
        $image_types = implode(',', $format_list['image']);

        $rules = array(
            'merchant_number' => [
                'required',
                function ($attr, $value, $fail) use ($request, $module_name) {
                    if (!isSuperAdministrator() && !in_array($value, auth()->user()->merchant_number_list->toArray())) {
                        $fail(__('common.text_not_found', ['field' => __("$module_name.merchant")]));
                    }
                },
            ],
            'user_group_id' => [
                'nullable',
                'exists:App\Models\Modules\UserGroup,user_group_id',
                function ($attr, $value, $fail) use ($request, $module_name) {
                    $user_group = UserGroup::find($value);
                    if ($user_group->merchant_number != $request->merchant_number) {
                        $fail(__("$module_name.error_user_group_merchant_number_inconsistent"));
                    }
                },
            ],
            'nickname' => 'nullable|max:45',
            'avatar_url' => 'nullable|mimes:' . $image_types,
            // telephone和email必须填写一个
            'telephone' => 'nullable|max:45|regex:/^\+[0-9]{1,3}[\s.-]?[0-9]{4,11}$/|unique:App\Models\Modules\AppUser,telephone,' . $request->user_id . ',user_id,merchant_number,' . $request->merchant_number,
            // 同一商户下邮箱唯一
            'email' => 'required_without:telephone|nullable|max:45|email:rfc|unique:App\Models\Modules\AppUser,email,' . $request->user_id . ',user_id,merchant_number,' . $request->merchant_number,
            'password' => 'nullable|min:4|max:20|regex:/^[a-zA-Z0-9._-]{4,20}$/|confirmed',
            'region_id' => [
                'nullable',
                'exists:App\Models\Modules\Region,region_id',
                function ($attr, $value, $fail) use ($module_name, $request) {
                    // 查询region
                    $region = Region::find($value);
                    // region的商户是否在当前管理员的商户列表中
                    if (filled($region) && !isSuperAdministrator() && !in_array($region->merchant_number, auth()->user()->merchant_number_list->toArray())) {
                        $fail(__('common.text_not_found', ['field' => __("$module_name.region")]));
                    }
                    // region的商户与用户的商户一致
                    if (filled($region) && $region->merchant_number != $request->merchant_number) {
                        $fail(__("$module_name.error_region_merchant_must_be_belong_merchant"));
                    }
                },
            ],
            /* 'is_auto_generate_member_card' => [
                function ($attr, $value, $fail) use ($request, $module_name) {
                    // 至少选择一种绑定会员卡方式
                    if (
                        $request->boolean('is_bind_member_card') === false &&
                        $request->boolean('is_auto_generate_member_card') === false
                    ) {
                        $fail(__("$module_name.choose_at_least_one_way_to_bind_member_card"));
                    }
                },
                function ($attr, $value, $fail) use ($module_name, $request) {
                    $value = filter_var($value, FILTER_VALIDATE_BOOLEAN);
                    // 查询选择的会员卡的商户
                    $member_card_id_list = explode(',', $request->member_card_id);
                    $select_merchant_member_card_result = MemberCard::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))
                        ->whereIn('member_card_id', $member_card_id_list)
                        ->pluck('merchant_number')
                        ->toArray();
                    // 当选择了自动生成卡就无法选择商户编号为env配置文件里的APP_NAME对应超级商户的会员卡
                    if ($value && in_array(strtoupper(env('APP_NAME')), $select_merchant_member_card_result)) {
                        $fail(__("$module_name.cannot_repeatedly_auto_generate"));
                    }
                },
            ],
            'member_card_id' => [
                Rule::requiredIf($request->boolean('is_bind_member_card') === true),
                'nullable',
                'exists:App\Models\Modules\MemberCard,member_card_id,user_id,NULL',
                function ($attr, $value, $fail) use ($module_name, $request) {
                    // 同一用户同一商户有且只有一张会员卡
                    $member_card_id_list = explode(',', $value);
                    // 查询选择的会员卡是否存在相同的商户
                    $select_merchant_member_card_result = MemberCard::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))
                        ->whereIn('member_card_id', $member_card_id_list)
                        ->pluck('merchant_number')
                        ->unique()
                        ->count();
                    if ($select_merchant_member_card_result < count($member_card_id_list)) {
                        $fail(__("$module_name.cannot_repeatedly_bind_by_merchant"));
                    }
                },
            ], */
        );

        // 新增时必填
        if (blank($request->user_id)) {
            // password 长度4-20 大小写字母+数字+特殊符号._-
            $rules['password'] = 'required|min:4|max:20|regex:/^[a-zA-Z0-9._-]{4,20}$/|confirmed';
        }

        $rules['site_number_list'] = ['nullable', function ($attribute, $value, $fail) use ($module_name) {
            $site_number_list = explode(',', $value) ?? array();
            foreach ($site_number_list as $site_number) {
                // 如果选中项有不存在的返回错误
                if (!filled(Site::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('site_number', $site_number))) {
                    $fail(__("$module_name.select_site_not_found"));
                }
            }
        }];

        return $rules;
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function systemRules(?Request $request, ?Model $model, $is_top_up): array
    {
        $module_name = self::$module_name;
        $user_id = $request->user_id ?? null;
        $rules = array(
            'user_id' => [
                'required',
                Rule::exists('App\Models\Modules\AppUser', 'user_id')->where(function ($query) {
                    $query->when(!isSuperAdministrator(), fn($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list));
                }),
            ],
            'points_wallet_id' => [
                'required',
                Rule::exists('App\Models\Modules\PointsWallet', 'points_wallet_id')->where(function ($query) use ($user_id) {
                    $query->where('user_id', $user_id);
                })
            ],
            'points_balance' => [
                'required',
                'numeric',
                'min:0.1',
                'max:999999',
                function ($attribute, $value, $fail) use ($module_name, $user_id, $is_top_up, $request) {
                    if (!preg_match("/^\d+(\.\d{0,1})?$/", $value)) {
                        $message = __("$module_name.one_decimal_places", [
                            'points_balance' => $value
                        ]);
                        $fail($message);
                        return;
                    }

                    $points_wallet = PointsWallet::where('user_id', $user_id)
                        ->where('points_wallet_id', $request->input('points_wallet_id', 0))
                        ->first();

                    if (!is_numeric($value) || !filled($points_wallet)) {
                        return;
                    }

                    $balance_change = floor(bcmul($value, 100) ?? 0);

                    if ($is_top_up && ($balance_change + $points_wallet->points_balance) > 99999900) {
                        $message = __("$module_name.user_points_max");
                        $fail($message);
                    } elseif (!$is_top_up && ($points_wallet->points_balance - $balance_change) < 0) {
                        $message = __("$module_name.user_points_min");
                        $fail($message);
                    }
                }
            ],
        );


        return $rules;
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @return array
     */
    protected static function passwordRules(): array
    {
        $rules = array(
            'user_id' => [
                'required',
                Rule::exists('App\Models\Modules\AppUser', 'user_id')->where(column: function ($query) {
                    $query->when(!isSuperAdministrator(), fn($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list));
                }),
            ],
            'password' => 'nullable|min:4|max:20|regex:/^[a-zA-Z0-9._-]{4,20}$/|confirmed',
        );

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'merchant_number' => __("$module_name.merchant"),
            'user_group_id' => __("$module_name.user_group"),
            'password' => __("$module_name.password"),
            'nickname' => __("$module_name.nickname"),
            'avatar_url' => __("$module_name.avatar_url"),
            'telephone' => __("$module_name.telephone"),
            'email' => __("$module_name.email"),
            'points_balance' => __("$module_name.points"),
            'site_number_list' => __("$module_name.site"),
            'member_card_id' => __("$module_name.member_card"),
            'points_wallet_id' => __("$module_name.points_wallet"),
        ];
    }

    /**
     * 返回地址栏参数
     *
     * @param Request|null $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => 'true',
            'merchant_search' => $request->get('merchant_search'),
            'user_group_search' => $request->get('user_group_search'),
            'telephone_search' => $request->get('telephone_search'),
            'email_search' => $request->get('email_search'),
        );
    }

    /**
     * 获取搜索数据
     *
     * @param Request $request
     * @return mixed
     * @Description
     * @example
     * <AUTHOR>
     * @date 2023-10-23
     */
    protected function _searchList(Request $request): mixed
    {
        // dataTable字段

        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $merchant_search = $request->merchant_search;
        $telephone_search = $request->telephone_search;
        $email_search = $request->email_search;
        $user_group_search = $request->user_group_search;
        $not_user_group_search = $request->not_user_group_search;

        return AppUser::with(['merchant', 'userGroup', 'pointsWallet'])
            ->when(!isSuperAdministrator(), function ($query) {
                $query->whereIn('merchant_number', auth()->user()->merchant_number_list);
            })
            ->when(filled($merchant_search), function ($query) use ($merchant_search) {
                $query->where('merchant_number', $merchant_search);
            })
            ->when(filled($telephone_search), function ($query) use ($telephone_search) {
                $query->where('telephone', 'like', "%$telephone_search%");
            })
            ->when(filled($email_search), function ($query) use ($email_search) {
                $query->where('email', 'like', "%$email_search%");
            })
            ->when(filled($user_group_search), function ($query) use ($user_group_search) {
                $query->where('user_group_id', $user_group_search);
            })
            ->when(filled($not_user_group_search), function ($query) use ($not_user_group_search) {
                // 如果传入了不查询的用户组ID，则查询除该用户组ID外的用户(包括没有用户组的用户，需要额外处理whereNull)
                $query->where(function ($query) use ($not_user_group_search) {
                    $query->where('user_group_id', '!=', $not_user_group_search)
                        ->orWhereNull('user_group_id');
                });
            })
            ->orderBy($order, $sort)
            ->orderBy('gmt_modified', 'desc');
    }

    /**
     * 只修改当前管理员可管理的场地
     *
     * @param array $admin_site_number_list 当前管理员可管理的场地
     * @param array $new_site_number_list 新场地
     * @param array $old_site_number_list 旧场地
     * @return array
     */
    protected static function onlyModifyMySiteNumberList(array $admin_site_number_list, array $new_site_number_list, array $old_site_number_list): array
    {
        // 超级管理员返回所有数据
        if (isSuperAdministrator()) {
            return $new_site_number_list;
        }

        // 过滤不存在的场地编号
        $admin_site_number_list = self::filterNotExistSiteNumberList($admin_site_number_list);

        // 先过滤新场地编号，确保不包含管理员无权限的新场地
        $new_site_number_list = self::filterSiteNumberList($admin_site_number_list, $new_site_number_list);

        // 获取管理员无权限的原场地编号
        $no_permission_site_number_list = self::getNoPermissionSiteNumberList($admin_site_number_list, $old_site_number_list);

        // 如果管理员无权限的原场地编号为空，直接返回过滤后的新场地编号
        if (empty($no_permission_site_number_list)) {
            return $new_site_number_list;
        }

        // 将管理员无权限的原场地编号添加到新场地编号列表中，并去重
        return array_unique(array_merge($new_site_number_list, $no_permission_site_number_list));
    }

    /**
     * 过滤不存在场地
     *
     * @param array $site_number_list
     * @return array
     */
    protected static function filterNotExistSiteNumberList(array $site_number_list): array
    {
        // 获取所有存在的场地编号
        $all_site_number_list = Site::select('site_number')
            ->whereIn('site_number', $site_number_list)
            ->pluck('site_number')
            ->toArray();

        // 返回存在的场地编号
        return $all_site_number_list;
    }

    /**
     * 判断site_number_list中是否包含管理员无权限的场地，返回数组，如果数组为空代表拥有所有权限
     *
     * @param array $admin_site_number_list 管理员可管理的场地
     * @param array $data_site_number_list 数据场地
     * @return array
     */
    protected static function getNoPermissionSiteNumberList(array $admin_site_number_list, array $data_site_number_list): array
    {
        // 超级管理员返回空数组
        if (isSuperAdministrator()) {
            return [];
        }

        // 过滤不存在的场地编号
        $admin_site_number_list = self::filterNotExistSiteNumberList($admin_site_number_list);

        // 如果管理员没有可管理的场地，返回所有数据场地
        if (empty($admin_site_number_list)) {
            return $data_site_number_list;
        }

        // 获取管理员无权限的场地编号
        return array_diff($data_site_number_list, $admin_site_number_list);
    }

    /**
     * 去除管理员无权限的场地编号
     *
     * @param array $admin_site_number_list 管理员可管理的场地
     * @param array $data_site_number_list 数据场地
     * @return array
     */
    protected static function filterSiteNumberList(array $admin_site_number_list, array $data_site_number_list): array
    {
        // 超级管理员返回所有数据
        if (isSuperAdministrator()) {
            return $data_site_number_list;
        }

        // 过滤不存在的场地编号
        $admin_site_number_list = self::filterNotExistSiteNumberList($admin_site_number_list);

        // 如果管理员没有可管理的场地，返回空数组
        if (empty($admin_site_number_list)) {
            return [];
        }

        // 如果数据场地为空，直接返回
        if (empty($data_site_number_list)) {
            return [];
        }

        // 去除管理员无权限的场地编号
        return array_intersect($data_site_number_list, $admin_site_number_list);
    }

    public static function getUserGroupOptionList(): array
    {
        $user_group_list = UserGroup::with('merchant')
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))
            ->orderBy('sort_order')
            ->get();
        // 追加merchant_name
        $user_group_list = $user_group_list->map(function ($item) {
            $item->merchant_name = self::getValueFromLanguageArray($item->merchant?->name_json) ?? '—/—';
            return $item;
        });
        $return = [];
        foreach ($user_group_list->groupBy('merchant_number') as $merchant_number => $user_group) {
            $return[] = [
                'merchant_number' => $merchant_number,
                'merchant_name' => $user_group->first()?->merchant_name ?? '—/—',
                'user_group_list' => $user_group->map(function ($item) {
                    return [
                        'user_group_id' => $item->user_group_id,
                        'user_group_name' => self::getValueFromLanguageArray($item->name_json) ?? '—/—',
                    ];
                }),
            ];
        }

        return $return;
    }

}

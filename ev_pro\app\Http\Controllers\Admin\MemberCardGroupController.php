<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use Illuminate\Database\Eloquent\Model;
use Illuminate\Validation\Rule;
use Illuminate\Foundation\Application;
use Illuminate\Http\{
    JsonResponse,
    RedirectResponse,
    Request,
};
use App\Http\Controllers\Admin\Traits\{
    Add,
    Edit,
};
use App\Models\Modules\{
    Site,
    MemberCardGroup,
    MemberCardGroupDescription,
    MemberCard,
};

class MemberCardGroupController extends CommonController
{
    use Add;

    protected static string $module_name = 'memberCardGroup'; // 模块名称
    protected static bool $module_check_site = true; // 标记该模块校验场地

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new MemberCardGroup;
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'add_url' => action([self::class, 'add']),
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'site_search' => $request->get('site_search'),
            'name_search' => $request->get('name_search'),
        );

        // 场地下拉列表
        $data['site_list'] = $this->getSiteOptionList();

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $length = (int)$request->input('length', 10);
        $search = $this->_searchList($request);
        $data_list = $search->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $site_name = $this->getValueFromLanguageArray($data->site?->name_json) ?? '—/—';

            $result[] = array(
                'member_card_group_id' => $data->member_card_group_id, // 会员卡组ID
                'name' => $this->getValueFromLanguageArray($data->name_json) ?? '—/—', // 名称
                'site_number' => $data->site_number, // 场地编号
                'site_name' => $site_name, // 场地名称
                'member_card_background_image_url' => existsImage('public', $data->member_card_background_image_url) ?: existsImage('icon', 'not_select_image.png'), // 会员卡背景图片路径
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );
        return response()->json($json);
    }

    public function edit(Request $request, int $id): View|Application|Factory|RedirectResponse|null
    {
        $data = array();

        $model = $this->model->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->findOrFail($id);

        if ($request->isMethod('post')) {
            return $this->modelValidateAndSave($request, $model);
        }

        $data['model'] = $model;

        return $this->getForm($request, $data);
    }

    protected function getForm(Request $request, array $data): View|Application|Factory
    {
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'],
            self::getUrlParams($request)
        );
        $site_number = $data['model']->site_number;
        if ((isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) && blank($data['model']?->member_card_group_id)) {
            // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下就直接获取页面传入的场地编号
            $site_number = $request->old('site_number', $data['model']->site_number);
        } else if (blank($data['model']?->member_card_group_id) && auth()->user()->site_number_list->count() === 1) {
            // 如果是新增并且管理员场地只有一个就直接赋值
            $site_number = auth()->user()->site_number_list->first();
        }
        $data['model']->site_number = $site_number; // 场地编号
        $site_name = $this->getValueFromLanguageArray(
            Site::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->firstWhere('site_number', $site_number)?->name_json);
        $data['model']->site_name = $site_name; // 场地名称

        foreach (config('languages') as $language_code => $language_name) {
            // 有旧数据
            $member_card_group_description_old_list = $request->old('item');
            if (filled($member_card_group_description_old_list)) {
                $data['item'][$language_code] = array(
                    'language_name' => $language_name,
                    'language_code' => $language_code,
                    'member_card_group_description_id' => $member_card_group_description_old_list[$language_code]['member_card_group_description_id'] ?? null, // ID
                    'name' => $member_card_group_description_old_list[$language_code]['name'] ?? null,
                );
            } else {
                $member_card_group_description_result = $data['model']->description->where('language_code', $language_code)->first();
                $data['item'][$language_code] = array(
                    'language_name' => $language_name,
                    'language_code' => $language_code,
                    'member_card_group_description_id' => $member_card_group_description_result->member_card_group_description_id ?? null, // ID
                    'name' => $member_card_group_description_result->name ?? null, // 名称
                );
            }
        }

        return view("pages.{$data['module_name']}.form", $data);
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @param MemberCardGroup $model
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    protected function modelValidateAndSave(Request $request, MemberCardGroup $model): RedirectResponse
    {
        $request->validate(self::rules($request, $model), [], self::attributes());
        // 判断是否为新增
        if (blank($model->member_card_group_id)) {
            $model = $this->model;
            $site_number = $model->site_number;

            if ((isSuperAdministrator() || auth()->user()->site_number_list->count() > 1)) {
                // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下就直接获取页面传入的场地编号
                $site_number = $request->input('site_number');
            } else if (auth()->user()->site_number_list->count() === 1) {
                // 如果是新增并且管理员场地只有一个就直接赋值
                $site_number = auth()->user()->site_number_list->first();
            }
            $model->site_number = $site_number; // 场地编号
            $model->merchant_number = Site::firstWhere('site_number', $model->site_number)?->merchant_number;
        }

        // 获取请求场地描述数据
        $member_card_group_description_list = $request->input('item', array());
        $name_json = array();
        foreach ($member_card_group_description_list as $member_card_group_description) {
            $name_json[$member_card_group_description['language_code']] = $member_card_group_description['name'];
        }


        $model->name_json = $name_json;
        $model->member_card_background_image_url = $request->input('member_card_background_image_url');
        $model->save();

        foreach ($member_card_group_description_list as $member_card_group_description) {
            if (isset($member_card_group_description['member_card_group_description_id'])) {
                $member_card_group_description_model =
                    MemberCardGroupDescription::findOr($member_card_group_description['member_card_group_description_id'], fn () => new MemberCardGroupDescription);
            } else {
                $member_card_group_description_model = new MemberCardGroupDescription;
            }
            $member_card_group_description_model->member_card_group_id = $model->member_card_group_id;
            $member_card_group_description_model->language_code = $member_card_group_description['language_code'];
            $member_card_group_description_model->name = $member_card_group_description['name'];

            $member_card_group_description_model->save();
        }

        return redirect()->action(
            [self::class, 'showPage'],
            self::getUrlParams($request)
        );
    }

    /**
     * 删除
     *
     * @param Request $request
     * @return JsonResponse
     * @Description 删除对应数据并且删除对应头像
     * @example
     * <AUTHOR>
     * @date 2023-10-24
     */
    public function delete(Request $request): JsonResponse
    {
        $id = $request->post('id');
        $module_name = self::$module_name;

        if (blank($id)) {
            $this->missingField('ID');
            return $this->returnJson();
        }

        $member_card_group = $this->model::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->find($id);

        if (blank($member_card_group)) {
            $this->notFoundData('ID');
            return $this->returnJson();
        }

        // 判断是否有绑定会员卡
        if (filled($member_card_list = $member_card_group->memberCard)) {
            $member_card_str = '';
            foreach ($member_card_list as $member_card) {
                $member_card_str .= '<li>' . $member_card->member_card_key . '</li>';
            }
            $this->code = 201;
            $this->message = __('common.error_has_binding_unable_to_delete_member_card_group', ['member_card' => $member_card_str]);
            return $this->returnJson();
        }

        // 判断是否有绑定收费表
        $tariff_tables = [
            'simple_tariff_table' => 'simpleTariffTableItem',
            'time_tariff_table' => 'timeTariffTableItem',
            'energy_tariff_table' => 'energyTariffTableItem',
            'idling_penalty_tariff_table' => 'idlingPenaltyTariffTableItem',
        ];

        $tariff_scheme_str = '';

        foreach ($tariff_tables as $name => $method) {
            if ($member_card_group->$method()->exists()) {
                $tariff_scheme_str .= '<li>' . __("$module_name.$name") . '</li>';
            }
        }

        if (!empty($tariff_scheme_str)) {
            $this->code = 201;
            $this->message = __("$module_name.error_has_binding_tariff_table_unable_to_delete_member_card_group", ['tariff_scheme' => $tariff_scheme_str]);
            return $this->returnJson();
        }

        $member_card_group->description()->delete();
        $member_card_group->delete();

        return $this->returnJson();
    }

    // 获取已绑定会员卡列表
    protected function getMemberCardGroupMemberCardList(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $length = (int)$request->input('length', 10);
        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $member_card_group_id = $request->input('member_card_group_id');
        $member_card_key_search = $request->input('member_card_key_search');

        $data_list = MemberCard::with(['site', 'user'])
            ->when(filled($member_card_key_search), fn ($query) => $query->where('member_card_key', 'like', "%$member_card_key_search%"))
            ->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->when(
                filled($member_card_group_id),
                fn ($query) => $query->where('member_card_group_id', $member_card_group_id),
                fn ($query) => $query->whereRaw('1 = 2')
            )
            ->orderBy($order, $sort)
            ->orderBy('gmt_modified', 'desc')
            ->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $site_name = $this->getValueFromLanguageArray($data->site?->name_json) ?? '—/—';
            $member_card_group_name = $this->getValueFromLanguageArray($data->memberCardGroup?->name_json) ?? '—/—';
            $result[] = array(
                'is_checked' => $data->member_card_group_id == $member_card_group_id ? true : false, // 判断是否有id来确认选中
                'member_card_id' => $data->member_card_id, // 会员卡ID
                'site_name' => $site_name, // 场地名称
                'member_card_key' => $data->member_card_key, // 会员卡号
                'user_email' => $data->user?->email ?? '—/—', // 用户名称
                'member_card_group_name' => $member_card_group_name, // 会员组名称
                'member_name' => $data->member_name ?? '—/—', // 会员名称
                'member_email' => $data->member_email ?? '—/—', // 会员电邮
                'octopus_card_number' => $data->octopus_card_number ?? '—/—', // 会员电邮
                'rfid_card_number' => $data->rfid_card_number ?? '—/—', // 会员电邮
                'is_enable' => $data->is_enable, // 是否启用
                'remark' => $data->remark ?? '—/—', // 备注
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );
        return response()->json($json);
    }

    // 保存会员卡组绑定关系
    public function saveMemberCardGroup(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        $member_card_list = $request->input('member_card_list', array());
        $member_card_group_id = $request->input('member_card_group_id');

        if (blank($member_card_group_id)) {
            $this->missingField(__("$module_name.member_card_group"));
            return $this->returnJson();
        }
        $member_card_group = MemberCardGroup::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->find($member_card_group_id);
        if (blank($member_card_group)) {
            $this->missingField(__("$module_name.member_card_group"));
            return $this->returnJson();
        }

        // 查找会员卡数据中的场地ID是否存在与会员卡组的场地ID不一致
        $site_member_card_list = MemberCard::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->whereIn('member_card_id', $member_card_list);

        $site_member_card_list_error = '';
        foreach ($site_member_card_list->get() as $site_member_card_model) {
            // 记录场地ID不一致的会员卡
            if ($site_member_card_model->site_number !== $member_card_group->site_number) {
                $site_member_card_list_error .= '<li>' . $site_member_card_model->member_card_key . '</li>';
            }
        }
        if (filled($site_member_card_list_error)) {
            $this->code = 201;
            $this->message = __("$module_name.add_member_card_error") . '<br>' . $site_member_card_list_error;
            return $this->returnJson();
        }

        $site_member_card_list->update(['member_card_group_id' => $member_card_group_id]);

        return $this->returnJson();
    }

    // 删除会员卡组绑定关系
    public function deleteMemberCardToMemberCardGroup(Request $request): JsonResponse
    {
        $member_card_id = $request->input('member_card_id');

        if (blank($member_card_id)) {
            $this->missingField('Member Card');
            return $this->returnJson();
        }
        $member_card = MemberCard::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->find($member_card_id);
        if (blank($member_card)) {
            $this->notFoundData('Member Card');
            return $this->returnJson();
        }

        $member_card->update(['member_card_group_id' => null]);

        return $this->returnJson();
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function rules(?Request $request, ?Model $model): array
    {
        $module_name = self::$module_name;

        $rules = array(
            'item.*.name' => 'required|max:45'
        );

        // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下才需要判断场地编号是否存在
        if (blank($model?->member_card_group_id)) {
            if ((isSuperAdministrator() || auth()->user()->site_number_list->count() > 1)) {
                $rules['site_number'] = [
                    'required',
                    'exists:App\Models\Modules\Site,site_number',
                    function ($attr, $value, $fail) use ($module_name, $request) {
                        // 判断选择的site是否为当前管理员权限下的
                        $site = Site::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('site_number', $value);
                        if (blank($site)) $fail(__('common.text_not_found', ['field' => __("$module_name.site")]));
                    },
                ];
            }
        }

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'name' => __("$module_name.name"),
            'site_number' => __("$module_name.site"),
            'item.*.name' => __("$module_name.description_name"),
        ];
    }

    /**
     * 返回地址栏参数
     *
     * @param Request|null $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => 'true',
            'site_search' => $request->get('site_search'),
            'name_search' => $request->get('name_search'),
        );
    }

    /**
     * 搜索数据
     *
     * @param Request $request
     * @return mixed
     */
    protected function _searchList(Request $request): mixed
    {
        // dataTable字段
        $order = $request->input('order', 'gmt_modified');
        $sort = $request->input('sort', 'asc');
        $site_search = $request->input('site_search');
        $name_search = $request->input('name_search');

        if ($order == 'name') $order = 'name_json';

        $member_card_group_list = MemberCardGroup::with(['description', 'site'])
            ->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->when(filled($name_search), function ($query) use ($name_search) {
                return $query->whereHas('description', function ($query) use ($name_search) {
                    return $query->where('name', 'like', "%$name_search%");
                });
            })
            ->when(filled($site_search), fn ($query) => $query->where('site_number', $site_search))
            ->orderBy($order, $sort)
            ->latest('gmt_modified');

        return $member_card_group_list;
    }
}

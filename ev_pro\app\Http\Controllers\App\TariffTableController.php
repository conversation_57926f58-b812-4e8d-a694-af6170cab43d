<?php

namespace App\Http\Controllers\App;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\{
    JsonResponse,
    Request,
};

use App\Models\Modules\{
    Connector,
    ChargeRecord,
    ChargePaymentRecordCalculation,
    ChargePaymentRecord,
    KioskSettingDescription,
    ChargeRecordChargedEnergy,
};

use App\Enums\{
    TariffTableType,
    DayType,
    LmsMode,
    ChargeTariffScheme,
};
use Illuminate\Support\Facades\{
    App,
};

class TariffTableController extends CommonController
{

    protected static string $module_name = 'tariffTable'; // 模块名称
    public function connectorTariffTable(Request $request): View|Factory|Application
    {
        $language_list = config('languages');
        $connector_id = $request->input('connector_id');
        $language_code = $request->input('language_code', 'en_US');
        $language_code = isset($language_list[$language_code]) ? $language_code : 'en_US';

        self::setLanguage($language_code);

        // Check connector_id
        if (blank($connector_id)) {
            abort(404);
        }

        $data = [
            'module_name' => self::$module_name,
            'language_code' => $language_code,
            'connector_id' => $connector_id,
            'get_connector_tariff_scheme_url' => action([self::class, 'getConnectorTariffScheme']),
            'get_kiosk_terms_url' => action([self::class, 'getKioskTerms']),
        ];

        return view("pages.app.{$data['module_name']}.connectorTariffTable", $data);
    }

    /**
     * 获取收费表规则
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getConnectorTariffScheme(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        $connector_id = $request->connector_id;
        $language_code = $request->input('language_code', app()->getLocale()); // 语言编码
        self::setLanguage($language_code);

        if (empty($connector_id) ||
            !filled($connector = Connector::with('setting')->find($connector_id))) {
            $this->notFoundData('Connector');
            return $this->returnJson();
        }

        // 收费表信息
        $tariff_scheme_list = $peak_time_table_item_json = array();

        switch ($connector->setting->tariff_table_type) {
            case TariffTableType::SimpleTariffTable:
                $simple_tariff_table = $connector?->setting?->simpleTariffTable;
                $idling_penalty_tariff_table = $connector?->setting
                ?->idlingPenaltyTariffTable
                ?->item()
                ->whereNull('member_card_group_id')
                ->oldest('idling_penalty_tariff_table_item.start_range')
                ->get();
                // 简单收费表
                if (filled($simple_tariff_table)) {
                    $tariff_scheme_list['simple_tariff_table_json'] = array(
                        'title' => __('appTariffTable.title_energy_charges'),
                        'data' => $simple_tariff_table,
                        'charge_value_interval' => $connector?->setting?->simpleTariffTable?->charge_value_interval,
                    );
                    if ($connector?->setting?->simpleTariffTable->charge_tariff_scheme == ChargeTariffScheme::PostPaid) {
                        $tariff_scheme_list['simple_tariff_table_json']['post_paid_maximum_charge_time'] = $connector?->setting?->simpleTariffTable?->post_paid_maximum_charge_time / 60;
                    }
                }
                // 闲时罚款表
                if (filled($idling_penalty_tariff_table)) {
                    $tariff_scheme_list['idling_penalty_tariff_table_item_json'] = array(
                        'title' => __('appTariffTable.title_penalty_charges'),
                        'data' => $idling_penalty_tariff_table,
                        'charge_value_interval' => 60,
                    );
                }
                $peak_time_table = $connector?->setting?->peakTimeTable;
                // 高峰时间表
                if (filled($peak_time_table)) {
                    $peak_time_table_json = self::setPeakTime($peak_time_table);
                    $tariff_scheme_list['peak_time_table_item_json'] = array(
                        'title' => __('appTariffTable.title_peak_hours'),
                        'data' => array(
                            'peak_time_table_list' => $peak_time_table_json,
                            'off_peak_time_table_list' => self::setOffPeakTime($peak_time_table_json),
                        )
                    );
                }
                break;
            case TariffTableType::ComplexTimeTariffTable:
                $time_tariff_table_item_json = $connector?->setting?->timeTariffTable?->item()->with([
                    'itemSpecial' => function ($query) {
                        $query->oldest('start_time');
                    }
                ])
                ->whereNull('member_card_group_id')
                ->orderByRaw(
                    'CASE WHEN day_type IS NULL THEN 0
                    WHEN day_type = "' . DayType::Monday . '" THEN 1
                    WHEN day_type = "' . DayType::Tuesday . '" THEN 2
                    WHEN day_type = "' . DayType::Wednesday . '" THEN 3
                    WHEN day_type = "' . DayType::Thursday . '" THEN 4
                    WHEN day_type = "' . DayType::Friday . '" THEN 5
                    WHEN day_type = "' . DayType::Saturday . '" THEN 6
                    WHEN day_type = "' . DayType::Sunday . '" THEN 7
                    WHEN day_type = "' . DayType::PublicHoliday . '" THEN 8 END'
                 )
                ->get();
                $idling_penalty_tariff_table = $connector?->setting
                ?->idlingPenaltyTariffTable
                ?->item()
                ->whereNull('member_card_group_id')
                ->oldest('idling_penalty_tariff_table_item.start_range')
                ->get();
                $peak_time_table = $connector?->setting?->peakTimeTable;
                // 高峰时间表
                if (filled($peak_time_table)) {
                    $peak_time_table_json = self::setPeakTime($peak_time_table);
                    $tariff_scheme_list['peak_time_table_item_json'] = array(
                        'title' => __('appTariffTable.title_peak_hours'),
                        'data' => array(
                            'peak_time_table_list' => $peak_time_table_json,
                            'off_peak_time_table_list' => self::setOffPeakTime($peak_time_table_json),
                        )
                    );
                }
                // 复杂时间收费表
                if (filled($time_tariff_table_item_json)) {
                    $tariff_scheme_list['time_tariff_table_item_json'] = array(
                        'title' => __('appTariffTable.title_energy_charges'),
                        'data' => $time_tariff_table_item_json,
                        'charge_value_interval' => $connector?->setting?->timeTariffTable?->charge_value_interval,
                    );
                    if ($connector?->setting?->timeTariffTable->charge_tariff_scheme == ChargeTariffScheme::PostPaid) {
                        $tariff_scheme_list['time_tariff_table_item_json']['post_paid_maximum_charge_time'] = $connector?->setting?->timeTariffTable?->post_paid_maximum_charge_time / 60;
                    }
                }
                // 闲时罚款表
                if (filled($idling_penalty_tariff_table)) {
                    $tariff_scheme_list['idling_penalty_tariff_table_item_json'] = array(
                        'title' => __('appTariffTable.title_penalty_charges'),
                        'data' => $idling_penalty_tariff_table,
                        'charge_value_interval' => 60,
                    );
                }
                break;
            case TariffTableType::ComplexEnergyTariffTable:
                $energy_tariff_table = $connector?->setting?->energyTariffTable?->item()->whereNull('member_card_group_id')->oldest('energy_tariff_table_item.start_range')->get();
                $idling_penalty_tariff_table = $connector?->setting?->idlingPenaltyTariffTable?->item()->whereNull('member_card_group_id')->oldest('idling_penalty_tariff_table_item.start_range')->get();
                $peak_time_table = $connector?->setting?->peakTimeTable;
                // 高峰时间表
                if (filled($peak_time_table)) {
                    $peak_time_table_json = self::setPeakTime($peak_time_table);
                    $tariff_scheme_list['peak_time_table_item_json'] = array(
                        'title' => __('appTariffTable.title_peak_hours'),
                        'data' => array(
                            'peak_time_table_list' => $peak_time_table_json,
                            'off_peak_time_table_list' => self::setOffPeakTime($peak_time_table_json),
                        )
                    );
                }
                // 复杂电量收费表
                if (filled($energy_tariff_table)) {
                    $tariff_scheme_list['energy_tariff_table_item_json'] = array(
                        'title' => __('appTariffTable.title_energy_charges'),
                        'data' => $energy_tariff_table,
                        'post_paid_maximum_charge_time' => $connector?->setting?->energyTariffTable?->post_paid_maximum_charge_time / 60,
                    );
                }
                // 闲时罚款表
                if (filled($idling_penalty_tariff_table)) {
                    $tariff_scheme_list['idling_penalty_tariff_table_item_json'] = array(
                        'title' => __('appTariffTable.title_penalty_charges'),
                        'data' => $idling_penalty_tariff_table,
                        'charge_value_interval' => 60,
                    );
                }
                break;
            default:
                break;
        }

        $site = $connector?->chargePoint?->site;
        $lms_mode = $site ? $site->lms_mode : '';
        $data = array(
            'tariff_scheme_list' => $tariff_scheme_list,
            'is_show_concessionary_rate' => LmsMode::EvenDistribution === $lms_mode,
        );
        if (filled($peak_time_table_item_json)) {
            $data['peak_time_table_item_json'] = $peak_time_table_item_json;
        }
        $this->data = $data;

        return $this->returnJson();
    }

    public function chargeRecordTariffRule(Request $request): View|Factory|Application
    {
        $language_list = config('languages');
        $charge_record_number = $request->input('charge_record_number');
        $language_code = $request->input('language_code', 'en_US');
        $language_code = isset($language_list[$language_code]) ? $language_code : 'en_US';

        self::setLanguage($language_code);

        // Check charge_record_number
        if (blank($charge_record_number)) {
            abort(404);
        }

        $data = [
            'module_name' => self::$module_name,
            'language_code' => $language_code,
            'charge_record_number' => $charge_record_number,
            'get_charge_record_tariff_rule_url' => action([self::class, 'getChargeRecordTariffRule']),
            'get_kiosk_terms_url' => action([self::class, 'getKioskTerms']),
        ];

        return view("pages.app.{$data['module_name']}.chargeRecordTariffRule", $data);
    }

    /**
     * 获取充电记录收费表json
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getChargeRecordTariffRule(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        $charge_record_number = $request->charge_record_number;
        $language_code = $request->input('language_code', app()->getLocale()); // 语言编码
        self::setLanguage($language_code);

        if (empty($charge_record_number) ||
            !filled($charge_record_model = ChargeRecord::firstWhere('charge_record_number', $charge_record_number)) || !filled($charge_record_model->tariffTableRule)) {
            $this->notFoundData('Charge Record');
            return $this->returnJson();
        }

        // 收费表信息
        $tariff_rule_list = array();
        if (filled($charge_record_model->tariffTableRule->peak_time_table_item_json)) {
            $peak_time_table_json = self::setPeakTime($charge_record_model->tariffTableRule);
            $tariff_rule_list['peak_time_table_item_json'] = array(
                'title' => __('appTariffTable.title_peak_hours'),
                'data' => array(
                            'peak_time_table_list' => $peak_time_table_json,
                            'off_peak_time_table_list' => self::setOffPeakTime($peak_time_table_json),
                        )
            );
        }

        if (filled($charge_record_model->tariffTableRule->simple_tariff_table_json)) {
            $tariff_rule_list['simple_tariff_table_json'] = array(
                'title' => __('appTariffTable.title_energy_charges'),
                'data' => $charge_record_model->tariffTableRule->simple_tariff_table_json,
                'charge_value_interval' => $charge_record_model->charge_value_interval,
            );
        }
        if (filled($charge_record_model->tariffTableRule->time_tariff_table_item_json)) {
            $tariff_rule_list['time_tariff_table_item_json'] = array(
                'title' => __('appTariffTable.title_energy_charges'),
                'data' => $charge_record_model->tariffTableRule->time_tariff_table_item_json,
                'charge_value_interval' => $charge_record_model->charge_value_interval,
            );
        }
        if (filled($charge_record_model->tariffTableRule->energy_tariff_table_item_json)) {
            $tariff_rule_list['energy_tariff_table_item_json'] = array(
                'title' => __('appTariffTable.title_energy_charges'),
                'data' => $charge_record_model->tariffTableRule->energy_tariff_table_item_json,
            );
        }
        if (filled($charge_record_model->tariffTableRule->idling_penalty_tariff_table_item_json)) {
            $tariff_rule_list['idling_penalty_tariff_table_item_json'] = array(
                'title' => __('appTariffTable.title_penalty_charges'),
                'data' => $charge_record_model->tariffTableRule->idling_penalty_tariff_table_item_json,
                'charge_value_interval' => 60,
            );
        }
        $data = array(
            'tariff_rule_list' => $tariff_rule_list,
            'is_show_concessionary_rate' => LmsMode::EvenDistribution == $charge_record_model->lms_mode,
            'connector_id' => $charge_record_model->connector_id,
        );
        if ($charge_record_model->charge_tariff_scheme == ChargeTariffScheme::PostPaid) {
            $data['post_paid_maximum_charge_time'] = $charge_record_model->post_paid_maximum_charge_time / 60;
        }
        $this->data = $data;

        return $this->returnJson();
    }

    public function chargeRecordChargedEnergy(Request $request): View|Factory|Application
    {
        $language_list = config('languages');
        $charge_record_number = $request->input('charge_record_number');
        $language_code = $request->input('language_code', 'en_US');
        $language_code = isset($language_list[$language_code]) ? $language_code : 'en_US';

        self::setLanguage($language_code);

        // Check charge_record_number
        if (blank($charge_record_number)) {
            abort(404);
        }

        $data = [
            'module_name' => self::$module_name,
            'language_code' => $language_code,
            'charge_record_number' => $charge_record_number,
            'get_charge_record_charged_energy_url' => action([self::class, 'getChargeRecordChargedEnergy']),
            'get_kiosk_terms_url' => action([self::class, 'getKioskTerms']),
        ];

        return view("pages.app.{$data['module_name']}.chargeRecordChargedEnergy", $data);
    }

    /**
     * 获取充电电量记录
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getChargeRecordChargedEnergy(Request $request): JsonResponse
    {
        $charge_record_number = $request->charge_record_number;
        // 通过json查询数据
        $data_list = ChargeRecordChargedEnergy::firstWhere('charge_record_number', $charge_record_number)->charged_energy_item_json ?? [];
        $charge_record_charged_energy_list = array();
        foreach ($data_list as $charge_record_charged_energy) {
            $charge_record_charged_energy_list[] = array(
                'charge_record_charged_energy' => $charge_record_charged_energy['charge_record_charged_energy'] ?? 0,
                'this_time_charged_energy' => $charge_record_charged_energy['this_time_charged_energy'] ?? 0,
                'gmt_create' => filled($charge_record_charged_energy['gmt_create']) ? date('Y-m-d H:i:s', strtotime($charge_record_charged_energy['gmt_create'])) : '—/—',
            );
        }
        $charge_record = ChargeRecord::firstWhere('charge_record_number', $charge_record_number);

        $this->data = array(
            'charged_energy_record' => $charge_record_charged_energy_list,
            'connector_id' => $charge_record->connector_id,
        );

        return $this->returnJson();
    }

    public function chargePaymentRecordTariffTable(Request $request): View|Factory|Application
    {
        $language_list = config('languages');
        $charge_payment_record_id = $request->input('charge_payment_record_id');
        $language_code = $request->input('language_code', 'en_US');
        $language_code = isset($language_list[$language_code]) ? $language_code : 'en_US';

        self::setLanguage($language_code);

        // Check charge_payment_record_id
        if (blank($charge_payment_record_id)) {
            abort(404);
        }

        $data = [
            'module_name' => self::$module_name,
            'language_code' => $language_code,
            'charge_payment_record_id' => $charge_payment_record_id,
            'idling_penalty_charge_value_interval' => 60,
            'get_charge_payment_record_tariff_scheme_url' => action([self::class, 'getChargePaymentRecordTariffScheme']),
            'get_kiosk_terms_url' => action([self::class, 'getKioskTerms']),
        ];

        return view("pages.app.{$data['module_name']}.chargePaymentRecordTariffTable", $data);
    }

    /**
     * 获取充电支付记录收费过程json
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getChargePaymentRecordTariffScheme(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        $charge_payment_record_id = $request->charge_payment_record_id;
        $language_code = $request->input('language_code', app()->getLocale()); // 语言编码
        self::setLanguage($language_code);

        if (empty($charge_payment_record_id) ||
            !filled($charge_payment_record_calculation_model = ChargePaymentRecordCalculation::where('charge_payment_record_id', $charge_payment_record_id)->first())) {
            $this->notFoundData('Charge Payment Record Calculation');
            return $this->returnJson();
        }

        $charge_payment_record = ChargePaymentRecord::find($charge_payment_record_id);
        $tariff_table_type = $charge_payment_record->chargeRecord->tariff_table_type ?? '';
        $charge_value_type = $charge_payment_record->chargeRecord->charge_value_type ?? '';

        $charge_payment_record_calculation_model->charge_value_amount_calculation_json = is_string($charge_payment_record_calculation_model->charge_value_amount_calculation_json) ? json_decode($charge_payment_record_calculation_model->charge_value_amount_calculation_json, true) : $charge_payment_record_calculation_model->charge_value_amount_calculation_json;
        $charge_payment_record_calculation_model->idling_penalty_amount_calculation_json = is_string($charge_payment_record_calculation_model->idling_penalty_amount_calculation_json) ? json_decode($charge_payment_record_calculation_model->idling_penalty_amount_calculation_json, true) : $charge_payment_record_calculation_model->idling_penalty_amount_calculation_json;

        $this->data = array(
            'charge_value_type' => $charge_value_type, // 用于判断简单收费表充电量类型
            'tariff_table_type' => $tariff_table_type, // 判断收费表类型
            'charge_value_amount_calculation_json' => $charge_payment_record_calculation_model->charge_value_amount_calculation_json, // 充电量计算json
            'idling_penalty_amount_calculation_json' => $charge_payment_record_calculation_model->idling_penalty_amount_calculation_json, // 闲时罚款计算json
            'connector_id' => $charge_payment_record->chargeRecord->connector_id, // 获取充电枪id
        );

        return $this->returnJson();
    }

    public function getKioskTerms(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        $connector_id = $request->connector_id;
        $language_code = $request->input('language_code', app()->getLocale()); // 语言编码

        if (empty($connector_id) ||
            !filled($connector = Connector::find($connector_id))) {
            return $this->returnJson();
        }
        $this->data = KioskSettingDescription::where('language_code',$language_code)->first()->disclaimer_html;

        return $this->returnJson();
    }

    public static function setPeakTime($peak_time_table, $member_card_group_id = null): array
    {
        $peak_time_table_list = [];
        $peak_time_table_item_json = $peak_time_table->peak_time_table_item_json;
        // 根据day_type分组
        $peak_time_table_item_json = collect($peak_time_table_item_json);
        $peak_time_table_list = $peak_time_table_item_json->where('member_card_group_id', $member_card_group_id)->groupBy('day_type')->transform(function ($item, $key) {
            return $item->sortBy('start_time');
        })->all();

        return $peak_time_table_list;
    }

    /**
     * 通过高峰时间表拿到对应的非高峰时间表
     *
     * @param [type] $peak_time_table
     * @return array
     * @Description
     * @example
     * @date 2023-10-11
     */
    public static function setOffPeakTime($peak_time_table): array
    {
        $off_peak_time_table = [];
        // 定义一天的总秒数
        $total_time_slots = 24 * 60 * 60;
        // 遍历每一天的繁忙时段区间
        foreach ($peak_time_table as $day_type => $peak_time_item) {
            $off_peak_time_table[$day_type] = self::getDayOffPeakTime($peak_time_item, $total_time_slots);
        }

        return $off_peak_time_table;
    }

    /**
     * 通过一天的高峰时间表拿到对应的非高峰时间表
     *
     * @param array $peak_time_item
     * @param int $total_time_slots
     * @return array
     * @Description
     * @example
     * @date 2023-10-11
     */
    public static function getDayOffPeakTime($peak_time_item, $total_time_slots): array
    {
        $off_peak_time_table_item = [];
        $start_time = 0;
        foreach ($peak_time_item as $item) {
            $end_time = $item['start_time'];
            if ($end_time > $start_time) {
                $off_peak_time_table_item[] = [
                    'start_time' => $start_time,
                    'end_time' => $end_time,
                ];
            }
            $start_time = $item['end_time'];
        }
        if ($start_time < $total_time_slots) {
            $off_peak_time_table_item[] = [
                'start_time' => $start_time,
                'end_time' => $total_time_slots,
            ];
        }

        return $off_peak_time_table_item;
    }
}

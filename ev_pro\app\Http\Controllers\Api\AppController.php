<?php

namespace App\Http\Controllers\Api;

use Exception;
use DB;
use DateTime;
use App\Enums\{
    ConnectorStatus,
    TariffTableType,
    ChargeValueType,
    ChargeTariffScheme,
    PaymentMethod,
    TransactionType,
    TransactionCategory,
    IdentityType,
    VerificationCodeTypeEnum,
    SubmitChargeRecordBillActionEnum,
    UserNotifyCategory,
    PaymentStatusEnum,
};
use App\Http\Controllers\Common\CommonController;
use App\Helpers\Payment\PaymentCommon;
use App\Models\Modules\{
    Connector,
    Vehicle,
    ChargePoint,
    ChargeRecord,
    Site,
    ChargePaymentRecord,
    ChargePaymentRecordCalculation,
    SiteImage,
    AppUser,
    PointsTransaction,
    UserNotify,
    UserNotifyToUser,
    News,
    NewsCategory,
    PublicHoliday,
    MemberCard,
    Kiosk,
};
use Illuminate\Http\{
    JsonResponse,
    Request,
};
use GuzzleHttp\{
    Client,
    Exception\GuzzleException,
    Exception\ClientException,
};
use Illuminate\Support\Facades\{
    App,
};
use App\Helpers\ChargeFeeCalculation;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Redis;
use App\Http\Controllers\Payment\{
    YedpayController,
};

class AppController extends CommonController
{

    protected static function getLanguageCode(Request $request)
    {

        $language_code = match ($request->input('language_code', 'en_US')) {
            'zh_HK' => 'zh_HK',
            default => 'en_US',
        };
        // 设置多语言
        self::setLanguage($language_code);
        return $language_code;
    }

    /**
     * 获取用户信息
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getUser(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $user_id = $request->input('user_id');  // 用户ID

        if (blank($user_id) || blank($user = AppUser::find($user_id))) {
            $this->notFoundData('user_id');
            return $this->returnJson();
        }

        // 拼接头像完整路径
        $user->avatar_url = existsImage('avatar', $user->avatar_url);

        $this->data = $user->toArray();
        unset($this->data['password'], $this->data['salt']);

        return $this->returnJson();
    }

    /**
     * 展示车辆列表
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function listVehicle(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        // 分页数据
        $page_size = (int)$request->input('page_size', 10);
        $current_page = (int)$request->input('current_page', 1);
        $user_id = $request->user_id;
        // 先通过is_common来排序然后通过gmt_modified修改时间排序
        $data_list = Vehicle::where('user_id', $user_id)
            ->latest('is_common')
            ->latest('gmt_modified')
            ->paginate($page_size, ['*'], 'current_page', $current_page);
        $this->data = $data_list;
        return $this->returnJson();
    }

    /**
     * 获取车辆
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getVehicle(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $vehicle_id = $request->vehicle_id;

        if (blank($vehicle_id) || blank($vehicle = Vehicle::find($vehicle_id))) {
            $this->missingField('vehicle_id');
            return $this->returnJson();
        }

        $this->data = $vehicle;

        return $this->returnJson();
    }

    /**
     * 新增、更新
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function updateVehicle(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $vehicle_id = $request->vehicle_id;
        $user_id = $request->user_id;
        $plate_number = $request->plate_number;
        $is_common = $request->boolean('is_common');
        // 默认为false
        $this->data = false;

        if (blank($user_id)) {
            $this->missingField('user_id');
            return $this->returnJson();
        }

        if (blank($plate_number)) {
            $this->missingField('plate_Number');
            return $this->returnJson();
        }

        // 判断该用户下是否存在相同车牌号
        $where = array(
            ['user_id', $user_id],
            ['plate_number', $plate_number]
        );
        // 如果有vehicle_id就是编辑，要把自己排除
        if (Vehicle::where($where)
            ->when(filled($vehicle_id), function ($query) use ($vehicle_id) {
                $query->where('vehicle_id', '!=', $vehicle_id);
            })
            ->exists()
        ) {
            $this->alreadyExists('Plate Number');
            return $this->returnJson();
        }

        // 如果is_common为true 就把该用户下的其他is_common设置为false
        if ($is_common) {
            Vehicle::where('user_id', $user_id)
                ->update(['is_common' => false]);
        }
        $vehicle = Vehicle::firstOrNew(
            ['vehicle_id' => $vehicle_id],
        );
        $vehicle->user_id = $user_id;
        $vehicle->plate_number = $plate_number;
        $vehicle->is_common = $is_common;
        if ($vehicle->save()) {
            $this->data = true;
        }
        return $this->returnJson();
    }

    /**
     * 删除车辆
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function deleteVehicle(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $user_id = $request->user_id;
        $vehicle_id = $request->vehicle_id;
        if (blank($user_id)) {
            $this->missingField('user_id');
            return $this->returnJson();
        }

        $vehicle = Vehicle::where('user_id', $user_id)->find($vehicle_id);
        // 判断是否存在vehicle_id，如果存在则执行删除
        if (filled($vehicle)) {
            $vehicle->delete();
            $this->data = true;
        } else {
            $this->data = false;
            $this->notFoundData('vehicle_id');
        }

        return $this->returnJson();
    }

    /**
     * 展示积分交易列表
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function listPointsTransactionSection(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        // dataTable字段
        $page_size = (int)$request->input('page_size', 10);
        $current_page = (int)$request->input('current_page', 1);
        $user_id = $request->user_id;
        $date_time = $request->date_time;
        $transaction_type = $request->transaction_type;
        $transaction_category = $request->transaction_category;

        if (blank($user_id)) {
            $this->missingField('user_id');
            return $this->returnJson();
        }

        $points_transaction_result_list = PointsTransaction::where('user_id', $user_id)
            ->when(filled($date_time), function ($query) use ($date_time) {
                $query->whereYear('gmt_create', date('Y', strtotime($date_time)))->whereMonth('gmt_create', date('m', strtotime($date_time)));
            })
            ->when(filled($transaction_type), function ($query) use ($transaction_type) {
                $query->where('transaction_type', $transaction_type);
            })
            ->when(filled($transaction_category), function ($query) use ($transaction_category) {
                $query->where('transaction_category', $transaction_category);
            })
            ->latest()
            ->paginate($page_size, ['*'], 'current_page', $current_page);
        // 显示gmt_create字段
        $points_transaction_result_list->data = $points_transaction_result_list->makeVisible(['gmt_create']);
        $results = array();
        foreach ($points_transaction_result_list as $points_transaction_result) {
            $date = date('Y-m', strtotime($points_transaction_result->gmt_create));
            if (!isset($results[$date])) {
                $results[$date] = [
                    'gmt_section' => $date,
                    'income_total' => 0,
                    'expense_total' => 0,
                    'points_transaction_list' => [],
                ];
            }

            $points_transaction_result->transaction_category_title = TransactionCategory::getDescription($points_transaction_result->transaction_category);
            $points_transaction_result->transaction_category_icon_url = existsImage('icon', 'transaction_category/' . $points_transaction_result->transaction_category . '.png');

            $results[$date]['points_transaction_list'][] = $points_transaction_result;
        }
        $data_time_year_month = array_keys($results);

        $count = count($data_time_year_month);
        $sum_total_list = PointsTransaction::selectRaw(
            'DATE_FORMAT(gmt_create, "%Y-%m") as year_months,SUM(CASE WHEN transaction_type = ? THEN amount ELSE 0 END) as income,SUM(CASE WHEN transaction_type = ? THEN amount ELSE 0 END) as expense',
            [TransactionType::Income, TransactionType::Expense]
        )
            ->groupBy('year_months')
            ->when($count > 0, function ($query) use ($count, $data_time_year_month) {
                if ($count > 1) {
                    $query->whereRaw('DATE_FORMAT(gmt_create, "%Y-%m") <= "' . $data_time_year_month[0] . '"')->whereRaw('DATE_FORMAT(gmt_create, "%Y-%m") >= "' . $data_time_year_month[$count - 1] . '"');
                } else {
                    $query->whereRaw('DATE_FORMAT(gmt_create, "%Y-%m") = "' . $data_time_year_month[0] . '"');
                }
            })
            ->when(filled($transaction_type), function ($query) use ($transaction_type) {
                $query->where('transaction_type', $transaction_type);
            })
            ->when(filled($transaction_category), function ($query) use ($transaction_category) {
                $query->where('transaction_category', $transaction_category);
            })
            ->get();

        foreach ($sum_total_list as $sum_total) {
            $time = $sum_total->year_months;
            if (isset($results[$time])) {
                $results[$time]['gmt_section'] = date('Y-m-d H:i:s', strtotime($time));
                $results[$time]['income_total'] = (int)$sum_total->income ?? 0;
                $results[$time]['expense_total'] = (int)$sum_total->expense ?? 0;
            }
        }


        $this->data = array(
            'current_page' => $points_transaction_result_list->currentPage(),
            'total' => $points_transaction_result_list->total(),
            'page_total' => $points_transaction_result_list->lastPage(),
            "data" => array_values($results),
        );
        return $this->returnJson();
    }

    /**
     * 获取积分交易详情
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getPointsTransaction(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        // dataTable字段
        $user_id = $request->user_id;
        $points_transaction_id = $request->points_transaction_id;

        if (blank($user_id)) {
            $this->missingField('user_id');
            return $this->returnJson();
        }

        $points_transaction_result_list = PointsTransaction::with(['chargePaymentRecord'])
            ->where('user_id', $user_id)
            ->find($points_transaction_id);

        $this->data = $points_transaction_result_list;
        return $this->returnJson();
    }

    /**
     * 展示用户积分
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getUserPoints(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        // dataTable字段
        $user_id = $request->user_id;
        if (blank($user_id)) {
            $this->missingField('user_id');
            return $this->returnJson();
        }

        $user_result = AppUser::select('user.points_balance')->find($user_id);

        if (filled($user_result)) {
            $this->data = (int)$user_result->points_balance;
        } else {
            $this->notFoundData('user_id');
        }
        return $this->returnJson();
    }

    /**
     * 充电记录列表
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function listChargeRecord(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        // dataTable字段
        $page_size = (int)$request->input('page_size', 10);
        $current_page = (int)$request->input('current_page', 1);
        $user_id = $request->user_id;

        if (blank($user_id)) {
            $this->missingField('user_id');
            return $this->returnJson();
        }

        $charge_record_result_list = ChargeRecord::with('chargePaymentRecord')
            ->where('user_id', $user_id)
            ->latest()
            ->paginate($page_size, ['*'], 'current_page', $current_page);
        // 显示gmt_create、gmt_modified字段
        $charge_record_result_list->data = $charge_record_result_list->makeVisible(['gmt_create', 'gmt_modified'])->transform(function ($item) {
            $item->merchant_name = self::getValueFromLanguageArray($item->merchant_name);
            $item->member_card_group_name = self::getValueFromLanguageArray($item->member_card_group_name);
            $item->site_name = self::getValueFromLanguageArray($item->site_name);
            $item->zone_name = self::getValueFromLanguageArray($item->zone_name);
            // 获取充电记录关联的支付表
            $item->charge_payment_record_list = $item->chargePaymentRecord;
            unset($item->chargePaymentRecord);
            return $item;
        });

        $this->data = array(
            'current_page' => $charge_record_result_list->currentPage(),
            'total' => $charge_record_result_list->total(),
            'page_total' => $charge_record_result_list->lastPage(),
            "data" => $charge_record_result_list->items(),
        );

        return $this->returnJson();
    }

    /**
     * 进行中充电记录列表
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function listInProgressChargeRecord(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $user_id = $request->user_id;

        if (blank($user_id)) {
            $this->missingField('user_id');
            return $this->returnJson();
        }

        $charge_record_result_list = ChargeRecord::with('chargePaymentRecord')
            ->where('user_id', $user_id)
            ->limit(2)
            // gmt_stop 或 gmt_unlocked为空的记录
            ->where(function ($query) {
                $query->whereNull('gmt_stop')
                    ->orWhereNull('gmt_unlocked');
            })
            ->latest()
            ->get();
        // 显示gmt_create、gmt_modified字段
        $charge_record_result_list->data = $charge_record_result_list->makeVisible(['gmt_create', 'gmt_modified'])->transform(function ($item) {
            $item->merchant_name = self::getValueFromLanguageArray($item->merchant_name);
            $item->member_card_group_name = self::getValueFromLanguageArray($item->member_card_group_name);
            $item->site_name = self::getValueFromLanguageArray($item->site_name);
            $item->zone_name = self::getValueFromLanguageArray($item->zone_name);
            // 获取充电记录关联的支付表
            $item->charge_payment_record_list = $item->chargePaymentRecord;
            unset($item->chargePaymentRecord);
            return $item;
        });

        $this->data = $charge_record_result_list;

        return $this->returnJson();
    }

    /**
     * 充电机列表
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function listChargePoint(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $site_id = $request->site_id;
        $user_id = $request->user_id;

        if (blank($site_id) || blank($site = Site::find($site_id))) {
            $this->missingField('site_id');
            return $this->returnJson();
        }
        if (blank($user_id) || blank($user = AppUser::find($user_id))) {
            $this->missingField('user_id');
            return $this->returnJson();
        }

        $page_size = (int)$request->input('page_size', 10);
        $current_page = (int)$request->input('current_page', 1);
        $connector_status_sql_in = '"' . ConnectorStatus::Preparing . '",
        "' . ConnectorStatus::Charging . '",
        "' . ConnectorStatus::Queuing . '",
        "' . ConnectorStatus::Finishing . '",
        "' . ConnectorStatus::Available . '",
        "' . ConnectorStatus::Unavailable . '",
        "' . ConnectorStatus::Faulted . '",
        "' . ConnectorStatus::Offline . '"';

        // 判断site是否有商户ID，有商户ID需要限制用户的会员卡的商户ID
        if (filled($site->merchant_id)) {
            // 获取user的会员卡的商户ID去重去空
            $merchant_id_list = $user->memberCard()->where('is_enable', 1)?->pluck('merchant_id')?->unique()?->toArray() ?? [];
            // 如果会员卡的商户ID不包含site的商户ID，返回空
            if (!in_array($site->merchant_id, $merchant_id_list)) {
                $this->data = array(
                    'current_page' => $current_page,
                    'total' => 0,
                    'page_total' => 0,
                    "data" => [],
                );
                return $this->returnJson();
            }
        }

        $charge_point_result_list = $site->chargePoint()->with([
            'connector' => [
                'setting',
                'zone' => [
                    'description' => function ($query) use ($language_code) {
                        // 筛选语言
                        $query->where('language_code', $language_code);
                    }
                ]
            ]
        ])
            ->orderByRaw('CASE WHEN EXISTS (
            SELECT 1 FROM connector WHERE connector.charge_point_id = charge_point.charge_point_id AND connector.status IN (' . $connector_status_sql_in . ')
        ) THEN 0 ELSE 1 END, CASE WHEN EXISTS (
            SELECT 1 FROM connector WHERE connector.charge_point_id = charge_point.charge_point_id AND connector.status IN (' . $connector_status_sql_in . ')
        ) THEN (
            SELECT MIN(CASE status
                WHEN "' . ConnectorStatus::Preparing . '" THEN 1
                WHEN "' . ConnectorStatus::Charging . '" THEN 2
                WHEN "' . ConnectorStatus::Queuing . '" THEN 3
                WHEN "' . ConnectorStatus::Finishing . '" THEN 4
                WHEN "' . ConnectorStatus::Available . '" THEN 5
                WHEN "' . ConnectorStatus::Unavailable . '" THEN 6
                WHEN "' . ConnectorStatus::Faulted . '" THEN 7
                WHEN "' . ConnectorStatus::Offline . '" THEN 8
                ELSE 9
            END) FROM connector WHERE connector.charge_point_id = charge_point.charge_point_id AND connector.status IN (' . $connector_status_sql_in . ')
        ) ELSE 9 END')
            ->latest()
            ->paginate($page_size, ['*'], 'current_page', $current_page);
        // 显示gmt_create、gmt_modified字段
        // 备注：transform方法会改变$charge_point_result_list的原数据
        $charge_point_result_list->data = $charge_point_result_list->makeVisible(['gmt_create', 'gmt_modified'])->transform(function ($item) {
            $item->vendor_image_url = existsImage('icon', 'charge_point_vendor/' . $item->vendor . '.png');

            // 获取充电枪关联的收费表
            $item->connector_list = $item->connector->map(function ($connector) {
                $connector->zone_name = $connector->zone?->description()->first()?->name ?? null;
                $connector = self::getTariffTableByConnector($connector);
                unset($connector['setting'], $connector['zone']);
                return $connector;
            });
            unset($item->connector);

            return $item;
        });

        $this->data = array(
            'current_page' => $charge_point_result_list->currentPage(),
            'total' => $charge_point_result_list->total(),
            'page_total' => $charge_point_result_list->lastPage(),
            "data" => $charge_point_result_list->items(),
        );

        return $this->returnJson();
    }

    /**
     * 场地列表
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function listSite(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        // dataTable字段
        $page_size = (int)$request->input('page_size', 10);
        $current_page = (int)$request->input('current_page', 1);
        $latitude = $request->latitude; // 纬度
        $longitude = $request->longitude; // 经度
        $keyword = $request->keyword; // 关键字
        $user_id = $request->user_id;

        if (blank($user_id) || blank($user = AppUser::find($user_id))) {
            $this->missingField('user_id');
            return $this->returnJson();
        }

        // 获取user的会员卡的商户ID去重去空
        $merchant_id_list = $user->memberCard()->where('is_enable', 1)?->pluck('merchant_id')?->unique()?->toArray() ?? [];
        // 判断是否存在NULL的商户ID
        $is_null_merchant_id = in_array(null, $merchant_id_list);

        $site_result_list = Site::select('site.*', 'site_description.name', 'site_description.address', 'site_description.opening_hours', 'site_description.description', 'site_description.google_map_url', 'site_description.apple_map_url', 'site_description.amap_map_url')
            ->selectSub('(
                SELECT COUNT(*)
                FROM connector
                WHERE connector.charge_point_id IN (
                    SELECT charge_point_id FROM charge_point WHERE charge_point.site_id = site.site_id
                )
            )', 'connector_count')
            ->selectSub('(
                SELECT COUNT(*)
                FROM connector
                WHERE connector.charge_point_id IN (
                    SELECT charge_point_id FROM charge_point WHERE charge_point.site_id = site.site_id
                )
                AND connector.status = "' . ConnectorStatus::Available . '"
            )', 'vacancy_connector_count')
            ->leftJoin('site_description', function ($query) use ($language_code) {
                $query->on('site_description.site_id', '=', 'site.site_id')->where('site_description.language_code', $language_code);
            })
            // 查询merchant_id在$merchant_id_list中的，如果$is_null_merchant_id为true，额外查询merchant_id为空的
            ->when(filled($merchant_id_list), function ($query) use ($merchant_id_list, $is_null_merchant_id) {
                // 如果存在NULL的商户ID，查询site的商户ID为空或者在user的会员卡的商户ID里面
                $query->when($is_null_merchant_id, function ($query) use ($merchant_id_list,) {
                    $query->where(function ($query) use ($merchant_id_list) {
                        $query->whereIn('site.merchant_id', $merchant_id_list)
                            ->orWhereNull('site.merchant_id');
                    });
                }, function ($query) use ($merchant_id_list) {
                    $query->whereIn('site.merchant_id', $merchant_id_list);
                });
            }, function ($query) use ($is_null_merchant_id) {
                // 如果不存在会员卡，不返回任何数据
                $query->whereRaw('1 = 2');
            })
            // 搜索 首字母大写 或 名称
            ->when(filled($keyword), function ($query) use ($keyword) {
                $query->where(function ($query) use ($keyword) {
                    $query->where('site.acronym', 'like', '%' . $keyword . '%')
                        ->orWhere('site_description.name', 'like', '%' . $keyword . '%');
                });
            })
            // 如果传入了经纬度，优先按照距离从近到远排序
            ->when(filled($latitude) && filled($longitude), function ($query) use ($latitude, $longitude) {
                // 使用半正矢公式获取距离判断，单位米(m)
                $query->selectRaw('
                ( 6371.01 * acos( cos( radians(?) ) *
                cos( radians( latitude ) )
                * cos( radians( longitude ) - radians(?)
                ) + sin( radians(?) ) *
                sin( radians( latitude ) ) )
                ) * 1000 AS distance', [$latitude, $longitude, $latitude])
                    // 只有填了经纬度的才排序，未填的排最后
                    ->orderByRaw('
                    CASE
                        WHEN latitude IS NULL OR longitude IS NULL THEN 1
                        ELSE 0
                    END, distance
                ');
            })
            ->oldest('sort_order')
            ->oldest('acronym')
            ->latest('site.gmt_create')
            ->paginate($page_size, ['*'], 'current_page', $current_page);

        // 显示gmt_create、gmt_modified字段
        $site_result_list->data = $site_result_list->makeVisible(['gmt_create', 'gmt_modified'])
            ->transform(function ($item) {
                $item->main_image_url = existsImage('public', $item->main_image_url);
                return $item;
            });

        $this->data = array(
            'current_page' => $site_result_list->currentPage(),
            'total' => $site_result_list->total(),
            'page_total' => $site_result_list->lastPage(),
            "data" => $site_result_list->items(),
        );

        return $this->returnJson();
    }

    /**
     * 充电枪列表
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function listConnector(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $site_id = $request->site_id;
        $user_id = $request->user_id;
        $page_size = (int)$request->input('page_size', 10);
        $current_page = (int)$request->input('current_page', 1);
        $keyword = $request->keyword; // 关键字

        if (blank($site_id) || blank($site = Site::find($site_id))) {
            $this->missingField('site_id');
            return $this->returnJson();
        }
        if (blank($user_id) || blank($user = AppUser::find($user_id))) {
            $this->missingField('user_id');
            return $this->returnJson();
        }

        // 获取user的会员卡的商户ID去重去空
        $merchant_id_list = $user->memberCard()->where('is_enable', 1)?->pluck('merchant_id')?->unique()?->toArray() ?? [];
        $is_null_merchant_id = in_array(null, $merchant_id_list);

        $site->load('chargePoint');
        $connector_result_list = Connector::select('connector.*', 'zone_description.name as zone_name')
            ->with(['setting'])
            ->leftJoin('zone', 'zone.zone_id', '=', 'connector.zone_id')
            ->leftJoin('zone_description', function ($query) use ($language_code) {
                $query->on('zone_description.zone_id', '=', 'zone.zone_id')->where('zone_description.language_code', $language_code);
            })
            ->leftJoin('charge_record', function ($query) use ($user_id) {
                $query->on('charge_record.charge_record_id', '=', 'connector.current_charge_record_id')->where('charge_record.user_id', $user_id);
            })
            // 查询merchant_id在$merchant_id_list中的，如果$is_null_merchant_id为true，额外查询merchant_id为空的
            ->when(filled($merchant_id_list), function ($query) use ($merchant_id_list, $is_null_merchant_id) {
                // 如果存在NULL的商户ID，查询商户ID为空或者在user的会员卡的商户ID里面
                $query->when($is_null_merchant_id, function ($query) use ($merchant_id_list,) {
                    $query->where(function ($query) use ($merchant_id_list) {
                        $query->whereIn('connector.merchant_id', $merchant_id_list)
                            ->orWhereNull('connector.merchant_id');
                    });
                }, function ($query) use ($merchant_id_list) {
                    $query->whereIn('connector.merchant_id', $merchant_id_list);
                });
            }, function ($query) use ($is_null_merchant_id) {
                // 如果不存在会员卡，不返回任何数据
                $query->whereRaw('1 = 2');
            })
            // 搜索
            ->when(filled($keyword), function ($query) use ($keyword) {
                $query->where('connector.name', 'like', '%' . $keyword . '%');
            })
            ->whereIn('charge_point_id', $site?->chargePoint?->pluck('charge_point_id') ?? [])
            // 首先查询connector是否有current_charge_record_id，根据current_charge_record_id查询charge_record，如果charge_record的user_id=传入的user_id，优先将这条数据排上面
            ->orderBy('charge_record.user_id', 'desc')
            // 然后排有zone_id的，然后再根据zone的sort_order排序
            ->orderByRaw('
                CASE
                    WHEN connector.zone_id IS NULL THEN 1
                    ELSE 0
                END, zone.sort_order
            ')
            ->orderByRaw('
                CASE
                    WHEN status = "' . ConnectorStatus::Preparing . '" THEN 1
                    WHEN status = "' . ConnectorStatus::Charging . '" THEN 2
                    WHEN status = "' . ConnectorStatus::Queuing . '" THEN 3
                    WHEN status = "' . ConnectorStatus::Finishing . '" THEN 4
                    WHEN status = "' . ConnectorStatus::Available . '" THEN 5
                    WHEN status = "' . ConnectorStatus::Unavailable . '" THEN 6
                    WHEN status = "' . ConnectorStatus::Faulted . '" THEN 7
                    WHEN status = "' . ConnectorStatus::Offline . '" THEN 8
                    ELSE 9
                END
            ')
            ->latest()
            ->paginate($page_size, ['*'], 'current_page', $current_page);
        // 显示gmt_create、gmt_modified字段
        // 备注：transform方法会改变$connector_result_list的原数据
        $connector_result_list->data = $connector_result_list->transform(function ($item) use ($site) {
            $item->vendor = $site->chargePoint->where('charge_point_id', $item->charge_point_id)->first()?->vendor ?? null;
            $item->vendor_image_url = existsImage('icon', 'charge_point_vendor/' . $item->vendor . '.png');
            // $item->zone_name = $item->zone?->description()->first()?->name ?? null;
            $item = self::getTariffTableByConnector($item);
            unset($item['setting'], $item['zone']);

            return $item;
        });

        $this->data = array(
            'current_page' => $connector_result_list->currentPage(),
            'total' => $connector_result_list->total(),
            'page_total' => $connector_result_list->lastPage(),
            "data" => $connector_result_list->items(),
        );

        return $this->returnJson();
    }

    /**
     * 会员卡列表
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function listMemberCard(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $user_id = $request->user_id;
        $page_size = (int)$request->input('page_size', 10);
        $current_page = (int)$request->input('current_page', 1);

        $member_card_result_list = MemberCard::with(['merchant', 'memberCardGroup'])
            ->where('user_id', $user_id)
            ->where('is_enable', 1)
            ->latest()
            ->paginate($page_size, ['*'], 'current_page', $current_page);

        // 显示gmt_create、gmt_modified字段
        $member_card_result_list->data = $member_card_result_list->transform(function ($item) {
            $item->logo_image_url = existsImage('public', $item?->merchant?->logo_image_url);
            $item->member_card_background_image_url = existsImage('public', $item?->memberCardGroup?->member_card_background_image_url);
            if (blank($item->member_card_background_image_url)) $item->member_card_background_image_url = existsImage('public', $item?->merchant?->member_card_background_image_url);
            unset($item->merchant, $item->memberCardGroup);
            return $item;
        });

        $this->data = array(
            'current_page' => $member_card_result_list->currentPage(),
            'total' => $member_card_result_list->total(),
            'page_total' => $member_card_result_list->lastPage(),
            "data" => $member_card_result_list->items(),
        );

        return $this->returnJson();
    }

    /**
     * 远程开始充电
     *
     * @param Request $request
     * @return JsonResponse
     * @throws GuzzleException
     */
    public function startCharge(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $unique_id = $request->input('unique_id');
        $connector_id = $request->input('connector_id');
        $charge_value = $request->input('charge_value');
        $operator_type = $request->input('operator_type');
        $operator_number = $request->input('operator_number');

        if (blank($connector_id) || blank($connector_model = Connector::find($connector_id))) {
            $this->missingField('connector_id');
            return $this->returnJson();
        }

        if (blank($operator_number)) {
            $this->missingField('operator_number');
            return $this->returnJson();
        }

        if (blank($operator_type)) {
            $this->missingField('operator_type');
            return $this->returnJson();
        }

        if (
            filled($connector_model->chargePoint) && filled($connector_model->chargePoint->comServer) &&
            filled($api_url = $connector_model->chargePoint->comServer->api_url)
        ) {
            $params = array(
                'unique_id' => $unique_id,
                'connector_id' => $connector_model->connector_id,
                'charge_value' => $charge_value,
                'charge_operator_type' => $operator_type,
                'charge_operator_number' => $operator_number,
            );

            $code = 200;

            try {
                $client = new Client();
                $response = $client->request('POST', "$api_url/charge/remoteStartCharge", array(
                    'query' => $params,
                ));

                if ($response->getStatusCode() == 200) {
                    $content = $response->getBody()->getContents(); // 此处只能读取一次，多次读取会拿到空字符串
                    // $code = $response->getStatusCode();
                    // $this->data = $response->getBody()->getContents();
                    $return_data = json_decode($content, true);
                    $code = $return_data['code'] ?? $code;
                    $this->data = $return_data['data'] ?? $this->data;
                    $this->message = $return_data['message'] ?? $this->message;
                }
            } catch (ClientException | Exception $e) {
                report($e);
                $this->message = __('common.request_request_error');
            }

            $this->code = $code;
        }

        return $this->returnJson();
    }

    /**
     * 远程停止充电
     *
     * @param Request $request
     * @return JsonResponse
     * @throws GuzzleException
     */
    public function stopCharge(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $unique_id = $request->input('unique_id');
        $connector_id = $request->input('connector_id');
        $reason = $request->input('reason');
        $detail = $request->input('detail');
        $operator_type = $request->input('operator_type');
        $operator_number = $request->input('operator_number');
        $user_id = $request->input('user_id');


        if (blank($connector_id) || blank($connector_model = Connector::find($connector_id))) {
            $this->missingField('connector_id');
            return $this->returnJson();
        }

        if (blank($operator_number)) {
            $this->missingField('operator_number');
            return $this->returnJson();
        }

        if (blank($operator_type)) {
            $this->missingField('operator_type');
            return $this->returnJson();
        }

        if (blank($connector_model->current_charge_record_id) || blank($user_id) || ChargeRecord::where('user_id', $user_id)->where('charge_record_id', $connector_model->current_charge_record_id)->doesntExist()) {
            $this->notFoundData('charge_record_id');
            return $this->returnJson();
        }

        if (
            filled($connector_model->chargePoint) && filled($connector_model->chargePoint->comServer) &&
            filled($api_url = $connector_model->chargePoint->comServer->api_url)
        ) {
            $params = array(
                'unique_id' => $unique_id,
                'connector_id' => $connector_model->connector_id,
                'remote_stop_charge_reason' => $reason,
                'remote_stop_charge_detail' => $detail,
                'charge_operator_type' => $operator_type,
                'charge_operator_number' => $operator_number,
            );

            $code = 200;

            try {
                $client = new Client();
                $response = $client->request('POST', "$api_url/charge/remoteStopCharge", array(
                    'query' => $params,
                ));

                if ($response->getStatusCode() == 200) {
                    $content = $response->getBody()->getContents(); // 此处只能读取一次，多次读取会拿到空字符串
                    // $code = $response->getStatusCode();
                    // $this->data = $response->getBody()->getContents();
                    $return_data = json_decode($content, true);
                    $code = $return_data['code'] ?? $code;
                    $this->data = $return_data['data'] ?? $this->data;
                    $this->message = $return_data['message'] ?? $this->message;
                }
            } catch (ClientException | Exception $e) {
                report($e);
                $this->message = __('common.request_request_error');
            }

            $this->code = $code;
        }

        return $this->returnJson();
    }

    /**
     * 远程解锁充电
     *
     * @param Request $request
     * @return JsonResponse
     * @throws GuzzleException
     */
    public function unlockConnector(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $unique_id = $request->input('unique_id');
        $connector_id = $request->input('connector_id');
        $operator_type = $request->input('operator_type');
        $operator_number = $request->input('operator_number');
        $user_id = $request->input('user_id');

        if (blank($connector_id) || !filled($connector_model = Connector::find($connector_id))) {
            $this->missingField('connector_id');
            return $this->returnJson();
        }

        if (blank($operator_number)) {
            $this->missingField('operator_number');
            return $this->returnJson();
        }

        if (blank($operator_type)) {
            $this->missingField('operator_type');
            return $this->returnJson();
        }

        if (blank($connector_model->current_charge_record_id) || blank($user_id) || ChargeRecord::where('user_id', $user_id)->where('charge_record_id', $connector_model->current_charge_record_id)->doesntExist()) {
            $this->notFoundData('charge_record_id');
            return $this->returnJson();
        }

        if (
            filled($connector_model->chargePoint) && filled($connector_model->chargePoint->comServer) &&
            filled($api_url = $connector_model->chargePoint->comServer->api_url)
        ) {
            $params = array(
                'unique_id' => $unique_id,
                'connector_id' => $connector_model->connector_id,
                'charge_operator_type' => $operator_type,
                'charge_operator_number' => $operator_number,
            );

            $code = 200;

            try {
                $client = new Client();
                $response = $client->request('POST', "$api_url/charge/unlockConnector", array(
                    'query' => $params,
                ));

                if ($response->getStatusCode() == 200) {
                    $content = $response->getBody()->getContents(); // 此处只能读取一次，多次读取会拿到空字符串
                    $return_data = json_decode($content, true);
                    $code = $return_data['code'] ?? $code;
                    $this->data = $return_data['data'] ?? $this->data;
                    $this->message = $return_data['message'] ?? $this->message;
                }
            } catch (ClientException | Exception $e) {
                report($e);
                $this->message = __('common.request_request_error');
            }

            $this->code = $code;
        }

        return $this->returnJson();
    }

    /**
     * 确认Connector存在
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function fakeAppCheckConnector(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $connector_id = $request->input('connector_id');

        if (blank($connector_id) || blank($connector_model = Connector::find($connector_id))) {
            $this->missingField('connector_id');
            return $this->returnJson();
        }

        $this->data = array(
            'connector_status' => $connector_model->status,
            'connector_status_text' => ConnectorStatus::hasValue($connector_model->status) ? (ConnectorStatus::getLocalDescription($connector_model->status, 'en_US') . ' ' . ConnectorStatus::getLocalDescription($connector_model->status, 'zh_HK')) : '',
            'is_charging' => $connector_model->current_charge_record_id ? true : false,
            'connector_name' => $connector_model->name,
        );


        return $this->returnJson();
    }

    /**
     * 远程开始充电
     *
     * @param Request $request
     * @return JsonResponse
     * @throws GuzzleException
     */
    public function fakeAppStartCharge(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $connector_id = $request->input('connector_id');
        $unique_id = $request->input('unique_id');

        if (blank($connector_id) || blank($connector_model = Connector::find($connector_id))) {
            $this->missingField('connector_id');
            return $this->returnJson();
        }


        if (filled($api_url = $connector_model?->chargePoint?->comServer?->api_url)) {
            $params = array(
                'charge_operator_type' => 'CMS',
                'connector_id' => $connector_model->connector_id,
                'charge_operator_number' => 'fakeApp',
                'unique_id' => $unique_id,
            );

            $code = 201;

            if ($connector_model->status !== ConnectorStatus::Preparing) {
                $this->code = 202;
                $this->message = __('common.request_request_error');
                return $this->returnJson();
            }

            try {
                $client = new Client();
                $response = $client->request('POST', "$api_url/charge/remoteStartCharge", array(
                    'query' => $params,
                ));

                if ($response->getStatusCode() == 200) {
                    $code = $response->getStatusCode();
                    $result = json_decode($response->getBody()->getContents(), true);
                    $this->data = $result['data'] ?? false;
                }
            } catch (ClientException | Exception $e) {
                report($e);
                $this->message = __('common.request_request_error');
            }

            $this->code = $code;
        }

        return $this->returnJson();
    }

    /**
     * 获取Charge Record和充电枪状态的详细信息
     *
     * @param Request $request
     * @return JsonResponse
     * @throws GuzzleException
     */
    public function fakeAppGetChargeRecord(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $connector_id = $request->input('connector_id');

        if (blank($connector_id) || blank($connector_model = Connector::find($connector_id))) {
            $this->missingField('connector_id');
            return $this->returnJson();
        }

        $charge_record = null;
        if (filled($connector_model->chargeRecord)) {
            $charge_record = $connector_model->chargeRecord;
            $charge_record->merchant_name = self::getValueFromLanguageArray($charge_record->merchant_name);
            $charge_record->member_card_group_name = self::getValueFromLanguageArray($charge_record->member_card_group_name);
            $charge_record->site_name = self::getValueFromLanguageArray($charge_record->site_name);
            $charge_record->zone_name = self::getValueFromLanguageArray($charge_record->zone_name);
            $charge_record->charge_payment_record_list = $connector_model->chargeRecord->chargePaymentRecord;
            unset($charge_record->chargePaymentRecord);
        }

        $this->data = array(
            'connector_status' => $connector_model->status,
            'charge_record' => $charge_record
        );


        return $this->returnJson();
    }

    /**
     * 远程停止充电
     *
     * @param Request $request
     * @return JsonResponse
     * @throws GuzzleException
     */
    public function fakeAppStopCharge(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $connector_id = $request->input('connector_id');
        $unique_id = $request->input('unique_id');

        if (blank($connector_id) || blank($connector_model = Connector::find($connector_id))) {
            $this->missingField('connector_id');
            return $this->returnJson();
        }

        if (filled($api_url = $connector_model?->chargePoint?->comServer?->api_url)) {
            $params = array(
                'charge_operator_type' => 'CMS',
                'connector_id' => $connector_model->connector_id,
                'charge_operator_number' => 'fakeApp',
                'remote_stop_charge_detail' => 'fakeApp',
                'remote_stop_charge_reason' => '',
                'unique_id' => $unique_id,
            );

            $code = 201;


            $stop_charging_url = match ($connector_model->status) {
                ConnectorStatus::Charging => "$api_url/charge/remoteStopCharge",
                ConnectorStatus::Finishing => "$api_url/charge/unlockConnector",
                default => '',
            };

            if (empty($stop_charging_url)) {
                $this->code = 202;
                $this->message = __('common.request_request_error');
                return $this->returnJson();
            }

            try {
                $client = new Client();
                $response = $client->request('POST', $stop_charging_url, array(
                    'query' => $params,
                ));

                if ($response->getStatusCode() == 200) {
                    $code = $response->getStatusCode();
                    $result = json_decode($response->getBody()->getContents(), true);
                    $this->data = $result['data'] ?? false;
                }
            } catch (ClientException | Exception $e) {
                report($e);
                $this->message = __('common.request_request_error');
            }

            $this->code = $code;
        }

        return $this->returnJson();
    }

    /**
     * 获取充电枪状态的详细信息
     *
     * @param Request $request
     * @return JsonResponse
     * @throws GuzzleException
     */
    public function fakeAppGetConnectorStatus(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $connector_id = $request->input('connector_id');

        if (blank($connector_id) || blank($connector_model = Connector::find($connector_id, ['status']))) {
            $this->missingField('connector_id');
            return $this->returnJson();
        }

        $this->data = array(
            'connector_status' => $connector_model->status,
        );


        return $this->returnJson();
    }

    /**
     * 获取会员卡相关信息
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * @date 2023-06-08
     */
    public function getMemberCard(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $member_card_id = $request->input('member_card_id'); // 充电枪ID
        $user_id = $request->user_id;


        if (blank($member_card_id) || blank($member_card = MemberCard::where('user_id', $user_id)->where('member_card_id', $member_card_id)->where('is_enable', 1)->first())) {
            $this->missingField('member_card_id');
            return $this->returnJson();
        }

        $member_card->load(['merchant', 'memberCardGroup']);
        $member_card->logo_image_url = existsImage('public', $member_card?->merchant?->logo_image_url);
        $member_card->member_card_background_image_url = existsImage('public', $member_card?->memberCardGroup?->member_card_background_image_url);
        if (blank($member_card->member_card_background_image_url)) $member_card->member_card_background_image_url = existsImage('public', $member_card?->merchant?->member_card_background_image_url);
        unset($member_card->merchant, $member_card->memberCardGroup);

        $this->data = $member_card;

        return $this->returnJson();
    }

    public function getMemberCardWithKey(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $member_card_key = $request->input('member_card_key'); // 会员卡key

        if (blank($member_card_key)) {
            $this->missingField('member_card_key');
            return $this->returnJson();
        }
        $member_card = MemberCard::where('member_card_key', $member_card_key)
            ->whereNull('user_id')
            ->firstWhere('is_enable', 1);
        if (blank($member_card)) {
            $this->notFoundData('member_card_id');
            return $this->returnJson();
        }

        $member_card->load(['merchant', 'memberCardGroup']);
        $member_card->logo_image_url = existsImage('public', $member_card?->merchant?->logo_image_url);
        $member_card->member_card_background_image_url = existsImage('public', $member_card?->memberCardGroup?->member_card_background_image_url);
        if (blank($member_card->member_card_background_image_url)) $member_card->member_card_background_image_url = existsImage('public', $member_card?->merchant?->member_card_background_image_url);
        unset($member_card->merchant, $member_card->memberCardGroup);
        $this->data = $member_card;

        return $this->returnJson();
    }

    public function bindMemberCard(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $member_card_id = $request->input('member_card_id'); // 会员卡ID
        $user_id = $request->user_id;

        $this->data = false;

        if (blank($user_id) || blank($user = AppUser::find($user_id))) {
            $this->missingField('user_id');
            return $this->returnJson();
        }

        if (blank($member_card_id) || blank($member_card = MemberCard::where('member_card_id', $member_card_id)->where('is_enable', 1)->first())) {
            $this->missingField('member_card_id');
            return $this->returnJson();
        }

        if (filled($member_card->user_id)) {
            $this->message = __('api.error_member_card_has_been_bound');
            if ($member_card->user_id === $user->user_id) {
                $this->message = __('api.error_member_card_repeat_binding');
            }
            return $this->returnJson();
        }

        // 同一商户下一个用户只能持有一张卡
        if (MemberCard::where('user_id', $user_id)->where('merchant_id', $member_card->merchant_id)->exists()) {
            $this->message = __('api.error_member_card_duplicate_merchant_member_card');
            return $this->returnJson();
        }

        $member_card->user_id = $user_id;
        if ($member_card->save()) {
            $this->data = true;
        } else {
            $this->modelSaveFail();
        }

        return $this->returnJson();
    }

    /**
     * 获取充电枪相关信息
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * @date 2023-06-08
     */
    public function getConnector(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $connector_id = $request->connector_id; // 充电枪ID
        $encode_connector_number = $request->encode_connector_number; // 编码之后的充电枪编号
        $user_id = $request->user_id;

        if (blank($connector_id) && blank($encode_connector_number)) {
            $this->missingField('connector_id');
            return $this->returnJson();
        }
        if (blank($user_id) || blank($user = AppUser::find($user_id))) {
            $this->missingField('user_id');
            return $this->returnJson();
        }
        if (blank($connector_id) && filled($encode_connector_number)) {
            $delimiter = env('DELIMITER', '-');
            // 将充电枪编号切割为数组
            $connector_number_array = explode($delimiter, $encode_connector_number);
            // 如果数组长度不等于三，视为找不到
            if (count($connector_number_array) !== 3) {
                $this->notFoundData('encode_connector_number');
                return $this->returnJson();
            }
            // 加载charge_point_vendor.json文件
            $charge_point_vendor_array = getArrayFromJsonFile('charge_point_vendor_number');
            if (blank($charge_point_vendor_array)) {
                logger()->error('cannot get charge_point_vendor.json file');
                $this->notFoundData('encode_connector_number');
                return $this->returnJson();
            }
            // 根据encode_connector_number获取对应的vendor
            $vendor = collect($charge_point_vendor_array)->where('vendor_number', $connector_number_array[0])->first();
            if (blank($vendor)) {
                $this->notFoundData('encode_connector_number');
                return $this->returnJson();
            }
            // 将encode_connector_number的vendor转回去获取真正的connector_number
            $connector_number_array[0] = $vendor['vendor_name'];
            // 转回字符串
            $encode_connector_number = implode($delimiter, $connector_number_array);
        }

        // 获取user的会员卡的商户ID去重去空
        $merchant_id_list = $user->memberCard()->where('is_enable', 1)?->pluck('merchant_id')?->unique()?->toArray() ?? [];
        $is_null_merchant_id = in_array(null, $merchant_id_list);

        $connector = Connector::when(filled($connector_id), function ($query) use ($connector_id) {
            $query->where('connector_id', $connector_id);
        })
            ->when(blank($connector_id) && filled($encode_connector_number), function ($query) use ($encode_connector_number) {
                $query->where('connector_number', 'like', "%$encode_connector_number%");
            })
            // 查询merchant_id在$merchant_id_list中的，如果$is_null_merchant_id为true，额外查询merchant_id为空的
            ->when(filled($merchant_id_list), function ($query) use ($merchant_id_list, $is_null_merchant_id) {
                // 如果存在NULL的商户ID，查询商户ID为空或者在user的会员卡的商户ID里面
                $query->when($is_null_merchant_id, function ($query) use ($merchant_id_list,) {
                    $query->where(function ($query) use ($merchant_id_list) {
                        $query->whereIn('merchant_id', $merchant_id_list)
                            ->orWhereNull('merchant_id');
                    });
                }, function ($query) use ($merchant_id_list) {
                    $query->whereIn('merchant_id', $merchant_id_list);
                });
            }, function ($query) use ($is_null_merchant_id) {
                // 如果不存在会员卡，不返回任何数据
                $query->whereRaw('1 = 2');
            })
            ->first();

        if (blank($connector)) {
            $this->notFoundData('connector_id');
            return $this->returnJson();
        }

        $connector->vendor = $connector->chargePoint()->select('vendor')->first()->vendor;
        $connector->vendor_image_url = existsImage('icon', 'charge_point_vendor/' . $connector->vendor . '.png');
        $connector->zone_name = $connector?->zone?->description()
            ->where('language_code', $language_code)
            ->select('name')
            ->first()
            ->name ?? null;
        unset($connector->chargePoint, $connector->zone);

        $this->data = self::getTariffTableByConnector($connector);

        return $this->returnJson();
    }

    /**
     * 预付校验充电枪
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * @date 2023-06-08
     */
    public function prePaidVerifyConnector(Request $request): JsonResponse
    {
        #TODO 最大欠款次数验证
        $language_code = self::getLanguageCode($request);
        $connector_id = $request->connector_id; // 充电枪id
        $setting_token = $request->setting_token; // 设置Token
        $user_id = $request->user_id;

        if (blank($user_id) || blank($user = AppUser::find($user_id))) {
            $this->missingField('user_id');
            return $this->returnJson();
        }

        // 获取user的会员卡的商户ID去重去空
        $merchant_id_list = $user->memberCard()->where('is_enable', 1)?->pluck('merchant_id')?->unique()?->toArray() ?? [];
        $is_null_merchant_id = in_array(null, $merchant_id_list);

        $this->data = false;

        // 校验connector
        if (
            blank($connector_id) || blank(
                $connector_model = Connector::with('setting:connector_setting_id,is_enable_verify_setting_token')
                    ->where('connector_id', $connector_id)
                    // 查询merchant_id在$merchant_id_list中的，如果$is_null_merchant_id为true，额外查询merchant_id为空的
                    ->when(filled($merchant_id_list), function ($query) use ($merchant_id_list, $is_null_merchant_id) {
                        // 如果存在NULL的商户ID，查询商户ID为空或者在user的会员卡的商户ID里面
                        $query->when($is_null_merchant_id, function ($query) use ($merchant_id_list,) {
                            $query->where(function ($query) use ($merchant_id_list) {
                                $query->whereIn('merchant_id', $merchant_id_list)
                                    ->orWhereNull('merchant_id');
                            });
                        }, function ($query) use ($merchant_id_list) {
                            $query->whereIn('merchant_id', $merchant_id_list);
                        });
                    })
                    ->first()
            )
        ) {
            $this->message = __('common.the_field_is_missing_the_correct_value', ['field' => __('api.field_connector_id')]);
            return $this->returnJson();
        }

        // 是否启用校验设置Token
        if ((bool)$connector_model->setting?->is_enable_verify_setting_token) {
            // 校验充电枪设置token
            if ($setting_token !== $connector_model->setting_token) {
                $this->message = __('api.setting_token_mismatch');
                return $this->returnJson();
            }
        }
        $this->data = $connector_model->status === ConnectorStatus::Preparing;

        if (!$this->data) {
            $this->message = __('api.connector_is_not_preparing');
        }

        return $this->returnJson();
    }

    /**
     * 后付校验充电枪
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * @date 2023-06-08
     */
    public function postPaidVerifyConnector(Request $request): JsonResponse
    {
        #TODO 最大欠款次数验证
        $language_code = self::getLanguageCode($request);
        $connector_id = $request->connector_id; // 充电枪id
        $setting_token = $request->setting_token; // 设置Token
        $user_id = $request->user_id;

        if (blank($user_id) || blank($user = AppUser::find($user_id))) {
            $this->missingField('user_id');
            return $this->returnJson();
        }

        // 获取user的会员卡的商户ID去重去空
        $merchant_id_list = $user->memberCard()->where('is_enable', 1)?->pluck('merchant_id')?->unique()?->toArray() ?? [];
        $is_null_merchant_id = in_array(null, $merchant_id_list);

        $this->data = false;

        // 校验connector
        if (
            blank($connector_id) || blank(
                $connector_model = Connector::with('setting:connector_setting_id,is_enable_verify_setting_token')
                    ->where('connector_id', $connector_id)
                    // 查询merchant_id在$merchant_id_list中，如果$is_null_merchant_id为true，额外查询merchant_id为空的
                    ->when(filled($merchant_id_list), function ($query) use ($merchant_id_list, $is_null_merchant_id) {
                        // 如果存在NULL的商户ID，查询商户ID为空或者在user的会员卡的商户ID里面
                        $query->when($is_null_merchant_id, function ($query) use ($merchant_id_list,) {
                            $query->where(function ($query) use ($merchant_id_list) {
                                $query->whereIn('merchant_id', $merchant_id_list)
                                    ->orWhereNull('merchant_id');
                            });
                        }, function ($query) use ($merchant_id_list) {
                            $query->whereIn('merchant_id', $merchant_id_list);
                        });
                    }, function ($query) use ($is_null_merchant_id) {
                        // 如果不存在会员卡，不返回任何数据
                        $query->whereRaw('1 = 2');
                    })
                    ->first()
            )
        ) {
            $this->message = __('common.the_field_is_missing_the_correct_value', ['field' => __('api.field_connector_id')]);
            return $this->returnJson();
        }

        // 是否启用校验设置Token
        if ((bool)$connector_model->setting?->is_enable_verify_setting_token) {
            // 校验充电枪设置token
            if ($setting_token !== $connector_model->setting_token) {
                $this->message = __('api.setting_token_mismatch');
                return $this->returnJson();
            }
        }
        $this->data = $connector_model->status === ConnectorStatus::Preparing;

        if (!$this->data) {
            $this->message = __('api.connector_is_not_preparing');
        }

        return $this->returnJson();
    }

    /**
     * 根据充电枪ID获取充电记录
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * @date 2023-06-08
     */
    public function getChargeRecordByConnectorId(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $connector_id = $request->input('connector_id'); // 充电枪ID

        // 校验connector
        if (blank($connector_id) || blank($connector_model = Connector::find($connector_id))) {
            $this->missingField('connector_id');
            return $this->returnJson();
        }

        // 校验充电记录ID
        if (blank($connector_model->current_charge_record_id)) {
            $this->message = __('api.no_charge_record_id');
            return $this->returnJson();
        }

        $this->data = $this->getChargeRecordAndChargePaymentRecordByChargeRecordId($connector_model->current_charge_record_id);

        return $this->returnJson();
    }

    /**
     * 获取充电记录
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * @date 2023-06-08
     */
    public function getChargeRecord(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $charge_record_id = $request->input('charge_record_id'); // 充电记录ID

        // 校验充电记录ID
        if (blank($charge_record_id)) {
            $this->missingField('charge_record_id');
            return $this->returnJson();
        }

        $this->data = $this->getChargeRecordAndChargePaymentRecordByChargeRecordId($charge_record_id);
        if (filled($this->data)) {
            $this->data->makeVisible(['gmt_create']);
        }

        return $this->returnJson();
    }

    public function getChargeRecordBill(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $user_id = $request->user_id;
        $charge_record_id = $request->charge_record_id; // 充电记录ID
        $selected_points = $request->selected_points; // 选择的积分
        $selected_payment_method = (PaymentMethod::hasValue($request->selected_payment_method) && $request->selected_payment_method !== PaymentMethod::Octopus) ? $request->selected_payment_method : null; // 选择的支付方式
        $total_amount = $request->total_amount; // 总金额

        if (blank($user_id)) {
            $this->missingField('user_id');
            return $this->returnJson();
        }
        if (blank($charge_record_id)) {
            $this->missingField('charge_record_id');
            return $this->returnJson();
        }

        // 判断用户存在
        $user = AppUser::find($user_id);
        if (blank($user)) {
            $this->notFoundData('user_id');
            return $this->returnJson();
        }
        // 判断充电记录存在
        $charge_record = ChargeRecord::with(['chargePaymentRecord' => function ($query) {
            $query->where('payment_status', PaymentStatusEnum::Pending);
        }])
            ->where('user_id', $user_id)
            ->where('charge_record_id', $charge_record_id)
            ->first();
        if (blank($charge_record)) {
            $this->notFoundData('charge_record_id');
            return $this->returnJson();
        }

        // 支付方式
        #TODO 暂时先写死默认支付方式为支付宝
        $this->data['selected_payment_method'] = filled($selected_payment_method) ? $selected_payment_method : PaymentMethod::Alipay;
        $this->data['available_payment_method_list'] = self::getAvailablePaymentMethodList();

        // 总金额
        $total_amount = 0;
        foreach ($charge_record->chargePaymentRecord as $charge_payment_record) {
            $total_amount += $charge_payment_record->total_amount;
        }

        // 积分
        $this->data['points_balance'] = $user->points_balance;
        $this->data['selected_points'] = $selected_points;
        // 传入了参数才需要抵扣积分
        if (filled($selected_points)) {
            // 默认抵扣所有积分
            $this->data['selected_points'] = $this->data['points_balance'];
            // 如果积分比总金额大，那么只抵扣总金额对应的积分
            if ($this->data['points_balance'] > $total_amount) {
                $this->data['selected_points'] = $total_amount;
            }
        }

        // 总金额 = 总金额 - 积分抵扣
        $this->data['total_amount'] = is_null($total_amount) ? $total_amount : ($total_amount - $this->data['selected_points']);
        // 格式化Charge Record
        $this->data['charge_record'] = $this->getChargeRecordAndChargePaymentRecordByChargeRecordModel($charge_record);
        if (filled($this->data['charge_record'])) {
            $this->data['charge_record']->makeVisible(['gmt_create']);
        }

        return $this->returnJson();
    }

    public function submitChargeRecordBill(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $user_id = $request->user_id;
        $charge_record_id = $request->charge_record_id; // 充电记录ID
        $selected_points_post = $request->selected_points; // 选择的积分
        $selected_payment_method = (PaymentMethod::hasValue($request->selected_payment_method) && $request->selected_payment_method !== PaymentMethod::Octopus) ? $request->selected_payment_method : null; // 选择的支付方式
        $total_amount_post = (int)$request->total_amount; // 总金额

        if (blank($user_id)) {
            $this->missingField('user_id');
            return $this->returnJson();
        }
        if (blank($charge_record_id)) {
            $this->missingField('charge_record_id');
            return $this->returnJson();
        }

        // 判断用户存在
        $user = AppUser::find($user_id);
        if (blank($user)) {
            $this->notFoundData('user_id');
            return $this->returnJson();
        }
        // 判断充电记录存在
        $charge_record = ChargeRecord::with(['chargePaymentRecord' => function ($query) {
            $query->where('payment_status', PaymentStatusEnum::Pending);
        }])
            ->where('user_id', $user_id)
            ->where('charge_record_id', $charge_record_id)
            ->first();
        if (blank($charge_record)) {
            $this->notFoundData('charge_record_id');
            return $this->returnJson();
        }

        $check_result = PaymentCommon::checkChargeRecordBillParameter($charge_record, $user, [
            'selected_points_post' => $selected_points_post,
            'selected_payment_method' => $selected_payment_method,
            'total_amount_post' => $total_amount_post,
        ]); // 验证结果

        $this->data['result'] = $check_result['result'];
        $this->data['action'] = null; // 行动

        // 校验失败
        if (!$check_result['result']) {
            $this->missingField($check_result['message']);
            return $this->returnJson();
        }

        $total_amount = $check_result['total_amount']; // 总金额

        // 支付方式
        // 如果传入了抵扣积分且总金额为0，那么action为积分支付
        if (filled($selected_points_post) && $total_amount === 0) {
            $this->data['action'] = SubmitChargeRecordBillActionEnum::PointsPayment;
        } else {
            $this->data['action'] = $selected_payment_method;
            // 不同支付方式生成不同链接
            switch ($selected_payment_method) {
                    /* case PaymentMethod::Yedpay:
                    $this->data['online_payment_url'] = action([YedpayController::class, 'checkout'], [
                        'user_id' => $user_id,
                        'charge_record_id' => $charge_record_id,
                        'selected_points' => $selected_points_post,
                        'selected_payment_method' => $selected_payment_method,
                        'total_amount' => $total_amount,
                    ]);
                    break; */
                default:
                    $this->data['online_payment_url'] = null;
                    break;
            }
        }


        return $this->returnJson();
    }

    public function pointsPaymentChargeRecord(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $user_id = $request->user_id;
        $charge_record_id = $request->charge_record_id; // 充电记录ID
        $selected_points_post = $request->selected_points; // 选择的积分
        $selected_payment_method = (PaymentMethod::hasValue($request->selected_payment_method) && $request->selected_payment_method !== PaymentMethod::Octopus) ? $request->selected_payment_method : null; // 选择的支付方式
        $total_amount_post = (int)$request->total_amount; // 总金额

        $this->data = false;

        if (blank($user_id)) {
            $this->missingField('user_id');
            return $this->returnJson();
        }
        if (blank($charge_record_id)) {
            $this->missingField('charge_record_id');
            return $this->returnJson();
        }

        // 判断用户存在
        $user = AppUser::find($user_id);
        if (blank($user)) {
            $this->notFoundData('user_id');
            return $this->returnJson();
        }
        // 判断充电记录存在
        $charge_record = ChargeRecord::with(['chargePaymentRecord' => function ($query) {
            $query->where('payment_status', PaymentStatusEnum::Pending);
        }])
            ->where('user_id', $user_id)
            ->where('charge_record_id', $charge_record_id)
            ->first();
        if (blank($charge_record)) {
            $this->notFoundData('charge_record_id');
            return $this->returnJson();
        }

        $check_result = PaymentCommon::checkChargeRecordBillParameter($charge_record, $user, [
            'selected_points_post' => $selected_points_post,
            'selected_payment_method' => $selected_payment_method,
            'total_amount_post' => $total_amount_post,
        ]); // 验证结果
        $total_amount = $check_result['total_amount']; // 总金额

        // 校验失败或者总金额不为0
        if (!$check_result['result'] || $total_amount > 0 || blank($selected_points_post)) {
            $this->missingField($check_result['message']);
            return $this->returnJson();
        }

        // 扣除积分
        $selected_points_post = $check_result['selected_points']; // 选择的积分

        try {
            $result = DB::transaction(function () use ($user, $selected_points_post, $charge_record) {
                // 生成积分交易记录
                $points_transaction = new PointsTransaction;
                $points_transaction->user_id = $user->user_id;
                $points_transaction->transaction_number = PaymentCommon::generatePointsTransactionNumber();
                $points_transaction->transaction_type = TransactionType::Expense;
                $points_transaction->transaction_category = TransactionCategory::ChargePayment;
                $points_transaction->save();

                // 循环更新payment record状态
                $actual_payment_amount = 0; // 实付金额
                $use_points = 0; // 累加积分
                foreach ($charge_record->chargePaymentRecord as $charge_payment_record) {
                    $charge_payment_record->payment_status = PaymentStatusEnum::Completed; // 已支付
                    $charge_payment_record->gmt_payment_status = now(); // 支付时间
                    $charge_payment_record->is_points_payment_at_full = 1; // 是否全额积分支付
                    $charge_payment_record->points_transaction_id = $points_transaction->points_transaction_id; // 积分交易ID
                    $charge_payment_record->actual_payment_amount = 0; // 实付金额
                    $actual_payment_amount += $charge_payment_record->actual_payment_amount; // 累加实付金额
                    $selected_points_post -= $charge_payment_record->total_amount; // 减去已使用的积分

                    $charge_payment_record->use_points = $charge_payment_record->total_amount; // 积分支付中使用积分为总金额
                    $use_points += $charge_payment_record->use_points; // 累加使用积分
                    $charge_payment_record->save();
                }
                $charge_record->actual_payment_amount = $actual_payment_amount; // 实付金额
                $charge_record->use_points = $use_points; // 使用积分
                // 如果是预付
                if ($charge_record->charge_tariff_scheme === ChargeTariffScheme::PrePaid) {
                    $charge_record->pre_paid_purchase_charge_value = $charge_payment_record->charge_value; // 预付购买充电量
                }
                $charge_record->save();

                // 扣除用户积分
                // 获取悲观锁
                $user = AppUser::where('user_id', $user->user_id)->lockForUpdate()->first();
                $user->points_balance -= $charge_record->use_points;
                $user->save();
                // 回填积分交易记录
                $points_transaction->amount = $charge_record->use_points;
                $points_transaction->points_balance = $user->points_balance;
                $points_transaction->save();

                return true;
            });

            if ($result) {
                // 事务成功提交
                $this->data = true;

                // 推送用户通知
                PaymentCommon::addUserNotifyAfterPaySuccess($charge_record->use_points, $charge_record->actual_payment_amount, $user);
            } else {
                // 事务回滚
                $this->message = __('common.request_request_error');
                return $this->returnJson();
            }
        } catch (Exception $e) {
            report($e);
            $this->message = __('common.request_request_error');
            return $this->returnJson();
        }


        return $this->returnJson();
    }

    public function getSite(Request $request)
    {
        $language_code = self::getLanguageCode($request);
        $site_id = $request->site_id; // 站点ID
        $user_id = $request->user_id;

        if (blank($user_id) || blank($user = AppUser::find($user_id))) {
            $this->missingField('user_id');
            return $this->returnJson();
        }
        // 获取user的会员卡的商户ID去重去空
        $merchant_id_list = $user->memberCard()->where('is_enable', 1)?->pluck('merchant_id')?->unique()?->toArray() ?? [];
        $is_null_merchant_id = in_array(null, $merchant_id_list);
        // 校验site_id
        if (blank($site_id) || blank($site = Site::select('*')->where('site_id', $site_id)
            ->selectSub('(
                    SELECT COUNT(*)
                    FROM connector
                    WHERE connector.charge_point_id IN (
                        SELECT charge_point_id FROM charge_point WHERE charge_point.site_id = site.site_id
                    )
                )', 'connector_count')
            ->selectSub('(
                    SELECT COUNT(*)
                    FROM connector
                    WHERE connector.charge_point_id IN (
                        SELECT charge_point_id FROM charge_point WHERE charge_point.site_id = site.site_id
                    )
                    AND connector.status = "' . ConnectorStatus::Available . '"
                )', 'vacancy_connector_count')
            // 查询merchant_id在$merchant_id_list中的，如果$is_null_merchant_id为true，额外查询merchant_id为空的
            ->when(filled($merchant_id_list), function ($query) use ($merchant_id_list, $is_null_merchant_id) {
                // 如果存在NULL的商户ID，查询商户ID为空或者在user的会员卡的商户ID里面
                $query->when($is_null_merchant_id, function ($query) use ($merchant_id_list,) {
                    $query->where(function ($query) use ($merchant_id_list) {
                        $query->whereIn('merchant_id', $merchant_id_list)
                            ->orWhereNull('merchant_id');
                    });
                }, function ($query) use ($merchant_id_list) {
                    $query->whereIn('merchant_id', $merchant_id_list);
                });
            }, function ($query) use ($is_null_merchant_id) {
                // 如果不存在会员卡，不返回任何数据
                $query->whereRaw('1 = 2');
            })->first())) {
            $this->missingField('site_id');
            return $this->returnJson();
        }

        $site->main_image_url = existsImage('public', $site->main_image_url);

        // 查询关联的对应语言描述表
        $site_description = $site->description->where('language_code', $language_code)->first();

        unset($site->description);
        // 将site和description的数据合并为一个数据
        $this->data = array_merge($site->toArray(), $site_description?->toArray() ?: []);

        return $this->returnJson();
    }

    public function listSiteImage(Request $request)
    {
        $language_code = self::getLanguageCode($request);
        $site_id = $request->input('site_id'); // 站点ID
        $page_size = (int)$request->input('page_size', 10); // 每页显示数量
        $current_page = (int)$request->input('current_page', 1); // 当前页码
        $user_id = $request->user_id;

        if (blank($user_id) || blank($user = AppUser::find($user_id))) {
            $this->missingField('user_id');
            return $this->returnJson();
        }
        // 获取user的会员卡的商户ID去重去空
        $merchant_id_list = $user->memberCard()->where('is_enable', 1)?->pluck('merchant_id')?->unique()?->toArray() ?? [];
        $is_null_merchant_id = in_array(null, $merchant_id_list);
        // 校验site_id
        if (blank($site_id) || blank($site = Site::where('site_id', $site_id)
            // 查询merchant_id在$merchant_id_list中的，如果$is_null_merchant_id为true，额外查询merchant_id为空的
            ->when(filled($merchant_id_list), function ($query) use ($merchant_id_list, $is_null_merchant_id) {
                // 如果存在NULL的商户ID，查询商户ID为空或者在user的会员卡的商户ID里面
                $query->when($is_null_merchant_id, function ($query) use ($merchant_id_list,) {
                    $query->where(function ($query) use ($merchant_id_list) {
                        $query->whereIn('merchant_id', $merchant_id_list)
                            ->orWhereNull('merchant_id');
                    });
                }, function ($query) use ($merchant_id_list) {
                    $query->whereIn('merchant_id', $merchant_id_list);
                });
            }, function ($query) use ($is_null_merchant_id) {
                // 如果不存在会员卡，不返回任何数据
                $query->whereRaw('1 = 2');
            })->first())) {
            $this->missingField('site_id');
            return $this->returnJson();
        }

        $site_image_list = SiteImage::where('site_id', $site_id)
            ->oldest('sort_order')
            ->latest()
            ->paginate($page_size, ['*'], 'current_page', $current_page);

        // 处理图片路径
        $site_image_list->data = $site_image_list->transform(function ($item) {
            $item->image_url = existsImage('public', $item->image_url);
            return $item;
        });

        $this->data = array(
            'current_page' => $site_image_list->currentPage(),
            'total' => $site_image_list->total(),
            'page_total' => $site_image_list->lastPage(),
            "data" => $site_image_list->items(),
        );

        return $this->returnJson();
    }

    // 生成充电支付记录
    public function generateChargePaymentRecord(Request $request)
    {
        $language_code = self::getLanguageCode($request);
        $charge_record_id = $request->input('charge_record_id');
        $charge_record_number = $request->input('charge_record_number');
        $user_id = $request->user_id;

        // 校验充电记录ID 修改了判断，等app改动完成只需要一个where('charge_record_number', $charge_record_number)就可以执行判断，when全部刪除
        if ((blank($charge_record_id) && blank($charge_record_number)) ||
            blank($charge_record = ChargeRecord::when((filled($charge_record_number)), function ($query) use ($charge_record_number) {
                $query->where('charge_record_number', $charge_record_number);
            })->when((filled($charge_record_id) && blank($charge_record_number)), function ($query) use ($charge_record_id) {
                $query->where('charge_record_id', $charge_record_id);
            })->first())
        ) {
            $this->missingField('charge_record_id');
            return $this->returnJson();
        }
        // 如果是后付，user_id为必填且为本人操作
        if ($charge_record->charge_tariff_scheme === ChargeTariffScheme::PostPaid && blank($user_id) && $charge_record->user_id !== $user_id) {
            $this->missingField('user_id');
            return $this->returnJson();
        }
        // 先查找是否已有充电支付表数据
        if ($charge_record->chargePaymentRecord()->exists()) {
            $this->data = $this->getChargeRecordAndChargePaymentRecordByChargeRecordModel($charge_record);
            $this->setPaymentData();
            return $this->returnJson();
        }

        $charge_payment_record_info = null;
        // try {
        // 计算Charge Payment Record数据
        $charge_payment_record_info = match ($charge_record->tariff_table_type) {
            TariffTableType::SimpleTariffTable => $this->getSimpleTariffTableChargeRecordInfo($charge_record),
            TariffTableType::ComplexTimeTariffTable => $this->getTimeTariffTableChargeRecordInfo($charge_record),
            TariffTableType::ComplexEnergyTariffTable => $this->getEnergyTariffTableChargeRecordInfo($charge_record),
            default => null,
        };
        /* } catch (\Throwable $th) {
            report($th);
        } */

        if (filled($charge_payment_record_info)) {
            $charge_payment_record = $charge_payment_record_info['charge_payment_record'];
            // 总金额是0的话，直接设置为已支付
            if ($charge_payment_record->total_amount === 0) {
                $charge_payment_record->payment_status = PaymentStatusEnum::Completed;
                $charge_payment_record->gmt_payment_status = now();
            }
            $charge_payment_record->save();
            // 保存计算json
            $charge_payment_record_calculation = $charge_payment_record_info['charge_payment_record_calculation']; // 充电支付记录计算
            ChargePaymentRecordCalculation::create([
                'charge_payment_record_number' => $charge_payment_record->charge_payment_record_number,
                'charge_value_amount_calculation_json' => $charge_payment_record_calculation['charge_value_amount_calculation_json'], // 充电量金额计算JSON
                'idling_penalty_amount_calculation_json' => $charge_payment_record_calculation['idling_penalty_amount_calculation_json'], // 闲置罚款金额计算JSON
            ]);
            // 更新charge record数据
            if ($charge_record->charge_tariff_scheme === ChargeTariffScheme::PrePaid) {
                $charge_record->pre_paid_charge_value = $charge_payment_record->charge_value; // 充电量
            } elseif ($charge_record->charge_tariff_scheme === ChargeTariffScheme::PostPaid) {
                $charge_record->post_paid_purchase_charge_value = $charge_payment_record->charge_value; // 充电量
            }
            $charge_record->charge_value_amount += $charge_payment_record->charge_value_amount; // 充电量金额
            $charge_record->idling_penalty_amount = $charge_payment_record->idling_penalty_amount; // 闲置罚款金额
            $charge_record->total_amount += $charge_payment_record->total_amount; // 合共金额
            $charge_record->save();

            /* $this->data['stop_charge_return'] = null;
            if (filled($api_url = $charge_record->chargePoint->comServer->api_url)) {
                // 停止充电
                $params = array(
                    'charge_operator_type' => $operator_type,
                    'charge_operator_number' => $operator_number,
                    'connector_id' => $charge_record->connector_id,
                    'remote_stop_charge_detail' => $detail,
                    'remote_stop_charge_reason' => $reason,
                    'unique_id' => $unique_id,
                );

                $code = 201;

                try {
                    $client = new Client();
                    $response = $client->request('POST', "$api_url/charge/remoteStopCharge", array(
                        'query' => $params,
                    ));

                    if ($response->getStatusCode() == 200) {
                        $return_data = json_decode($response->getBody()->getContents(), true);
                        $code = $return_data['code'] ?? $code;
                        $this->data['stop_charge_return']['data'] = $return_data['data'] ?? null;
                        $this->data['stop_charge_return']['message'] = $this->data['stop_charge_return']['message'] ?? null;
                    }
                } catch (ClientException|Exception $e) {
                    $this->data['stop_charge_return']['message'] = __('common.request_request_error');
                }

                $this->data['stop_charge_return']['code'] = $code;
            } */
        }

        $this->data = $this->getChargeRecordAndChargePaymentRecordByChargeRecordModel($charge_record->refresh());

        $this->setPaymentData();

        return $this->returnJson();
    }

    // 获取充电中预估金额
    public function getChargeRecordEstimatedAmount(Request $request)
    {
        $language_code = self::getLanguageCode($request);
        $charge_record_id = $request->input('charge_record_id');

        // 校验充电记录ID
        if (blank($charge_record_id) || blank($charge_record = ChargeRecord::find($charge_record_id))) {
            $this->missingField('charge_record_id');
            return $this->returnJson();
        }

        // 先查找是否已有充电支付表数据
        if ($charge_record->chargePaymentRecord()->exists()) {
            // 如果有支付记录，查找所有未支付的支付记录并累加charge_value_amount、idling_penalty_amount、total_amount三个字段返回
            $charge_payment_record_list = $charge_record->chargePaymentRecord()->oldest()->get();
            $this->data['charge_value_amount'] = null;
            $this->data['idling_penalty_amount'] = null;
            // $this->data['charge_total'] = null;
            foreach ($charge_payment_record_list as $charge_payment_record) {
                if ($charge_payment_record->payment_status === PaymentStatusEnum::Pending) {
                    $this->data['charge_value_amount'] += $charge_payment_record->charge_value_amount; // 充电量金额
                    $this->data['idling_penalty_amount'] += $charge_payment_record->idling_penalty_amount; // 闲置罚款金额
                    // $this->data['charge_total'] += $charge_payment_record->total_amount; // 合共金额
                }
            }
            // $this->data['charge_payment_record_list'] = $charge_payment_record_list->toArray();

            return $this->returnJson();
        }

        $charge_payment_record_info = null;
        // try {
        // 计算Charge Payment Record数据
        $charge_payment_record_info = match ($charge_record->tariff_table_type) {
            TariffTableType::SimpleTariffTable => $this->getSimpleTariffTableChargeRecordInfo($charge_record),
            TariffTableType::ComplexTimeTariffTable => $this->getTimeTariffTableChargeRecordInfo($charge_record),
            TariffTableType::ComplexEnergyTariffTable => $this->getEnergyTariffTableChargeRecordInfo($charge_record),
            default => null,
        };
        /* } catch (\Throwable $th) {
            report($th);
        } */

        $this->data['charge_value_amount'] = null; // 预估充电量金额
        $this->data['idling_penalty_amount'] = null; // 预估闲置罚款金额
        // $this->data['charge_total'] = null; // 预估合共金额
        // $this->data['gmt_idling_penalty_start'] = null; // 闲置罚款开始时间
        if (filled($charge_payment_record_info)) {
            $charge_payment_record = $charge_payment_record_info['charge_payment_record'] ?? null;
            $this->data['charge_value_amount'] = $charge_payment_record->charge_value_amount ?? null;
            $this->data['idling_penalty_amount'] = ($charge_payment_record->idling_penalty_amount ?? 0) ?: null;
            // $this->data['charge_total'] = $charge_payment_record->total_amount ?? null;
            // $this->data['gmt_idling_penalty_start'] = $charge_payment_record_info['gmt_idling_penalty_start'] ?? null;
        }


        return $this->returnJson();
    }

    public function getChargePaymentRecord(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $charge_payment_record_id = $request->input('charge_payment_record_id'); // 充电支付记录ID

        // 校验充电支付记录ID
        if (blank($charge_payment_record_id)) {
            $this->missingField('charge_payment_record_id');
            return $this->returnJson();
        }

        $charge_payment_record = ChargePaymentRecord::with('chargeRecord:charge_record_id,charge_tariff_scheme,charge_value_type')
            ->find($charge_payment_record_id);

        if (blank($charge_payment_record)) {
            $this->notFoundData('charge_payment_record_id');
            return $this->returnJson();
        }

        $charge_payment_record->makeVisible(['gmt_create']);
        $charge_payment_record->charge_tariff_scheme = $charge_payment_record->chargeRecord?->charge_tariff_scheme;
        $charge_payment_record->charge_value_type = $charge_payment_record->chargeRecord?->charge_value_type;
        unset($charge_payment_record->chargeRecord);

        $this->data = $charge_payment_record;

        return $this->returnJson();
    }

    // 获取未充电预估金额
    public function getUnchargedEstimatedAmount(Request $request)
    {
        $language_code = self::getLanguageCode($request);
        $connector_id = $request->connector_id; // 充电枪Id
        $charge_value = (float)$request->charge_value; // 充电量
        $gmt_start = $request->input('gmt_start', date('Y-m-d H:i:s')); // 开始时间

        // 校验充电枪ID
        if (blank($connector_id)) {
            $this->missingField('connector_id');
            return $this->returnJson();
        }
        // 校验充电量
        if (blank($charge_value)) {
            $this->missingField('charge_value');
            return $this->returnJson();
        }
        $connector = Connector::with(['setting'])->find($connector_id);
        // 校验充电枪是否存在
        if (blank($connector)) {
            $this->notFoundData('connector_id');
            return $this->returnJson();
        }


        // try {
        $this->data = match ($connector->setting->tariff_table_type) {
            TariffTableType::SimpleTariffTable => self::getSimpleTariffTableUnchargedEstimatedAmount($connector, $charge_value, $gmt_start),
            TariffTableType::ComplexTimeTariffTable => $this->getTimeTariffTableUnchargedEstimatedAmount($connector, $charge_value, $gmt_start),
            TariffTableType::ComplexEnergyTariffTable => $this->getEnergyTariffTableUnchargedEstimatedAmount($connector, $charge_value, $gmt_start),
            default => null,
        };
        /* } catch (\Throwable $th) {
            report($th);
        } */


        return $this->returnJson();
    }

    // 注册
    public function registrationWithUsername(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $username = $request->input('username'); // 用户名
        $password = $request->input('password'); // 密码
        $member_card_key = $request->input('member_card_key'); // 会员卡KEY
        $is_register_platform_member_card = $request->boolean('is_register_platform_member_card', true); // 是否注册平台会员卡

        if (blank($username)) {
            $this->missingField('username');
            return $this->returnJson();
        }
        if (blank($password)) {
            $this->missingField('password');
            return $this->returnJson();
        }
        $username_pattern = '/^(?=.*[a-zA-Z])[a-zA-Z0-9._-]{4,45}$/';
        if (!preg_match($username_pattern, $username)) {
            $this->code = 40002;
            $this->message = __('api.error_username_rule');
            return $this->returnJson();
        }
        $password_pattern = '/^(?=.*[a-zA-Z])[a-zA-Z0-9._-]{4,20}$/';
        // 密码最小4位，最大20位
        if (!preg_match($password_pattern, $password)) {
            $this->code = 40002;
            $this->message = __('api.error_password_rule');
            return $this->returnJson();
        }
        if (AppUser::where('username', $username)->exists()) {
            $this->alreadyExists('username');
            return $this->returnJson();
        }
        // 如果传入了会员卡ID，那么校验会员卡是否存在且未被绑定
        $member_card = null;
        if (filled($member_card_key)) {
            $member_card = MemberCard::where('is_enable', 1)->firstWhere('member_card_key', $member_card_key);
            if (blank($member_card)) {
                $this->notFoundData('member_card_id');
                return $this->returnJson();
            }
            if (filled($member_card->user_id)) {
                $this->code = 40004;
                $this->message = __('api.error_member_card_has_been_bound');
                return $this->returnJson();
            }
            // 如果勾选了注册平台会员卡，那么会员卡不能是平台会员卡。即商户不能为NULL
            if ($is_register_platform_member_card && blank($member_card->merchant_id)) {
                $this->code = 40004;
                $this->message = __('api.error_member_card_duplicate_platform_member_card');
                return $this->returnJson();
            }
        }

        $salt = Str::random(10);
        // 随机生成账户名和昵称
        $nickname = 'user_' . Str::random(10);

        try {
            DB::transaction(function () use ($username, $nickname, $password, $salt, $member_card, $member_card_key, $is_register_platform_member_card) {
                // 创建用户
                $user = AppUser::create([
                    'username' => $username,
                    'nickname' => $nickname,
                    'telephone' => null,
                    'password' => hash('sha512', $password . $salt),
                    'salt' => $salt,
                    'points_balance' => 0,
                ]);

                if (filled($member_card_key)) {
                    $member_card->user_id = $user->user_id;
                    $member_card->save();
                }

                // 创建平台会员卡
                if ($is_register_platform_member_card) {
                    $member_card = MemberCard::create([
                        'user_id' => $user->user_id,
                        'member_card_key' => self::getRandomString(20, function ($member_card_key) {
                            return MemberCard::where('member_card_key', $member_card_key)
                                ->whereNull('merchant_id')
                                ->exists();
                        }),
                        'merchant_id' => null,
                        'is_enable' => 1,
                    ]);

                    if (blank($member_card)) {
                        $this->message = __('common.request_request_error');
                        throw new Exception('Member card is blank');
                    }
                }

                $this->data = $user->toArray();
                unset($this->data['password'], $this->data['salt']);
            });
        } catch (Exception $e) {
            report($e);
            $this->message = __('common.request_request_error');
            return $this->returnJson();
        }

        return $this->returnJson();
    }

    public function registrationWithTelephone(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $telephone = $request->input('telephone'); // 用户名
        $verification_code = $request->input('verification_code'); // 验证码
        $member_card_key = $request->input('member_card_key'); // 会员卡KEY
        $is_register_platform_member_card = $request->boolean('is_register_platform_member_card', true); // 是否注册平台会员卡

        if (blank($telephone)) {
            $this->missingField('telephone');
            return $this->returnJson();
        }
        // 验证电话格式
        if (!$this->checkTelephoneValid($telephone)) {
            return $this->returnJson();
        }
        if (AppUser::where('telephone', $telephone)->exists()) {
            $this->alreadyExists('telephone');
            return $this->returnJson();
        }
        if (blank($verification_code)) {
            $this->missingField('verification_code');
            return $this->returnJson();
        }
        // 验证验证码
        $redis_key = 'app_send_telephone_verification_code_' . $telephone . '_' . VerificationCodeTypeEnum::Registration;
        $verification_code_in_redis = Redis::get($redis_key);
        // 判断验证码是否正确
        if ($verification_code !== $verification_code_in_redis) {
            $this->verificationCodeError();
            return $this->returnJson();
        }
        // 如果传入了会员卡ID，那么校验会员卡是否存在且未被绑定
        $member_card = null;
        if (filled($member_card_key)) {
            $member_card = MemberCard::where('is_enable', 1)->firstWhere('member_card_key', $member_card_key);
            if (blank($member_card)) {
                $this->notFoundData('member_card_id');
                return $this->returnJson();
            }
            if (filled($member_card->user_id)) {
                $this->code = 40004;
                $this->message = __('api.error_member_card_has_been_bound');
                return $this->returnJson();
            }
            // 如果勾选了注册平台会员卡，那么会员卡不能是平台会员卡。即商户不能为NULL
            if ($is_register_platform_member_card && blank($member_card->merchant_id)) {
                $this->code = 40004;
                $this->message = __('api.error_member_card_duplicate_platform_member_card');
                return $this->returnJson();
            }
        }

        // 随机生成账户名和昵称
        $nickname = 'user_' . Str::random(10);

        try {
            $username = self::getRandomString(20, function ($username) {
                return AppUser::where('username', $username)->exists();
            });
            DB::transaction(function () use ($username, $telephone, $nickname, $member_card, $member_card_key, $is_register_platform_member_card, $redis_key) {
                // 创建用户
                $user = AppUser::create([
                    'username' => $username,
                    'nickname' => $nickname,
                    'telephone' => $telephone,
                    'points_balance' => 0,
                ]);

                if (filled($member_card_key)) {
                    $member_card->user_id = $user->user_id;
                    $member_card->save();
                }

                // 创建平台会员卡
                if ($is_register_platform_member_card) {
                    $member_card = MemberCard::create([
                        'user_id' => $user->user_id,
                        'member_card_key' => self::getRandomString(20, function ($member_card_key) {
                            return MemberCard::where('member_card_key', $member_card_key)
                                ->whereNull('merchant_id')
                                ->exists();
                        }),
                        'merchant_id' => null,
                        'is_enable' => 1,
                    ]);

                    if (blank($member_card)) {
                        $this->message = __('common.request_request_error');
                        throw new Exception('Member card is blank');
                    }
                }

                $this->data = $user->toArray();
                unset($this->data['password'], $this->data['salt']);
                // 移除redis
                Redis::del($redis_key);
            });
        } catch (Exception $e) {
            report($e);
            $this->message = __('common.request_request_error');
            return $this->returnJson();
        }

        return $this->returnJson();
    }

    // 用户使用密码登录
    public function loginWithPassword(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $account = $request->input('account'); // 账号
        $password = $request->input('password'); // 密码

        if (blank($account)) {
            $this->missingField('account');
            return $this->returnJson();
        }
        if (blank($password)) {
            $this->missingField('password');
            return $this->returnJson();
        }
        if (blank($user = AppUser::where('username', $account)->first()) || self::checkUserPassword($user, $password) === false) {
            $this->usernameOrPasswordError();
            return $this->returnJson();
        }

        // 拼接头像完整路径
        $user->avatar_url = existsImage('avatar', $user->avatar_url);
        $this->data = $user->toArray();
        unset($this->data['password'], $this->data['salt']);

        return $this->returnJson();
    }

    // 用户使用电话登录
    public function loginWithTelephone(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $telephone = $request->input('telephone'); // 电话
        $verification_code = $request->input('verification_code'); // 验证码

        $this->data = false;

        if (blank($user = AppUser::where('telephone', $telephone)->first())) {
            $this->notFoundData('user_id');
            return $this->returnJson();
        }
        // 电话是否有效
        if (!$this->checkTelephoneValid($telephone)) {
            return $this->returnJson();
        }
        if (blank($verification_code)) {
            $this->missingField('verification_code');
            return $this->returnJson();
        }
        // 从redis中获取验证码
        $redis_key = 'app_send_telephone_verification_code_' . $telephone . '_' . VerificationCodeTypeEnum::Login;
        $verification_code_in_redis = Redis::get($redis_key);
        // 判断验证码是否正确
        if ($verification_code !== $verification_code_in_redis) {
            $this->verificationCodeError();
            return $this->returnJson();
        }

        // 拼接头像完整路径
        $user->avatar_url = existsImage('avatar', $user->avatar_url);
        $this->data = $user->toArray();
        unset($this->data['password'], $this->data['salt']);

        // 移除redis
        Redis::del($redis_key);

        return $this->returnJson();
    }

    /**
     * 发送手机验证码
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function sendTelephoneVerificationCode(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $telephone = $request->input('telephone'); // 电话
        $verification_code_type = $request->input('verification_code_type'); // 验证码类型
        $is_telephone_exists = $request->input('is_telephone_exists'); // 电话是否存在（true需手机存在, false需手机不存在）

        $this->data = false;

        if (blank($telephone)) {
            $this->missingField('telephone');
            return $this->returnJson();
        }
        // 电话是否有效
        if (!$this->checkTelephoneValid($telephone)) {
            return $this->returnJson();
        }
        if (blank($verification_code_type) || !VerificationCodeTypeEnum::hasValue($verification_code_type)) {
            $this->missingField('verification_code_type');
            return $this->returnJson();
        }
        if (blank($is_telephone_exists)) {
            $this->missingField('is_telephone_exists');
            return $this->returnJson();
        }
        $is_telephone_exists = $request->boolean('is_telephone_exists'); // 电话是否存在（true需手机存在, false需手机不存在）

        // 判断电话是否存在
        $user_exists = AppUser::where('telephone', $telephone)->exists();
        if ($is_telephone_exists && !$user_exists) {
            $this->notFoundData('telephone');
            return $this->returnJson();
        } elseif (!$is_telephone_exists && $user_exists) {
            $this->alreadyExists('telephone');
            return $this->returnJson();
        }

        // redis key
        $redis_key = "app_send_telephone_verification_code_{$telephone}_{$verification_code_type}";
        // 默认验证码有效期 （十分钟）
        $default_app_verification_code_second = env('DEFAULT_APP_VERIFICATION_CODE_SECOND', 600);
        // ttl (-2 key不存在，-1 key存在但没有设置剩余过期时间，>0 剩余过期时间)
        $ttl = Redis::ttl($redis_key);
        // 验证码已发送时间 = 验证码有效期 - 剩余过期时间
        if ($ttl > 0 && ($default_app_verification_code_second - $ttl) < 60) {
            // 如果一分钟内发送过验证码，那么不再发送，直接返回true
            $this->data = true;
            return $this->returnJson();
        }

        // 生成6位数验证码
        $verification_code = self::getRandomNumber();

        // 发送验证码短信
        $verification_code_type_enum = VerificationCodeTypeEnum::coerce($verification_code_type);
        // 发送消息
        $msg = __('api.sms_content', [
            'verification_code_type' => $verification_code_type_enum?->description ?? '',
            'verification_code' => $verification_code,
        ]);
        // 电话去除空格和+
        $telephone = str_replace([' ', '+'], '', $telephone);
        $send_result = $this->sendAccessYouSMSVerificationCode($telephone, $msg);

        // 验证码短信发送失败
        if (!$send_result) {
            $this->message = __('api.error_sms_send');
            return $this->returnJson();
        }

        // 保存验证码至redis中，10分钟有效期
        Redis::setex($redis_key, $default_app_verification_code_second, $verification_code);

        $this->data = true;

        return $this->returnJson();
    }

    /**
     * 发送邮箱验证码
     *
     * @throws GuzzleException
     */
    public function sendEmailVerificationCode(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $email = $request->input('email'); // 邮箱
        $verification_code_type = $request->input('verification_code_type'); // 验证码类型
        $is_email_exists = $request->input('is_email_exists'); // email是否存在（true需email存在, false需email不存在）

        $this->data = false;

        if (blank($email)) {
            $this->missingField('email');
            return $this->returnJson();
        }
        // 电邮是否有效
        if (!$this->checkEmailValid($email)) {
            return $this->returnJson();
        }
        if (blank($verification_code_type) || !VerificationCodeTypeEnum::hasValue($verification_code_type)) {
            $this->missingField('verification_code_type');
            return $this->returnJson();
        }
        // 暂时只有修改邮箱时才需要验证邮箱验证码
        $allow_verification_code_type_list = array(
            VerificationCodeTypeEnum::UpdateEmail,
        );
        if (!in_array($verification_code_type, $allow_verification_code_type_list)) {
            $this->notFoundData('verification_code_type');
            return $this->returnJson();
        }
        if (blank($is_email_exists)) {
            $this->missingField('is_email_exists');
            return $this->returnJson();
        }
        $is_email_exists = $request->boolean('is_email_exists'); // email是否存在（true需email存在, false需email不存在）

        // 判断email是否存在
        $user_exists = AppUser::where('email', $email)->exists();
        if ($is_email_exists && !$user_exists) {
            $this->notFoundData('email');
            return $this->returnJson();
        } elseif (!$is_email_exists && $user_exists) {
            $this->alreadyExists('email');
            return $this->returnJson();
        }


        $redis_key = "app_send_email_verification_code_{$email}_{$verification_code_type}";
        // 默认验证码有效期 （十分钟）
        $default_app_verification_code_second = env('DEFAULT_APP_VERIFICATION_CODE_SECOND', 600);
        // ttl (-2 key不存在，-1 key存在但没有设置剩余过期时间，>0 剩余过期时间)
        $ttl = Redis::ttl($redis_key);
        // 验证码已发送时间 = 验证码有效期 - 剩余过期时间
        if ($ttl > 0 && ($default_app_verification_code_second - $ttl) < 60) {
            // 如果一分钟内发送过验证码，那么不再发送，直接返回true
            $this->data = true;
            return $this->returnJson();
        }

        // 生成6位数验证码
        $verification_code = self::getRandomNumber();

        #发送验证码邮件
        $verification_code_type_enum = VerificationCodeTypeEnum::coerce($verification_code_type);
        $result = self::sendEmailByClientVeeotech(
            $email,
            __('api.mail_verification_code_subject'),
            __(
                'api.mail_verification_code_content',
                [
                    'verification_code_type' => $verification_code_type_enum?->description ?? '',
                    'verification_code' => $verification_code
                ]
            )
        );
        // 验证码短信发送失败
        if ($result['code'] !== 200) {
            $this->message = __('api.email_send_error');
            return $this->returnJson();
        }

        // 保存验证码至redis中，10分钟有效期
        Redis::setex($redis_key, $default_app_verification_code_second, $verification_code);
        $this->data = true;


        return $this->returnJson();
    }

    /**
     * 验证电话验证码
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function verifyTelephoneVerificationCode(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $telephone = $request->input('telephone'); // 电话
        $verification_code_type = $request->input('verification_code_type'); // 验证码类型
        $verification_code = $request->input('verification_code'); // 验证码

        $this->data = false;

        if (blank($telephone)) {
            $this->missingField('telephone');
            return $this->returnJson();
        }
        // 电话是否有效
        if (!$this->checkTelephoneValid($telephone)) {
            return $this->returnJson();
        }
        // 验证码不为空且长度为6
        if (blank($verification_code) || strlen($verification_code) !== 6) {
            $this->missingField('verification_code');
            return $this->returnJson();
        }
        if (blank($verification_code_type) || !VerificationCodeTypeEnum::hasValue($verification_code_type)) {
            $this->missingField('verification_code_type');
            return $this->returnJson();
        }
        if (blank($verification_code)) {
            $this->missingField('verification_code');
            return $this->returnJson();
        }

        // redis key
        $redis_key = "app_send_telephone_verification_code_{$telephone}_{$verification_code_type}";
        // 从redis中获取验证码
        $verification_code_in_redis = Redis::get($redis_key);
        // 判断验证码是否正确
        if ($verification_code === $verification_code_in_redis) {
            $this->data = true;
        } else {
            $this->verificationCodeError();
        }

        return $this->returnJson();
    }

    /**
     * 验证邮箱验证码
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function verifyEmailVerificationCode(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $email = $request->input('email'); // 邮箱
        $verification_code_type = $request->input('verification_code_type'); // 验证码类型
        $verification_code = $request->input('verification_code'); // 验证码

        $this->data = false;

        if (blank($email)) {
            $this->missingField('email');
            return $this->returnJson();
        }
        // 电邮是否有效
        if (!$this->checkEmailValid($email)) {
            return $this->returnJson();
        }
        // 验证码不为空且长度为6
        if (blank($verification_code) || strlen($verification_code) !== 6) {
            $this->missingField('verification_code');
            return $this->returnJson();
        }
        if (blank($verification_code_type) || !VerificationCodeTypeEnum::hasValue($verification_code_type)) {
            $this->missingField('verification_code_type');
            return $this->returnJson();
        }
        // 暂时只有修改邮箱时才需要验证邮箱验证码
        $allow_verification_code_type_list = array(
            VerificationCodeTypeEnum::UpdateEmail,
        );
        if (!in_array($verification_code_type, $allow_verification_code_type_list)) {
            $this->notFoundData('verification_code_type');
            return $this->returnJson();
        }
        if (blank($verification_code)) {
            $this->missingField('verification_code');
            return $this->returnJson();
        }

        // redis key
        $redis_key = "app_send_email_verification_code_{$email}_{$verification_code_type}";
        // 从redis中获取验证码
        $verification_code_in_redis = Redis::get($redis_key);
        // 判断验证码是否正确
        if ($verification_code === $verification_code_in_redis) {
            $this->data = true;
        } else {
            $this->verificationCodeError();
        }

        return $this->returnJson();
    }

    /**
     * 验证用户密码
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function verifyUserPassword(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $user_id = $request->input('user_id'); // 用户ID
        $password = $request->input('password'); // 密码

        $this->data = false;

        if (blank($user_id) || blank($user = AppUser::find($user_id))) {
            $this->notFoundData('user_id');
            return $this->returnJson();
        }
        if (blank($password)) {
            $this->missingField('password');
            return $this->returnJson();
        }
        if (self::checkUserPassword($user, $password) === false) {
            $this->missingField('password');
            return $this->returnJson();
        }

        $this->data = true;

        return $this->returnJson();
    }

    // 更改用户资料
    public function updateUserProfile(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $user_id = $request->input('user_id'); // 用户ID
        $nickname = $request->input('nickname'); // 昵称


        if (blank($user_id) || blank($user = AppUser::find($user_id))) {
            $this->notFoundData('user_id');
            return $this->returnJson();
        }
        if (filled($nickname)) {
            $user->nickname = $nickname;
        }

        if (filled($request->file('avatar'))) {
            $user = $this->updateUserAvatar($request, $user);
        }

        if ($this->code === 200) {
            if ($user->save()) {
                $user->avatar_url = existsImage('avatar', $user->avatar_url);
                $this->data = $user->toArray();
                unset($this->data['password'], $this->data['salt']);
            } else {
                $this->modelSaveFail();
                Storage::disk('avatar')->delete($user->avatar_url);
            }
        }

        return $this->returnJson();
    }

    // 更改用户账户名
    public function updateUserUsername(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $user_id = $request->input('user_id'); // 用户ID
        $password = $request->input('password'); // 密码
        $username = $request->input('username'); // 用户名

        if (blank($user_id) || blank($user = AppUser::find($user_id))) {
            $this->notFoundData('user_id');
            return $this->returnJson();
        }
        if (blank($username)) {
            $this->missingField('username');
            return $this->returnJson();
        }
        if (blank($password)) {
            $this->missingField('password');
            return $this->returnJson();
        }
        $pattern = '/^(?=.*[a-zA-Z])[a-zA-Z0-9._-]{4,45}$/';
        if (!preg_match($pattern, $username)) {
            $this->code = 40002;
            $this->message = __('api.error_username_rule');
            return $this->returnJson();
        }
        if (AppUser::where('username', $username)->exists()) {
            $this->alreadyExists('username');
            return $this->returnJson();
        }

        // 判断密码是否正确
        if (self::checkUserPassword($user, $password) === false) {
            $this->missingField('password');
            return $this->returnJson();
        }

        $user->username = $username;

        if ($user->save()) {
            $this->data = $user->toArray();
            unset($this->data['password'], $this->data['salt']);
        } else {
            $this->modelSaveFail();
        }

        return $this->returnJson();
    }

    // 更改用户密码
    public function updateUserPassword(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $user_id = $request->input('user_id'); // 用户ID
        $old_password = $request->input('old_password'); // 旧密码
        $new_password = $request->input('new_password'); // 新密码

        if (blank($user_id) || blank($user = AppUser::find($user_id))) {
            $this->notFoundData('user_id');
            return $this->returnJson();
        }
        if (blank($old_password)) {
            $this->missingField('old_password');
            return $this->returnJson();
        }
        if (blank($new_password)) {
            $this->missingField('new_password');
            return $this->returnJson();
        }
        $password_pattern = '/^(?=.*[a-zA-Z])[a-zA-Z0-9._-]{4,20}$/';
        // 密码最小4位，最大20位
        if (!preg_match($password_pattern, $new_password)) {
            $this->code = 40002;
            $this->message = __('api.error_password_rule');
            return $this->returnJson();
        }
        if (self::checkUserPassword($user, $old_password) === false) {
            $this->code = 40002;
            $this->message = __('api.error_old_password');
            return $this->returnJson();
        }

        $user->salt = Str::random(10);
        $user->password = hash('sha512', $new_password . $user->salt);

        if ($user->save()) {
            $this->data = $user->toArray();
            unset($this->data['password'], $this->data['salt']);
        } else {
            $this->modelSaveFail();
        }

        return $this->returnJson();
    }

    // 更改用户电话
    public function updateUserTelephone(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $user_id = $request->input('user_id'); // 用户ID
        $password = $request->input('password'); // 密码
        $telephone = $request->input('telephone'); // 电话
        $verification_code = $request->input('verification_code'); // 验证码

        if (blank($user_id) || blank($user = AppUser::find($user_id))) {
            $this->notFoundData('user_id');
            return $this->returnJson();
        }
        if (blank($password)) {
            $this->missingField('password');
            return $this->returnJson();
        }
        if (blank($verification_code)) {
            $this->missingField('verification_code');
            return $this->returnJson();
        }
        // 电话是否有效
        if (!$this->checkTelephoneValid($telephone)) {
            return $this->returnJson();
        }
        // 判断密码是否正确
        if (self::checkUserPassword($user, $password) === false) {
            $this->missingField('password');
            return $this->returnJson();
        }
        if (blank($verification_code)) {
            $this->missingField('verification_code');
            return $this->returnJson();
        }
        // 从redis中获取验证码
        $redis_key = 'app_send_telephone_verification_code_' . $telephone . '_' . VerificationCodeTypeEnum::UpdateTelephone;
        $verification_code_in_redis = Redis::get($redis_key);
        // 判断验证码是否正确
        if ($verification_code !== $verification_code_in_redis) {
            $this->verificationCodeError();
            return $this->returnJson();
        }

        $user->telephone = $telephone;

        if ($user->save()) {
            $this->data = $user->toArray();
            unset($this->data['password'], $this->data['salt']);
            // 移除redis
            Redis::del($redis_key);
        } else {
            $this->modelSaveFail();
        }

        return $this->returnJson();
    }

    // 更改用户电邮
    public function updateUserEmail(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $user_id = $request->input('user_id'); // 用户ID
        $password = $request->input('password'); // 密码
        $email = $request->input('email'); // 电邮
        $verification_code = $request->input('verification_code'); // 验证码

        if (blank($user_id) || blank($user = AppUser::find($user_id))) {
            $this->notFoundData('user_id');
            return $this->returnJson();
        }
        // 电邮是否有效
        if (!$this->checkEmailValid($email)) {
            return $this->returnJson();
        }
        if (AppUser::where('email', $email)->exists()) {
            $this->alreadyExists('email');
            return $this->returnJson();
        }
        // 判断密码是否正确
        if (self::checkUserPassword($user, $password) === false) {
            $this->missingField('password');
            return $this->returnJson();
        }
        if (blank($verification_code)) {
            $this->missingField('verification_code');
            return $this->returnJson();
        }

        // 从redis中获取验证码
        $redis_key = 'app_send_email_verification_code_' . $email . '_' . VerificationCodeTypeEnum::UpdateEmail;
        $verification_code_in_redis = Redis::get($redis_key);
        // 判断验证码是否正确
        if ($verification_code !== $verification_code_in_redis) {
            $this->verificationCodeError();
            return $this->returnJson();
        }

        $user->email = $email;

        if ($user->save()) {
            $this->data = $user->toArray();
            unset($this->data['password'], $this->data['salt']);
            // 移除redis
            Redis::del($redis_key);
        } else {
            $this->modelSaveFail();
        }

        return $this->returnJson();
    }

    // 重置密码
    public function resetUserPasswordWithTelephone(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $telephone = $request->input('telephone'); // 电话
        $password = $request->input('password'); // 密码
        $verification_code = $request->input('verification_code'); // 验证码

        $this->data = false;

        if (blank($user = AppUser::where('telephone', $telephone)->first())) {
            $this->notFoundData('user_id');
            return $this->returnJson();
        }
        if (blank($password)) {
            $this->missingField('password');
            return $this->returnJson();
        }
        // 密码最短4位最长20位
        if (strlen($password) < 4 || strlen($password) > 20) {
            $this->code = 40002;
            $this->message = __('api.error_password_rule');
            return $this->returnJson();
        }

        // 电话是否有效
        if (!$this->checkTelephoneValid($telephone)) {
            return $this->returnJson();
        }
        if (blank($verification_code)) {
            $this->missingField('verification_code');
            return $this->returnJson();
        }
        // 从redis中获取验证码
        $redis_key = 'app_send_telephone_verification_code_' . $telephone . '_' . VerificationCodeTypeEnum::ResetPassword;
        $verification_code_in_redis = Redis::get($redis_key);
        // 判断验证码是否正确
        if ($verification_code !== $verification_code_in_redis) {
            $this->verificationCodeError();
            return $this->returnJson();
        }

        $user->salt = Str::random(10);
        $user->password = hash('sha512', $password . $user->salt);

        if ($user->save()) {
            $this->data = true;
            // 移除redis
            Redis::del($redis_key);
        } else {
            $this->modelSaveFail();
        }

        return $this->returnJson();
    }

    public function forceImproveUserInformation(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $user_id = $request->user_id;
        $username = $request->username; // 不传不修改，确认唯一性(排除自己)，确认格式
        $password = $request->password; // 密码和盐为空才可以改，确认格式

        if (blank($user_id) || blank($user = AppUser::find($user_id))) {
            $this->notFoundData('user_id');
            return $this->returnJson();
        }

        if (filled($username)) {
            $username_pattern = '/^(?=.*[a-zA-Z])[a-zA-Z0-9._-]{4,45}$/';
            if (!preg_match($username_pattern, $username)) {
                $this->code = 40002;
                $this->message = __('api.error_username_rule');
                return $this->returnJson();
            }
            if (AppUser::where('username', $username)->where('user_id', '!=', $user_id)->exists()) {
                $this->alreadyExists('username');
                return $this->returnJson();
            }
            $user->username = $username;
        }
        if (blank($password)) {
            $this->missingField('password');
            return $this->returnJson();
        }
        $password_pattern = '/^(?=.*[a-zA-Z])[a-zA-Z0-9._-]{4,20}$/';
        // 密码最小4位，最大20位
        if (!preg_match($password_pattern, $password)) {
            $this->code = 40002;
            $this->message = __('api.error_password_rule');
            return $this->returnJson();
        }
        // 密码和盐为空才可以改
        if (filled($user->password) && filled($user->salt)) {
            $this->code = 40004;
            $this->message = __('api.error_user_information_has_been_improved');
        }

        $user->salt = Str::random(10);
        $user->password = hash('sha512', $password . $user->salt);

        if ($user->save()) {
            $this->data = $user->toArray();
            unset($this->data['password'], $this->data['salt']);
        } else {
            $this->modelSaveFail();
        }

        return $this->returnJson();
    }

    public function getAppUpdateInformation(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $platform = $request->platform; // 平台
        $version = $request->version; // 版本号

        if (blank($platform)) {
            $this->missingField('platform');
            return $this->returnJson();
        }
        if (blank($version)) {
            $this->missingField('version');
            return $this->returnJson();
        }

        $platform = strtoupper($platform);

        $this->data = [
            'has_new_version' => false, // 是否有新版本
            'version' => null, // 版本号
            'description' => null, // 描述
            'is_constraint' => false, // 是否强制更新
            'update_url' => null, // 更新地址
        ];

        // 获取根目录json文件夹下的app_version.json文件
        $app_version_array = getArrayFromJsonFile('app_version');

        // 无法解析或者为空
        if (blank($app_version_array)) {
            return $this->returnJson();
        }
        // 平台不存在
        if (!isset($app_version_array[$platform])) {
            return $this->returnJson();
        }

        $app_version = $app_version_array[$platform];
        if (version_compare($app_version['version'] ?? '', $version, '>')) {
            $this->data['has_new_version'] = true;
        }
        $this->data['description'] = $app_version['description'][$language_code] ?? ($app_version['description']['en_US'] ?? (reset($app_version['description']) ?? null));
        $this->data['is_constraint'] = $app_version['is_constraint'] ?? false;
        $this->data['update_url'] = $app_version['update_url'] ?? null;
        $this->data['version'] = $app_version['version'] ?? null;

        return $this->returnJson();
    }

    /**
     * 用户通知列表
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * @date 2023-11-01
     */
    public function listUserNotify(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $page_size = (int)$request->input('page_size', 10); // 每页显示数量
        $current_page = (int)$request->input('current_page', 1); // 当前页码
        $user_id = $request->input('user_id'); // 用户ID
        $category = $request->input('category'); // 类别
        $search_text = $request->input('search_text'); // 搜索内容

        if (blank($user_id)) {
            $this->missingField('user_id');
            return $this->returnJson();
        }
        // 查询user_id=null或者等于传入的user_id的数据
        $user_notify_list = UserNotify::where(function ($query) use ($user_id) {
            $query->where('user_id', $user_id)
                ->orWhereNull('user_id');
        })
            // 发布时间为空或者到发布时间
            ->where(function ($query) {
                $query->whereNull('gmt_release')
                    ->orWhere('gmt_release', '<=', now());
            })
            // 筛选分类
            ->when(filled($category), function ($query) use ($category) {
                $query->where('category', $category);
            })
            // 连接描述表
            ->with(['description' => function ($query) use ($language_code) {
                $query->where('language_code', $language_code);
            }])
            // 筛选搜索内容
            ->when(filled($search_text), function ($query) use ($language_code, $search_text) {
                $query->where(function ($query) use ($language_code, $search_text) {
                    $query->whereHas('description', function ($query) use ($language_code, $search_text) {
                        $query->where('language_code', $language_code)
                            ->where(function ($query) use ($search_text) {
                                $query->where('title', 'like', "%$search_text%")
                                    ->orWhere('content', 'like', "%$search_text%");
                            });
                    });
                });
            })
            // 排序，关联user_notify_to_user表，按照已读时间gmt_read为空、主表user_notify的创建时间gmt_create倒序
            ->leftJoinSub(function ($query) use ($user_id) {
                $query->select('user_notify_id AS n2u_user_notify_id', 'gmt_read', 'gmt_delete')
                    ->from('user_notify_to_user')
                    ->where('user_id', $user_id);
            }, 'user_notify_to_user', function ($join) {
                $join->on('user_notify_id', '=', 'n2u_user_notify_id');
            })
            ->whereNull('user_notify_to_user.gmt_delete')
            // ->orderByRaw('CASE WHEN gmt_read IS NULL THEN 0 ELSE 1 END')
            /* ->latest('gmt_release')
            ->latest('gmt_create') */
            ->orderByRaw('COALESCE(gmt_release, gmt_create) DESC')
            ->paginate($page_size, ['*'], 'current_page', $current_page);

        // 显示创建时间，循环合并描述和主表数据
        $user_notify_list->data = $user_notify_list->makeVisible(['gmt_create'])->transform(function ($item) {
            $item = array_merge($item->toArray(), $item->firstDescription->only(['title', 'content', 'image_url']));
            unset($item['description'], $item['n2u_user_notify_id'], $item['gmt_delete']);
            return $item;
        });

        $this->data = array(
            'current_page' => $user_notify_list->currentPage(),
            'total' => $user_notify_list->total(),
            'page_total' => $user_notify_list->lastPage(),
            "data" => $user_notify_list->items(),
        );

        return $this->returnJson();
    }

    /**
     * 用户通知详情
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * @date 2023-11-01
     */
    public function getUserNotify(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $user_id = $request->input('user_id'); // 用户ID
        $user_notify_id = $request->input('user_notify_id'); // 用户通知ID

        if (blank($user_id)) {
            $this->missingField('user_id');
            return $this->returnJson();
        }
        if (blank($user_notify_id)) {
            $this->missingField('user_notify_id');
            return $this->returnJson();
        }
        $user_notify = UserNotify::where('user_notify_id', $user_notify_id)
            ->where(function ($query) use ($user_id) {
                $query->where('user_id', $user_id)
                    ->orWhereNull('user_id');
            })
            // 发布时间为空或者到发布时间
            ->where(function ($query) {
                $query->whereNull('gmt_release')
                    ->orWhere('gmt_release', '<=', now());
            })
            // 排序，关联user_notify_to_user表，筛选未删除的数据
            ->leftJoinSub(function ($query) use ($user_id) {
                $query->select('user_notify_id AS n2u_user_notify_id', 'gmt_read', 'gmt_delete')
                    ->from('user_notify_to_user')
                    ->where('user_id', $user_id);
            }, 'user_notify_to_user', function ($join) {
                $join->on('user_notify_id', '=', 'n2u_user_notify_id');
            })
            ->whereNull('user_notify_to_user.gmt_delete')
            ->first();
        if (blank($user_notify)) {
            $this->notFoundData('user_notify_id');
            return $this->returnJson();
        }
        // 显示创建时间
        $user_notify->makeVisible(['gmt_create']);
        $description = $user_notify->description()->firstWhere('language_code', $language_code);
        $this->data = array_merge($user_notify->toArray(), $description ? $description->only(['title', 'content', 'image_url']) : []);

        // 未读则更新已读时间
        $user_notify_to_user = UserNotifyToUser::firstOrCreate([
            'user_id' => $user_id,
            'user_notify_id' => $user_notify_id,
            'gmt_delete' => null,
        ]);
        if (blank($user_notify_to_user->gmt_read)) {
            $user_notify_to_user->gmt_read = date('Y-m-d H:i:s');
            $user_notify_to_user->save();
        }
        $this->data['gmt_read'] = $user_notify_to_user->gmt_read;
        unset($this->data['n2u_user_notify_id'], $this->data['gmt_delete']);

        return $this->returnJson();
    }

    public function deleteUserNotify(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $user_id = $request->input('user_id'); // 用户ID
        $user_notify_id = $request->input('user_notify_id'); // 用户通知ID

        if (blank($user_id)) {
            $this->missingField('user_id');
            return $this->returnJson();
        }
        if (blank($user_notify_id)) {
            $this->missingField('user_notify_id');
            return $this->returnJson();
        }
        $user_notify = UserNotify::where('user_notify_id', $user_notify_id)
            ->where(function ($query) use ($user_id) {
                $query->where('user_id', $user_id)
                    ->orWhereNull('user_id');
            })
            // 发布时间为空或者到发布时间
            ->where(function ($query) {
                $query->whereNull('gmt_release')
                    ->orWhere('gmt_release', '<=', now());
            })
            ->leftJoinSub(function ($query) use ($user_id) {
                $query->select('user_notify_id AS n2u_user_notify_id', 'gmt_read', 'gmt_delete')
                    ->from('user_notify_to_user')
                    ->where('user_id', $user_id);
            }, 'user_notify_to_user', function ($join) {
                $join->on('user_notify_id', '=', 'n2u_user_notify_id');
            })
            ->whereNull('user_notify_to_user.gmt_delete')
            ->first();
        if (blank($user_notify)) {
            $this->notFoundData('user_notify_id');
            return $this->returnJson();
        }
        // 如果是群发通知，则删除user_notify_to_user表中的数据（软删除）
        if (blank($user_notify->user_id)) {
            // 关联表
            $user_notify_to_user = UserNotifyToUser::firstOrCreate([
                'user_notify_id' => $user_notify_id,
                'user_id' => $user_id,
            ]);
            $user_notify_to_user->gmt_delete = date('Y-m-d H:i:s');
            $this->data = $user_notify_to_user->save();
        } else {
            // 如果是单发通知，则删除user_notify表和user_notify_description表和user_notify_to_user表中的数据 (真删除)
            UserNotifyToUser::where('user_notify_id', $user_notify_id)
                ->where('user_id', $user_id)->forceDelete();
            $user_notify->description()->delete();
            $user_notify->delete();
            $this->data = true;
        }

        return $this->returnJson();
    }

    // 一键已读用户通知
    public function readAllUserNotify(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $user_id = $request->input('user_id'); // 用户ID

        if (blank($user_id)) {
            $this->missingField('user_id');
            return $this->returnJson();
        }
        // 获取未读的通知ID
        $user_notify_id_list = UserNotify::where(function ($query) use ($user_id) {
            $query->where('user_id', $user_id)
                ->orWhereNull('user_id');
        })
            // 发布时间为空或者到发布时间
            ->where(function ($query) {
                $query->whereNull('gmt_release')
                    ->orWhere('gmt_release', '<=', now());
            })
            ->leftJoinSub(function ($query) use ($user_id) {
                $query->select('user_notify_id AS n2u_user_notify_id', 'gmt_read', 'gmt_delete')
                    ->from('user_notify_to_user')
                    ->where('user_id', $user_id);
            }, 'user_notify_to_user', function ($join) {
                $join->on('user_notify_id', '=', 'n2u_user_notify_id');
            })
            ->whereNull('user_notify_to_user.gmt_delete')
            ->whereNull('user_notify_to_user.gmt_read')
            ->pluck('user_notify_id');
        // 删除关联表中数据
        UserNotifyToUser::whereIn('user_notify_id', $user_notify_id_list)->forceDelete();
        // 批量创建已读的关联
        $user_notify_to_user_data_list = [];
        $current_time = date('Y-m-d H:i:s');
        foreach ($user_notify_id_list as $user_notify_id) {
            $user_notify_to_user_data_list[] = array(
                'user_notify_id' => $user_notify_id,
                'user_id' => $user_id,
                'gmt_read' => $current_time,
                'gmt_create' => $current_time,
                'gmt_modified' => $current_time,
            );
        }

        $this->data = UserNotifyToUser::insert($user_notify_to_user_data_list);

        return $this->returnJson();
    }

    /**
     * 用户通知未读数量
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * @date 2023-11-01
     */
    public function getUserNotifyUnreadCount(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $user_id = $request->input('user_id'); // 用户ID

        if (blank($user_id) || AppUser::find($user_id)->doesntExist()) {
            $this->notFoundData('user_id');
            return $this->returnJson();
        }
        // 获取未读通知数量
        $user_notify_unread_count = UserNotify::where(function ($query) use ($user_id) {
            $query->where('user_id', $user_id)
                ->orWhereNull('user_id');
        })
            // 发布时间为空或者到发布时间
            ->where(function ($query) {
                $query->whereNull('gmt_release')
                    ->orWhere('gmt_release', '<=', now());
            })
            ->leftJoinSub(function ($query) use ($user_id) {
                $query->select('user_notify_id AS n2u_user_notify_id', 'gmt_read', 'gmt_delete')
                    ->from('user_notify_to_user')
                    ->where('user_id', $user_id);
            }, 'user_notify_to_user', function ($join) {
                $join->on('user_notify_id', '=', 'n2u_user_notify_id');
            })
            ->whereNull('user_notify_to_user.gmt_delete')
            ->whereNull('user_notify_to_user.gmt_read')
            ->count();

        $this->data = $user_notify_unread_count;

        return $this->returnJson();
    }

    /**
     * 更新充电记录身份
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    public function updateChargeRecordIdentity(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $charge_record_id = $request->input('charge_record_id');
        $identity_type = $request->input('identity_type'); // 身份类型
        $identity_number = $request->input('identity_number'); // 身份号码

        $this->data = null;
        if (!empty($charge_record_id) && !empty($identity_type) && !empty($identity_number) && IdentityType::hasValue($identity_type)) {
            $charge_record = ChargeRecord::find($charge_record_id);
            if (filled($charge_record)) {
                $charge_record->identity_type = $identity_type;
                $charge_record->identity_number = $identity_number;
                $charge_record->save();
                $this->data = $this->chargeRecordArrayToJson($charge_record);
            } else {
                $this->missingField('charge_record_id');
            }
        } else {
            $this->missingField('charge_record_id|identity_type|identity_number');
        }

        return $this->returnJson();
    }

    /**
     * 更新充电记录用户ID
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    public function updateChargeRecordUserId(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $charge_record_id = $request->input('charge_record_id');
        $user_id = $request->input('user_id'); // 用户ID

        $this->data = false;
        if (blank($charge_record_id) || blank($user_id)) {
            $this->missingField('charge_record_id|user_id');
            return $this->returnJson();
        }

        $charge_record = ChargeRecord::find($charge_record_id);
        if (blank($charge_record)) {
            $this->notFoundData('charge_record_id');
            return $this->returnJson();
        }

        // 存储User ID
        $charge_record->user_id = $user_id;
        $this->data = $charge_record->save();

        return $this->returnJson();
    }

    public function listNews(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $page_size = (int)$request->input('page_size', 10); // 每页显示数量
        $current_page = (int)$request->input('current_page', 1); // 当前页码
        $news_category_id = $request->news_category_id; // 新闻分类ID
        $search_text = $request->search_text; // 搜索内容
        $user_id = $request->user_id; // 用户ID

        if (blank($user_id) || blank($user = AppUser::find($user_id))) {
            $this->missingField('user_id');
            return $this->returnJson();
        }
        // 获取user的会员卡的商户ID去重去空
        $merchant_id_list = $user->memberCard()->where('is_enable', 1)?->pluck('merchant_id')?->unique()?->toArray() ?? [];
        $is_null_merchant_id = in_array(null, $merchant_id_list);

        $news_list = News::where('is_enable', 1)
            // 大于发布时间
            ->where('gmt_release', '<=', now())
            ->with(['description' => function ($query) use ($language_code) {
                $query->where('language_code', $language_code);
            }])
            // 查询merchant_id在$merchant_id_list中的，如果$is_null_merchant_id为true，额外查询merchant_id为空的
            ->when(filled($merchant_id_list), function ($query) use ($merchant_id_list, $is_null_merchant_id) {
                // 如果存在NULL的商户ID，查询商户ID为空或者在user的会员卡的商户ID里面
                $query->when($is_null_merchant_id, function ($query) use ($merchant_id_list,) {
                    $query->where(function ($query) use ($merchant_id_list) {
                        $query->whereIn('merchant_id', $merchant_id_list)
                            ->orWhereNull('merchant_id');
                    });
                }, function ($query) use ($merchant_id_list) {
                    $query->whereIn('merchant_id', $merchant_id_list);
                });
            }, function ($query) use ($is_null_merchant_id) {
                // 如果不存在会员卡，不返回任何数据
                $query->whereRaw('1 = 2');
            })
            // 筛选搜索内容
            ->when(filled($search_text), function ($query) use ($language_code, $search_text) {
                $query->where(function ($query) use ($language_code, $search_text) {
                    $query->whereHas('description', function ($query) use ($language_code, $search_text) {
                        $query->where('language_code', $language_code)
                            ->where(function ($query) use ($search_text) {
                                $query->where('title', 'like', "%$search_text%")
                                    ->orWhere('content', 'like', "%$search_text%");
                            });
                    });
                });
            })
            // 如果传入了news_category_id，则优先按照中间表的sort_order排序。news和category多对多中间表关联
            ->when(filled($news_category_id), function ($query) use ($news_category_id) {
                $query->joinSub(function ($query) use ($news_category_id) {
                    $query->select('news_id', 'sort_order')
                        ->from('news_category_to_news')
                        ->where('news_category_id', $news_category_id);
                }, 'news_category_to_news', function ($join) {
                    $join->on('news.news_id', '=', 'news_category_to_news.news_id');
                })
                    ->orderBy('news_category_to_news.sort_order');
            })
            ->latest()
            ->paginate($page_size, ['*'], 'current_page', $current_page);

        // 循环合并描述和主表数据
        $news_list->data = $news_list->transform(function ($item) {
            $item = array_merge($item->toArray(), $item->firstDescription->only(['title', 'content', 'image_url']));
            unset($item['description']);
            return $item;
        });

        $this->data = array(
            'current_page' => $news_list->currentPage(),
            'total' => $news_list->total(),
            'page_total' => $news_list->lastPage(),
            "data" => $news_list->items(),
        );

        return $this->returnJson();
    }

    public function getNews(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $news_id = $request->input('news_id'); // 新闻ID
        $user_id = $request->input('user_id'); // 用户ID


        if (blank($user_id) || blank($user = AppUser::find($user_id))) {
            $this->missingField('user_id');
            return $this->returnJson();
        }
        if (blank($news_id)) {
            $this->missingField('news_id');
            return $this->returnJson();
        }
        // 获取user的会员卡的商户ID去重去空
        $merchant_id_list = $user->memberCard()->where('is_enable', 1)?->pluck('merchant_id')?->unique()?->toArray() ?? [];
        $is_null_merchant_id = in_array(null, $merchant_id_list);

        $news = News::where('news_id', $news_id)
            ->where('is_enable', 1)
            // 大于发布时间
            ->where('gmt_release', '<=', now())
            // 查询merchant_id在$merchant_id_list中的，如果$is_null_merchant_id为true，额外查询merchant_id为空的
            ->when(filled($merchant_id_list), function ($query) use ($merchant_id_list, $is_null_merchant_id) {
                // 如果存在NULL的商户ID，查询商户ID为空或者在user的会员卡的商户ID里面
                $query->when($is_null_merchant_id, function ($query) use ($merchant_id_list,) {
                    $query->where(function ($query) use ($merchant_id_list) {
                        $query->whereIn('merchant_id', $merchant_id_list)
                            ->orWhereNull('merchant_id');
                    });
                }, function ($query) use ($merchant_id_list) {
                    $query->whereIn('merchant_id', $merchant_id_list);
                });
            }, function ($query) use ($is_null_merchant_id) {
                // 如果不存在会员卡，不返回任何数据
                $query->whereRaw('1 = 2');
            })
            ->first();
        if (blank($news)) {
            $this->notFoundData('news_id');
            return $this->returnJson();
        }
        $this->data = $news;
        $description =  $news->description()?->firstWhere('language_code', $language_code)?->only(['title', 'content', 'image_url']);
        $this->data->title = $description['title'] ?? null;
        $this->data->content = $description['content'] ?? null;
        $this->data->image_url = $description['image_url'] ?? null;
        // 浏览量+1
        $news->increment('view_count');

        return $this->returnJson();
    }

    public function listHomeNewsCategory(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $user_id = $request->input('user_id'); // 用户ID

        if (blank($user_id) || blank($user = AppUser::find($user_id))) {
            $this->missingField('user_id');
            return $this->returnJson();
        }
        // 获取user的会员卡的商户ID去重去空
        $merchant_id_list = $user->memberCard()->where('is_enable', 1)?->pluck('merchant_id')?->unique()?->toArray() ?? [];
        $is_null_merchant_id = in_array(null, $merchant_id_list);
        $limit = 2;
        $news_category_list = NewsCategory::with(['description' => function ($query) use ($language_code) {
            $query->where('language_code', $language_code);
        }])
            // 查询merchant_id在$merchant_id_list中的，如果$is_null_merchant_id为true，额外查询merchant_id为空的
            ->when(filled($merchant_id_list), function ($query) use ($merchant_id_list, $is_null_merchant_id) {
                // 如果存在NULL的商户ID，查询商户ID为空或者在user的会员卡的商户ID里面
                $query->when($is_null_merchant_id, function ($query) use ($merchant_id_list,) {
                    $query->where(function ($query) use ($merchant_id_list) {
                        $query->whereIn('merchant_id', $merchant_id_list)
                            ->orWhereNull('merchant_id');
                    });
                }, function ($query) use ($merchant_id_list) {
                    $query->whereIn('merchant_id', $merchant_id_list);
                });
            }, function ($query) use ($is_null_merchant_id) {
                // 如果不存在会员卡，不返回任何数据
                $query->whereRaw('1 = 2');
            })
            // 获取分类下新闻的数量
            ->withCount(['news' => function ($query) use ($merchant_id_list, $is_null_merchant_id) {
                // 查询merchant_id在$merchant_id_list中的，如果$is_null_merchant_id为true，额外查询merchant_id为空的
                $query->when(filled($merchant_id_list), function ($query) use ($merchant_id_list, $is_null_merchant_id) {
                    // 如果存在NULL的商户ID，查询商户ID为空或者在user的会员卡的商户ID里面
                    $query->when($is_null_merchant_id, function ($query) use ($merchant_id_list,) {
                        $query->where(function ($query) use ($merchant_id_list) {
                            $query->whereIn('merchant_id', $merchant_id_list)
                                ->orWhereNull('merchant_id');
                        });
                    }, function ($query) use ($merchant_id_list) {
                        $query->whereIn('merchant_id', $merchant_id_list);
                    });
                }, function ($query) use ($is_null_merchant_id) {
                    // 如果不存在会员卡，不返回任何数据
                    $query->whereRaw('1 = 2');
                })
                    ->where('is_enable', 1)
                    // 大于发布时间
                    ->where('gmt_release', '<=', now());
            }])
            ->whereHas('news', function ($query) {
                $query->where('is_enable', 1)
                    // 大于发布时间
                    ->where('gmt_release', '<=', now());
            })
            ->latest()
            ->get();

        $this->data = $news_category_list->transform(function ($item) use ($limit, $language_code, $merchant_id_list, $is_null_merchant_id) {
            // 每个分类查询limit条新闻，优先按照中间表的sort_order、news的发布时间排序。news和category多对多中间表关联
            $item->news_list = $item->news()->with(['description' => function ($query) use ($language_code) {
                $query->where('language_code', $language_code);
            }])
                // 查询merchant_id在$merchant_id_list中的，如果$is_null_merchant_id为true，额外查询merchant_id为空的
                ->when(filled($merchant_id_list), function ($query) use ($merchant_id_list, $is_null_merchant_id) {
                    // 如果存在NULL的商户ID，查询商户ID为空或者在user的会员卡的商户ID里面
                    $query->when($is_null_merchant_id, function ($query) use ($merchant_id_list,) {
                        $query->where(function ($query) use ($merchant_id_list) {
                            $query->whereIn('merchant_id', $merchant_id_list)
                                ->orWhereNull('merchant_id');
                        });
                    }, function ($query) use ($merchant_id_list) {
                        $query->whereIn('merchant_id', $merchant_id_list);
                    });
                }, function ($query) use ($is_null_merchant_id) {
                    // 如果不存在会员卡，不返回任何数据
                    $query->whereRaw('1 = 2');
                })
                ->where('is_enable', 1)
                // 大于发布时间
                ->where('gmt_release', '<=', now())
                ->orderBy('news_category_to_news.sort_order')
                ->latest()
                ->limit($limit)
                ->get()
                ->transform(function ($news) {
                    $news = array_merge($news->only(['news_id', 'is_main_page_display_text_content', 'content_type']), $news->firstDescription->only(['title', 'content', 'image_url']));
                    unset($news['description'], $news['pivot']);
                    return $news;
                });


            $item = array_merge($item->toArray(), $item->firstDescription->only(['title']));
            $item['is_show_all'] = $item['news_count'] > $limit;

            unset($item['description'], $item['news'], $item['news_count']);

            return $item;
        });


        return $this->returnJson();
    }

    public function getKioskAuthorize(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $authorize_code = $request->input('authorize_code'); // 授权码
        $user_id = $request->input('user_id'); // 用户ID

        if (blank($authorize_code)) {
            $this->missingField('authorize_code');
            return $this->returnJson();
        }
        if (blank($user_id) || blank($user = AppUser::find($user_id))) {
            $this->missingField('user_id');
            return $this->returnJson();
        }

        // 判断授权码是否存在redis
        $kiosk_number = Redis::get('KioskBindAppUUIDToKioskNumber:' . $authorize_code);
        // 授权码UUID未找到
        if (blank($kiosk_number)) {
            $this->code = 40003;
            $this->message = __('api.error_authorize_code_expired');
            return $this->returnJson();
        }
        // kiosk已绑定用户
        if (filled(Redis::get('KioskBindAppKioskNumberToUser:' . $kiosk_number))) {
            $this->code = 40003;
            $this->message = __('api.error_authorize_code_used');
            return $this->returnJson();
        }
        $kiosk = Kiosk::with([
            'site.merchant',
            'kioskSetting',
            'kioskSetting.description' => function ($query) use ($language_code) {
                $query->where('language_code', $language_code);
            }
        ])
            ->firstWhere('kiosk_number', $kiosk_number);
        // kiosk不存在
        if (blank($kiosk)) {
            $this->code = 40003;
            $this->message = __('api.error_authorize_code_expired');
            return $this->returnJson();
        }

        $kiosk_setting_description = $kiosk->kioskSetting?->description()?->first();
        $merchant = $kiosk->site?->merchant;
        $this->data = [
            'kiosk_id' => $kiosk->kiosk_id,
            'kiosk_name' => $kiosk->name,
            'kiosk_main_image_url' => existsImage('public', $kiosk_setting_description->home_page_image_url),
            'merchant_name' => self::getValueFromLanguageArray($merchant?->name_json ?? null),
            'merchant_logo_image_url' => existsImage('public', $merchant?->logo_image_url),
        ];

        return $this->returnJson();
    }

    public function authorizeKiosk(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $authorize_code = $request->input('authorize_code'); // 授权码
        $user_id = $request->input('user_id'); // 用户ID

        $this->data = false;
        if (blank($authorize_code)) {
            $this->missingField('authorize_code');
            return $this->returnJson();
        }
        if (blank($user_id) || blank($user = AppUser::find($user_id))) {
            $this->missingField('user_id');
            return $this->returnJson();
        }

        // 判断授权码是否存在redis
        $kiosk_number = Redis::get('KioskBindAppUUIDToKioskNumber:' . $authorize_code);
        // 授权码UUID未找到
        if (blank($kiosk_number)) {
            $this->code = 40003;
            $this->message = __('api.error_authorize_code_expired');
            return $this->returnJson();
        }
        // kiosk已绑定用户
        if (filled(Redis::get('KioskBindAppKioskNumberToUser:' . $kiosk_number))) {
            $this->code = 40003;
            $this->message = __('api.error_authorize_code_used');
            return $this->returnJson();
        }
        // 二维码过期时间
        $qrcode_expired_time = env('KIOSK_BIND_APP_QR_CODE_SECOND', 600);
        try {
            // 重置过期时间，防止刚绑定就过期
            Redis::expire('KioskBindAppKioskNumberToUUID:' . $kiosk_number, $qrcode_expired_time);
            Redis::expire('KioskBindAppUUIDToKioskNumber:' . $authorize_code, $qrcode_expired_time);
            Redis::expire('KioskBindAppKioskNumberToUser:' . $kiosk_number, $qrcode_expired_time);
            // 绑定用户
            Redis::set('KioskBindAppKioskNumberToUser:' . $kiosk_number, $user_id);
        } catch (Exception $e) {
            $error = [
                'kiosk_number' => $kiosk_number,
                'authorize_code' => $authorize_code,
                'user_id' => $user_id,
                'error' => $e->getMessage(),
            ];
            logger('Kiosk Bind App QRCode AuthorizeKioskUserChargeRecordBillPayment: ' . json($error));
            $this->code = 40008;
            $this->message = __('api.error_operation_failed');
            return $this->returnJson();
        }

        $this->data = true;

        return $this->returnJson();
    }

    /**
     * 获取简单收费表充电记录信息
     *
     * @param ChargeRecord $charge_record
     * @return array|null
     * @Description
     * @example
     * @date 2023-07-18
     */
    protected function getSimpleTariffTableChargeRecordInfo(ChargeRecord $charge_record): ?array
    {
        return match ($charge_record->charge_value_type) {
            ChargeValueType::Time => $this->getSimpleTariffTableChargeRecordInfoByTime($charge_record), // 简单时间收费
            ChargeValueType::Energy => $this->getSimpleTariffTableChargeRecordInfoByEnergy($charge_record), // 简单电量收费
            default => null,
        };
    }

    /**
     * 获取简单时间收费表充电记录信息
     *
     * @param ChargeRecord $charge_record
     * @return array
     * @Description
     * @example
     * @date 2023-07-18
     */
    protected function getSimpleTariffTableChargeRecordInfoByTime(ChargeRecord $charge_record): ?array
    {
        // 判断必要参数
        if (blank($charge_record?->tariffTableRule?->simple_tariff_table_json)) {
            return null;
        }

        // 定义用到的参数
        $now = new DateTime(); // 当前时间
        $start_date_time = new DateTime($charge_record->gmt_power_on ?: $charge_record->gmt_start); // 开始时间 = 上电时间 或 开始充电时间
        $stop_date_time = $charge_record->gmt_stop ? new DateTime($charge_record->gmt_stop) : (clone $now); // 停止充电时间

        // 如果是预付，结束时间为pre_paid_charge_value
        if ($charge_record->charge_tariff_scheme === ChargeTariffScheme::PrePaid) {
            $stop_date_time = (clone $start_date_time)->modify("+{$charge_record->pre_paid_selected_charge_value} seconds");
            $gmt_receipt_stop = null;
        }
        $simple_tariff_table = $charge_record->tariffTableRule->simple_tariff_table_json; // 简单收费表JSON（自动转数组）

        // 计算简单时间收费
        $calculate_simple_time_tariff = ChargeFeeCalculation::calculateSimpleTimeTariff($start_date_time, $stop_date_time, $simple_tariff_table);

        // 填充充电支付记录模型
        $gmt_receipt_start = $start_date_time->format('Y-m-d H:i:s'); // 收据开始时间
        $gmt_receipt_stop = $gmt_receipt_stop ?? $stop_date_time->format('Y-m-d H:i:s'); // 收据结束时间
        $charge_payment_record = new ChargePaymentRecord();
        $charge_payment_record->fill([
            'charge_record_number' => $charge_record->charge_record_number, // 充电记录编号
            'charge_payment_record_number' => $charge_record->charge_record_number . env('DELIMITER') . generateHashCode($charge_record->charge_record_number . time()), // 充电支付记录编号
            'charge_value' => $stop_date_time->getTimestamp() - $start_date_time->getTimestamp(), // 充电量(s) = 停止充电时间 - 开始时间
            'charge_value_amount' => $calculate_simple_time_tariff['amount'], // 充电量金额
            'total_amount' => $calculate_simple_time_tariff['amount'], // 合共金额
            'payment_status' => PaymentStatusEnum::Pending, // 支付状态
            'gmt_payment_status' => date('Y-m-d H:i:s'), // 支付状态时间
            'gmt_receipt_start' => $gmt_receipt_start, // 收据开始时间
            'gmt_receipt_stop' => $gmt_receipt_stop, // 收据结束时间
        ]);

        return [
            'charge_payment_record' => $charge_payment_record, // 充电支付表
            'charge_payment_record_calculation' => [
                'charge_value_amount_calculation_json' => $calculate_simple_time_tariff, // 充电金额计算JSON
                'idling_penalty_amount_calculation_json' => null, // 闲置罚款金额计算JSON
            ],
            'gmt_idling_penalty_start' => null
        ];
    }

    /**
     * 获取简单电量收费表充电记录信息
     *
     * @param ChargeRecord $charge_record
     * @return array
     * @Description
     * @example
     * @date 2023-07-18
     */
    protected function getSimpleTariffTableChargeRecordInfoByEnergy(ChargeRecord $charge_record): ?array
    {

        // 判断必要参数
        if (blank($charge_record?->tariffTableRule?->simple_tariff_table_json)) {
            return null;
        }

        // 定义用到的参数
        $now = new DateTime(); // 当前时间
        $energy = $charge_record->charged_energy; // 电量 = 结束充电仪表值 - 开始充电仪表值
        $simple_tariff_table = $charge_record->tariffTableRule->simple_tariff_table_json;

        // 计算简单电量收费
        $calculate_simple_energy_tariff = ChargeFeeCalculation::calculateSimpleEnergyTariff($energy, $simple_tariff_table);

        // 填充充电支付记录模型
        $charge_payment_record = new ChargePaymentRecord();
        $gmt_receipt_start = $charge_record->gmt_power_on ?: $charge_record->gmt_start; // 收据开始时间
        $gmt_receipt_stop = null; // 收据结束时间
        if ($charge_record->charge_tariff_scheme === ChargeTariffScheme::PostPaid) {
            $gmt_receipt_stop = $charge_record->gmt_stop ?: $now->format('Y-m-d H:i:s');
        }
        $charge_payment_record->fill([
            'charge_record_number' => $charge_record->charge_record_number, // 充电记录编号
            'charge_payment_record_number' => $charge_record->charge_record_number . env('DELIMITER') . generateHashCode($charge_record->charge_record_number . time()), // 充电支付记录编号
            'charge_value' => $energy, // 充电量
            'charge_value_amount' => $calculate_simple_energy_tariff['amount'], // 充电量金额
            'total_amount' => $calculate_simple_energy_tariff['amount'], // 合共金额
            'payment_status' => PaymentStatusEnum::Pending, // 支付状态
            'gmt_payment_status' => date('Y-m-d H:i:s'), // 支付状态时间
            'gmt_receipt_start' => $gmt_receipt_start, // 收据开始时间
            'gmt_receipt_stop' => $gmt_receipt_stop, // 收据结束时间
        ]);

        return [
            'charge_payment_record' => $charge_payment_record, // 充电支付表
            'charge_payment_record_calculation' => [
                'charge_value_amount_calculation_json' => $calculate_simple_energy_tariff, // 充电金额计算JSON
                'idling_penalty_amount_calculation_json' => null, // 闲置罚款金额计算JSON
            ],
            'gmt_idling_penalty_start' => null
        ];
    }

    /**
     * 获取时间收费表充电记录信息
     *
     * @param ChargeRecord $charge_record
     * @return array|null
     * @Description
     * @example
     * @date 2023-07-19
     */
    protected function getTimeTariffTableChargeRecordInfo(ChargeRecord $charge_record): ?array
    {
        // 判断必要参数
        if (blank($charge_record?->tariffTableRule?->time_tariff_table_item_json)) {
            return null;
        }

        // 定义用到的参数
        $now = new DateTime(); // 当前时间
        $start_date_time = new DateTime($charge_record->gmt_power_on ?: $charge_record->gmt_start); // 开始时间 = 上电时间 或 开始充电时间
        $stop_date_time = $charge_record->gmt_stop ? new DateTime($charge_record->gmt_stop) : (clone $now); // 停止充电时间
        // 如果是预付，结束时间为pre_paid_charge_value
        if ($charge_record->charge_tariff_scheme === ChargeTariffScheme::PrePaid) {
            $stop_date_time = (clone $start_date_time)->modify("+{$charge_record->pre_paid_selected_charge_value} seconds");
            $gmt_receipt_stop = null;
        }
        $charge_value_interval = $charge_record->charge_value_interval; // 充电量间隔
        $time_tariff_table = $charge_record->tariffTableRule->time_tariff_table_item_json ?: []; // 时间收费表JSON（自动转数组）
        // 循环修改time_tariff_table_item_special为time_tariff_table_item_special_list
        foreach ($time_tariff_table as $key => $item) {
            $time_tariff_table[$key]['time_tariff_table_item_special_list'] = $item['time_tariff_table_item_special'] ?? [];
            unset($time_tariff_table[$key]['time_tariff_table_item_special']);
        }
        $public_holiday_list = $charge_record->tariffTableRule->public_holiday_json ?: []; // 公共假期JSON（自动转数组）
        $lms_mode_enum = $charge_record->lms_mode; // LMS模式

        $charge_payment_record_array = [];
        // 计算罚款
        $idling_penalty_tariff_table_item_json = $charge_record->tariffTableRule->idling_penalty_tariff_table_item_json ?: []; // 闲置罚款收费表JSON（自动转数组）
        $idling_penalty_start_date_time = (clone $start_date_time)->modify("+{$charge_record->post_paid_maximum_charge_time} seconds"); // 闲置罚款开始时间 = 开始时间 + 后付最大充电时间
        // 如果是后付且结束时间大于罚款开始时间
        if ($charge_record->charge_tariff_scheme === ChargeTariffScheme::PostPaid && $now > $idling_penalty_start_date_time) {
            // 设置了闲置罚款收费表
            if (filled($idling_penalty_tariff_table_item_json)) {
                $peak_time_table_item_json = $charge_record->tariffTableRule->peak_time_table_item_json ?: []; // 高峰时间收费表JSON（自动转数组）
                // 计算闲置罚款
                $idling_penalty = ChargeFeeCalculation::calculateIdlingPenalty($idling_penalty_start_date_time, $now, $idling_penalty_tariff_table_item_json, $peak_time_table_item_json, $public_holiday_list);

                // 填充充电支付记录模型
                $charge_payment_record_array = [
                    'gmt_idling_penalty_start' => $idling_penalty_start_date_time->format('Y-m-d H:i:s'), // 闲置罚款开始时间
                    'gmt_idling_penalty_stop' => $now->format('Y-m-d H:i:s'), // 闲置罚款结束时间
                    'idling_penalty_time' => $now->getTimestamp() - $idling_penalty_start_date_time->getTimestamp(), // 闲置罚款时间(s) =  now - 闲置罚款开始时间
                    'idling_penalty_amount' => $idling_penalty['amount'], // 闲置罚款金额
                    'total_amount' => $idling_penalty['amount'], // 合共金额
                ];
            }
            // 结束时间为罚款开始时间
            $stop_date_time = $idling_penalty_start_date_time;
        } else {
            $idling_penalty_start_date_time = null;
        }

        // 计算时间收费
        $calculate_time_tariff = ChargeFeeCalculation::calculateTimeTariff($start_date_time, $stop_date_time, $charge_value_interval, $time_tariff_table, $public_holiday_list, $lms_mode_enum);
        // dd($start_date_time, $stop_date_time, $charge_value_interval, $time_tariff_table, $public_holiday_list, $lms_mode_enum, $calculate_time_tariff);

        // 填充充电支付记录模型
        $gmt_receipt_start = $start_date_time->format('Y-m-d H:i:s'); // 收据开始时间
        $gmt_receipt_stop = $gmt_receipt_stop ?? $stop_date_time->format('Y-m-d H:i:s'); // 收据结束时间
        $charge_payment_record = new ChargePaymentRecord();
        $charge_payment_record_array = array_merge($charge_payment_record_array, [
            'charge_record_number' => $charge_record->charge_record_number, // 充电记录编号
            'charge_payment_record_number' => $charge_record->charge_record_number . env('DELIMITER') . generateHashCode($charge_record->charge_record_number . time()), // 充电支付记录编号
            'charge_value' => $stop_date_time->getTimestamp() - $start_date_time->getTimestamp(), // 充电量(s) = 停止充电时间 - 开始时间
            'charge_value_amount' => $calculate_time_tariff['amount'], // 充电量金额
            'payment_status' => PaymentStatusEnum::Pending, // 支付状态
            'gmt_payment_status' => date('Y-m-d H:i:s'), // 支付状态时间
            'gmt_receipt_start' => $gmt_receipt_start, // 收据开始时间
            'gmt_receipt_stop' => $gmt_receipt_stop, // 收据结束时间
        ]);
        if (isset($charge_payment_record_array['total_amount'])) {
            $charge_payment_record_array['total_amount'] += $calculate_time_tariff['amount']; // 合共金额
        } else {
            $charge_payment_record_array['total_amount'] = $calculate_time_tariff['amount']; // 合共金额
        }

        // 填充充电支付记录模型
        $charge_payment_record->fill($charge_payment_record_array);

        return [
            'charge_payment_record' => $charge_payment_record, // 充电支付表
            'charge_payment_record_calculation' => [
                'charge_value_amount_calculation_json' => $calculate_time_tariff, // 充电金额计算JSON
                'idling_penalty_amount_calculation_json' => $idling_penalty ?? null, // 闲置罚款金额计算JSON
            ],
            'gmt_idling_penalty_start' => $idling_penalty_start_date_time ?? null
        ];
    }

    /**
     * 获取电量收费表充电记录信息
     *
     * @param ChargeRecord $charge_record
     * @return array|null
     * @Description
     * @example
     * @date 2023-07-19
     */
    protected function getEnergyTariffTableChargeRecordInfo(ChargeRecord $charge_record): ?array
    {
        // 判断必要参数
        if (blank($charge_record?->tariffTableRule?->energy_tariff_table_item_json)) {
            return null;
        }

        // 定义用到的参数
        $now = new DateTime(); // 当前时间
        $start_date_time = new DateTime($charge_record->gmt_power_on ?: $charge_record->gmt_start); // 开始时间 = 上电时间 或 开始充电时间
        $stop_date_time = $charge_record->gmt_stop ? new DateTime($charge_record->gmt_stop) : (clone $now); // 停止充电时间

        // 如果是预付，结束时间为pre_paid_charge_value
        if ($charge_record->charge_tariff_scheme === ChargeTariffScheme::PrePaid) {
            $stop_date_time = (clone $start_date_time)->modify("+{$charge_record->pre_paid_selected_charge_value} seconds");
            $gmt_receipt_stop = null;
        }
        // $charged_energy_record_list = $charge_record->chargedEnergyRecord()->oldest('charge_record_charged_energy')->oldest()->get(); // 已充电量记录
        $charged_energy_record_list = $charge_record?->chargeRecordChargedEnergy?->charged_energy_item_json ?? []; // 已充电量记录
        // 按照charge_record_charged_energy排序，然后重置key
        $charged_energy_record_list = collect($charged_energy_record_list)->sortBy('charge_record_charged_energy')->values()->toArray();
        // if (filled($charged_energy_record_list)) $charged_energy_record_list->makeVisible(['gmt_create']);
        $energy_tariff_table = $charge_record->tariffTableRule->energy_tariff_table_item_json ?: []; // 电量收费表JSON（自动转数组）
        $peak_time_table_item_json = $charge_record->tariffTableRule->peak_time_table_item_json ?: []; // 高峰时间收费表JSON（自动转数组）
        $public_holiday_list = $charge_record->tariffTableRule->public_holiday_json ?: []; // 公共假期JSON（自动转数组）
        $lms_mode_enum = $charge_record->lms_mode; // LMS模式

        $charge_payment_record_array = [];
        // 计算罚款
        $idling_penalty_amount = 0;
        $idling_penalty_tariff_table_item_json = $charge_record->tariffTableRule->idling_penalty_tariff_table_item_json ?: []; // 闲置罚款收费表JSON（自动转数组）
        $idling_penalty_start_date_time = (clone $start_date_time)->modify("+{$charge_record->post_paid_maximum_charge_time} seconds"); // 闲置罚款开始时间 = 开始时间 + 后付最大充电时间

        // 当前时间大于开始时间+后付最大充电时间，计算罚款
        if ($charge_record->charge_tariff_scheme === ChargeTariffScheme::PostPaid && $now > $idling_penalty_start_date_time) {
            // 设置了闲置罚款收费表
            if (filled($idling_penalty_tariff_table_item_json)) {

                // 计算闲置罚款
                $idling_penalty = ChargeFeeCalculation::calculateIdlingPenalty($idling_penalty_start_date_time, $now, $idling_penalty_tariff_table_item_json, $peak_time_table_item_json, $public_holiday_list);

                // 填充充电支付记录模型
                $charge_payment_record_array = [
                    'gmt_idling_penalty_start' => $idling_penalty_start_date_time->format('Y-m-d H:i:s'), // 闲置罚款开始时间
                    'gmt_idling_penalty_stop' => $now->format('Y-m-d H:i:s'), // 闲置罚款结束时间
                    'idling_penalty_time' => $now->getTimestamp() - $idling_penalty_start_date_time->getTimestamp(), // 闲置罚款时间(s) =  now - 闲置罚款开始时间
                    'idling_penalty_amount' => $idling_penalty['amount'], // 闲置罚款金额
                    'total_amount' => $idling_penalty['amount'], // 合共金额
                ];
            }
            // 结束时间为罚款开始时间
            $stop_date_time = $idling_penalty_start_date_time;
        } else {
            $idling_penalty_start_date_time = null;
        }

        // 计算电量收费
        $calculate_energy_tariff = ChargeFeeCalculation::calculateEnergyTariff($charged_energy_record_list, $energy_tariff_table, $peak_time_table_item_json, $public_holiday_list, $charge_record->is_enable_round_up_tail_charge_value_calculation, $lms_mode_enum);

        // 填充充电支付记录模型
        $energy = $charge_record->charged_energy; // 电量 = 结束充电仪表值 - 开始充电仪表值
        $gmt_receipt_start = $start_date_time->format('Y-m-d H:i:s'); // 收据开始时间
        $gmt_receipt_stop = $gmt_receipt_stop ?? $stop_date_time->format('Y-m-d H:i:s'); // 收据结束时间
        $charge_payment_record = new ChargePaymentRecord();
        $charge_payment_record_array = array_merge($charge_payment_record_array, [
            'charge_record_number' => $charge_record->charge_record_number, // 充电记录编号
            'charge_payment_record_number' => $charge_record->charge_record_number . env('DELIMITER') . generateHashCode($charge_record->charge_record_number . time()), // 充电支付记录编号
            'charge_value' => $energy, // 充电量
            'charge_value_amount' => $calculate_energy_tariff['amount'], // 充电量金额
            'total_amount' => $calculate_energy_tariff['amount'], // 合共金额
            'payment_status' => PaymentStatusEnum::Pending, // 支付状态
            'gmt_payment_status' => date('Y-m-d H:i:s'), // 支付状态时间
        ]);
        if (isset($charge_payment_record_array['total_amount'])) {
            $charge_payment_record_array['total_amount'] += $calculate_energy_tariff['amount']; // 合共金额
        } else {
            $charge_payment_record_array['total_amount'] = $calculate_energy_tariff['amount']; // 合共金额
        }

        // 填充充电支付记录模型
        $charge_payment_record->fill($charge_payment_record_array);

        return [
            'charge_payment_record' => $charge_payment_record, // 充电支付表
            'charge_payment_record_calculation' => [
                'charge_value_amount_calculation_json' => $calculate_energy_tariff, // 充电金额计算JSON
                'idling_penalty_amount_calculation_json' => $idling_penalty ?? null, // 闲置罚款金额计算JSON
            ],
            'gmt_idling_penalty_start' => $idling_penalty_start_date_time ?? null
        ];
    }

    /**
     * 获取充电记录信息及支付记录数组
     *
     * @param integer $charge_record_id
     * @return ChargeRecord|null
     * @Description
     * @example
     * @date 2023-06-08
     */
    protected function getChargeRecordAndChargePaymentRecordByChargeRecordId(int $charge_record_id): ?ChargeRecord
    {
        if (blank($charge_record = ChargeRecord::with('chargePaymentRecord')->find($charge_record_id))) {
            $this->message = __('api.no_charge_record');
        } else {
            return $this->getChargeRecordAndChargePaymentRecordByChargeRecordModel($charge_record);
        }


        return $charge_record;
    }

    /**
     * 获取充电记录信息及支付记录数组
     *
     * @param integer $charge_record_id
     * @return ChargeRecord|null
     * @Description
     * @example
     * @date 2023-06-08
     */
    protected function getChargeRecordAndChargePaymentRecordByChargeRecordModel(ChargeRecord $charge_record): ?ChargeRecord
    {
        if (blank($charge_record)) {
            $this->message = __('api.no_charge_record');
        } else {
            $charge_record->charge_payment_record_list = $charge_record->chargePaymentRecord;
            unset($charge_record->chargePaymentRecord);
            $charge_record->merchant_name = self::getValueFromLanguageArray($charge_record->merchant_name);
            $charge_record->member_card_group_name = self::getValueFromLanguageArray($charge_record->member_card_group_name);
            $charge_record->site_name = self::getValueFromLanguageArray($charge_record->site_name);
            $charge_record->zone_name = self::getValueFromLanguageArray($charge_record->zone_name);
        }


        return $charge_record;
    }

    protected function setPaymentData()
    {
        $this->data['payment_charge_value_amount'] = null;
        $this->data['payment_idling_penalty_amount'] = null;
        $this->data['payment_charge_total'] = null;
        foreach ($this->data->charge_payment_record_list as $charge_payment_record) {
            if ($charge_payment_record->payment_status === PaymentStatusEnum::Pending) {
                $this->data['payment_charge_value_amount'] += $charge_payment_record->charge_value_amount; // 充电量金额
                $this->data['payment_idling_penalty_amount'] += $charge_payment_record->idling_penalty_amount; // 闲置罚款金额
                $this->data['payment_charge_total'] += $charge_payment_record->total_amount; // 合共金额
            }
        }
    }

    /**
     * 根据充电枪获取收费表
     *
     * @param Connector $connector
     * @return array
     * @Description
     * @example
     * @date 2023-07-13
     */
    protected static function getTariffTableByConnector(Connector $connector): array
    {
        $return = $connector->toArray();

        // Connector Setting
        $return['is_enable_estimate_amount'] = $connector?->setting?->is_enable_estimate_amount;
        $return['is_enable_verify_setting_token'] = $connector?->setting?->is_enable_verify_setting_token;
        $return['trial_charge_timeout'] = $connector?->setting?->trial_charge_timeout;
        $return['tariff_table_type'] = $connector?->setting?->tariff_table_type;

        // Tariff Table
        $tariff_table_model = match ($return['tariff_table_type']) {
            TariffTableType::SimpleTariffTable => $connector?->setting?->simpleTariffTable,
            TariffTableType::ComplexTimeTariffTable => $connector?->setting?->timeTariffTable,
            TariffTableType::ComplexEnergyTariffTable => $connector?->setting?->energyTariffTable,
            default => null,
        };

        $tariff_table_array = [
            'charge_tariff_scheme' => $return['tariff_table_type'] === TariffTableType::ComplexEnergyTariffTable ? ChargeTariffScheme::PostPaid : $tariff_table_model?->charge_tariff_scheme,
            'charge_value_type' => match ($return['tariff_table_type']) {
                TariffTableType::ComplexTimeTariffTable => ChargeValueType::Time,
                TariffTableType::ComplexEnergyTariffTable => ChargeValueType::Energy,
                default => $tariff_table_model?->charge_value_type,
            },
            'charge_value_interval' => $tariff_table_model?->charge_value_interval,
            'pre_paid_charge_value_maximum_selection' => $tariff_table_model?->pre_paid_charge_value_maximum_selection,
            'post_paid_identity_type' => $tariff_table_model?->post_paid_identity_type,
            'post_paid_maximum_charge_time' => $tariff_table_model?->post_paid_maximum_charge_time,
        ];


        $return = array_merge($return, $tariff_table_array);

        return $return;
    }

    /**
     * 获取简单收费表预估金额
     *
     * @param Connector $connector
     * @param string|integer $charge_value
     * @param string $gmt_start
     * @return array|null
     * @Description
     * @example
     * @date 2023-11-07
     */
    protected static function getSimpleTariffTableUnchargedEstimatedAmount(Connector $connector, string|int $charge_value, string $gmt_start): ?array
    {
        return match ($connector->setting->simpleTariffTable->charge_value_type) {
            ChargeValueType::Time => self::getSimpleTariffTableUnchargedEstimatedAmountByTime($connector, $charge_value, $gmt_start),
            ChargeValueType::Energy => self::getSimpleTariffTableUnchargedEstimatedAmountByEnergy($connector, $charge_value, $gmt_start),
            default => null,
        };
    }

    /**
     * 获取简单时间收费表预估金额
     *
     * @param Connector $connector
     * @param string|integer $charge_value
     * @param string $gmt_start
     * @return array|null
     * @Description
     * @example
     * @date 2023-11-07
     */
    protected static function getSimpleTariffTableUnchargedEstimatedAmountByTime(Connector $connector, string|int $charge_value, string $gmt_start): ?array
    {
        $start_date_time = new DateTime($gmt_start); // 开始时间
        $stop_date_time = (clone $start_date_time)->modify("+{$charge_value} seconds"); // 停止充电时间

        // 计算简单时间收费
        $calculate_simple_time_tariff = ChargeFeeCalculation::calculateSimpleTimeTariff($start_date_time, $stop_date_time, $connector->setting->simpleTariffTable->toArray());

        return [
            'charge_value_amount' => $calculate_simple_time_tariff['amount'], // 充电量金额
            'idling_penalty_amount' => 0, // 闲置罚款金额
        ];
    }

    /**
     * 获取简单电量收费表预估金额
     *
     * @param Connector $connector
     * @param string|integer $charge_value
     * @param string $gmt_start
     * @return array|null
     * @Description
     * @example
     * @date 2023-11-07
     */
    protected static function getSimpleTariffTableUnchargedEstimatedAmountByEnergy(Connector $connector, string|int $charge_value, string $gmt_start): ?array
    {
        // 计算简单电量收费
        $calculate_simple_energy_tariff = ChargeFeeCalculation::calculateSimpleEnergyTariff($charge_value, $connector->setting->simpleTariffTable->toArray());

        return [
            'charge_value_amount' => $calculate_simple_energy_tariff['amount'], // 充电量金额
            'idling_penalty_amount' => 0, // 闲置罚款金额
        ];
    }

    /**
     * 获取时间收费表预估金额
     *
     * @param Connector $connector
     * @param string|integer $charge_value
     * @param string $gmt_start
     * @return array|null
     * @Description
     * @example
     * @date 2023-11-07
     */
    protected static function getTimeTariffTableUnchargedEstimatedAmount(Connector $connector, string|int $charge_value, string $gmt_start): ?array
    {
        $time_tariff_table_model = $connector->setting->timeTariffTable; // 时间收费表模型
        $start_date_time = new DateTime($gmt_start); // 开始时间
        $stop_date_time = (clone $start_date_time)->modify("+{$charge_value} seconds"); // 停止充电时间
        $charge_value_interval = $time_tariff_table_model->charge_value_interval; // 充电量间隔
        $time_tariff_table_model->with([
            'item' => function ($query) {
                $query->whereNull('member_card_group_id');
            },
            // itemSpecial 按照时间排序
            'item.itemSpecial' => function ($query) {
                $query->oldest('start_time');
            }
        ]);
        // 将itemSpecial改名为time_tariff_table_item_special_list
        $time_tariff_table_model->item->map(function ($item) {
            $item->time_tariff_table_item_special_list = $item->itemSpecial->toArray();
            unset($item->itemSpecial);
        });
        $time_tariff_table = $time_tariff_table_model->item->toArray(); // 时间收费表JSON（自动转数组）
        $public_holiday_list = PublicHoliday::oldest('start_date')->get()->toArray(); // 公共假期JSON（自动转数组）
        $lms_mode_enum = $connector->chargePoint->site->lms_mode; // LMS模式

        // 计算罚款
        $idling_penalty_amount = 0;
        $idling_penalty_tariff_table_item_json = $connector->setting?->idlingPenaltyTariffTable?->item()->oldest('start_range')->get()->toArray() ?? []; // 闲置罚款收费表JSON（自动转数组）
        $idling_penalty_start_date_time = (clone $start_date_time)->modify("+{$time_tariff_table_model->post_paid_maximum_charge_time} seconds"); // 闲置罚款开始时间 = 开始时间 + 后付最大充电时间
        // 如果是后付且结束时间大于罚款开始时间
        if ($time_tariff_table_model->charge_tariff_scheme === ChargeTariffScheme::PostPaid && $stop_date_time > $idling_penalty_start_date_time) {
            // 设置了闲置罚款收费表
            if (filled($idling_penalty_tariff_table_item_json)) {
                $peak_time_table_item_json = $connector->setting?->peakTimeTable?->item()->oldest('start_time')->get()->toArray() ?? []; // 高峰时间收费表JSON（自动转数组）
                // 计算闲置罚款
                $idling_penalty = ChargeFeeCalculation::calculateIdlingPenalty($idling_penalty_start_date_time, $stop_date_time, $idling_penalty_tariff_table_item_json, $peak_time_table_item_json, $public_holiday_list);
                $idling_penalty_amount = $idling_penalty['amount']; // 闲置罚款金额
            }
            // 结束时间为罚款开始时间
            $stop_date_time = $idling_penalty_start_date_time;
        }

        // 计算时间收费
        $calculate_time_tariff = ChargeFeeCalculation::calculateTimeTariff($start_date_time, $stop_date_time, $charge_value_interval, $time_tariff_table, $public_holiday_list, $lms_mode_enum);

        return [
            'charge_value_amount' => $calculate_time_tariff['amount'], // 充电量金额
            'idling_penalty_amount' => $idling_penalty_amount, // 闲置罚款金额
        ];
    }

    /**
     * 获取电量收费表预估金额
     *
     * @param Connector $connector
     * @param string|integer $charge_value
     * @param string $gmt_start
     * @return array|null
     * @Description
     * @example
     * @date 2023-11-07
     */
    protected static function getEnergyTariffTableUnchargedEstimatedAmount(Connector $connector, string|int $charge_value, string $gmt_start): ?array
    {
        $charged_energy_record_list = [
            [
                'gmt_create' => $gmt_start,
                'charge_record_charged_energy' => 0, // 已充电量
                'this_time_charged_energy' => $charge_value, // 此次已充电量
            ]
        ]; // 已充电量记录
        $energy_tariff_table = $connector->setting->energyTariffTable->item()->oldest('start_range')->get()->toArray(); // 电量收费表JSON（自动转数组）
        $peak_time_table_item_json = $connector->setting?->peakTimeTable?->item()->oldest('start_time')->get()->toArray() ?? []; // 高峰时间收费表JSON（自动转数组）
        $public_holiday_list = PublicHoliday::oldest('start_date')->get()->toArray(); // 公共假期JSON（自动转数组）
        $lms_mode_enum = $connector->chargePoint->site->lms_mode; // LMS模式

        // 计算电量收费
        $calculate_energy_tariff = ChargeFeeCalculation::calculateEnergyTariff($charged_energy_record_list, $energy_tariff_table, $peak_time_table_item_json, $public_holiday_list, $connector->setting->energyTariffTable->is_enable_round_up_tail_charge_value_calculation, $lms_mode_enum);

        return [
            'charge_value_amount' => $calculate_energy_tariff['amount'], // 充电量金额
            'idling_penalty_amount' => 0, // 闲置罚款金额
        ];
    }

    /**
     * 获取可用支付方式列表(排除八达通)
     *
     * @return array
     * @Description
     * @example
     * @date 2023-11-28
     */
    protected static function getAvailablePaymentMethodList(): array
    {
        $available_payment_method_list = PaymentMethod::getValues() ?? [];
        $available_payment_method_list = array_values(array_diff($available_payment_method_list, [PaymentMethod::Octopus])); // 删除八达通

        return $available_payment_method_list;
    }



    // 更改用户头像
    protected function updateUserAvatar(Request $request, AppUser $user): AppUser
    {
        $avatar = $request->file('avatar'); // 头像文件

        $format_list = json_decode(file_get_contents(base_path() . '/format.json'), true);
        $allow_extension_list = $format_list['image'] ?? []; // 允许的文件扩展名
        $file_extension = strtolower($avatar->getClientOriginalExtension()); // 文件扩展名
        // 校验文件扩展名
        if (!in_array($file_extension, $allow_extension_list)) {
            $this->missingField('avatar');
            $this->message = __('api.error_file_extension', ['extension' => implode(',', $allow_extension_list)]);
            return $user;
        }
        // 校验文件是否有效
        if (!$avatar->isValid()) {
            $this->missingField('avatar');
            $this->message = __('api.error_file_valid');
            return $user;
        }
        // 校验文件是否上传成功
        if ($avatar->getError() !== 0) {
            $this->missingField('avatar');
            $this->message = __('api.error_file_upload');
            return $user;
        }

        // 判断是否存在文件夹app_user，没有则创建此文件夹
        if (!Storage::disk('avatar')->exists('app_user')) {
            Storage::disk('avatar')->makeDirectory('app_user');
        }
        // 保存文件
        $avatar_path = $avatar->store('app_user', 'avatar');
        $user->avatar_url = $avatar_path;

        return $user;
    }

    protected static function checkUserPassword(AppUser $user, string $password): bool
    {
        $password = trim($password);
        if (blank($password)) {
            return false;
        }
        if ($user->password !== hash('sha512', $password . $user->salt)) {
            return false;
        }

        return true;
    }

    protected function missingField(string $field_name): void
    {
        $this->code = 40002;
        // 如果字段名包含|，则为多个字段名
        if (str_contains($field_name, '|')) {
            $field_name = explode('|', $field_name);
            $field_name = array_map(function ($item) {
                return __('api.field_' . $item);
            }, $field_name);
            $field_name = implode('、', $field_name);
        } else {
            $field_name = __('api.field_' . $field_name);
        }
        $this->message = __('common.the_field_is_missing_the_correct_value', ['field' => $field_name]);
    }

    protected function notFoundData(string $name): void
    {
        $this->code = 40003;
        // 如果字段名包含|，则为多个字段名
        if (str_contains($name, '|')) {
            $name = explode('|', $name);
            $name = array_map(function ($item) {
                return __('api.field_' . $item);
            }, $name);
            $name = implode('、', $name);
        } else {
            $name = __('api.field_' . $name);
        }
        $this->message = __('common.text_not_found', ['field' => $name]);
    }

    protected function alreadyExists(string $field_name): void
    {
        $this->code = 40004;
        // 如果字段名包含|，则为多个字段名
        if (str_contains($field_name, '|')) {
            $field_name = explode('|', $field_name);
            $field_name = array_map(function ($item) {
                return __('api.field_' . $item);
            }, $field_name);
            $field_name = implode('、', $field_name);
        } else {
            $field_name = __('api.field_' . $field_name);
        }
        $this->message = __('common.text_already_exists', ['field' => $field_name]);
    }
}

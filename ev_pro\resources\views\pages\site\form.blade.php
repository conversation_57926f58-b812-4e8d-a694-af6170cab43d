@extends('layouts.contentLayoutMaster')
{{-- page title --}}
@section('title',$model->site_id ? __('common.text_edit') : __('common.text_add'))
{{-- vendor styles --}}
@section('vendor-styles')
    <link rel="stylesheet" type="text/css" href="{{asset('css/plugins/forms/validation/form-validation.css')}}">
    <link rel="stylesheet" type="text/css" href="{{asset('vendors/css/forms/select/select2.min.css')}}">
    <link rel="stylesheet" type="text/css" href="{{asset('vendors/css/leaflet/leaflet.css')}}">
    <link rel="stylesheet" type="text/css" href="{{asset('vendors/js/summernote/summernote-bs4.min.css')}}">
@endsection

{{-- page styles --}}
@section('page-styles')
    <link rel="stylesheet" type="text/css" href="{{asset('css/pages/app-users.css')}}">
@endsection

@section('content')
    <!-- users edit start -->
    <section class="users-edit">

        <form id="form" class="needs-validation novalidate" action="" method="post">
            <div class="breadcrumb_head">
                <h4 class="breadcrumb-wrapper">
                    <span class="text-muted fw-light">{{ __("$module_name.web_title") }} /</span> @if ($model->site_id)
                        {{ __('common.text_edit') }}
                    @else
                        {{ __('common.text_add') }}
                    @endif
                </h4>
            </div>
            @csrf
            <div class="breadcrumb_btn">
                <button type="button" class="btn btn-primary glow mb-1 mb-sm-0 mr-0 mr-sm-1"
                        onclick="submitBtn()">{{ __('common.save_btn') }}</button>
                <a class="btn btn-light" href="{{ $cancel_url }}"
                   role="button">{{ __('common.cancel_btn') }}</a>
            </div>
            <div class="card mt-2">
                <div class="card-body">
                    <ul class="nav nav-tabs mb-2" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link d-flex align-items-center active"
                               id="general-tab-check"
                               href="#general-check"
                               data-toggle="tab"
                               aria-controls="general-check" role="tab" aria-selected="true">
                                <i class="bx bx-detail"></i>{{ __('common.title_general') }}</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link d-flex align-items-center"
                               id="description-tab-check"
                               href="#description-check"
                               data-toggle="tab"
                               aria-controls="description-check" role="tab" aria-selected="true">
                                <i class="bx bx-book-content"></i>{{ __("$module_name.site_description") }}</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link d-flex align-items-center"
                               id="site_image-tab-check"
                               href="#site_image-check"
                               data-toggle="tab"
                               aria-controls="site_image-check" role="tab" aria-selected="true">
                                <i class='bx bx-images'></i>{{ __("$module_name.site_image") }}</a>
                        </li>
                    </ul>
                    <div class="tab-content">
                        {{-- 基本信息--}}
                        <div class="tab-pane active fade show"
                             id="general-check"
                             aria-labelledby="general-tab-check"
                             role="tabpanel">
                            <div class="row">
                                {{-- 编辑时或者单商户不显示商户编号 --}}
                                <div class="col-12 col-sm-12"
                                     @if (filled($model->site_id) || auth()->user()->merchant_number_list->count() == 1) style="display: none" @endif>
                                    <div class="form-group required">
                                        <div class="controls @error('merchant_number') is-invalid @enderror">
                                            <label>{{ __("$module_name.merchant") }}</label>
                                            <div class="row mt-1">
                                                <div id="merchant-info" class="col-12">
                                                    <input name="merchant_number"
                                                           value="{{ $model->merchant_number }}"
                                                           hidden/>
                                                    <div id="merchant-info-name"
                                                         class="btn btn-primary glow mb-1 mr-sm-1"
                                                         onclick="showMerchantManagerModal()" role="button">
                                                        <i class="bx bx-cog"
                                                           style="margin-right: 5px;"></i>{{ filled($model->merchant_number) && filled($model->merchant_name) ? $model->merchant_name : __('common.text_select_default') }}
                                                    </div>
                                                    {{--<div class="btn btn-warning mb-1 mr-sm-1"
                                                         onclick="clearMerchantManager()"
                                                         role="button">{{ __('common.text_clear') }}</div>--}}
                                                </div>
                                            </div>
                                        </div>
                                        @error('merchant_number')
                                        <div class="invalid-feedback">
                                            {{ $message }}
                                        </div>
                                        @enderror
                                    </div>
                                </div>

                                {{-- 编辑时不显示场地编号 --}}
                                <div class="col-12 col-sm-12"
                                     @if(filled($model->site_id)) style="display: none;" @endif>
                                    <div class="form-group required">
                                        <div class="controls">
                                            <label>{{ __("$module_name.site_number") }}</label>
                                            <input type="text"
                                                   class="form-control only_letter_number @error('site_number') is-invalid @enderror"
                                                   placeholder="{{ __("$module_name.site_number") }}"
                                                   value="{{ $model->site_number }}"
                                                   name="site_number" maxlength="10">
                                            @error('site_number')
                                            <div class="invalid-feedback">
                                                {{ $message }}
                                            </div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 col-sm-12">
                                    <div class="form-group">
                                        <div class="controls treeview-height @error('region_id') is-invalid @enderror">
                                            <label>{{ __("$module_name.region") }}</label>
                                            <div class="row mt-1">
                                                <div id="region-info" class="col-12">
                                                    <input name="region_id"
                                                            value="{{ $model->region_id }}" hidden/>
                                                    <div id="region-info-name"
                                                            class="btn btn-primary glow mb-1 mr-sm-1"
                                                            onclick="showRegionTreeModal()" role="button">
                                                        <i class="bx bx-cog"
                                                            style="margin-right: 5px;"></i>{{ filled($model->region_id) && filled($model->region_name) ? $model->region_name : __('common.text_select_default') }}
                                                    </div>
                                                    <div class="btn btn-warning mb-1 mr-sm-1"
                                                            onclick="clearRegion()"
                                                            role="button">{{ __('common.text_clear') }}</div>
                                                </div>
                                            </div>
                                        </div>
                                        @error('region_id')
                                        <div class="invalid-feedback">
                                            {{ $message }}
                                        </div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-12 col-sm-12 currency_code" style="display: none">
                                    <div class="form-group">
                                        <div class="controls @error('currency_code') is-invalid @enderror">
                                            <label>{{ __("$module_name.currency_code") }}</label>
                                            <select id="currency_code" name="currency_code"
                                                    class="form-control select2"
                                                    data-placeholder="{{ __("$module_name.currency_code") }}">
                                                <option value=""></option>
                                                @foreach ($merchant_currency_list as $merchant_currency)
                                                    <option @selected($model->currency_code == $merchant_currency['currency_code']) value="{{ $merchant_currency['currency_code'] }}">{{ $merchant_currency['currency_code'] }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        @error('currency_code')
                                            <div class="invalid-feedback">
                                                {{ $message }}
                                            </div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-12 col-sm-12">
                                    <div class="form-group">
                                        <div class="controls">
                                            <label>{{ __("$module_name.acronym") }}</label>
                                            <input type="text"
                                                   class="form-control @error('acronym') is-invalid @enderror"
                                                   placeholder="{{ __("$module_name.acronym") }}"
                                                   value="{{ $model->acronym }}"
                                                   name="acronym" maxlength="45">
                                            @error('acronym')
                                            <div class="invalid-feedback">
                                                {{ $message }}
                                            </div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 col-sm-12">
                                    <div class="form-group">
                                        <div class="controls custom-control custom-switch custom-switch-glow">
                                            <label
                                                class="slip_mutiSwitch">{{ __("$module_name.is_public_site") }}</label>
                                            <input type="checkbox"
                                                   class="custom-control-input form-control-lg @error('is_public_site') is-invalid @enderror"
                                                   name="is_public_site" id="customSwitchIsPublic" value="1"
                                                   @if ($model->is_public_site == 1) checked @endif>
                                            <label class="custom-control-label slip_mutiLable" for="customSwitchIsPublic"
                                                   style="margin-left: 15px">
                                                <span class="switch-icon-left"><i class="bx bx bx-check"></i></span>
                                                <span class="switch-icon-right"><i class="bx bx bx-x"></i></span>
                                            </label>
                                            @error('is_public_site')
                                            <div class="invalid-feedback">
                                                {{ $message }}
                                            </div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 col-sm-12">
                                    <div class="form-group required">
                                        <div class="controls @error('lms_mode') is-invalid @enderror">
                                            <label class="label_block">{{ __("$module_name.lms_mode") }}</label>
                                            <x-lms-mode-manager-select :lms-value="$model->lms_mode"/>
                                        </div>
                                        @error('lms_mode')
                                        <div class="invalid-feedback">
                                            {!! $message !!}
                                        </div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-12 col-sm-12">
                                    <div class="form-group required">
                                        <div class="controls">
                                            <label>{{ __("$module_name.maximum_charging_connector_count") }}</label>
                                            <input type="number" oninput='if(value.length > 9)value=value.slice(0,9)'
                                                   class="form-control @error('maximum_charging_connector_count') is-invalid @enderror"
                                                   placeholder="{{ __("$module_name.maximum_charging_connector_count") }}"
                                                   value="{{ $model->maximum_charging_connector_count }}"
                                                   name="maximum_charging_connector_count">
                                            @error('maximum_charging_connector_count')
                                            <div class="invalid-feedback">
                                                {{ $message }}
                                            </div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 col-sm-12">
                                    <div class="form-group">
                                        <div class="controls markdown_diy"
                                             style="width: 100%;">
                                            <label>{{ __("$module_name.main_image_url") }}</label>
                                            <x-file-manager
                                                :input-name="'main_image_url'"
                                                :value="$model->main_image_url"
                                                :show-tag-name="false"/>
                                        </div>
                                        @error("main_image_url")
                                        <div class="invalid-feedback">
                                            {{ $message }}
                                        </div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-12 col-sm-12">
                                    <div class="form-group">
                                        <div class="controls">
                                            <label>{{ __("$module_name.telephone") }}</label>
                                            <input type="text"
                                                   class="form-control @error('telephone') is-invalid @enderror"
                                                   placeholder="{{ __("$module_name.telephone") }}"
                                                   value="{{ $model->telephone }}"
                                                   name="telephone" maxlength="100">
                                            @error('telephone')
                                            <div class="invalid-feedback">
                                                {{ $message }}
                                            </div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 col-sm-12">
                                    <div class="form-group">
                                        <div class="controls">
                                            <label>{{ __("$module_name.email") }}</label>
                                            <input type="text" class="form-control @error('email') is-invalid @enderror"
                                                   placeholder="{{ __("$module_name.email") }}"
                                                   value="{{ $model->email }}"
                                                   name="email" maxlength="100">
                                            @error('email')
                                            <div class="invalid-feedback">
                                                {{ $message }}
                                            </div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 col-sm-3">
                                    <div class="form-group">
                                        <div class="controls">
                                            <label>{{ __("$module_name.longitude") }}</label>
                                            <input type="number"
                                                   class="form-control @error('longitude') is-invalid @enderror"
                                                   placeholder="{{ __("$module_name.longitude") }}"
                                                   value="{{ $model->longitude }}"
                                                   name="longitude" maxlength="11">
                                            @error('longitude')
                                            <div class="invalid-feedback">
                                                {{ $message }}
                                            </div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 col-sm-3">
                                    <div class="form-group">
                                        <div class="controls">
                                            <label>{{ __("$module_name.latitude") }}</label>
                                            <input type="number"
                                                   class="form-control @error('latitude') is-invalid @enderror"
                                                   placeholder="{{ __("$module_name.latitude") }}"
                                                   value="{{ $model->latitude }}"
                                                   name="latitude" maxlength="11">
                                            @error('latitude')
                                            <div class="invalid-feedback">
                                                {{ $message }}
                                            </div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 col-sm-6">
                                    <div class="form-group">
                                        <div class="controls">
                                            <br>
                                            <button type="button" class="btn btn-primary" data-toggle="modal"
                                                    data-target="#mapModal">
                                                {{ __("$module_name.select_location") }}
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 col-sm-12">
                                    <div class="form-group">
                                        <div class="controls">
                                            <label>{{ __("$module_name.remark") }}</label>
                                            <textarea class="form-control @error('remark') is-invalid @enderror"
                                                      placeholder="{{ __("$module_name.remark") }}" name="remark"
                                                      rows="3"
                                                      maxlength="1000">{{ $model->remark }}</textarea>
                                            @error('remark')
                                            <div class="invalid-feedback">
                                                {{ $message }}
                                            </div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 col-sm-12">
                                    <div class="form-group">
                                        <div class="controls d-inline-block">
                                            <label>{{ __("$module_name.sort_order") }}</label>
                                            <input type="number" class="form-control touchspin"
                                                   placeholder="{{ __("$module_name.sort_order") }}"
                                                   value="{{ $model->sort_order }}" name="sort_order">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {{-- 描述 --}}
                        <div class="tab-pane fade show"
                             id="description-check"
                             aria-labelledby="description-tab-check"
                             role="tabpanel">
                            <!-- Navigation Bar -->
                            <ul class="nav nav-tabs mt-2 mb-2" role="tablist">
                                @foreach($item as $site_description)
                                    <li class="nav-item">
                                        <a class="nav-link d-flex align-items-center @if($loop->first) active @endif"
                                           id="{{ $site_description['language_code'] }}-tab"
                                           href="#{{ $site_description['language_code'] }}"
                                           data-toggle="tab"
                                           aria-controls="{{ $site_description['language_code'] }}"
                                           role="tab"
                                           aria-selected="true">{{ $site_description['language_name'] }}</a>
                                    </li>
                                @endforeach
                            </ul>

                            <div class="tab-content">
                                @foreach ($item as $site_description)
                                    <div class="tab-pane @if ($loop->first) active @endif fade show"
                                         id="{{ $site_description['language_code'] }}"
                                         aria-labelledby="{{ $site_description['language_code'] }}-tab"
                                         role="tabpanel">
                                        <div class="row">
                                            <input type="hidden"
                                                   value="{{ $item[$site_description['language_code']]['site_description_id'] }}"
                                                   name="item[{{$site_description['language_code']}}][site_description_id]">
                                            <input type="hidden"
                                                   value="{{ $site_description['language_code'] }}"
                                                   name="item[{{$site_description['language_code']}}][language_code]">


                                            {{-- <div class="col-12 col-sm-12">
                                                <div class="form-group">
                                                    <div class="controls markdown_diy"
                                                         style="width: 100%;">
                                                        <label>{{ __("$module_name.main_image_url") }}</label>
                                                    <x-file-manager
                                                        :input-name="'item[' . $site_description['language_code'] . '][main_image_url]'"
                                                        :value="$item[$site_description['language_code']]['main_image_url']"
                                                        :src="$item[$site_description['language_code']]['main_image_url']"
                                                        :show-tag-name="false"/>
                                                </div>
                                                    <div class="invalid-feedback">
                                                    </div>
                                                </div>
                                            </div> --}}
                                            <div class="col-12 col-sm-12">
                                                <div class="form-group required">
                                                    <div class="controls">
                                                        <label>{{ __("$module_name.description_name") }}</label>
                                                        <textarea
                                                            class="form-control @error("item.{$site_description['language_code']}.name") is-invalid @enderror"
                                                            placeholder="{{ __("$module_name.description_name") }}"
                                                            name="item[{{$site_description['language_code']}}][name]"
                                                            rows="2"
                                                            maxlength="45">{{ $item[$site_description['language_code']]['name'] }}</textarea>
                                                        @error("item.{$site_description['language_code']}.name")
                                                        <div class="invalid-feedback">
                                                            {{ $message }}
                                                        </div>
                                                        @enderror
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-12 col-sm-12">
                                                <div class="form-group">
                                                    <div class="controls">
                                                        <label>{{ __("$module_name.address") }}</label>
                                                        <textarea
                                                            class="form-control @error("item.{$site_description['language_code']}.address") is-invalid @enderror"
                                                            placeholder="{{ __("$module_name.address") }}"
                                                            name="item[{{$site_description['language_code']}}][address]"
                                                            maxlength="1000">{{ $item[$site_description['language_code']]['address'] }}</textarea>
                                                        @error("item.{$site_description['language_code']}.address")
                                                        <div class="invalid-feedback">
                                                            {{ $message }}
                                                        </div>
                                                        @enderror
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-12 col-sm-12">
                                                <div class="form-group">
                                                    <div class="controls">
                                                        <label>{{ __("$module_name.google_map_url") }}</label>
                                                        <textarea
                                                            class="form-control @error("item.{$site_description['language_code']}.google_map_url") is-invalid @enderror"
                                                            placeholder="{{ __("$module_name.google_map_url") }}"
                                                            name="item[{{$site_description['language_code']}}][google_map_url]"
                                                            maxlength="1000">{{ $item[$site_description['language_code']]['google_map_url'] }}</textarea>
                                                        @error("item.{$site_description['language_code']}.google_map_url")
                                                        <div class="invalid-feedback">
                                                            {{ $message }}
                                                        </div>
                                                        @enderror
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-12 col-sm-12">
                                                <div class="form-group">
                                                    <div class="controls">
                                                        <label>{{ __("$module_name.apple_map_url") }}</label>
                                                        <textarea
                                                            class="form-control @error("item.{$site_description['language_code']}.apple_map_url") is-invalid @enderror"
                                                            placeholder="{{ __("$module_name.apple_map_url") }}"
                                                            name="item[{{$site_description['language_code']}}][apple_map_url]"
                                                            maxlength="1000">{{ $item[$site_description['language_code']]['apple_map_url'] }}</textarea>
                                                        @error("item.{$site_description['language_code']}.apple_map_url")
                                                        <div class="invalid-feedback">
                                                            {{ $message }}
                                                        </div>
                                                        @enderror
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-12 col-sm-12">
                                                <div class="form-group">
                                                    <div class="controls">
                                                        <label>{{ __("$module_name.amap_map_url") }}</label>
                                                        <textarea
                                                            class="form-control @error("item.{$site_description['language_code']}.amap_map_url") is-invalid @enderror"
                                                            placeholder="{{ __("$module_name.amap_map_url") }}"
                                                            name="item[{{$site_description['language_code']}}][amap_map_url]"
                                                            maxlength="1000">{{ $item[$site_description['language_code']]['amap_map_url'] }}</textarea>
                                                        @error("item.{$site_description['language_code']}.amap_map_url")
                                                        <div class="invalid-feedback">
                                                            {{ $message }}
                                                        </div>
                                                        @enderror
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-12 col-sm-12">
                                                <div class="form-group">
                                                    <div class="controls">
                                                        <label>{{ __("$module_name.opening_hours") }}</label>
                                                        <textarea
                                                            class="form-control @error("item.{$site_description['language_code']}.opening_hours") is-invalid @enderror"
                                                            placeholder="{{ __("$module_name.opening_hours") }}"
                                                            name="item[{{$site_description['language_code']}}][opening_hours]"
                                                            maxlength="1000">{{ $item[$site_description['language_code']]['opening_hours'] }}</textarea>
                                                        @error("item.{$site_description['language_code']}.opening_hours")
                                                        <div class="invalid-feedback">
                                                            {{ $message }}
                                                        </div>
                                                        @enderror
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-12 col-sm-12">
                                                <div class="form-group">
                                                    <div class="controls">
                                                        <label>{{ __("$module_name.description") }}</label>
                                                        <textarea
                                                            class="form-control @error("item.{$site_description['language_code']}.description") is-invalid @enderror"
                                                            placeholder="{{ __("$module_name.description") }}"
                                                            name="item[{{$site_description['language_code']}}][description]"
                                                            maxlength="1000">{{ $item[$site_description['language_code']]['description'] }}</textarea>
                                                        @error("item.{$site_description['language_code']}.description")
                                                        <div class="invalid-feedback">
                                                            {{ $message }}
                                                        </div>
                                                        @enderror
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-12 col-sm-12">
                                                <div class="form-group">
                                                    <div class="controls markdown_diy">
                                                        <label>{{ __("$module_name.fee") }}</label>
                                                        <textarea
                                                            class="form-control editorDisclaimerHtml @error("item.{$site_description['language_code']}.fee") is-invalid @enderror"
                                                            rows="3"
                                                            name="item[{{$site_description['language_code']}}][fee]"
                                                            placeholder="{{ __("$module_name.fee")}}">{{ $item[$site_description['language_code']]['fee'] }}</textarea>
                                                        @error("item.{$site_description['language_code']}.fee")
                                                        <div class="invalid-feedback">
                                                            {{ $message }}
                                                        </div>
                                                        @enderror
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>

                        {{-- 场地图片 --}}
                        <div class="tab-pane fade show"
                             id="site_image-check"
                             aria-labelledby="site_image-tab-check"
                             role="tabpanel">
                            <div class="col-12 col-sm-12">
                                <div class="form-group">
                                    <table
                                        class="table table-striped table-bordered table-hover student-list">
                                        <thead>
                                        <tr>
                                            <th scope="col">{{ __("$module_name.image_url") }}</th>
                                            <th scope="col">{{ __("$module_name.sort_order") }}</th>
                                            <th scope="col">{{ __('common.text_action') }}</th>
                                        </tr>
                                        </thead>
                                        <tbody class="site_image_tbody">
                                        @php
                                            $row = 0;
                                        @endphp
                                        @if(isset($site_image_list))
                                            @foreach ($site_image_list as $index => $site_image)
                                                <tr class="tr-{{ $index }}">
                                                    <td>
                                                        <x-file-manager
                                                            :input-name="'site_image_list['.$index.'][image_url]'"
                                                            :value="$site_image['image_url_name']"
                                                            :src="$site_image['image_url_name']"
                                                            :show-tag-name="false"/>
                                                        @error('site_image_list.'.$index.'.image_url')
                                                        <br>
                                                        <p class="is-invalid"
                                                           style="color: red;font-size:10px">{{ $errors->first('site_image_list.'.$index.'.image_url') }}</p>
                                                        @enderror
                                                        @if (isset($site_image['site_image_id']))
                                                            <input type="hidden"
                                                                   name="site_image_list[{{ $index }}][site_image_id]"
                                                                   value="{{ $site_image['site_image_id'] }}">
                                                        @endif
                                                    </td>
                                                    <td><input type="number" class="form-control"
                                                               name="site_image_list[{{ $index }}][sort_order]"
                                                               value="{{ $site_image['sort_order'] }}">
                                                        @error('site_image_list.'.$index.'.sort_order')
                                                        <br>
                                                        <p class="is-invalid"
                                                           style="color: red;font-size:10px">{{ $errors->first('site_image_list.'.$index.'.sort_order') }}</p>
                                                        @enderror
                                                    </td>
                                                    <td>
                                                        <button type="button"
                                                                onclick="removeImage({{ $index }})"
                                                                class="btn btn-danger btn-sm"><i
                                                                class="bx bx-minus"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                                @php
                                                    $row = $index++;
                                                @endphp
                                            @endforeach
                                        @endif
                                        </tbody>
                                        <tfoot>
                                        <tr>
                                            <td colspan="2"><input type="hidden"
                                                                   id="row_site_image"
                                                                   value={{ $row }}></td>
                                            <td>
                                                <button type="button" class="btn btn-primary btn-sm"
                                                        onclick="addSiteImage()">
                                                    <i
                                                        class="bx bx-plus"></i></button>
                                            </td>
                                        </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-12 d-flex flex-sm-row flex-column justify-content-end mt-1 mobile_block">
                        <button type="button" class="btn btn-primary glow mb-1 mb-sm-0 mr-0 mr-sm-1"
                                onclick="submitBtn()">{{ __('common.save_btn') }}</button>
                        <a class="btn btn-light" href="{{ $cancel_url }}"
                           role="button">{{ __('common.cancel_btn') }}</a>
                    </div>
                </div>
            </div>
        </form>
        <!-- Modal -->
        <div class="modal fade" id="mapModal" data-backdrop="static" data-keyboard="false" tabindex="-1"
             aria-labelledby="mapModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="mapModalLabel">{{ __("$module_name.select_location") }}</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div id="mapInModal" style="height: 400px;"></div>
                        <div class="form-group">
                            <label for="longitudeInput">{{ __("$module_name.longitude") }}</label>
                            <input type="text" class="form-control" id="longitudeInput"
                                   placeholder="{{ __("$module_name.longitude") }}">
                            <div class="invalid-feedback">
                                {{ __('validation.required', ['attribute' => __("$module_name.longitude")]) }}
                                {{ __('validation.numeric', ['attribute' => __("$module_name.latitude")]) }}
                                {{ __("$module_name.longitude_validation") }}
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="latitudeInput">{{ __("$module_name.latitude") }}</label>
                            <input type="text" class="form-control" id="latitudeInput"
                                   placeholder="{{ __("$module_name.latitude") }}">
                            <div class="invalid-feedback">
                                {{ __('validation.required', ['attribute' => __("$module_name.latitude")]) }}
                                {{ __('validation.numeric', ['attribute' => __("$module_name.latitude")]) }}
                                {{ __("$module_name.latitude_validation") }}
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer setting-footer">
                        <button type="button" class="btn btn-info"
                                onclick="defaultMapLocation()">{{ __("$module_name.reset_map_location") }}</button>
                        <div class="setting-btn-group">
                            <button type="button" class="btn btn-secondary"
                                    data-dismiss="modal">{{ __('common.close_btn') }}</button>
                            <button type="button" class="btn btn-primary"
                                    onclick="updateMapLocation()">{{ __("$module_name.update_map_location") }}</button>
                            <button type="button" class="btn btn-primary"
                                    onclick="submitMapLocation()">{{ __('common.submit_btn') }}</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <x-merchant-manager-modal/>
    <x-region-tree-modal/>

    <script>
        const textSelectDefault = "{{ __("common.text_select_default") }}";
        const textLongitude = 114.16586577; // 开始默认定位在中華人民共和國香港特別行政區政府總部
        const textLatitude = 22.28030591;
        var mapInModal; // 生成的map
        var marker; // map上的标记

        window.onload = function () {
            // 如果货币的select中无货币，或者只有1个货币，隐藏(数量应当还要判断空的option，所以数量+1)
            if ($('#currency_code option').length <= 2) {
                $('.currency_code').hide();
                // 如果只有一个货币，选中并禁用select2
                if ($('#currency_code option').length == 2) {
                    $('#currency_code').val($('#currency_code option[value!=""]').val()).trigger('change');
                    $('#currency_code').select2('disabled');
                }
            } else {
                
            }
            $('#mapModal').on('shown.bs.modal', function () {
                createMap($('input[name="latitude"]').val(), $('input[name="longitude"]').val());
            });
            $('#mapModal').on('hidden.bs.modal', function () {
                clearValidationMapLocationModal();
                // 清空地图标记
                if (marker) {
                    mapInModal.removeLayer(marker);
                }

                // 清空输入框的值
                $('#longitudeInput').val('');
                $('#latitudeInput').val('');
            });
            $('.editorDisclaimerHtml').summernote({
                placeholder: '{{ __("$module_name.fee")}}',
                tabsize: 2,
                height: '20rem'
            });

            const form = document.getElementById('form');//form为form表单的id
            var flag = false;  //监听变量用于提交一次表单
            form.onsubmit = function (e) {
                e.preventDefault();
                if (flag) {
                    return;
                }
                flag = true;
                setTimeout(function () {
                    form.submit();
                }, 10);
            };
        }

        function submitBtn() {
            // 查找富文本，如果为空textarea赋空
            $('.editorDisclaimerHtml').each(function (index, item) {
                var itemEL = $(item);
                itemEL.val(itemEL.summernote('isEmpty') ? '' : itemEL.summernote('code'));
            });

            $("#form").submit();
            $("button").attr("disabled", true);
        }

        // 设置商户弹窗
        function showMerchantManagerModal() {
            // 获取当前选中值
            checkMerchant($("#merchant-info input[name=merchant_number]").val());
            $('#merchantManagerModal').modal('show');
        };

        function merchantManagerModalSave() {
            // 获取选中数据
            var merchant_number = $("#merchantManagerModal input[name='merchant_number']:checked").val();
            var merchant_name = $("#merchantManagerModal input[name='merchant_number']:checked").next().children().text();
            // 关闭弹窗
            $("#merchantManagerModal .close").click();
            if (merchant_number && $('#merchant-info').length > 0) {
                // 更改选中值
                $("#merchant-info input[name=merchant_number]").val(merchant_number);
                var iconHtmlText = $('#merchant-info-name i')[0]?.outerHTML ?? '';
                $('#merchant-info-name').html(iconHtmlText + merchant_name);
            }
        }

        function clearMerchantManager() {
            if ($('#merchant-info').length > 0) {
                $("#merchant-info input[name=merchant_number]").val(null);
                var iconHtmlText = $('#merchant-info-name i')[0]?.outerHTML ?? '';
                $('#merchant-info-name').html(iconHtmlText + textSelectDefault);
            }
        }

        function showRegionTreeModal() {
            // 获取当前选中值
            checkRegion($("#region-info input[name=region_id]").val(), $("#merchant-info input[name=merchant_number]").val());
            $('#regionTreeModal').modal('show');
        }

        function regionTreeModalSave() {
            // 获取选中数据
            var regionId = $("#regionTreeModal input[name='region_id']:checked").val();
            var regionName = $("#regionTreeModal input[name='region_id']:checked").closest("label").text().trim();
            // 关闭弹窗
            $("#regionTreeModal .close").click();
            if (regionId) {
                // 更改选中值
                $("#region-info input[name=region_id]").val(regionId);
                let iconHtmlText = $('#region-info-name i')[0].outerHTML;
                $('#region-info-name').html(iconHtmlText + regionName);
            }
        }

        function clearRegion() {
            $("#region-info input[name=region_id]").val(null);
            let iconHtmlText = $('#region-info-name i')[0].outerHTML;
            $('#region-info-name').html(iconHtmlText + textSelectDefault);
        }

        function createMap(latitude = null, longitude = null) {
            // 如果有值就先移除map后再重新创建，保证可以移动过去
            if (mapInModal) {
                mapInModal.remove();
            }
            var latitudeInput = locationFormat(textLatitude);
            var longitudeInput = locationFormat(textLongitude);
            if (longitude != null && longitude != '' && latitude != null && latitude != '' && $.isNumeric(latitude) && $.isNumeric(longitude) && locationFormat(longitude) <= 180 && locationFormat(longitude) >= -180 && locationFormat(latitude) <= 90 && locationFormat(latitude) >= -90) {
                latitudeInput = locationFormat(latitude);
                longitudeInput = locationFormat(longitude);
                $('#latitudeInput').val(latitudeInput);
                $('#longitudeInput').val(longitudeInput);
            }
            mapInModal = L.map('mapInModal').setView([latitudeInput, longitudeInput], 12);
            var tileLayer = L.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
                maxZoom: 20,
            });

            tileLayer.addTo(mapInModal);
            marker = L.marker([latitudeInput, longitudeInput]).addTo(mapInModal);
            mapInModal.on('click', function (e) {
                var clickedLatLng = e.latlng;
                if (marker) {
                    mapInModal.removeLayer(marker);
                }
                marker = L.marker(clickedLatLng).addTo(mapInModal);
                $('#latitudeInput').val(locationFormat(clickedLatLng.lat));
                $('#longitudeInput').val(locationFormat(clickedLatLng.lng));
            });
            // 移除右下角官方自带的a链接
            $('.leaflet-control-attribution').remove();
        }

        // 通过获取填入的坐标更新地图位置
        function updateMapLocation() {
            if (!validationMapLocationModal()) return;
            var latitudeInput = $('#latitudeInput').val();
            var longitudeInput = $('#longitudeInput').val();
            if (longitudeInput != null && longitudeInput != '' && latitudeInput != null && latitudeInput != '' && $.isNumeric(latitudeInput) && $.isNumeric(longitudeInput)) {
                createMap(latitudeInput, longitudeInput);
            }
        }

        // 提交填入的坐标位置
        function submitMapLocation() {
            if (!validationMapLocationModal()) return;
            var latitudeInput = $('#latitudeInput').val();
            var longitudeInput = $('#longitudeInput').val();
            if (longitudeInput != null && longitudeInput != '' && latitudeInput != null && latitudeInput != '' && $.isNumeric(latitudeInput) && $.isNumeric(longitudeInput)) {
                $('input[name="latitude"]').val(locationFormat(latitudeInput));
                $('input[name="longitude"]').val(locationFormat(longitudeInput));
            }
            // 关闭弹窗
            $("#mapModal .close").click();
        }

        // 回到默认位置
        function defaultMapLocation() {
            createMap(textLatitude, textLongitude);
        }

        // 校验地图弹窗
        function validationMapLocationModal() {
            clearValidationMapLocationModal();
            // 获取值
            var longitudeInput = $('#longitudeInput').val();
            var latitudeInput = $('#latitudeInput').val();
            var result = true;
            if (longitudeInput === '' || longitudeInput === undefined || longitudeInput === 'undefined' || !$.isNumeric(longitudeInput) || (parseFloat(longitudeInput) > 180) || (parseFloat(longitudeInput) < -180)) {
                $('#longitudeInput').parent().find('.invalid-feedback').show();
                $('#longitudeInput').addClass('is-invalid');
                result = false;
            }
            if (latitudeInput === '' || latitudeInput === undefined || latitudeInput === 'undefined' || !$.isNumeric(latitudeInput) || (parseFloat(latitudeInput) > 90) || (parseFloat(latitudeInput) < -90)) {
                $('#latitudeInput').parent().find('.invalid-feedback').show();
                $('#latitudeInput').addClass('is-invalid');
                result = false;
            }
            return result;
        }

        // 清除地图弹窗校验
        function clearValidationMapLocationModal() {
            $('#latitudeInput').parent().parent().parent().find('.invalid-feedback').hide();
            $('#latitudeInput').removeClass('is-invalid');
            $('#longitudeInput').parent().parent().parent().find('.invalid-feedback').hide();
            $('#longitudeInput').removeClass('is-invalid');
        }

        function locationFormat(value) {
            return (parseFloat(parseFloat(value).toFixed(8)) ?? 0);
        }

        function addSiteImage() {
            var addRow = Number($("#row_site_image").val()) + 1;
            var tableHtml = `<tr class="tr-${addRow}">
                                <td><x-file-manager :show-tag-name="false" :input-name="'site_image_list[${addRow}][image_url]'" /></td>
                                <td><input type="number" class="form-control" name="site_image_list[${addRow}][sort_order]" value=0>
                                <td>
                                    <button type="button" onclick="removeImage(${addRow})" class="btn btn-danger btn-sm"><i class="bx bx-minus"></i></button>
                                </td>
                    </tr>`;
            $('table .site_image_tbody').append(tableHtml);
            $("#row_site_image").val(addRow);
        }

        function removeImage(row) {
            // 防止元素删掉了原本遗留的openFileManager弹出框还存在
            $('.openFileManager').popover('dispose');
            $('table .site_image_tbody .tr-' + row).remove();
        }
    </script>

    <!-- users edit ends -->
@endsection

{{-- vendor scripts --}}
@section('vendor-scripts')
    <script src="{{asset('vendors/js/forms/select/select2.full.min.js')}}"></script>
    <script src="{{asset('vendors/js/forms/validation/jquery.validate.min.js')}}"></script>
    <script src="{{asset('vendors/js/forms/spinner/jquery.bootstrap-touchspin.js')}}"></script>
    <script src="{{asset('vendors/js/leaflet/leaflet.js')}}"></script>
    <script src="{{asset('vendors/js/summernote/summernote-bs4.min.js')}}"></script>
@endsection

{{-- page scripts --}}
@section('page-scripts')
    <script src="{{asset('js/scripts/pages/app-users.js')}}"></script>
    <script src="{{asset('js/scripts/pages/common/common.js')}}"></script>
    <script src="{{asset('js/scripts/navs/navs.js')}}"></script>

    {{-- module manage dialog --}}
    <script src="{{asset('js/scripts/forms/custom-options.js')}}"></script>
    <link rel="stylesheet" href="{{asset('css/plugins/forms/custom-options.css')}}">
@endsection

<?php

namespace App\Enums;

use BenSampo\Enum\Enum;
use BenSampo\Enum\Contracts\LocalizedEnum;
use App\Enums\Traits\Tools;

/**
 * @method static Octopus
 * @method static UnplugCode
 * @method static PosPreAuthorization
 * @method static OctopusPreAuthorization
 * @method static AppUser
 */
final class IdentityType extends Enum implements LocalizedEnum
{
    use Tools;

    const Octopus = 'OCTOPUS';
    const UnplugCode = 'UNPLUG_CODE';
    const PosPreAuthorization = 'POS_PRE_AUTHORIZATION';
    const OctopusPreAuthorization = 'OCTOPUS_PRE_AUTHORIZATION';
    const AppUser = 'APP_USER';
}

<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;

class TimeTariffTableItem extends Model
{
    protected $table = 'time_tariff_table_item'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'time_tariff_table_item_id'; //定义主键，默认为id
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    protected $fillable = ['day_type', 'time_tariff_table_number', 'member_card_group_id', 'user_group_id', 'default_normal_rate', 'default_concessionary_rate'];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [
        'default_normal_rate' => 0, // 开始时间
        'default_concessionary_rate' => 0, // 结束时间
    ];

    /**
     * 类型转化器
     */
    protected $casts = [

    ];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    // creating event
    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->time_tariff_table_item_number = getUUID(TimeTariffTableItem::class, 'time_tariff_table_item_number', $model->time_tariff_table_number . '-');
        });
    }

    // 一对多关联特殊item表
    public function itemSpecial()
    {
        return $this->hasMany(TimeTariffTableItemSpecial::class, 'time_tariff_table_item_number', 'time_tariff_table_item_number');
    }
}

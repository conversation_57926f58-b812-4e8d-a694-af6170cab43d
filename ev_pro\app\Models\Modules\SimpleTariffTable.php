<?php

namespace App\Models\Modules;

use App\Enums\TariffTableType;
use Illuminate\Database\Eloquent\Model;
use App\Models\Traits\Emoji;

class SimpleTariffTable extends Model
{
    use emoji;

    protected $table = 'simple_tariff_table'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'simple_tariff_table_id'; //定义主键，默认为id
    // 表的唯一number
    public static $table_number = 'simple_tariff_table_number';
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    //protected $fillable = [];

    /**
     * 类型转化器
     */
    protected $casts = [
        'is_enable_top_up' => 'bool',
        'is_top_up_need_confirm_identity' => 'bool',
        'is_enable_use_remain_charge_value' => 'bool',
        'is_enable_admin_octopus_card_free_deduct' => 'bool',
        'is_enable_free_octopus_card' => 'bool',
        'is_enable_charge_value_adjust_selected_base_on_remain' => 'bool',
        'member_card_group_simple_tariff_table_item_json' => 'array',
        'user_group_simple_tariff_table_item_json' => 'array',
    ];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [
        'charge_value_interval' => 0, // 充电量间隔
        'rate' => 0, // 费率
        'pre_paid_charge_value_maximum_selection' => 0, // 预付充电量最大选择
        'post_paid_maximum_charge_time' => 0, // 后付最大充电时间
        'is_enable_top_up' => false, // 是否启用续充
        'is_enable_admin_octopus_card_free_deduct' => false, // 是否开启管理员八达通卡免费扣款
        'is_enable_free_octopus_card' => false, // 是否启用免费八达通卡
        'is_top_up_need_confirm_identity' => false, // 是否续充需要确认身份
        'is_enable_use_remain_charge_value' => false, // 是否允许使用剩余充电量
        'is_enable_charge_value_adjust_selected_base_on_remain' => true, // 是否启用根据剩余充电量调整已选充电量
        'sort_order' => 0, // 排序
    ];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * 查找多个connector setting
     */
    public function connectorSetting()
    {
        return $this->hasMany(ConnectorSetting::class, 'simple_tariff_table_number', 'simple_tariff_table_number')
            ->where('tariff_table_type', '=', TariffTableType::SimpleTariffTable);
    }

    /**
     * 一对多关联SimpleTariffTableItem
     */
    public function item()
    {
        return $this->hasMany(SimpleTariffTableItem::class, 'simple_tariff_table_number', 'simple_tariff_table_number');
    }

    // deleting event
    public static function boot()
    {
        parent::boot();

        static::deleting(function ($model) {
            $model->item()->delete();
        });

        /* static::updating(function ($model) {
            $model->item()->delete();
        }); */
    }
}

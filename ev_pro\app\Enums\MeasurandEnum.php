<?php

namespace App\Enums;

use BenSampo\Enum\Enum;
use BenSampo\Enum\Contracts\LocalizedEnum;
use App\Enums\Traits\Tools;

/**
 * @method static CurrentExport
 * @method static CurrentImport
 * @method static CurrentOffered
 * @method static EnergyActiveExportRegister
 * @method static EnergyActiveImportRegister
 * @method static EnergyReactiveExportRegister
 * @method static EnergyReactiveImportRegister
 * @method static EnergyActiveImportInterval
 * @method static EnergyActiveExportInterval
 * @method static EnergyReactiveExportInterval
 * @method static EnergyReactiveImportInterval
 * @method static FREQUENCY
 * @method static PowerActiveExport
 * @method static PowerActiveImport
 * @method static PowerFactor
 * @method static OFFERED
 * @method static PowerReactiveExport
 * @method static PowerReactiveImport
 * @method static RPM
 * @method static SOC
 * @method static TEMPERATURE
 * @method static VOLTAGE
 * @method static PowerApparent
 */
final class MeasurandEnum extends Enum implements LocalizedEnum
{
    use Tools;

    const CurrentExport = 'Current.Export';
    const CurrentImport = 'Current.Import';
    const CurrentOffered = 'Current.Offered';
    const EnergyActiveExportRegister = 'Energy.Active.Export.Register';
    const EnergyActiveImportRegister = 'Energy.Active.Import.Register';
    const EnergyReactiveExportRegister = 'Energy.Reactive.Export.Register';
    const EnergyReactiveImportRegister = 'Energy.Reactive.Import.Register';
    const EnergyActiveImportInterval = 'Energy.Active.Import.Interval';
    const EnergyActiveExportInterval = 'Energy.ActiveExport.Interval';
    const EnergyReactiveExportInterval = 'Energy.Reactive.Export.Interval';
    const EnergyReactiveImportInterval = 'Energy.Reactive.Import.Interval';
    const Frequency = 'Frequency';
    const PowerActiveExport = 'Power.Active.Export';
    const PowerActiveImport = 'Power.Active.Import';
    const PowerFactor = 'Power.Factor';
    const Offered = 'Power.Offered';
    const PowerReactiveExport = 'Power.Reactive.Export';
    const PowerReactiveImport = 'Power.Reactive.Import';
    const RPM = 'RPM';
    const SOC = 'SoC';
    const Temperature = 'Temperature';
    const Voltage = 'Voltage';
    const PowerApparent = 'Power.Apparent';
}

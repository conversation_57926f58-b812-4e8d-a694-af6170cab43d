<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;

use Illuminate\Http\{
    JsonResponse,
    Request,
};
use Illuminate\Foundation\Application;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use App\Enums\{
    EventTypeEnum,
    IdentityType,
    PaymentMethod,
    ChargeTariffScheme,
    ChargeValueType,
    LmsMode,
    TariffTableType,
    RemoteStopChargeReasonEnum,
    ReadingContextEnum,
    MeasurandEnum,
    PaymentStatusEnum,
    IdlingPenaltyEffectiveType,
    RemainChargeValueGenerationTrigger,
    ChargePreAuthorizationStatus,
    TransactionType,
    TransactionCategory,
    ChargeArrearsStatus,
    PaymentDeviceEnum,
    UserCredentialType,
};
use App\Models\Modules\{
    AppUser,
    ChargeRecordChargedEnergy,
    ChargeRecord,
    ChargeRecordTrialCharge,
    ChargeRecordTariffRule,
    Connector,
    ChargeRecordMeterValue,
    MemberCardGroup,
    UserGroup,
};

class ChargeRecordController extends CommonController
{
    protected static string $module_name = 'chargeRecord'; // 模块名称
    protected static string $delimiter; // 分隔符

    public function __construct(Request $request)
    {
        parent::__construct($request);
        self::$delimiter = env('DELIMITER', '-');
    }

    /**
     * 初始页
     *
     * @param Request $request
     * @return View|Application|Factory
     */
    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'list_url' => action([self::class, 'list']),
            'connector_name_search' => $request->get('connector_name_search'),
            'show_page_url' => action([self::class, 'showPage']),
            'charge_tariff_scheme_search' => $request->get('charge_tariff_scheme_search'),
            'charge_value_type_search' => $request->get('charge_value_type_search'),
            'charge_start_time_search' => $request->get('charge_start_time_search'),
        );

        $data['charge_tariff_scheme_list'] = array();
        foreach (ChargeTariffScheme::asSelectArray() as $value => $name) {
            $data['charge_tariff_scheme_list'][] = array(
                'name' => $name,
                'value' => $value,
            );
        }

        $data['charge_value_type_list'] = array();
        foreach (ChargeValueType::asSelectArray() as $value => $name) {
            $data['charge_value_type_list'][] = array(
                'name' => $name,
                'value' => $value,
            );
        }

        return view("pages.{$data['module_name']}.list", $data);
    }

    /**
     * 列表接口
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $length = (int)$request->input('length', 10);
        $search = $this->_searchList($request);
        $data_list = $search->paginate($length, ['*'], 'start');

        $result = array();

        foreach ($data_list as $data) {
            $is_pre_paid = $data->charge_tariff_scheme === ChargeTariffScheme::PrePaid; // 预付
            $is_post_paid = $data->charge_tariff_scheme === ChargeTariffScheme::PostPaid; // 后付
            $is_time = $data->charge_value_type === ChargeValueType::Time; // 时间
            $is_energy = $data->charge_value_type === ChargeValueType::Energy; // 能量

            $charge_value_interval = $data->charge_value_interval ?? 0;
            $charge_value_interval = match ($data->charge_value_type) {
                ChargeValueType::Time => round($charge_value_interval / 60) . __('common.unit_mins'),
                ChargeValueType::Energy => match ($data->tariff_table_type) {
                    TariffTableType::ComplexEnergyTariffTable => '—/—',
                    default => (float)(bcdiv($charge_value_interval, 1000, 3)) . __('common.unit_kwh'),
                },
                default => $charge_value_interval,
            };
            $site_name = $this->getValueFromLanguageArray($data->site_name) ?? '—/—';
            $zone_name = $this->getValueFromLanguageArray($data->zone_name) ?? '—/—';
            $merchant_name = $data->merchant_number ? ($this->getValueFromLanguageArray($data->merchant_name) ?? '—/—') : env('APP_NAME');
            $user_group_name = $this->getValueFromLanguageArray($data->user_group_name) ?? '—/—';
            $member_card_group_name = $this->getValueFromLanguageArray($data->member_card_group_name) ?? '—/—';

            $result[] = array(
                'charge_record_id' => $data->charge_record_id, // 充电记录ID
                'charge_record_number' => $data->charge_record_number, // 充电记录编号
                'connector_number' => $data->connector_number, // 充电枪编号
                'gmt_start' => $data->gmt_power_on ?? $data->gmt_start, // 开始充电时间
                'gmt_stop' => $data->gmt_stop ?? '—/—', // 停止充电时间
                'charged_time' => round($data->charged_time / 60) . __('common.unit_mins'), // 已充时间
                'charged_energy' => (float)(bcdiv($data->charged_energy, 1000, 3)) . __('common.unit_kwh'), // 已充电量
                'merchant_name' => $merchant_name, // 商户名称
                'user_email' => $data?->user?->email ?? ($data?->user?->telephone ?? '—/—'), // 用户邮箱/电话
                'user_group_name' => $user_group_name, // 用户组名称
                'user_credential_type' => UserCredentialType::getDescription($data->user_credential_type), // 用户凭证类型
                'user_credential_number' => $data->user_credential_number ?? '—/—', // 用户凭证号码
                'member_name' => $data->member_name ?? '—/—', // 会员名称
                'member_card_group_name' => $member_card_group_name, // 会员卡组名称
                'site_name' => $site_name, // 场地名称
                'zone_name' => $zone_name, // 区域名称
                'parking_bay_number' => $data->parking_bay_number ?? '—/—', // 停车位编号
                'charge_point_name' => $data->charge_point_name ?? '—/—', // 充电机名称
                'connector_name' => $data->connector_name ?? '—/—', // 充电枪名称
                'lms_mode' => LmsMode::getDescription($data->lms_mode), // LMS模式
                'is_record_charge_record_meter_value' => $data->is_record_charge_record_meter_value, // 是否记录充电记录仪表值
                'is_enable_charge_arrears' => $data->is_enable_charge_arrears, // 是否启用充电欠款
                'license_plate_number' => $data->license_plate_number ?? '—/—', // 车牌号码
                'gmt_park_sensor_entry' => $data->gmt_park_sensor_entry ?? '—/—', // 停车传感器进场时间
                'is_enable_push_notification' => $data->is_enable_push_notification, // 是否启用推送通知
                'is_allow_points_overdraft' => $data->is_allow_points_overdraft, // 是否允许积分透支
                'charge_tariff_scheme' => ChargeTariffScheme::getDescription($data->charge_tariff_scheme), // 充电收费方案
                'charge_value_type' => ChargeValueType::getDescription($data->charge_value_type), // 充电量类型
                'charge_value_interval' => $charge_value_interval, // 充电量间隔 - 选择/收费
                'charge_value_amount' => __('common.unit_hk') . (float)(bcdiv($data->charge_value_amount, 100, 1)), // 充电量金额
                'idling_penalty_amount' => __('common.unit_hk') . (float)(bcdiv($data->idling_penalty_amount, 100, 1)), // 闲置罚款金额
                'total_amount' => __('common.unit_hk') . (float)(bcdiv($data->total_amount, 100, 1)), // 合共金额
                'actual_payment_amount' => __('common.unit_hk') . (float)(bcdiv($data->actual_payment_amount, 100, 1)), // 实际支付金额
                'use_points' => (float)(bcdiv($data->use_points, 100, 1)), // 使用积分
                'merchant_handling_fee_rate' => filled($data->merchant_handling_fee_rate) ? ($data->merchant_handling_fee_rate . __('common.unit_percent_blank')) : '—/—', // 商户手续费率
                'merchant_handling_fee' => __('common.unit_hk') . (float)(bcdiv($data->merchant_handling_fee, 100, 2)), // 商户手续费金额
                'merchant_receivable' => __('common.unit_hk') . (float)(bcdiv($data->merchant_receivable, 100, 2)), // 商户应收金额
                'pre_paid_charge_value' => $is_pre_paid ? ($is_time ? round($data->pre_paid_charge_value / 60) . __('common.unit_mins') : (float)(bcdiv($data->pre_paid_charge_value, 1000, 3)) . __('common.unit_kwh')) : '—/—', // 预付充电量
                'pre_paid_purchase_charge_value' => $is_pre_paid ? ($is_time ? round($data->pre_paid_purchase_charge_value / 60) . __('common.unit_mins') : (float)(bcdiv($data->pre_paid_purchase_charge_value, 1000, 3)) . __('common.unit_kwh')) : '—/—', // 预付购买充电量
                'pre_paid_use_remain_charge_value' => $is_pre_paid ? ($is_time ? round($data->pre_paid_use_remain_charge_value / 60) . __('common.unit_mins') : (float)(bcdiv($data->pre_paid_use_remain_charge_value, 1000, 3)) . __('common.unit_kwh')) : '—/—', // 预付使用剩余充电量
                'pre_paid_charge_value_maximum_selection' => $is_pre_paid ? ($is_time ? round($data->pre_paid_charge_value_maximum_selection / 60) . __('common.unit_mins') : (float)(bcdiv($data->pre_paid_charge_value_maximum_selection, 1000, 3)) . __('common.unit_kwh')) : '—/—', // 预付充电量最大选择量
                'post_paid_purchase_charge_value' => $is_post_paid ? ($is_time ? round($data->post_paid_purchase_charge_value / 60) . __('common.unit_mins') : (float)(bcdiv($data->post_paid_purchase_charge_value, 1000, 3)) . __('common.unit_kwh')) : '—/—', // 后付购买充电量
                'post_paid_maximum_charge_time' => $is_post_paid ? round($data->post_paid_maximum_charge_time / 60) . __('common.unit_mins') : '—/—', // 后付最大充电时间
                'idling_penalty_effective_type' => IdlingPenaltyEffectiveType::getDescription($data->idling_penalty_effective_type), // 闲置罚款生效类型
                'is_enable_member_card_group_tariff_table' => $data->is_enable_member_card_group_tariff_table, // 是否启用会员卡组收费表
                'is_enable_user_group_tariff_table' => $data->is_enable_user_group_tariff_table, // 是否启用用户组收费表
                'idling_penalty_grace_period' => filled($data->idling_penalty_grace_period) ? (($data->idling_penalty_grace_period / 60) . __('common.unit_mins')) : '—/—', // 闲置罚款宽限期
                'tariff_table_type' => TariffTableType::getDescription($data->tariff_table_type), // 收费表类型
                'trial_charge_timeout' => filled($data->trial_charge_timeout) ? $data->trial_charge_timeout . __('common.unit_s') : '—/—', // 试充超时时间
                'gmt_trial_charge_reached' => $data->gmt_trial_charge_reached ?? '—/—', // 试充达标时间
                'charge_maximum_vehicle_soc_limit' => isset($data->charge_maximum_vehicle_soc_limit) ? ($data->charge_maximum_vehicle_soc_limit . __('common.unit_percent_blank')) : '—/—', // 充电最大车辆SoC限制
                'is_enable_admin_octopus_card_free_deduct' => $data->is_enable_admin_octopus_card_free_deduct, // 是否开启管理员八达通卡免费扣款
                'is_enable_free_octopus_card' => $data->is_enable_free_octopus_card, // 是否启用免费八达通卡
                'is_enable_top_up' => $data->is_enable_top_up, // 是否启用续充
                'top_up_buffer_limit' => filled($data->top_up_buffer_limit) ? $data->top_up_buffer_limit . __('common.unit_s') : '—/—', // 续充缓冲限制 - 剩余充电量需大于此数才可续充
                'is_top_up_need_confirm_identity' => $data->is_top_up_need_confirm_identity, // 是否续充需要确认身份
                'remain_charge_value_generation_trigger' => RemainChargeValueGenerationTrigger::getDescription($data->remain_charge_value_generation_trigger), // 剩余充电量生成触发条件
                'remain_charge_value_validity_period' => filled($data->remain_charge_value_validity_period) ? ($data->remain_charge_value_validity_period / 60) . __('common.unit_mins') : '—/—', // 剩余充电量有效期
                'remain_charge_value_minimum_limit' => $data->remain_charge_value_minimum_limit ? match ($data->charge_value_type) {
                    ChargeValueType::Time => round($data->remain_charge_value_minimum_limit / 60) . __('common.unit_mins'),
                    ChargeValueType::Energy => (float)(bcdiv($data->remain_charge_value_minimum_limit, 1000, 3)) . __('common.unit_kwh'),
                    default => $data->remain_charge_value_minimum_limit,
                }
                    : '—/—', // 剩余充电量最小限制
                'is_enable_use_remain_charge_value' => $data->is_enable_use_remain_charge_value, // 是否允许使用剩余充电量
                'is_enable_charge_value_adjust_selected_base_on_remain' => $data->is_enable_charge_value_adjust_selected_base_on_remain, // 是否启用根据剩余充电量调整已选充电量
                'is_enable_round_up_tail_charge_value_calculation' => $data->is_enable_round_up_tail_charge_value_calculation, // 是否启用向上取整尾部充电量计算
                'start_id_tag' => $data->start_id_tag ?? '—/—', // 开始ID标识
                'meter_start' => $data->meter_start, // 开始充电仪表值
                'gmt_power_on' => $data->gmt_power_on ?? '—/—', // 上电时间
                'reminder_email' => $data->reminder_email ?? '—/—', // 提醒邮箱
                'reminder_telephone' => $data->reminder_telephone ?? '—/—', // 提醒电话
                'off_peak_charged_time' => round($data->off_peak_charged_time / 60) . __('common.unit_mins'), // 非高峰已充时间
                'on_peak_charged_time' => round($data->on_peak_charged_time / 60) . __('common.unit_mins'), // 高峰已充时间
                'off_peak_charged_energy' => (float)(bcdiv($data->off_peak_charged_energy, 1000, 3)) . __('common.unit_kwh'), // 非高峰已充电量
                'on_peak_charged_energy' => (float)(bcdiv($data->on_peak_charged_energy, 1000, 3)) . __('common.unit_kwh'), // 高峰已充电量
                'identity_type' => IdentityType::getDescription($data->identity_type), // 身份类型
                'identity_number' => $data->identity_number ?? '—/—', // 身份号码
                'charge_pre_authorization_record_number' => $data->charge_pre_authorization_record_number ?? '—/—', // 充电预授权记录编号
                'meter_value_voltage' => $data->meter_value_voltage ?? '—/—', // 仪表值-电压
                'meter_value_current' => $data->meter_value_current ?? '—/—', // 仪表值-电流
                'meter_value_power' => $data->meter_value_power ?? '—/—', // 仪表值-功率
                'meter_value_power_factor' => $data->meter_value_power_factor ?? '—/—', // 仪表值-功率因数
                'meter_value_power_offered' => $data->meter_value_power_offered ?? '—/—', // 仪表值-最大功率
                'meter_value_start_vehicle_soc' => $data->meter_value_start_vehicle_soc ?? '—/—', // 仪表值-开始车辆电量
                'meter_value_vehicle_soc' => $data->meter_value_vehicle_soc ?? '—/—', // 仪表值-车辆电量
                'meter_value_temperature' => $data->meter_value_temperature ?? '—/—', // 仪表值-温度
                'meter_value_fan_speed' => $data->meter_value_fan_speed ?? '—/—', // 仪表值-风扇速度
                'remote_stop_operator_type' => $data->remote_stop_operator_type ?? '—/—', // 远程停止操作者类型
                'remote_stop_operator_number' => $data->remote_stop_operator_number ?? '—/—', // 远程停止操作者编号
                'remote_stop_reason' => RemoteStopChargeReasonEnum::getDescription($data->remote_stop_reason), // 远程停止原因
                'remote_stop_detail' => $data->remote_stop_detail ?? '—/—', // 远程停止详情
                'gmt_remote_stop' => $data->gmt_remote_stop ?? '—/—', // 远程停止时间
                'stop_id_tag' => $data->stop_id_tag ?? '—/—', // 停止ID标识
                'meter_stop' => $data->meter_stop ?? '—/—', // 结束充电仪表值
                'stop_reason' => $data->stop_reason ?? '—/—', // 停止充电原因
                'remote_unlock_operator_type' => $data->remote_unlock_operator_type ?? '—/—', // 远程解锁操作者类型
                'remote_unlock_operator_number' => $data->remote_unlock_operator_number ?? '—/—', // 远程解锁操作者编号
                'gmt_remote_unlock' => $data->gmt_remote_unlock ?? '—/—', // 远程解锁时间
                'gmt_unlocked' => $data->gmt_unlocked ?? '—/—', // 解锁时间
                'idling_penalty_time' => round($data->idling_penalty_time / 60) . __('common.unit_mins'), // 闲置罚款时间
                'off_peak_idling_penalty_time' => round($data->off_peak_idling_penalty_time / 60) . __('common.unit_mins'), // 非高峰闲置罚款时间
                'on_peak_idling_penalty_time' => round($data->on_peak_idling_penalty_time / 60) . __('common.unit_mins'), // 高峰闲置罚款时间
                'gmt_idling_penalty_locked' => $data->gmt_idling_penalty_locked ?? '—/—', // 闲置罚款已锁定时间
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString() // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            'data' => $result,
        );

        return response()->json($json);
    }

    /**
     * 视图页
     *
     * @param Request $request
     * @param $charge_record_number
     * @return View|Application|Factory
     */
    public function view(Request $request, $charge_record_number): View|Application|Factory
    {
        // dataTable字段
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'],
            self::getUrlParams($request)
        );
        $data['charge_record_trial_charge_list_url'] = action([self::class, 'chargeRecordTrialChargeList']);
        $data['charge_record_meter_value_list_url'] = action([self::class, 'chargeRecordMeterValueList']);
        $data['charge_record_charged_energy_list_url'] = action([self::class, 'chargeRecordChargedEnergyList']);
        $data['magic_charge_url'] = action([self::class, 'magicCharge']);

        // 如果从仪表盘进入返回到仪表盘
        if (filled($request->get('module_name')) && "recentTransaction" == $request->get('module_name')) {
            $data['cancel_url'] = action(
                [RecentTransactionController::class, 'showPage'],
                RecentTransactionController::getUrlParams($request)
            );
        }

        // 获取充电记录信息
        $charge_record_info = ChargeRecord::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->with([
                'chargePreAuthorizationRecord',
                'chargeArrearsRecord',
                'emailSendRecord',
                'smsSendRecord',
                'eventLog',
                'chargePaymentRecord' => [
                    'pointsTransaction' => [
                        'appUser'
                    ],
                    'chargePaymentRecordCalculation'
                ]
            ])
            ->where('charge_record_number', $charge_record_number)
            ->firstOrFail();

        if (filled($charge_record_info)) {
            $charge_pre_authorization_record = null; // 充电预授权记录
            $charge_arrears_record = null; // 欠款记录
            $charge_payment_record_list = []; // 充电支付记录
            $points_transaction_list = []; // 积分交易记录
            $event_log_list = []; // 事件记录
            $error_log_list = []; // 错误事件记录
            $email_send_record_list = []; // 邮箱发送记录
            $sms_send_record_list = []; // 短信发送记录
            if (filled($charge_record_info->chargePreAuthorizationRecord)) {
                $charge_pre_authorization_record_model = $charge_record_info->chargePreAuthorizationRecord;
                switch ($charge_pre_authorization_record_model->payment_device) {
                    case PaymentDeviceEnum::Pos:
                        $payment_method = $charge_pre_authorization_record_model->pos_payment_method_name;
                        $payment_method_image = existsImage('icon', 'payment_methods/' . strtolower($charge_pre_authorization_record_model->pos_payment_method_name) . '.png');
                        $gmt_create = $charge_pre_authorization_record_model->gmt_pos_create ?? '—/—';
                        $card_number = $charge_pre_authorization_record_model->pos_card_number ?? '—/—';
                        break;
                    case PaymentDeviceEnum::Octopus:
                        $payment_method = $charge_pre_authorization_record_model->octopus_payment_method_name;
                        $payment_method_image = existsImage('icon', 'payment_methods/octopus.png');
                        $gmt_create = $charge_pre_authorization_record_model->gmt_octopus_deduct ?? '—/—';
                        $card_number = $charge_pre_authorization_record_model->octopus_card_number ?? '—/—';
                        break;
                    default:
                        $payment_method = null;
                        $payment_method_image = null;
                        $gmt_create = $charge_pre_authorization_record_model->gmt_create->toDateTimeString() ?? '—/—';
                        $card_number = '—/—';
                        break;
                }
                $charge_pre_authorization_record = [
                    'charge_pre_authorization_record_id' => $charge_pre_authorization_record_model->charge_pre_authorization_record_id, // 充电预授权记录ID
                    'charge_pre_authorization_record_number' => $charge_pre_authorization_record_model->charge_pre_authorization_record_number, // 充电预授权记录编号
                    'payment_device' => $charge_pre_authorization_record_model->payment_device, // 付款设备
                    'payment_method' => $payment_method, // 支付方式
                    'payment_method_image' => $payment_method_image, // 支付方式icon
                    'charge_pre_authorization_status' => ChargePreAuthorizationStatus::getDescription($charge_pre_authorization_record_model->charge_pre_authorization_status), // 充电预授权状态
                    'gmt_charge_pre_authorization_status' => $charge_pre_authorization_record_model->gmt_charge_pre_authorization_status ?? '—/—', // 充电预授权状态时间
                    'pre_authorization_amount' => (float)(bcdiv($charge_pre_authorization_record_model->pre_authorization_amount, 100, 1)), // 预授权金额
                    'deduct_amount' => (float)(bcdiv($charge_pre_authorization_record_model->deduct_amount ?? 0, 100, 1)), // 扣款金额
                    'refund_amount' => filled($charge_pre_authorization_record_model->refund_amount) ? (float)bcdiv($charge_pre_authorization_record_model->refund_amount, 100, 1) : '—/—', // 退款金额
                    'actual_refund_amount' => filled($charge_pre_authorization_record_model->actual_refund_amount) ? (float)bcdiv($charge_pre_authorization_record_model->actual_refund_amount, 100, 1) : '—/—', // 实际退款金额
                    'is_has_refund_amount' => filled($charge_pre_authorization_record_model->refund_amount) && $charge_pre_authorization_record_model->refund_amount > 0, // 是否有退款金额
                    'is_has_actual_refund_amount' => filled($charge_pre_authorization_record_model->actual_refund_amount), // 是否有实际退款金额
                    'is_refund_amount_equal_actual_refund_amount' => $charge_pre_authorization_record_model->refund_amount === $charge_pre_authorization_record_model->actual_refund_amount, // 退款金额是否等于实际退款金额
                    'card_number' => $card_number, // 卡号
                    'remark' => $charge_pre_authorization_record_model->remark ?? '—/—', // 备注
                    'gmt_create' => $gmt_create, // 创建时间
                    'gmt_modified' => $charge_pre_authorization_record_model->gmt_modified->toDateTimeString(),
                    'is_show_modify_status_button' => $charge_pre_authorization_record_model->charge_pre_authorization_status === ChargePreAuthorizationStatus::DeductFailure,
                    'modify_status_url' => action([ChargePreAuthorizationRecordController::class, 'modifyStatus']),
                ];
            }

            if (filled($charge_record_info->chargeArrearsRecord)) {
                $charge_arrears_record_model = $charge_record_info->chargeArrearsRecord;
                switch ($charge_arrears_record_model->identity_type) {
                    case IdentityType::Octopus:
                        $identity_type_image = existsImage('icon', 'payment_methods/' . strtolower(IdentityType::Octopus) . '.png');
                        break;
                    default:
                        $identity_type_image = null;
                        break;
                }
                $charge_arrears_record = [
                    'charge_arrears_record_id' => $charge_arrears_record_model->charge_arrears_record_id, // 充电欠款记录ID
                    'charge_arrears_record_number' => $charge_arrears_record_model->charge_arrears_record_number, // 充电欠款记录编号
                    'charge_arrears_status' => ChargeArrearsStatus::getDescription($charge_arrears_record_model->charge_arrears_status), // 充电欠款状态
                    'gmt_charge_arrears_status' => $charge_arrears_record_model->gmt_charge_arrears_status ?? '—/—', // 充电欠款状态时间
                    'identity_type' => IdentityType::getDescription($charge_arrears_record_model->identity_type), // 身份类型
                    'identity_type_image' => $identity_type_image, // 身份类型图片
                    'identity_number' => $charge_arrears_record_model->identity_number ?? '—/—', // 身份号码
                    'remark' => $charge_arrears_record_model->remark ?? '—/—', // 备注
                    'gmt_create' => $charge_arrears_record_model->gmt_create->toDateTimeString(),
                    'gmt_modified' => $charge_arrears_record_model->gmt_modified->toDateTimeString(),
                    'is_show_modify_status_button' => $charge_arrears_record_model->charge_arrears_status === ChargeArrearsStatus::CalculationFailure,
                    'modify_status_url' => action([ChargeArrearsRecordController::class, 'modifyStatus']),
                ];
            }

            // 获取邮箱发送记录
            foreach ($charge_record_info->emailSendRecord->sortByDesc('gmt_create') as $email_send_record) {
                // 判断发送状态 给前端拼接样式字符和多语言文件
                $send_state = null;
                if (!filled($email_send_record->gmt_sent)) {
                    if (strtotime($email_send_record->gmt_expiry) < strtotime('now')) {
                        $send_state = 'fail';
                    } else {
                        $send_state = 'pending';
                    }
                } else {
                    $send_state = 'success';
                }

                $email_send_record_list[] = array(
                    'recipient' => $email_send_record->recipient,
                    'title' => $email_send_record->title ?? '—/—',
                    'message' => $email_send_record->message ?? '—/—',
                    'gmt_sent' => $email_send_record->gmt_sent ?? null,
                    'gmt_expiry' => $email_send_record->gmt_expiry,
                    'send_state' => $send_state
                );
            }
            // 获取短信发送记录
            foreach ($charge_record_info->smsSendRecord->sortByDesc('gmt_create') as $sms_send_record) {
                // 判断发送状态 给前端拼接样式字符和多语言文件
                $send_state = null;
                if (!filled($sms_send_record->gmt_sent)) {
                    if (strtotime($sms_send_record->gmt_expiry) < strtotime('now')) {
                        $send_state = 'fail';
                    } else {
                        $send_state = 'pending';
                    }
                } else {
                    $send_state = 'success';
                }

                $sms_send_record_list[] = array(
                    'recipient' => $sms_send_record->recipient,
                    'message' => $sms_send_record->message ?? '—/—',
                    'gmt_sent' => $sms_send_record->gmt_sent ?? null,
                    'gmt_expiry' => $sms_send_record->gmt_expiry,
                    'send_state' => $send_state
                );
            }

            // 获取event_log
            foreach ($charge_record_info->eventLog->sortByDesc('gmt_create') as $event) {
                $event_log_list[] = array(
                    'date' => !empty($event->gmt_create) ? $event->gmt_create->toDateTimeString() : '—/—',
                    'event_log_id' => $event->event_log_id ?? '—/—',
                    'event_type' => EventTypeEnum::getDescription($event->event_type),
                    'description' => $event->description ?? '—/—'
                );
                $error_log_list[] = array(
                    'target' => $event->target ?? '—/—',
                    'target_number' => $event->target_number ?? '—/—',
                    'exception' => $event->exception ?? '—/—',
                    'gmt_create' => !empty($event->gmt_create) ? $event->gmt_create->toDateTimeString() : '—/—',
                );
            }

            // 获取支付记录
            foreach ($charge_record_info->chargePaymentRecord->sortByDesc('gmt_create') as $payment) {
                if (filled($payment->chargePaymentRecordCalculation)) {
                    $payment->chargePaymentRecordCalculation->charge_value_amount_calculation_json = is_string($payment?->chargePaymentRecordCalculation?->charge_value_amount_calculation_json) ? json_decode($payment?->chargePaymentRecordCalculation?->charge_value_amount_calculation_json, true) : $payment?->chargePaymentRecordCalculation?->charge_value_amount_calculation_json;
                    $payment->chargePaymentRecordCalculation->idling_penalty_amount_calculation_json = is_string($payment?->chargePaymentRecordCalculation?->idling_penalty_amount_calculation_json) ? json_decode($payment?->chargePaymentRecordCalculation?->idling_penalty_amount_calculation_json, true) : $payment?->chargePaymentRecordCalculation?->idling_penalty_amount_calculation_json;
                }
                $charge_payment_record = array(
                    'charge_payment_record_id' => $payment->charge_payment_record_id, // 充电支付记录ID
                    'charge_payment_record_number' => $payment->charge_payment_record_number, // 充电支付记录编号
                    'charge_record_number' => $payment->charge_record_number, // 充电记录编号
                    'payment_device' => $payment->payment_device,
                    'payment_method' => PaymentMethod::getDescription($payment->payment_method), // 支付方式
                    'payment_method_image' => existsImage('icon', 'payment_methods/' . strtolower($payment->payment_method ?? '') . '.png') ?? '', // 支付方式icon
                    'payment_status' => PaymentStatusEnum::getDescription($payment->payment_status), // 支付状态
                    'gmt_payment_status' => $payment->gmt_payment_status ?? '—/—', // 支付状态时间
                    'is_top_up' => $payment->is_top_up, // 是否续充
                    'charge_value' => (float)(bcdiv($payment->charge_value, 1000, 3)),
                    'charge_value_amount' => (float)(bcdiv($payment->charge_value_amount, 100, 1)), // 充电量金额
                    'charge_value_amount_calculation_json' => $payment?->chargePaymentRecordCalculation?->charge_value_amount_calculation_json, // 充电量金额计算JSON
                    'charge_tariff_scheme' => ChargeTariffScheme::getDescription($payment->charge_tariff_scheme), // 充电收费方案
                    'use_remain_charge_value' => (float)(bcdiv($payment->use_remain_charge_value, 1000, 3)), // 使用剩余充电量
                    'idling_penalty_time' => $this->secTime($payment->idling_penalty_time), // 闲置罚款时间
                    'idling_penalty_amount' => (float)(bcdiv($payment->idling_penalty_amount, 100, 1)), // 闲置罚款金额
                    'idling_penalty_amount_calculation_json' => $payment?->chargePaymentRecordCalculation?->idling_penalty_amount_calculation_json, // 闲置罚款金额计算JSON
                    'gmt_idling_penalty_start' => $payment->gmt_idling_penalty_start, // 闲置罚款开始时间
                    'gmt_idling_penalty_stop' => $payment->gmt_idling_penalty_stop, // 闲置罚款结束时间
                    'total_amount' => (float)(bcdiv($payment->total_amount, 100, 1)), // 金额
                    'actual_payment_amount' => (float)(bcdiv($payment->actual_payment_amount, 100, 1)), // 实际付款金额
                    'is_total_amount_equal_actual_payment_amount' => $payment->total_amount === $payment->actual_payment_amount, // 总金额是否等于实际付款金额
                    'points_transaction_id' => $payment->points_transaction_id, // 积分交易ID
                    'use_points' => (float)(bcdiv($payment->use_points, 100, 1)), // 使用积分
                    'points_transaction_number' => $payment->pointsTransaction?->transaction_number ?? '—/—', // 积分交易编号
                    'merchant_handling_fee' => (float)(bcdiv($payment->merchant_handling_fee, 100, 2)), // 商户手续费
                    'merchant_receivable' => (float)(bcdiv($payment->merchant_receivable, 100, 2)), // 商户应收款
                    'is_has_merchant_handling_fee' => $payment->merchant_handling_fee > 0, // 是否存在商户手续费
                    'gmt_octopus_deduct' => $payment->gmt_octopus_deduct, // 八达通扣款时间
                    'octopus_receipt_number' => $payment->octopus_receipt_number, // 八达通收据编号
                    'octopus_device_number' => $payment->octopus_device_number ?? '—/—', // 八达通设备编号
                    'octopus_card_number' => $payment->octopus_card_number ?? '—/—', // 八达通卡号
                    'octopus_card_type' => $payment->octopus_card_type ?? '—/—', // 八达通卡类型
                    'octopus_balance' => filled($payment->octopus_balance) ? __('common.unit_hk') . (float)(bcdiv($payment->octopus_balance, 100, 1)) : '—/—', // 八达通余额
                    'octopus_last_added_value_type' => $payment->octopus_last_added_value_type ?? '—/—', // 八达通上次充值类型
                    'octopus_last_added_value_date' => $payment->octopus_last_added_value_date ?? '—/—', // 八达通上次充值日期
                    'pos_receipt_number' => $payment->pos_receipt_number ?? '—/—', // POS收据编号
                    'gmt_pos_deduct' => $payment->gmt_pos_deduct ?? '—/—', // POS扣款时间
                    'pos_transaction_id' => $payment->pos_transaction_id ?? '—/—', // POS交易ID
                    'pos_payment_method_name' => $payment->pos_payment_method_name ?? '—/—', // POS支付方式名称
                    'pos_card_number' => $payment->pos_card_number ?? '—/—', // POS卡号
                    'pos_trace_no' => $payment->pos_trace_no ?? '—/—', // POS追踪编号
                    'pos_reference_id' => $payment->pos_reference_id ?? '—/—', // POS参考编号
                    'charge_pre_authorization_record_number' => $payment->charge_pre_authorization_record_number ?? '—/—', // 充电预授权记录编号
                    'charge_value_type' => ChargeValueType::getDescription($payment->charge_value_type), // 充电量类型
                    'gmt_create' => $payment->gmt_create->toDateTimeString(), // 创建时间
                    'gmt_modified' => $payment->gmt_modified->toDateTimeString(), // 修改时间
                    'points_transaction' => null,
                );

                if (filled($payment->pointsTransaction)) {
                    $points_transaction = array(
                        'points_transaction_id' => $payment->pointsTransaction->points_transaction_id, // 积分交易ID
                        'user_id' => $payment->pointsTransaction->user_id, // 用户ID
                        'nickname' => $payment->pointsTransaction?->appUser?->nickname ?? $payment->pointsTransaction->user_id, // 用户昵称
                        'avatar' => existsImage('avatar', $payment->pointsTransaction?->appUser?->avatar_url) ?: existsImage('icon', 'not_select_image.png'), // 用户头像
                        'transaction_number' => $payment->pointsTransaction->transaction_number, // 交易编号
                        'transaction_type' => TransactionType::getDescription($payment->pointsTransaction->transaction_type), // 交易类型
                        'transaction_category' => TransactionCategory::getDescription($payment->pointsTransaction->transaction_category), // 交易类别
                        'amount' => (float)bcdiv($payment->pointsTransaction->amount, 100, 1), // 金额
                        'points_balance' => (float)bcdiv($payment->pointsTransaction->points_balance, 100, 1), // 积分余额
                        'gmt_create' => $payment->pointsTransaction->gmt_create->toDateTimeString(), // 创建时间
                        'gmt_modified' => $payment->pointsTransaction->gmt_modified->toDateTimeString(), // 修改时间
                    );
                    $charge_payment_record['points_transaction'] = $points_transaction_list[] = &$points_transaction;
                }

                $charge_payment_record_list[] = $charge_payment_record;
            }

            // 充电间隔
            $charge_value_interval = $charge_record_info->charge_value_interval ?? 0;
            $charge_value_interval = match ($charge_record_info->charge_value_type) {
                ChargeValueType::Time => round($charge_value_interval / 60) . __('common.unit_mins'),
                ChargeValueType::Energy => (float)(bcdiv($charge_value_interval, 1000, 3)) . __('common.unit_kwh'),
                default => $charge_value_interval,
            };

            // 获取支付记录的充电及闲时罚款金额计算
            $charge_amount_calculation_list = array();

            if (!empty($charge_payment_record_list)) {
                $charge_amount_calculation_list = array(
                    'charge_value_amount_calculation_json' => $charge_payment_record_list[0]['charge_value_amount_calculation_json'],
                    'idling_penalty_amount_calculation_json' => $charge_payment_record_list[0]['idling_penalty_amount_calculation_json'],
                );
            }

            $site_name = $this->getValueFromLanguageArray($charge_record_info->site_name) ?? '—/—';
            $zone_name = $this->getValueFromLanguageArray($charge_record_info->zone_name) ?? '—/—';
            // 报表数据
            // 充电记录编号取最后一位
            $charge_record_number = explode(self::$delimiter, $charge_record_info->charge_record_number);
            $data['report'] = array(
                'charge_record_id' => $charge_record_info->charge_record_id, // 充电记录ID
                //当前用户id
                'view_by' => auth()->user()->name ?? '—/—',
                //Charging Section ID
                'charge_record_number' => end($charge_record_number) ?? '—/—', // 充电记录编号
                'octopus_card_number' => $charge_payment_record_list[0]['octopus_card_number'] ?? '—/—', // 八达通卡号
                //Device No.
                'octopus_device_number' => $charge_payment_record_list[0]['octopus_device_number'] ?? '—/—',
                //Receipt No.
                'octopus_receipt_number' => $charge_payment_record_list[0]['octopus_receipt_number'] ?? '—/—', // 收据编号
                'start_from' => $charge_record_info->gmt_start ?? '—/—', // 开始充电时间
                'end_from' => $charge_record_info->gmt_stop ?? '—/—', // 停止充电时间
                //Car Park
                'site_name' => $site_name, // 场地名称
                'zone_name' => $zone_name, // 区域名称
                //Charger
                'charge_point_name' => $charge_record_info->charge_point_name ?? '—/—',
                //Kiosk 空着
                'kiosk' => '—/—',
                'tariff_type' => ChargeTariffScheme::getDescription($charge_record_info->charge_tariff_scheme),
                'charge_type' => ChargeValueType::getDescription($charge_record_info->charge_value_type),
                'phone' => $charge_record_info->reminder_telephone ?? '—/—',
                'email' => $charge_record_info->reminder_email ?? '—/—',
            );
            $time = array(
                'charged_time' => $this->formatTime($charge_record_info->charged_time),
                'off_peak_charged_time' => $this->formatTime($charge_record_info->off_peak_charged_time),
                'on_peak_charged_time' => $this->formatTime($charge_record_info->on_peak_charged_time),
                'idling_penalty_time' => $this->formatTime($charge_record_info->idling_penalty_time),
                'off_peak_idling_penalty_time' => $this->formatTime($charge_record_info->off_peak_idling_penalty_time),
                'on_peak_idling_penalty_time' => $this->formatTime($charge_record_info->on_peak_idling_penalty_time)
            );
            $time_to_detail = array(
                'charged_time' => $this->formatTimeToDetail($charge_record_info->charged_time),
                'off_peak_charged_time' => $this->formatTimeToDetail($charge_record_info->off_peak_charged_time),
                'on_peak_charged_time' => $this->formatTimeToDetail($charge_record_info->on_peak_charged_time),
                'idling_penalty_time' => $this->formatTimeToDetail($charge_record_info->idling_penalty_time),
                'off_peak_idling_penalty_time' => $this->formatTimeToDetail($charge_record_info->off_peak_idling_penalty_time),
                'on_peak_idling_penalty_time' => $this->formatTimeToDetail($charge_record_info->on_peak_idling_penalty_time)
            );

            $merchant_name = $charge_record_info->merchant_number ? ($this->getValueFromLanguageArray($charge_record_info->merchant_name) ?? '—/—') : env('APP_NAME');
            $user_group_name = $this->getValueFromLanguageArray($charge_record_info->user_group_name) ?? '—/—';
            $member_card_group_name = $this->getValueFromLanguageArray($charge_record_info->member_card_group_name) ?? '—/—';
            $app_user = AppUser::find($charge_record_info->user_id);
            $user_email = $app_user?->email ?? ($app_user?->telephone ?? '—/—');

            $is_pre_paid = $charge_record_info->charge_tariff_scheme === ChargeTariffScheme::PrePaid; // 预付
            $is_post_paid = $charge_record_info->charge_tariff_scheme === ChargeTariffScheme::PostPaid; // 后付
            $is_time = $charge_record_info->charge_value_type === ChargeValueType::Time; // 时间
            $is_energy = $charge_record_info->charge_value_type === ChargeValueType::Energy; // 电量

            $data['data'] = array(
                'charge_record_id' => $charge_record_info->charge_record_id, // 充电记录ID
                'charge_record_number' => $charge_record_info->charge_record_number, // 充电记录编号
                'merchant_number' => $charge_record_info->merchant_number ?? env('APP_NAME'), // 商户编号
                'merchant_name' => $merchant_name, // 商户名称
                'site_number' => $charge_record_info->site_number, // 场地编号
                'site_name' => $site_name, // 场地名称
                'zone_number' => $charge_record_info->zone_number ?? '—/—', // 区域编号
                'zone_name' => $zone_name, // 区域名称
                'parking_bay_number' => $charge_record_info->parking_bay_number ?? '—/—', // 停车位编号
                'charge_point_number' => $charge_record_info->charge_point_number, // 充电机编号
                'charge_point_name' => $charge_record_info->charge_point_name ?? '—/—', // 充电机名称
                'connector_number' => $charge_record_info->connector_number, // 充电枪编号
                'connector_name' => $charge_record_info->connector_name ?? '—/—', // 充电枪编号
                'user_email' => $user_email, // 用户邮箱
                'user_group_name' => $user_group_name, // 用户组名称
                'user_credential_type' => UserCredentialType::getDescription($charge_record_info->user_credential_type), // 用户凭证类型
                'user_credential_number' => $charge_record_info->user_credential_number ?? '—/—', // 用户凭证号码
                'member_name' => $charge_record_info->member_name ?? '—/—', // 会员名称
                'member_card_group_name' => $member_card_group_name, // 会员卡组名称
                'lms_mode' => LmsMode::getDescription($charge_record_info->lms_mode), // LMS模式
                'is_record_charge_record_meter_value' => $charge_record_info->is_record_charge_record_meter_value, // 是否记录充电记录仪表值
                'is_enable_charge_arrears' => $charge_record_info->is_enable_charge_arrears, // 是否启用充电欠款
                'license_plate_number' => $charge_record_info->license_plate_number ?? '—/—', // 车牌号码
                'gmt_park_sensor_entry' => $charge_record_info->gmt_park_sensor_entry ?? '—/—', // 停车传感器进场时间
                'is_enable_push_notification' => $charge_record_info->is_enable_push_notification, // 是否启用推送通知
                'is_allow_points_overdraft' => $charge_record_info->is_allow_points_overdraft, // 是否允许积分透支
                'charge_tariff_scheme' => ChargeTariffScheme::getDescription($charge_record_info->charge_tariff_scheme), // 充电收费方案
                'charge_tariff_scheme_original' => $charge_record_info->charge_tariff_scheme, // 充电收费方案原始值
                'charge_value_type' => ChargeValueType::getDescription($charge_record_info->charge_value_type), // 充电量类型
                'charge_value_type_original' => $charge_record_info->charge_value_type, // 充电量类型原始值
                'charge_value_interval' => $charge_value_interval, // 充电量间隔 - 选择/收费
                'charge_value_interval_original' => $charge_record_info->charge_value_interval ?? 0,
                'charge_value_amount' => (float)(bcdiv($charge_record_info->charge_value_amount, 100, 1)), // 充电量金额
                'idling_penalty_amount' => (float)(bcdiv($charge_record_info->idling_penalty_amount, 100, 1)), // 闲置罚款金额
                'total_amount' => (float)(bcdiv($charge_record_info->total_amount, 100, 1)),
                'actual_payment_amount' => (float)(bcdiv($charge_record_info->actual_payment_amount, 100, 1)),
                'is_total_amount_equal_actual_payment_amount' => $charge_record_info->total_amount === $charge_record_info->actual_payment_amount, // 总金额是否等于实际付款金额
                'use_points' => (float)(bcdiv($charge_record_info->use_points, 100, 1)),
                'merchant_handling_fee_rate' => filled($charge_record_info->merchant_handling_fee_rate) ? ($charge_record_info->merchant_handling_fee_rate . __('common.unit_percent_blank')) : null, // 商户手续费率
                'merchant_handling_fee' => (float)(bcdiv($charge_record_info->merchant_handling_fee, 100, 2)), // 商户手续费金额
                'merchant_receivable' => (float)(bcdiv($charge_record_info->merchant_receivable, 100, 2)), // 商户应收金额
                'is_has_merchant_handling_fee' => $charge_record_info->merchant_handling_fee > 0, // 是否存在商户手续费
                'pre_paid_selected_charge_value' => $is_pre_paid ? ($is_time ? round($charge_record_info->pre_paid_selected_charge_value / 60) . __('common.unit_mins') : (float)(bcdiv($charge_record_info->pre_paid_selected_charge_value, 1000, 3)) . __('common.unit_kwh')) : '—/—', // 预付已选充电量
                'pre_paid_charge_value' => $is_pre_paid ? ($is_time ? round($charge_record_info->pre_paid_charge_value / 60) . __('common.unit_mins') : (float)(bcdiv($charge_record_info->pre_paid_charge_value, 1000, 3)) . __('common.unit_kwh')) : '—/—', // 预付充电量
                'pre_paid_purchase_charge_value' => $is_pre_paid ? ($is_time ? round($charge_record_info->pre_paid_purchase_charge_value / 60) . __('common.unit_mins') : (float)(bcdiv($charge_record_info->pre_paid_purchase_charge_value, 1000, 3)) . __('common.unit_kwh')) : '—/—', // 预付购买充电量
                'pre_paid_use_remain_charge_value' => $is_pre_paid ? ($is_time ? round($charge_record_info->pre_paid_use_remain_charge_value / 60) . __('common.unit_mins') : (float)(bcdiv($charge_record_info->pre_paid_use_remain_charge_value, 1000, 3)) . __('common.unit_kwh')) : '—/—', // 预付使用剩余充电量
                'pre_paid_charge_value_maximum_selection' => $is_pre_paid ? ($is_time ? round($charge_record_info->pre_paid_charge_value_maximum_selection / 60) . __('common.unit_mins') : (float)(bcdiv($charge_record_info->pre_paid_charge_value_maximum_selection, 1000, 3)) . __('common.unit_kwh')) : '—/—', // 预付充电量最大选择量
                'post_paid_purchase_charge_value' => $is_post_paid ? ($is_time ? round($charge_record_info->post_paid_purchase_charge_value / 60) . __('common.unit_mins') : (float)(bcdiv($charge_record_info->post_paid_purchase_charge_value, 1000, 3)) . __('common.unit_kwh')) : '—/—', // 后付购买充电量
                'post_paid_maximum_charge_time' => $is_post_paid ? round($charge_record_info->post_paid_maximum_charge_time / 60) . __('common.unit_mins') : '—/—', // 后付最大充电时间
                'idling_penalty_effective_type' => IdlingPenaltyEffectiveType::getDescription($charge_record_info->idling_penalty_effective_type), // 闲置罚款生效类型
                'trial_charge_timeout' => filled($charge_record_info->trial_charge_timeout) ? $charge_record_info->trial_charge_timeout . __('common.unit_s') : '—/—', // 试充超时时间
                'gmt_trial_charge_reached' => $charge_record_info->gmt_trial_charge_reached ?? '—/—', // 试充达标时间
                'charge_maximum_vehicle_soc_limit' => isset($charge_record_info->charge_maximum_vehicle_soc_limit) ? ($charge_record_info->charge_maximum_vehicle_soc_limit . __('common.unit_percent_blank')) : '—/—', // 充电最大车辆SoC限制
                'tariff_table_type' => TariffTableType::getDescription($charge_record_info->tariff_table_type), // 收费表类型
                'is_enable_member_card_group_tariff_table' => $charge_record_info->is_enable_member_card_group_tariff_table, // 是否启用会员卡组收费表
                'is_enable_user_group_tariff_table' => $charge_record_info->is_enable_user_group_tariff_table, // 是否启用用户组收费表
                'idling_penalty_grace_period' => filled($charge_record_info->idling_penalty_grace_period) ? (($charge_record_info->idling_penalty_grace_period / 60) . __('common.unit_mins')) : '—/—', // 闲置罚款宽限期
                'is_enable_admin_octopus_card_free_deduct' => $charge_record_info->is_enable_admin_octopus_card_free_deduct, // 是否启用免费八达通卡
                'is_enable_free_octopus_card' => $charge_record_info->is_enable_free_octopus_card, // 是否开启管理员八达通卡免费扣款
                'is_enable_top_up' => $charge_record_info->is_enable_top_up, // 是否启用续充
                'top_up_buffer_limit' => filled($charge_record_info->top_up_buffer_limit) ? $charge_record_info->top_up_buffer_limit . __('common.unit_s') : '—/—', // 续充缓冲限制 - 剩余充电量需大于此数才可续充
                'is_top_up_need_confirm_identity' => $charge_record_info->is_top_up_need_confirm_identity, // 是否续充需要确认身份
                'remain_charge_value_generation_trigger' => RemainChargeValueGenerationTrigger::getDescription($charge_record_info->remain_charge_value_generation_trigger), // 剩余充电量生成触发条件
                'remain_charge_value_validity_period' => filled($charge_record_info->remain_charge_value_validity_period) ? ($charge_record_info->remain_charge_value_validity_period / 60) . __('common.unit_mins') : '—/—', // 剩余充电量有效期
                'remain_charge_value_minimum_limit' => $charge_record_info->remain_charge_value_minimum_limit ? match ($charge_record_info->charge_value_type) {
                    ChargeValueType::Time => round($charge_record_info->remain_charge_value_minimum_limit / 60) . __('common.unit_mins'),
                    ChargeValueType::Energy => (float)(bcdiv($charge_record_info->remain_charge_value_minimum_limit, 1000, 3)) . __('common.unit_kwh'),
                    default => $charge_record_info->remain_charge_value_minimum_limit,
                }
                    : '—/—', // 剩余充电量最小限制
                'is_enable_use_remain_charge_value' => $charge_record_info->is_enable_use_remain_charge_value, // 是否允许使用剩余充电量
                'is_enable_charge_value_adjust_selected_base_on_remain' => $charge_record_info->is_enable_charge_value_adjust_selected_base_on_remain, // 是否启用根据剩余充电量调整已选充电量
                'is_enable_round_up_tail_charge_value_calculation' => $charge_record_info->is_enable_round_up_tail_charge_value_calculation, // 是否启用向上取整尾部充电量计算
                'gmt_start' => $charge_record_info->gmt_start, // 开始充电时间
                'start_id_tag' => $charge_record_info->start_id_tag ?? '—/—', // 开始ID标识
                'meter_start' => $charge_record_info->meter_start, // 开始充电仪表值
                'gmt_power_on' => $charge_record_info->gmt_power_on ?? '—/—', // 上电时间
                'reminder_email' => $charge_record_info->reminder_email ?? '—/—', // 提醒邮箱
                'reminder_telephone' => $charge_record_info->reminder_telephone ?? '—/—', // 提醒电话
                'charged_time' => $time_to_detail['charged_time'],
                'off_peak_charged_time' => $time_to_detail['off_peak_charged_time'],
                'on_peak_charged_time' => $time_to_detail['on_peak_charged_time'],
                'charged_energy' => (float)(bcdiv($charge_record_info->charged_energy, 1000, 3)), // 已充电量
                'off_peak_charged_energy' => (float)(bcdiv($charge_record_info->off_peak_charged_energy, 1000, 3)), // 非高峰已充电量
                'on_peak_charged_energy' => (float)(bcdiv($charge_record_info->on_peak_charged_energy, 1000, 3)), // 高峰已充电量
                'identity_type' => IdentityType::getDescription($charge_record_info->identity_type), // 身份类型
                'identity_number' => $charge_record_info->identity_number ?? '—/—', // 身份号码
                'charge_pre_authorization_record_number' => $charge_record_info->charge_pre_authorization_record_number ?? '—/—', // 充电预授权记录编号
                'meter_value_voltage' => $charge_record_info->meter_value_voltage ?? '—/—', // 仪表值-电压
                'meter_value_current' => $charge_record_info->meter_value_current ?? '—/—', // 仪表值-电流
                'meter_value_power' => $charge_record_info->meter_value_power ?? '—/—', // 仪表值-功率
                'meter_value_power_factor' => $charge_record_info->meter_value_power_factor ?? '—/—', // 仪表值-功率因数
                'meter_value_power_offered' => $charge_record_info->meter_value_power_offered ?? '—/—', // 仪表值-最大功率
                'meter_value_start_vehicle_soc' => $charge_record_info->meter_value_start_vehicle_soc ?? '—/—', // 仪表值-开始车辆电量
                'meter_value_vehicle_soc' => $charge_record_info->meter_value_vehicle_soc ?? '—/—', // 仪表值-车辆电量
                'meter_value_temperature' => $charge_record_info->meter_value_temperature ?? '—/—', // 仪表值-温度
                'meter_value_fan_speed' => $charge_record_info->meter_value_fan_speed ?? '—/—', // 仪表值-风扇速度
                'remote_stop_operator_type' => $charge_record_info->remote_stop_operator_type ?? '—/—', // 远程停止操作者类型
                'remote_stop_operator_number' => $charge_record_info->remote_stop_operator_number ?? '—/—', // 远程停止操作者编号
                'remote_stop_reason' => RemoteStopChargeReasonEnum::getDescription($charge_record_info->remote_stop_reason), // 远程停止原因
                'remote_stop_detail' => $charge_record_info->remote_stop_detail ?? '—/—', // 远程停止详情
                'gmt_remote_stop' => $charge_record_info->gmt_remote_stop ?? '—/—', // 远程停止时间
                'gmt_stop' => $charge_record_info->gmt_stop ?? '—/—', // 停止充电时间
                'stop_id_tag' => $charge_record_info->stop_id_tag ?? '—/—', // 停止ID标识
                'meter_stop' => $charge_record_info->meter_stop ?? '—/—', // 结束充电仪表值
                'stop_reason' => $charge_record_info->stop_reason ?? '—/—', // 停止充电原因
                'is_support_unlock_connector' => $charge_record_info->is_support_unlock_connector, // 是否支持解锁充电枪
                'remote_unlock_operator_type' => $charge_record_info->remote_unlock_operator_type ?? '—/—', // 远程解锁操作者类型
                'remote_unlock_operator_number' => $charge_record_info->remote_unlock_operator_number ?? '—/—', // 远程解锁操作者编号
                'gmt_remote_unlock' => $charge_record_info->gmt_remote_unlock ?? '—/—', // 远程解锁时间
                'gmt_unlocked' => $charge_record_info->gmt_unlocked ?? '—/—', // 解锁时间
                'idling_penalty_time' => $time_to_detail['idling_penalty_time'],
                'off_peak_idling_penalty_time' => $time_to_detail['off_peak_idling_penalty_time'],
                'on_peak_idling_penalty_time' => $time_to_detail['on_peak_idling_penalty_time'],
                'gmt_idling_penalty_locked' => $charge_record_info->gmt_idling_penalty_locked ?? '—/—', // 闲置罚款已锁定时间
                'gmt_create' => $charge_record_info->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $charge_record_info->gmt_modified->toDateTimeString(), // 修改时间
                'is_show_magic_charge_btn' => is_null($charge_record_info->gmt_stop) && is_null($charge_record_info->gmt_unlocked) && $charge_record_info->pre_paid_charge_value == 0, // 是否显示按钮

                'charge_pre_authorization_record' => $charge_pre_authorization_record,
                'charge_arrears_record' => $charge_arrears_record,
                'event_log_list' => $event_log_list,
                'error_log_list' => $error_log_list,
                'email_send_record_list' => $email_send_record_list,
                'sms_send_record_list' => $sms_send_record_list,
                // 支付记录
                'charge_payment_record_list' => $charge_payment_record_list,
                'charge_value_type_value' => $charge_record_info->charge_value_type, // 充电量类型
                'tariff_table_type_value' => $charge_record_info->tariff_table_type, // 收费表类型值
                // 充电时间统计
                'time' => $time,
                /*// 收费表信息
                'tariff_scheme_list' => $tariff_scheme_list,*/
                // 支付计算金额
                'charge_amount_calculation_list' => $charge_amount_calculation_list,
                'is_has_charge_record_trial_charge_json' => ChargeRecordTrialCharge::where('charge_record_number', $charge_record_info->charge_record_number)->exists(),
                'reading_context_enum_list' => ReadingContextEnum::asSelectArray(),
                'measurand_enum_list' => MeasurandEnum::asSelectArray(),
                // 积分交易
                'points_transaction_list' => $points_transaction_list,
            );
        }

        // 模擬付款POS支付方式
        $data['payment_method_list'] = array();
        // 支付方式多語言与图片
        foreach (PaymentMethod::asSelectArray() as $value => $name) {
            if ($name != PaymentMethod::Octopus) {
                $data['payment_method_list'][$value] = array(
                    'name' => $name,
                    'url' => existsImage('icon', 'payment_methods/' . strtolower($value) . '.png'),
                );
            }
        }

        if ($is_time) {
            $data['unit'] = __("common.unit_mins_blank");
        } else {
            $data['unit'] = __("common.unit_kwh_blank");
        }

        // 用于判断预付是否是续充
        $data['data']['is_top_up'] = 0;
        if (!empty($charge_payment_record_list) && $is_pre_paid) {
            $data['data']['is_top_up'] = 1;
        }

        // 用于后付显示和计算闲时罚款
        $data['data']['post_paid'] = ChargeTariffScheme::PostPaid;
        $data['data']['charge_start_time'] = $charge_record_info->gmt_power_on ?? $charge_record_info->gmt_start;
        $data['data']['post_paid_maximum_charge_time_original'] = $charge_record_info->charge_tariff_scheme ? $charge_record_info->post_paid_maximum_charge_time : 0; // 后付最大充电时间

        return view("pages.{$data['module_name']}.view", $data);
    }

    /**
     * 获取收费表规则
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getChargeRecordTariffRule(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        $charge_record_number = $request->input('charge_record_number');
        if (
            blank($charge_record_number) || ChargeRecord::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->where('charge_record_number', $charge_record_number)->doesntExist() ||
            blank($charge_record_tariff_rule_model = ChargeRecordTariffRule::firstWhere('charge_record_number', $charge_record_number))
        ) {
            $this->notFoundData('Charge Record Tariff Rule');
            return $this->returnJson();
        }

        // 收费表信息
        $tariff_scheme_list = array();
        if (filled($charge_record_tariff_rule_model->simple_tariff_table_json)) {
            $tariff_scheme_list['simple_tariff_table_json'] = array(
                'title' => __("$module_name.simple_tariff_table_list"),
                'data' => $charge_record_tariff_rule_model->simple_tariff_table_json,
            );
            $member_card_group_simple_tariff_table_item_json = $charge_record_tariff_rule_model->member_card_group_simple_tariff_table_item_json;
            if (filled($member_card_group_simple_tariff_table_item_json)) {
                $tariff_scheme_list['simple_tariff_table_json']['member_group_data'] = $this->getMemberCardGroupArray($member_card_group_simple_tariff_table_item_json);
            }
            $user_group_simple_tariff_table_item_json = $charge_record_tariff_rule_model->user_group_simple_tariff_table_item_json;
            if (filled($user_group_simple_tariff_table_item_json)) {
                $tariff_scheme_list['simple_tariff_table_json']['user_group_data'] = $this->getUserGroupArray($user_group_simple_tariff_table_item_json);
            }
        }
        if (filled($charge_record_tariff_rule_model->time_tariff_table_item_json)) {
            $tariff_scheme_list['time_tariff_table_item_json'] = array(
                'title' => __("$module_name.time_tariff_table_item_list"),
                'data' => $charge_record_tariff_rule_model->time_tariff_table_item_json,
            );
            $member_card_group_time_tariff_table_item_json = $charge_record_tariff_rule_model->member_card_group_time_tariff_table_item_json;
            if (filled($member_card_group_time_tariff_table_item_json)) {
                $tariff_scheme_list['time_tariff_table_item_json']['member_group_data'] = $this->getMemberCardGroupArray($member_card_group_time_tariff_table_item_json);
            }
            $user_group_time_tariff_table_item_json = $charge_record_tariff_rule_model->user_group_time_tariff_table_item_json;
            if (filled($user_group_time_tariff_table_item_json)) {
                $tariff_scheme_list['time_tariff_table_item_json']['user_group_data'] = $this->getUserGroupArray($user_group_time_tariff_table_item_json);
            }
        }
        if (filled($charge_record_tariff_rule_model->energy_tariff_table_item_json)) {
            $tariff_scheme_list['energy_tariff_table_item_json'] = array(
                'title' => __("$module_name.energy_tariff_table_item_list"),
                'data' => $charge_record_tariff_rule_model->energy_tariff_table_item_json,
            );
            $member_card_group_energy_tariff_table_item_json = $charge_record_tariff_rule_model->member_card_group_energy_tariff_table_item_json;
            if (filled($member_card_group_energy_tariff_table_item_json)) {
                $tariff_scheme_list['energy_tariff_table_item_json']['member_group_data'] = $this->getMemberCardGroupArray($member_card_group_energy_tariff_table_item_json);
            }
            $user_group_energy_tariff_table_item_json = $charge_record_tariff_rule_model->user_group_energy_tariff_table_item_json;
            if (filled($user_group_energy_tariff_table_item_json)) {
                $tariff_scheme_list['energy_tariff_table_item_json']['user_group_data'] = $this->getUserGroupArray($user_group_energy_tariff_table_item_json);
            }
        }
        if (filled($charge_record_tariff_rule_model->idling_penalty_tariff_table_item_json)) {
            $tariff_scheme_list['idling_penalty_tariff_table_item_json'] = array(
                'title' => __("$module_name.idling_penalty_tariff_table_item_list"),
                'data' => $charge_record_tariff_rule_model->idling_penalty_tariff_table_item_json,
            );
            $member_card_group_idling_penalty_tariff_table_item_json = $charge_record_tariff_rule_model->member_card_group_idling_penalty_tariff_table_item_json;
            if (filled($member_card_group_idling_penalty_tariff_table_item_json)) {
                $tariff_scheme_list['idling_penalty_tariff_table_item_json']['member_group_data'] = $this->getMemberCardGroupArray($member_card_group_idling_penalty_tariff_table_item_json);
            }
            $user_group_idling_penalty_tariff_table_item_json = $charge_record_tariff_rule_model->user_group_idling_penalty_tariff_table_item_json;
            if (filled($user_group_idling_penalty_tariff_table_item_json)) {
                $tariff_scheme_list['idling_penalty_tariff_table_item_json']['user_group_data'] = $this->getUserGroupArray($user_group_idling_penalty_tariff_table_item_json);
            }
        }
        if (filled($charge_record_tariff_rule_model->peak_time_table_item_json)) {
            $tariff_scheme_list['peak_time_table_item_json'] = array(
                'title' => __("$module_name.peak_time_table_item_list"),
                'data' => $charge_record_tariff_rule_model->peak_time_table_item_json,
            );
        }
        if (filled($charge_record_tariff_rule_model->public_holiday_json)) {
            $tariff_scheme_list['public_holiday_json'] = array(
                'title' => __("$module_name.public_holiday_list"),
                'data' => $charge_record_tariff_rule_model->public_holiday_json,
            );
        }

        $this->data = $tariff_scheme_list;

        return $this->returnJson();
    }

    /**
     * 导出
     *
     * @param Request $request
     * @return void
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function excelExport(Request $request): void
    {
        ini_set('memory_limit', '-1');
        // 获取数据
        $search = $this->_searchList($request);
        $data_list = $search->get();
        $module_name = self::$module_name;

        $result = array();
        foreach ($data_list as $data) {
            $site_name = $this->getValueFromLanguageArray($data->site_name) ?? '—/—';
            $zone_name = $this->getValueFromLanguageArray($data->zone_name) ?? '—/—';
            $merchant_name = $data->merchant_number ? ($this->getValueFromLanguageArray($data->merchant_name) ?? '—/—') : env('APP_NAME');
            $user_group_name = $this->getValueFromLanguageArray($data->user_group_name) ?? '—/—';
            $member_card_group_name = $this->getValueFromLanguageArray($data->member_card_group_name) ?? '—/—';
            $is_pre_paid = $data->charge_tariff_scheme === ChargeTariffScheme::PrePaid; // 预付
            $is_post_paid = $data->charge_tariff_scheme === ChargeTariffScheme::PostPaid; // 后付
            $is_time = $data->charge_value_type === ChargeValueType::Time; // 时间
            $is_energy = $data->charge_value_type === ChargeValueType::Energy; // 电量
            $is_pre_paid_time = $is_pre_paid && $is_time; //预付时间
            $is_pre_paid_energy = $is_pre_paid && $is_energy; //预付电量
            $is_post_paid_time = $is_post_paid && $is_time; // 后付时间
            $is_post_paid_energy = $is_post_paid && $is_energy; // 后付电量

            $result[] = array(
                'charge_record_id' => $data->charge_record_id, // 充电记录ID
                'charge_record_number' => $data->charge_record_number, // 充电记录编号
                'connector_number' => $data->connector_number, // 充电枪编号
                'gmt_start' => $data->gmt_power_on ?? $data->gmt_start, // 开始充电时间
                'gmt_stop' => $data->gmt_stop ?? '—/—', // 停止充电时间
                'charged_time' => (string)round($data->charged_time / 60), // 已充时间
                'charged_energy' => bcdiv($data->charged_energy, 1000, 3), // 已充电量
                'total_amount' => bcdiv($data->total_amount, 100, 1), // 合共金额
                'merchant_name' => $merchant_name, // 商户名称
                'user_email' => $data->user?->email ?? ($data->user?->telephone ?? '—/—'), // 用户邮箱/电话
                'user_group_name' => $user_group_name, // 用户组名称
                'user_credential_type' => UserCredentialType::getDescription($data->user_credential_type), // 用户凭证类型
                'user_credential_number' => $data->user_credential_number ?? '—/—', // 用户凭证号码
                'member_name' => $data->member_name ?? '—/—', // 会员名称
                'member_card_group_name' => $member_card_group_name, // 会员卡组名称
                'site_name' => $site_name, // 场地名称
                'zone_name' => $zone_name, // 区域名称
                'parking_bay_number' => $data->parking_bay_number ?? '—/—', // 停车位编号
                'charge_point_name' => $data->charge_point_name ?? '—/—', // 充电机名称
                'connector_name' => $data->connector_name ?? '—/—', // 充电枪名称
                'lms_mode' => LmsMode::getDescription($data->lms_mode), // LMS模式
                'is_record_charge_record_meter_value' => $data->is_record_charge_record_meter_value, // 是否记录充电记录仪表值
                'is_enable_charge_arrears' => $data->is_enable_charge_arrears, // 是否启用充电欠款
                'license_plate_number' => $data->license_plate_number ?? '—/—', // 车牌号码
                'gmt_park_sensor_entry' => $data->gmt_park_sensor_entry ?? '—/—', // 停车传感器进场时间
                'is_allow_points_overdraft' => $data->is_allow_points_overdraft, // 是否允许积分透支
                'charge_tariff_scheme' => ChargeTariffScheme::getDescription($data->charge_tariff_scheme), // 充电收费方案
                'charge_value_type' => ChargeValueType::getDescription($data->charge_value_type), // 充电量类型
                'charge_value_interval_time' => $is_time ? (string)round($data->charge_value_interval / 60) : '', // 充电量间隔 - 选择/收费
                'charge_value_interval_energy' => $is_energy ? bcdiv($data->charge_value_interval, 1000, 3) : '', // 充电量间隔 - 选择/收费
                'charge_value_amount' => bcdiv($data->charge_value_amount, 100, 1), // 充电量金额
                'idling_penalty_amount' => bcdiv($data->idling_penalty_amount, 100, 1), // 闲置罚款金额
                'actual_payment_amount' => bcdiv($data->actual_payment_amount, 100, 1), // 实际支付金额
                'use_points' => bcdiv($data->use_points, 100, 1), // 使用积分
                'merchant_handling_fee_rate' => filled($data->merchant_handling_fee_rate) ? ($data->merchant_handling_fee_rate . __('common.unit_percent_blank')) : '—/—', // 商户手续费率
                'merchant_handling_fee' => bcdiv($data->merchant_handling_fee, 100, 2), // 商户手续费
                'merchant_receivable' => bcdiv($data->merchant_receivable, 100, 2), // 商户应收款
                'pre_paid_charge_value_time' => $is_pre_paid_time ? (string)round($data->pre_paid_charge_value / 60) : '', // 预付充电量
                'pre_paid_charge_value_energy' => $is_pre_paid_energy ? bcdiv($data->pre_paid_charge_value, 1000, 3) : '', // 预付充电量
                'pre_paid_purchase_charge_value_time' => $is_pre_paid_time ? (string)round($data->pre_paid_purchase_charge_value / 60) : '', // 预付购买充电量
                'pre_paid_purchase_charge_value_energy' => $is_pre_paid_energy ? bcdiv($data->pre_paid_purchase_charge_value, 1000, 3) : '', // 预付购买充电量
                'pre_paid_use_remain_charge_value_time' => $is_pre_paid_time ? (string)round($data->pre_paid_use_remain_charge_value / 60) : '', // 预付使用剩余充电量
                'pre_paid_use_remain_charge_value_energy' => $is_pre_paid_energy ? bcdiv($data->pre_paid_use_remain_charge_value, 1000, 3) : '', // 预付使用剩余充电量
                'pre_paid_charge_value_maximum_selection_time' => $is_pre_paid_time ? (string)round($data->pre_paid_charge_value_maximum_selection / 60) : '', // 预付充电量最大选择量
                'pre_paid_charge_value_maximum_selection_energy' => $is_pre_paid_energy ? bcdiv($data->pre_paid_charge_value_maximum_selection, 1000, 3) : '', // 预付充电量最大选择量
                'post_paid_purchase_charge_value_time' => $is_post_paid_time ? (string)round($data->post_paid_purchase_charge_value / 60) : '', // 后付购买充电量
                'post_paid_purchase_charge_value_energy' => $is_post_paid_energy ? bcdiv($data->post_paid_purchase_charge_value, 1000, 3) : '', // 后付购买充电量
                'post_paid_maximum_charge_time' => $is_post_paid ? (string)round($data->post_paid_maximum_charge_time / 60) : '', // 后付最大充电时间
                'idling_penalty_effective_type' => IdlingPenaltyEffectiveType::getDescription($data->idling_penalty_effective_type), // 闲置罚款生效类型
                'is_enable_member_card_group_tariff_table' => $data->is_enable_member_card_group_tariff_table, // 是否启用会员卡组收费表
                'is_enable_user_group_tariff_table' => $data->is_enable_user_group_tariff_table, // 是否启用用户组收费表
                'idling_penalty_grace_period' => filled($data->idling_penalty_grace_period) ? ($data->idling_penalty_grace_period / 60) : '', // 闲置罚款宽限期
                'tariff_table_type' => TariffTableType::getDescription($data->tariff_table_type), // 收费表类型
                'trial_charge_timeout' => filled($data->trial_charge_timeout) ? $data->trial_charge_timeout : '—/—', // 试充超时时间
                'gmt_trial_charge_reached' => $data->gmt_trial_charge_reached ?? '—/—', // 试充达标时间
                'charge_maximum_vehicle_soc_limit' => isset($data->charge_maximum_vehicle_soc_limit) ? ($data->charge_maximum_vehicle_soc_limit . __('common.unit_percent_blank')) : '—/—', // 充电最大车辆SoC限制
                'is_enable_admin_octopus_card_free_deduct' => $data->is_enable_admin_octopus_card_free_deduct, // 是否开启管理员八达通卡免费扣款
                'is_enable_free_octopus_card' => $data->is_enable_free_octopus_card, // 是否启用免费八达通卡
                'is_enable_top_up' => $data->is_enable_top_up, // 是否启用续充
                'top_up_buffer_limit' => filled($data->top_up_buffer_limit) ? $data->top_up_buffer_limit : '', // 续充缓冲限制 - 剩余充电量需大于此数才可续充
                'is_top_up_need_confirm_identity' => $data->is_top_up_need_confirm_identity, // 是否续充需要确认身份
                'remain_charge_value_generation_trigger' => RemainChargeValueGenerationTrigger::getDescription($data->remain_charge_value_generation_trigger), // 剩余充电量生成触发条件
                'remain_charge_value_validity_period' => filled($data->remain_charge_value_validity_period) ? ($data->remain_charge_value_validity_period / 60) : '', // 剩余充电量有效期
                'remain_charge_value_minimum_limit_time' => $is_time ? (string)round($data->remain_charge_value_minimum_limit / 60) : '', // 剩余充电量最小限制
                'remain_charge_value_minimum_limit_energy' => $is_energy ? bcdiv($data->remain_charge_value_minimum_limit, 1000, 3) : '', // 剩余充电量最小限制
                'is_enable_use_remain_charge_value' => $data->is_enable_use_remain_charge_value, // 是否允许使用剩余充电量
                'is_enable_charge_value_adjust_selected_base_on_remain' => $data->is_enable_charge_value_adjust_selected_base_on_remain, // 是否启用根据剩余充电量调整已选充电量
                'is_enable_round_up_tail_charge_value_calculation' => $data->is_enable_round_up_tail_charge_value_calculation, // 是否启用向上取整尾部充电量计算
                'start_id_tag' => $data->start_id_tag ?? '—/—', // 开始ID标识
                'meter_start' => $data->meter_start, // 开始充电仪表值
                'gmt_power_on' => $data->gmt_power_on ?? '—/—', // 上电时间
                'reminder_email' => $data->reminder_email ?? '—/—', // 提醒邮箱
                'reminder_telephone' => $data->reminder_telephone ?? '—/—', // 提醒电话
                'off_peak_charged_time' => (string)round($data->off_peak_charged_time / 60), // 非高峰已充时间
                'on_peak_charged_time' => (string)round($data->on_peak_charged_time / 60), // 高峰已充时间
                'off_peak_charged_energy' => bcdiv($data->off_peak_charged_energy, 1000, 3), // 非高峰已充电量
                'on_peak_charged_energy' => bcdiv($data->on_peak_charged_energy, 1000, 3), // 高峰已充电量
                'identity_type' => IdentityType::getDescription($data->identity_type), // 身份类型
                'identity_number' => $data->identity_number ?? '—/—', // 身份号码
                'charge_pre_authorization_record_number' => $data->charge_pre_authorization_record_number ?? '—/—', // 充电预授权记录编号
                'meter_value_voltage' => $data->meter_value_voltage ?? '—/—', // 仪表值-电压
                'meter_value_current' => $data->meter_value_current ?? '—/—', // 仪表值-电流
                'meter_value_power' => $data->meter_value_power ?? '—/—', // 仪表值-功率
                'meter_value_power_factor' => $data->meter_value_power_factor ?? '—/—', // 仪表值-功率因数
                'meter_value_power_offered' => $data->meter_value_power_offered ?? '—/—', // 仪表值-最大功率
                'meter_value_start_vehicle_soc' => $data->meter_value_start_vehicle_soc ?? '—/—', // 仪表值-开始车辆电量
                'meter_value_vehicle_soc' => $data->meter_value_vehicle_soc ?? '—/—', // 仪表值-车辆电量
                'meter_value_temperature' => $data->meter_value_temperature ?? '—/—', // 仪表值-温度
                'meter_value_fan_speed' => $data->meter_value_fan_speed ?? '—/—', // 仪表值-风扇速度
                'remote_stop_operator_type' => $data->remote_stop_operator_type ?? '—/—', // 远程停止操作者类型
                'remote_stop_operator_number' => $data->remote_stop_operator_number ?? '—/—', // 远程停止操作者编号
                'remote_stop_reason' => RemoteStopChargeReasonEnum::getDescription($data->remote_stop_reason), // 远程停止原因
                'remote_stop_detail' => $data->remote_stop_detail ?? '—/—', // 远程停止详情
                'gmt_remote_stop' => $data->gmt_remote_stop ?? '—/—', // 远程停止时间
                'stop_id_tag' => $data->stop_id_tag ?? '—/—', // 停止ID标识
                'meter_stop' => $data->meter_stop ?? '—/—', // 结束充电仪表值
                'stop_reason' => $data->stop_reason ?? '—/—', // 停止充电原因
                'remote_unlock_operator_type' => $data->remote_unlock_operator_type ?? '—/—', // 远程解锁操作者类型
                'remote_unlock_operator_number' => $data->remote_unlock_operator_number ?? '—/—', // 远程解锁操作者编号
                'gmt_remote_unlock' => $data->gmt_remote_unlock ?? '—/—', // 远程解锁时间
                'gmt_unlocked' => $data->gmt_unlocked ?? '—/—', // 解锁时间
                'idling_penalty_time' => (string)round($data->idling_penalty_time / 60), // 闲置罚款时间
                'off_peak_idling_penalty_time' => (string)round($data->off_peak_idling_penalty_time / 60), // 非高峰闲置罚款时间
                'on_peak_idling_penalty_time' => (string)round($data->on_peak_idling_penalty_time / 60), // 高峰闲置罚款时间
                'gmt_idling_penalty_locked' => $data->gmt_idling_penalty_locked ?? '—/—', // 闲置罚款锁定时间
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString() // 修改时间
            );
        }

        // 先把语言赋值才可以在后面array出来表头，不然会输出未编译前的字符串
        $charge_record_number = __("$module_name.charge_record_number");
        $connector_name = __("$module_name.connector_name");
        $gmt_start = __("$module_name.gmt_start");
        $gmt_stop = __("$module_name.gmt_stop");
        $charged_time = __("$module_name.charged_time") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $charged_energy = __("$module_name.charged_energy") . ' ( ' . __('common.unit_kwh_blank') . ' )';
        $total_amount = __("$module_name.total_amount") . ' ( ' . __('common.unit_hk_blank') . ' )';
        $merchant_name = __("$module_name.merchant_name");
        $user = __("$module_name.user");
        $user_group_name = __("$module_name.user_group_name");
        $user_credential_type = __("$module_name.user_credential_type");
        $user_credential_number = __("$module_name.user_credential_number");
        $member_name = __("$module_name.member_name");
        $member_card_group_name = __("$module_name.member_card_group_name");
        $site_name = __("$module_name.site_name");
        $zone_name = __("$module_name.zone_name");
        $parking_bay_number = __("$module_name.parking_bay_number");
        $charge_point_name = __("$module_name.charge_point_name");
        $lms_mode = __("$module_name.lms_mode");
        $is_record_charge_record_meter_value = __("$module_name.is_record_charge_record_meter_value");
        $is_enable_charge_arrears = __("$module_name.is_enable_charge_arrears");
        $license_plate_number = __("$module_name.license_plate_number");
        $gmt_park_sensor_entry = __("$module_name.gmt_park_sensor_entry");
        $is_allow_points_overdraft = __("$module_name.is_allow_points_overdraft");
        $charge_tariff_scheme = __("$module_name.charge_tariff_scheme");
        $charge_value_type = __("$module_name.charge_value_type");
        $charge_value_interval_time = __("$module_name.charge_value_interval") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $charge_value_interval_energy = __("$module_name.charge_value_interval") . ' ( ' . __('common.unit_kwh_blank') . ' )';
        $charge_value_amount = __("$module_name.charge_value_amount") . ' ( ' . __('common.unit_hk_blank') . ' )';
        $idling_penalty_amount = __("$module_name.idling_penalty_amount") . ' ( ' . __('common.unit_hk_blank') . ' )';
        $actual_payment_amount = __("$module_name.actual_payment_amount") . ' ( ' . __('common.unit_hk_blank') . ' )';
        $use_points = __("$module_name.use_points");
        $merchant_handling_fee_rate = __("$module_name.merchant_handling_fee_rate") . ' ( ' . __('common.unit_percent_blank') . ' )';
        $merchant_handling_fee = __("$module_name.merchant_handling_fee") . ' ( ' . __('common.unit_hk_blank') . ' )';
        $merchant_receivable = __("$module_name.merchant_receivable") . ' ( ' . __('common.unit_hk_blank') . ' )';
        $pre_paid_charge_value_time = __("$module_name.pre_paid_charge_value") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $pre_paid_charge_value_energy = __("$module_name.pre_paid_charge_value") . ' ( ' . __('common.unit_kwh_blank') . ' )';
        $pre_paid_purchase_charge_value_time = __("$module_name.pre_paid_purchase_charge_value") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $pre_paid_purchase_charge_value_energy = __("$module_name.pre_paid_purchase_charge_value") . ' ( ' . __('common.unit_kwh_blank') . ' )';
        $pre_paid_purchase_charge_value_time = __("$module_name.pre_paid_purchase_charge_value") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $pre_paid_purchase_charge_value_energy = __("$module_name.pre_paid_purchase_charge_value") . ' ( ' . __('common.unit_kwh_blank') . ' )';
        $pre_paid_use_remain_charge_value_time = __("$module_name.pre_paid_use_remain_charge_value") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $pre_paid_use_remain_charge_value_energy = __("$module_name.pre_paid_use_remain_charge_value") . ' ( ' . __('common.unit_kwh_blank') . ' )';
        $pre_paid_charge_value_maximum_selection_time = __("$module_name.pre_paid_charge_value_maximum_selection") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $pre_paid_charge_value_maximum_selection_energy = __("$module_name.pre_paid_charge_value_maximum_selection") . ' ( ' . __('common.unit_kwh_blank') . ' )';
        $post_paid_purchase_charge_value_time = __("$module_name.post_paid_purchase_charge_value") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $post_paid_purchase_charge_value_energy = __("$module_name.post_paid_purchase_charge_value") . ' ( ' . __('common.unit_kwh_blank') . ' )';
        $post_paid_maximum_charge_time = __("$module_name.post_paid_maximum_charge_time") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $idling_penalty_effective_type = __("$module_name.idling_penalty_effective_type");
        $is_enable_member_card_group_tariff_table = __("$module_name.is_enable_member_card_group_tariff_table");
        $is_enable_user_group_tariff_table = __("$module_name.is_enable_user_group_tariff_table");
        $idling_penalty_grace_period = __("$module_name.idling_penalty_grace_period") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $tariff_table_type = __("$module_name.tariff_table_type");
        $trial_charge_timeout = __("$module_name.trial_charge_timeout");
        $gmt_trial_charge_reached = __("$module_name.gmt_trial_charge_reached");
        $charge_maximum_vehicle_soc_limit = __("$module_name.charge_maximum_vehicle_soc_limit");
        $is_enable_admin_octopus_card_free_deduct = __("$module_name.is_enable_admin_octopus_card_free_deduct");
        $is_enable_free_octopus_card = __("$module_name.is_enable_free_octopus_card");
        $is_enable_top_up = __("$module_name.is_enable_top_up");
        $top_up_buffer_limit = __("$module_name.top_up_buffer_limit") . ' ( ' . __('common.unit_s_blank') . ' )';
        $is_top_up_need_confirm_identity = __("$module_name.is_top_up_need_confirm_identity");
        $remain_charge_value_generation_trigger = __("$module_name.remain_charge_value_generation_trigger");
        $remain_charge_value_validity_period = __("$module_name.remain_charge_value_validity_period") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $remain_charge_value_minimum_limit_time = __("$module_name.remain_charge_value_minimum_limit") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $remain_charge_value_minimum_limit_energy = __("$module_name.remain_charge_value_minimum_limit") . ' ( ' . __('common.unit_kwh_blank') . ' )';
        $is_enable_use_remain_charge_value = __("$module_name.is_enable_use_remain_charge_value");
        $is_enable_charge_value_adjust_selected_base_on_remain = __("$module_name.is_enable_charge_value_adjust_selected_base_on_remain");
        $is_enable_round_up_tail_charge_value_calculation = __("$module_name.is_enable_round_up_tail_charge_value_calculation");
        $start_id_tag = __("$module_name.start_id_tag");
        $meter_start = __("$module_name.meter_start");
        $gmt_power_on = __("$module_name.gmt_power_on");
        $reminder_email = __("$module_name.reminder_email");
        $reminder_telephone = __("$module_name.reminder_telephone");
        $off_peak_charged_time = __("$module_name.off_peak_charged_time") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $on_peak_charged_time = __("$module_name.on_peak_charged_time") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $off_peak_charged_energy = __("$module_name.off_peak_charged_energy") . ' ( ' . __('common.unit_kwh_blank') . ' )';
        $on_peak_charged_energy = __("$module_name.on_peak_charged_energy") . ' ( ' . __('common.unit_kwh_blank') . ' )';
        $identity_type = __("$module_name.identity_type");
        $identity_number = __("$module_name.identity_number");
        $charge_pre_authorization_record_number = __("$module_name.charge_pre_authorization_record_number");
        $meter_value_voltage = __("$module_name.meter_value_voltage");
        $meter_value_current = __("$module_name.meter_value_current");
        $meter_value_power = __("$module_name.meter_value_power");
        $meter_value_power_factor = __("$module_name.meter_value_power_factor");
        $meter_value_power_offered = __("$module_name.meter_value_power_offered");
        $meter_value_start_vehicle_soc = __("$module_name.meter_value_start_vehicle_soc");
        $meter_value_vehicle_soc = __("$module_name.meter_value_vehicle_soc");
        $meter_value_temperature = __("$module_name.meter_value_temperature");
        $meter_value_fan_speed = __("$module_name.meter_value_fan_speed");
        $remote_stop_operator_type = __("$module_name.remote_stop_operator_type");
        $remote_stop_operator_number = __("$module_name.remote_stop_operator_number");
        $remote_stop_reason = __("$module_name.remote_stop_reason");
        $remote_stop_detail = __("$module_name.remote_stop_detail");
        $gmt_remote_stop = __("$module_name.gmt_remote_stop");
        $stop_id_tag = __("$module_name.stop_id_tag");
        $meter_stop = __("$module_name.meter_stop");
        $stop_reason = __("$module_name.stop_reason");
        $remote_unlock_operator_type = __("$module_name.remote_unlock_operator_type");
        $remote_unlock_operator_number = __("$module_name.remote_unlock_operator_number");
        $gmt_remote_unlock = __("$module_name.gmt_remote_unlock");
        $gmt_unlocked = __("$module_name.gmt_unlocked");
        $idling_penalty_time = __("$module_name.idling_penalty_time") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $off_peak_idling_penalty_time = __("$module_name.off_peak_idling_penalty_time") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $on_peak_idling_penalty_time = __("$module_name.on_peak_idling_penalty_time") . ' ( ' . __('common.unit_mins_blank') . ' )';
        $gmt_idling_penalty_locked = __("$module_name.gmt_idling_penalty_locked");
        $gmt_create = __("$module_name.gmt_create");
        $web_title = __("$module_name.web_title");

        $remove_column_list = [];
        switch (env('CURRENT_PROJECT_NAME')) {
            default:
                // 需要移除的列
                $remove_column_list = [
                    $charge_value_interval_time,
                    $charge_value_interval_energy,
                    $charge_value_amount,
                    $idling_penalty_amount,
                    $total_amount,
                    $actual_payment_amount,
                    $use_points,
                    $merchant_handling_fee_rate,
                    $merchant_handling_fee,
                    $merchant_receivable,
                    $pre_paid_charge_value_time,
                    $pre_paid_charge_value_energy,
                    $pre_paid_purchase_charge_value_time,
                    $pre_paid_purchase_charge_value_energy,
                    $pre_paid_use_remain_charge_value_time,
                    $pre_paid_use_remain_charge_value_energy,
                    $pre_paid_charge_value_maximum_selection_time,
                    $pre_paid_charge_value_maximum_selection_energy,
                    $post_paid_purchase_charge_value_time,
                    $post_paid_purchase_charge_value_energy,
                    $post_paid_maximum_charge_time,
                ];
                break;
        }

        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet(); //实例化 Spreadsheet 对象
        $worksheet = $spreadsheet->getActiveSheet();

        // 工作簿名称为 "Charge Record"
        $worksheet->setTitle($web_title);

        $styleArray = [
            'font' => [
                'bold' => false
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
        ];

        // 计算数组长度用于动态补充表格样式
        $len = count($result);
        // 因为前面两格表头样式占了一行，所以要加1用于设置单元格样式
        $total_rows = $len + 1;
        $styleArrayBody = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['argb' => '666666'],
                ],
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
        ];

        // 指定表头字段
        $excel_data = array(
            array(
                $charge_record_number,
                $connector_name,
                $gmt_start,
                $gmt_stop,
                $charged_time,
                $charged_energy,
                $total_amount,
                $merchant_name,
                $user,
                $user_group_name,
                $user_credential_type,
                $user_credential_number,
                $member_name,
                $member_card_group_name,
                $site_name,
                $zone_name,
                $parking_bay_number,
                $charge_point_name,
                $lms_mode,
                $is_record_charge_record_meter_value,
                $is_enable_charge_arrears,
                $license_plate_number,
                $gmt_park_sensor_entry,
                $is_allow_points_overdraft,
                $charge_tariff_scheme,
                $charge_value_type,
                $charge_value_interval_time,
                $charge_value_interval_energy,
                $charge_value_amount,
                $idling_penalty_amount,
                $actual_payment_amount,
                $use_points,
                $merchant_handling_fee_rate,
                $merchant_handling_fee,
                $merchant_receivable,
                $pre_paid_charge_value_time,
                $pre_paid_charge_value_energy,
                $pre_paid_purchase_charge_value_time,
                $pre_paid_purchase_charge_value_energy,
                $pre_paid_use_remain_charge_value_time,
                $pre_paid_use_remain_charge_value_energy,
                $pre_paid_charge_value_maximum_selection_time,
                $pre_paid_charge_value_maximum_selection_energy,
                $post_paid_purchase_charge_value_time,
                $post_paid_purchase_charge_value_energy,
                $post_paid_maximum_charge_time,
                $idling_penalty_effective_type,
                $is_enable_member_card_group_tariff_table,
                $is_enable_user_group_tariff_table,
                $idling_penalty_grace_period,
                $tariff_table_type,
                $trial_charge_timeout,
                $gmt_trial_charge_reached,
                $charge_maximum_vehicle_soc_limit,
                $is_enable_admin_octopus_card_free_deduct,
                $is_enable_free_octopus_card,
                $is_enable_top_up,
                $top_up_buffer_limit,
                $is_top_up_need_confirm_identity,
                $remain_charge_value_generation_trigger,
                $remain_charge_value_validity_period,
                $remain_charge_value_minimum_limit_time,
                $remain_charge_value_minimum_limit_energy,
                $is_enable_use_remain_charge_value,
                $is_enable_charge_value_adjust_selected_base_on_remain,
                $is_enable_round_up_tail_charge_value_calculation,
                $start_id_tag,
                $meter_start,
                $gmt_power_on,
                $reminder_email,
                $reminder_telephone,
                $off_peak_charged_time,
                $on_peak_charged_time,
                $off_peak_charged_energy,
                $on_peak_charged_energy,
                $identity_type,
                $identity_number,
                $charge_pre_authorization_record_number,
                $meter_value_voltage,
                $meter_value_current,
                $meter_value_power,
                $meter_value_power_factor,
                $meter_value_power_offered,
                $meter_value_start_vehicle_soc,
                $meter_value_vehicle_soc,
                $meter_value_temperature,
                $meter_value_fan_speed,
                $remote_stop_operator_type,
                $remote_stop_operator_number,
                $remote_stop_reason,
                $remote_stop_detail,
                $gmt_remote_stop,
                $stop_id_tag,
                $meter_stop,
                $stop_reason,
                $remote_unlock_operator_type,
                $remote_unlock_operator_number,
                $gmt_remote_unlock,
                $gmt_unlocked,
                $idling_penalty_time,
                $off_peak_idling_penalty_time,
                $on_peak_idling_penalty_time,
                $gmt_idling_penalty_locked,
                $gmt_create,
            )
        );

        // 移除指定列
        if (filled($remove_column_list)) $excel_data[0] = array_diff($excel_data[0], $remove_column_list);
        //设置单元格样式
        // 将数组长度转换为 Excel 列名
        $last_column = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex(count($excel_data[0]));
        $worksheet->getStyle("A1:{$last_column}$total_rows")->applyFromArray($styleArray)->getFont()->setName('Times New Roman')->setSize(11);
        $worksheet->getStyle("A1:{$last_column}1")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('c0c0c0');
        $worksheet->getStyle("A1:{$last_column}$total_rows")->applyFromArray($styleArrayBody);

        foreach ($result as $report) {
            $excel_row = array(
                $charge_record_number => $report['charge_record_number'],
                $connector_name => $report['connector_name'],
                $gmt_start => $report['gmt_start'],
                $gmt_stop => $report['gmt_stop'],
                $charged_time => $report['charged_time'],
                $charged_energy => $report['charged_energy'],
                $total_amount => $report['total_amount'],
                $merchant_name => $report['merchant_name'],
                $user => $report['user_email'],
                $user_group_name => $report['user_group_name'],
                $user_credential_type => $report['user_credential_type'],
                $user_credential_number => $report['user_credential_number'],
                $member_name => $report['member_name'],
                $member_card_group_name => $report['member_card_group_name'],
                $site_name => $report['site_name'],
                $zone_name => $report['zone_name'],
                $parking_bay_number => $report['parking_bay_number'],
                $charge_point_name => $report['charge_point_name'],
                $lms_mode => $report['lms_mode'],
                $is_record_charge_record_meter_value => ($report['is_record_charge_record_meter_value']) ? '✔' : '✘',
                $is_enable_charge_arrears => ($report['is_enable_charge_arrears']) ? '✔' : '✘',
                $license_plate_number => $report['license_plate_number'],
                $gmt_park_sensor_entry => $report['gmt_park_sensor_entry'],
                $is_allow_points_overdraft => ($report['is_allow_points_overdraft']) ? '✔' : '✘',
                $charge_tariff_scheme => $report['charge_tariff_scheme'],
                $charge_value_type => $report['charge_value_type'],
                $charge_value_interval_time => $report['charge_value_interval_time'],
                $charge_value_interval_energy => $report['charge_value_interval_energy'],
                $charge_value_amount => $report['charge_value_amount'],
                $idling_penalty_amount => $report['idling_penalty_amount'],
                $actual_payment_amount => $report['actual_payment_amount'],
                $use_points => $report['use_points'],
                $merchant_handling_fee_rate => $report['merchant_handling_fee_rate'],
                $merchant_handling_fee => $report['merchant_handling_fee'],
                $merchant_receivable => $report['merchant_receivable'],
                $pre_paid_charge_value_time => $report['pre_paid_charge_value_time'],
                $pre_paid_charge_value_energy => $report['pre_paid_charge_value_energy'],
                $pre_paid_purchase_charge_value_time => $report['pre_paid_purchase_charge_value_time'],
                $pre_paid_purchase_charge_value_energy => $report['pre_paid_purchase_charge_value_energy'],
                $pre_paid_use_remain_charge_value_time => $report['pre_paid_use_remain_charge_value_time'],
                $pre_paid_use_remain_charge_value_energy => $report['pre_paid_use_remain_charge_value_energy'],
                $pre_paid_charge_value_maximum_selection_time => $report['pre_paid_charge_value_maximum_selection_time'],
                $pre_paid_charge_value_maximum_selection_energy => $report['pre_paid_charge_value_maximum_selection_energy'],
                $post_paid_purchase_charge_value_time => $report['post_paid_purchase_charge_value_time'],
                $post_paid_purchase_charge_value_energy => $report['post_paid_purchase_charge_value_energy'],
                $post_paid_maximum_charge_time => $report['post_paid_maximum_charge_time'],
                $idling_penalty_effective_type => $report['idling_penalty_effective_type'],
                $is_enable_member_card_group_tariff_table => ($report['is_enable_member_card_group_tariff_table']) ? '✔' : '✘',
                $is_enable_user_group_tariff_table => ($report['is_enable_user_group_tariff_table']) ? '✔' : '✘',
                $idling_penalty_grace_period => $report['idling_penalty_grace_period'],
                $tariff_table_type => $report['tariff_table_type'],
                $trial_charge_timeout => $report['trial_charge_timeout'],
                $gmt_trial_charge_reached => $report['gmt_trial_charge_reached'],
                $charge_maximum_vehicle_soc_limit => $report['charge_maximum_vehicle_soc_limit'],
                $is_enable_admin_octopus_card_free_deduct => ($report['is_enable_admin_octopus_card_free_deduct']) ? '✔' : '✘',
                $is_enable_free_octopus_card => ($report['is_enable_free_octopus_card']) ? '✔' : '✘',
                $is_enable_top_up => ($report['is_enable_top_up']) ? '✔' : '✘',
                $top_up_buffer_limit => $report['top_up_buffer_limit'],
                $is_top_up_need_confirm_identity => ($report['is_top_up_need_confirm_identity']) ? '✔' : '✘',
                $remain_charge_value_generation_trigger => $report['remain_charge_value_generation_trigger'],
                $remain_charge_value_validity_period => $report['remain_charge_value_validity_period'],
                $remain_charge_value_minimum_limit_time => $report['remain_charge_value_minimum_limit_time'],
                $remain_charge_value_minimum_limit_energy => $report['remain_charge_value_minimum_limit_energy'],
                $is_enable_use_remain_charge_value => ($report['is_enable_use_remain_charge_value']) ? '✔' : '✘',
                $is_enable_charge_value_adjust_selected_base_on_remain => ($report['is_enable_charge_value_adjust_selected_base_on_remain']) ? '✔' : '✘',
                $is_enable_round_up_tail_charge_value_calculation => ($report['is_enable_round_up_tail_charge_value_calculation']) ? '✔' : '✘',
                $start_id_tag => $report['start_id_tag'],
                $meter_start => (string)$report['meter_start'],
                $gmt_power_on => $report['gmt_power_on'],
                $reminder_email => (string)$report['reminder_email'],
                $reminder_telephone => (string)$report['reminder_telephone'],
                $off_peak_charged_time => $report['off_peak_charged_time'],
                $on_peak_charged_time => $report['on_peak_charged_time'],
                $off_peak_charged_energy => $report['off_peak_charged_energy'],
                $on_peak_charged_energy => $report['on_peak_charged_energy'],
                $identity_type => $report['identity_type'],
                $identity_number => $report['identity_number'],
                $charge_pre_authorization_record_number => $report['charge_pre_authorization_record_number'],
                $meter_value_voltage => $report['meter_value_voltage'],
                $meter_value_current => $report['meter_value_current'],
                $meter_value_power => $report['meter_value_power'],
                $meter_value_power_factor => $report['meter_value_power_factor'],
                $meter_value_power_offered => $report['meter_value_power_offered'],
                $meter_value_start_vehicle_soc => $report['meter_value_start_vehicle_soc'],
                $meter_value_vehicle_soc => $report['meter_value_vehicle_soc'],
                $meter_value_temperature => $report['meter_value_temperature'],
                $meter_value_fan_speed => $report['meter_value_fan_speed'],
                $remote_stop_operator_type => $report['remote_stop_operator_type'],
                $remote_stop_operator_number => $report['remote_stop_operator_number'],
                $remote_stop_reason => (string)$report['remote_stop_reason'],
                $remote_stop_detail => (string)$report['remote_stop_detail'],
                $gmt_remote_stop => $report['gmt_remote_stop'],
                $stop_id_tag => $report['stop_id_tag'],
                $meter_stop => (string)$report['meter_stop'],
                $stop_reason => $report['stop_reason'],
                $remote_unlock_operator_type => $report['remote_unlock_operator_type'],
                $remote_unlock_operator_number => $report['remote_unlock_operator_number'],
                $gmt_remote_unlock => $report['gmt_remote_unlock'],
                $gmt_unlocked => $report['gmt_unlocked'],
                $idling_penalty_time => $report['idling_penalty_time'],
                $off_peak_idling_penalty_time => $report['off_peak_idling_penalty_time'],
                $on_peak_idling_penalty_time => $report['on_peak_idling_penalty_time'],
                $gmt_idling_penalty_locked => $report['gmt_idling_penalty_locked'],
                $gmt_create => $report['gmt_create'],
            );
            // 移除指定列
            if (filled($remove_column_list)) {
                foreach ($remove_column_list as $remove_column) {
                    unset($excel_row[$remove_column]);
                }
            }
            $excel_data[] = $excel_row;
        }

        $worksheet->fromArray($excel_data);
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $file_name = $web_title . ' ' . date('Y-m-d H:i:s') . '.xlsx';
        //设置单元格的宽度
        foreach ($spreadsheet->getActiveSheet()->getColumnIterator() as $column) {
            $spreadsheet->getActiveSheet()->getColumnDimension($column->getColumnIndex())->setAutoSize(true);
        }

        // 客户端文件下载
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $file_name . '"');
        header('Cache-Control: max-age=0');
        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
        exit;
    }

    /**
     * chargeRecordTrialCharge列表接口
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function chargeRecordTrialChargeList(Request $request): JsonResponse
    {
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $charge_record_number = $request->input('charge_record_number', '');
        $trial_charge_item_list = ChargeRecord::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->where('charge_record_number', $charge_record_number)
            ->first()
            ?->chargeRecordTrialCharge
            ?->trial_charge_item_json ?? [];
        // 按照order传入字段排序
        $trial_charge_item_list = collect($trial_charge_item_list)->sortBy($order, SORT_REGULAR, $sort)->values()->all();

        $json = array(
            'draw' => $draw,
            'recordsTotal' => count($trial_charge_item_list),
            'recordsFiltered' => count($trial_charge_item_list),
            "data" => $trial_charge_item_list,
        );

        return response()->json($json);
    }

    /**
     * chargeRecordMeterValue列表接口
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function chargeRecordMeterValueList(Request $request): JsonResponse
    {
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $charge_record_number = $request->input('charge_record_number', '');
        $meter_value_item_list = ChargeRecord::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->where('charge_record_number', $charge_record_number)
            ->first()
            ?->chargeRecordMeterValue
            ?->meter_value_item_json ?? [];
        // 按照order传入字段排序
        $meter_value_item_list = collect($meter_value_item_list)->sortBy($order, SORT_REGULAR, $sort)->values()->all();

        $json = array(
            'draw' => $draw,
            'recordsTotal' => count($meter_value_item_list),
            'recordsFiltered' => count($meter_value_item_list),
            "data" => $meter_value_item_list,
        );

        return response()->json($json);
    }

    /**
     * chargeRecordChargedEnergy列表接口
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function chargeRecordChargedEnergyList(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $charge_record_number = $request->input('charge_record_number', '');

        // 直接获取子表的json数据
        $charged_energy_item_list = ChargeRecord::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->where('charge_record_number', $charge_record_number)
            ->first()
            ?->chargeRecordChargedEnergy
            ?->charged_energy_item_json ?? [];
        // 按照order传入字段排序
        $charged_energy_item_list = collect($charged_energy_item_list)->sortBy($order, SORT_REGULAR, $sort)->values()->all();

        $this->data = $charged_energy_item_list;
        return $this->returnJson();
    }

    public function magicCharge(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        $charge_record_number = $request->input('charge_record_number');
        $pre_paid_charge_value = $request->input('pre_paid_charge_value');

        if (blank($charge_record_number)) {
            $this->missingField(__("$module_name.charge_record_number"));
            return $this->returnJson();
        }

        if (blank($pre_paid_charge_value)) {
            $this->missingField(__("$module_name.pre_paid_charge_value"));
            return $this->returnJson();
        }

        // 判断pre_paid_charge_value必须是H:i格式
        $pre_paid_charge_value_arr = explode(':', $pre_paid_charge_value);
        if (count($pre_paid_charge_value_arr) !== 2) {
            $this->missingField(__("$module_name.pre_paid_charge_value"));
            return $this->returnJson();
        }
        // 判断pre_paid_charge_value_arr是否全为正整数，并且不能全为0或者00
        if (!preg_match('/^[1-9]\d*$|0|00+$/', $pre_paid_charge_value_arr[0]) || !preg_match('/^[1-9]\d*$|0|00+$/', $pre_paid_charge_value_arr[1])) {
            $this->missingField(__("$module_name.pre_paid_charge_value"));
            return $this->returnJson();
        }

        // 检查两个元素是否都是0或者00
        if (($pre_paid_charge_value_arr[0] === '0' || preg_match('/^00+$/', $pre_paid_charge_value_arr[0])) &&
            ($pre_paid_charge_value_arr[1] === '0' || preg_match('/^00+$/', $pre_paid_charge_value_arr[1]))) {
            $this->missingField(__("$module_name.pre_paid_charge_value"));
            return $this->returnJson();
        }

        // 时分转秒
        $pre_paid_charge_value_seconds = $pre_paid_charge_value_arr[0] * 3600 + $pre_paid_charge_value_arr[1] * 60;

        // 判断pre_paid_charge_value_seconds是否大于0
        if ($pre_paid_charge_value_seconds <= 0) {
            $this->missingField(__("$module_name.pre_paid_charge_value"));
            return $this->returnJson();
        }

        $charge_record = ChargeRecord::firstWhere('charge_record_number', $charge_record_number);

        if (blank($charge_record)) {
            $this->notFoundData(__("$module_name.web_title"));
            return $this->returnJson();
        }

        // 如果gmt_stop或gmt_unlocked不为null，禁止修改
        if (!is_null($charge_record->gmt_stop) || !is_null($charge_record->gmt_unlocked)) {
            $this->code = 201;
            // 已停止充电或已解锁，禁止修改
            $this->message = 'Charge record has been stopped or unlocked, cannot be modified';
            return $this->returnJson();
        }
        // 如果pre_paid_charge_value!=0，禁止修改
        if ($charge_record->pre_paid_charge_value != 0) {
            $this->code = 201;
            // 已预付费，禁止修改
            $this->message = 'Pre-paid charge value is not 0, cannot be modified';
            return $this->returnJson();
        }

        $charge_record->pre_paid_charge_value = $pre_paid_charge_value_seconds;
        $result = $charge_record->save();

        if (!$result) {
            $this->modelSaveFail();
            return $this->returnJson();
        }

        return $this->returnJson();
    }

    /**
     * 返回地址栏参数
     *
     * @param Request $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(Request $request): array
    {
        return array(
            'is_page' => 'true',
            'connector_name_search' => $request->get('connector_name_search'),
            'charge_tariff_scheme_search' => $request->get('charge_tariff_scheme_search'),
            'charge_value_type_search' => $request->get('charge_value_type_search'),
            'charge_start_time_search' => $request->get('charge_start_time_search'),
        );
    }

    /**
     * 搜索数据
     *
     * @param Request $request
     * @return mixed
     */
    protected function _searchList(Request $request): mixed
    {
        // dataTable字段
        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $connector_name_search = $request->input('connector_name_search');
        $charge_tariff_scheme_search = $request->input('charge_tariff_scheme_search');
        $charge_value_type_search = $request->input('charge_value_type_search');
        $charge_start_time_search = $request->input('charge_start_time_search');

        $where = array();
        $charge_start_time_search_list = self::getRangeDateTimeArray($charge_start_time_search ?: '') ?: null;

        if (filled($connector_name_search)) {
            $where[] = ["connector_name like '%$connector_name_search%'"];
        }
        if (filled($charge_tariff_scheme_search)) {
            $where[] = ["charge_tariff_scheme = '$charge_tariff_scheme_search'"];
        }
        if (filled($charge_value_type_search)) {
            $where[] = ["charge_value_type = '$charge_value_type_search'"];
        }
        if (filled($charge_start_time_search_list)) {
            $where[] = ["IFNULL(gmt_power_on,gmt_start) >= '$charge_start_time_search_list[0]'"];
            $where[] = ["IFNULL(gmt_power_on,gmt_start) <= '$charge_start_time_search_list[1]'"];
        }

        $condition_list = null;
        foreach ($where as $condition) { // 拼接查询条件，用于在whereRaw执行，因为上面查询条件需要用到IFNULL函数
            if (empty($condition_list)) {
                $condition_list = $condition[0];
            } else {
                $condition_list = $condition_list . " AND " . $condition[0];
            }
        }

        $charge_record = ChargeRecord::with(['connector', 'user']);
        if (!empty($condition_list)) {
            $charge_record = $charge_record->whereRaw("$condition_list");
        }
        if ($order == 'gmt_start') {
            $sort_order = "CASE WHEN gmt_power_on IS NULL THEN gmt_start ELSE gmt_power_on END $sort";
            //$sort_order = "COALESCE(gmt_power_on, gmt_start) $sort";
        } else {
            $sort_order = "$order $sort";
        }
        return $charge_record->when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->orderByRaw($sort_order)
            ->latest();
    }

    /**
     * 时间计算时分秒
     *
     * @param $time
     * @return false|string
     */
    protected function secTime($time): false|string
    {
        if ($time == 0) {
            return 0 . __('common.unit_s');
        }

        if (!is_numeric($time)) {
            return false;
        }

        $value = array("hours" => 0, "minutes" => 0, "seconds" => 0);
        if ($time >= 3600) {
            $value["hours"] = floor($time / 3600);
            $time = ($time % 3600);
        }
        if ($time >= 60) {
            $value["minutes"] = floor($time / 60);
            $time = ($time % 60);
        }
        $value["seconds"] = floor($time);
        if ($value["hours"] != 0) {
            if ($value["minutes"] != 0) {
                if ($value["seconds"] != 0) {
                    return $value["hours"] . __('common.unit_h') . $value["minutes"] . __('common.unit_m') . $value["seconds"] . __('common.unit_s');
                } else {
                    return $value["hours"] . __('common.unit_h') . $value["minutes"] . __('common.unit_m') . ':00';
                }
            } else {
                return $value["hours"] . __('common.unit_h') . $value["seconds"] . __('common.unit_s');
            }
        } else {
            if ($value["minutes"] != 0) {
                if ($value["seconds"] != 0) {
                    return $value["minutes"] . __('common.unit_m') . $value["seconds"] . __('common.unit_s');
                } else {
                    return $value["minutes"] . __('common.unit_m');
                }
            } else {
                return $value["seconds"] . __('common.unit_s');
            }
        }
    }

    /**
     * 时间计算时分秒
     *
     * @param $time
     * @return false|string
     */
    protected function secPeakTime($time): false|string
    {
        if ($time == 0) {
            return "00:00:00";
        }
        if (!is_numeric($time)) {
            return false;
        }

        $value = array("hours" => 0, "minutes" => 0, "seconds" => 0);
        if ($time >= 3600) {
            $value["hours"] = floor($time / 3600);
            $time = ($time % 3600);
        }
        if ($time >= 60) {
            $value["minutes"] = floor($time / 60);
            $time = ($time % 60);
        }
        $value["seconds"] = floor($time);
        if ($value["hours"] != 0) {
            if ($value["minutes"] != 0) {
                if ($value["seconds"] != 0) {
                    return $value["hours"] . ':' . $value["minutes"] . ':' . $value["seconds"];
                } else {
                    return $value["hours"] . ':' . $value["minutes"] . ':00';
                }
            } else {
                return $value["hours"] . ':00:' . $value["seconds"];
            }
        } else {
            if ($value["minutes"] != 0) {
                if ($value["seconds"] != 0) {
                    return '00:' . $value["minutes"] . ':' . $value["seconds"];
                } else {
                    return '00:' . $value["minutes"] . ':00';
                }
            } else {
                return '00:00:' . $value["seconds"];
            }
        }
    }

    /**
     * 格式化时间
     *
     * @param $time
     * @return array
     */
    protected function formatTime($time): array
    {
        $time_array = explode(':', $this->secPeakTime($time));
        // 因为前端页面显示只到时分，所有秒数要向上取整
        if ($time_array[2] != 0 || $time_array[2] != '00') {
            $time_array[1]++;
        }
        return array(
            'hours' => isset($time_array[0]) ? str_pad($time_array[0], 2, '0', STR_PAD_LEFT) : '00',
            'mins' => isset($time_array[1]) ? str_pad($time_array[1], 2, '0', STR_PAD_LEFT) : '00',
            'seconds' => isset($time_array[2]) ? str_pad($time_array[2], 2, '0', STR_PAD_LEFT) : '00',
        );
    }

    protected function formatTimeToDetail($seconds): string
    {
        if (blank($seconds)) return '00' . __('common.unit_h') . '00' . __('common.unit_m');

        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);

        $formatted_hours = str_pad($hours, 2, '0', STR_PAD_LEFT);
        $formatted_minutes = str_pad($minutes, 2, '0', STR_PAD_LEFT);

        return $formatted_hours . __('common.unit_h') . $formatted_minutes . __('common.unit_m');
    }

    /**
     * 获取会员组以id为index，name_json为value的数组
     *
     * @param $tariff_scheme
     * @return array
     */
    protected function getMemberCardGroupArray($tariff_scheme_json): array
    {
        // 获取所有 member_card_group_id
        $member_card_group_id_list = array_column($tariff_scheme_json, 'member_card_group_id');

        // 查询 MemberCardGroup 模型
        $member_card_groups = MemberCardGroup::whereIn('member_card_group_id', $member_card_group_id_list)->get();

        // 将查询到的数据转换成以 member_card_group_id 为键的关联数组
        $member_card_groups_map = $member_card_groups->keyBy('member_card_group_id')->all();

        // 循环 $tariff_scheme_json
        foreach ($tariff_scheme_json as &$item) {
            // 如果找到了对应的记录，插入新的 key
            if (isset($item['member_card_group_id'], $member_card_groups_map[$item['member_card_group_id']])) {
                $member_card_group = $member_card_groups_map[$item['member_card_group_id']];
                $item['member_card_group_name'] = $this->getValueFromLanguageArray($member_card_group->name_json) ?? '—/—';
            } else {
                $item['member_card_group_name'] = '—/—';
            }
        }
        $tariff_scheme_json = collect($tariff_scheme_json)->groupBy('member_card_group_id')->toArray();
        return $tariff_scheme_json;
    }

    protected function getUserGroupArray($tariff_scheme_json): array
    {
        // 获取所有 user_group_id
        $user_group_id_list = array_column($tariff_scheme_json, 'user_group_id');

        // 查询 UserGroup 模型
        $user_groups = UserGroup::whereIn('user_group_id', $user_group_id_list)->get();

        // 将查询到的数据转换成以 user_group_id 为键的关联数组
        $user_groups_map = $user_groups->keyBy('user_group_id')->all();

        // 循环 $tariff_scheme_json
        foreach ($tariff_scheme_json as &$item) {
            // 如果找到了对应的记录，插入新的 key
            if (isset($item['user_group_id'], $user_groups_map[$item['user_group_id']])) {
                $user_group = $user_groups_map[$item['user_group_id']];
                $item['user_group_name'] = $this->getValueFromLanguageArray($user_group->name_json) ?? '—/—';
            } else {
                $item['user_group_name'] = '—/—';
            }
        }
        $tariff_scheme_json = collect($tariff_scheme_json)->groupBy('user_group_id')->toArray();
        return $tariff_scheme_json;
    }
}

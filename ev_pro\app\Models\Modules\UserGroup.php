<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;

class UserGroup extends Model
{
    protected $table = 'user_group';
    protected $primaryKey = 'user_group_id';

    const CREATED_AT = 'gmt_create'; //默认是小写的模型类名复数格式，此处为自定义表名
    const UPDATED_AT = 'gmt_modified'; //定义主键，默认为id

    // protected $fillable = [];
    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 类型转化器
     */
    protected $casts = [
        'name_json' => 'array',
        'is_enable' => 'bool',
    ];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [
        'is_enable' => 1,
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    protected function asJson($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    // 一对一关联商户
    public function merchant()
    {
        return $this->hasOne(Merchant::class, 'merchant_number', 'merchant_number');
    }

    // 一对多关联用戶(user表的user_group_id字段)
    public function user()
    {
        return $this->hasMany(AppUser::class, 'user_group_id', 'user_group_id');
    }

    /**
     * 一对多关联description
     */
    public function description()
    {
        return $this->hasMany(UserGroupDescription::class, 'user_group_id', 'user_group_id');
    }

    /**
     * 一对多关联simpleTariffTableItem
     */
    public function simpleTariffTableItem()
    {
        return $this->hasMany(SimpleTariffTableItem::class, 'user_group_id', 'user_group_id');
    }

    /**
     * 一对多关联TimeTariffTableItem
     */
    public function timeTariffTableItem()
    {
        return $this->hasMany(TimeTariffTableItem::class, 'user_group_id', 'user_group_id');
    }

    /**
     * 一对多关联EnergyTariffTableItem
     */
    public function energyTariffTableItem()
    {
        return $this->hasMany(EnergyTariffTableItem::class, 'user_group_id', 'user_group_id');
    }

    /**
     * 一对多关联IdlingPenaltyTariffTableItem
     */
    public function idlingPenaltyTariffTableItem()
    {
        return $this->hasMany(IdlingPenaltyTariffTableItem::class, 'user_group_id', 'user_group_id');
    }

}

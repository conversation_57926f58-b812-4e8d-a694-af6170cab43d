<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Validation\Rule;
use Illuminate\Http\{
    JsonResponse,
    RedirectResponse,
    Request,
};
use App\Http\Controllers\Admin\Traits\{
    Add,
    Edit,
};
use Illuminate\Foundation\Application;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use App\Models\Modules\{
    PointsTopUpTier,
    Merchant,
};
use App\Enums\PaymentVendor;

class PointsTopUpTierController extends CommonController
{
    use Add, Edit;

    protected static string $module_name = 'pointsTopUpTier'; // 模块名称
    protected static bool $module_check_merchant = true; // 标记该模块校验商户

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new PointsTopUpTier;
    }

    /**
     * 初始页
     *
     * @param Request $request
     * @return View|Application|Factory
     */
    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'add_url' => action([self::class, 'add']),
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'merchant_search' => $request->get('merchant_search'),
            'merchant_list' => $this->getMerchantOptionList(),
        );

        return view("pages.{$data['module_name']}.list", $data);
    }

    /**
     * 列表接口
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        $draw = (int)$request->input('draw', 1);
        $length = (int)$request->input('length', 10);
        $search = $this->_searchList($request);
        $data_list = $search->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $merchant_name = $this->getValueFromLanguageArray($data->merchant_name_json_init) ?? '—/—';
            $result[] = array(
                'points_top_up_tier_id' => $data->points_top_up_tier_id, // 积分增值档位ID
                'merchant_name' => $merchant_name, // 商户名称
                'payment_vendor' => PaymentVendor::getDescription($data->payment_vendor), // 支付供应商
                'price' =>  __('common.unit_hk') . (float)bcdiv($data->price, 100, 1), // 价格
                'points' => (float)bcdiv($data->points, 100, 1), // 积分
                'is_enable' => $data->is_enable, // 是否启用
                'sort_order' => $data->sort_order, // 排序
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );
        return response()->json($json);
    }

    /**
     * 表单页
     *
     * @param Request $request
     * @param array $data
     * @return View|Application|Factory
     */
    protected function getForm(Request $request, array $data): View|Application|Factory
    {
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'], self::getUrlParams($request)
        );
        $merchant_number = $data['model']->merchant_number;
        if ((isSuperAdministrator() || auth()->user()->merchant_number_list->count() > 1) && blank($data['model']?->merchant_number)) {
            // 如果是新增，并且是超级管理员或者管理员的商户大于1的情况下就直接获取页面传入的编号
            $merchant_number = $request->old('merchant_number', $data['model']->merchant_number);
        } else if (blank($data['model']?->merchant_number) && auth()->user()->merchant_number_list->count() === 1) {
            // 如果是新增并且管理员商户只有一个就直接赋值
            $merchant_number = auth()->user()->merchant_number_list->first();
        }
        $merchant_name = $this->getValueFromLanguageArray(
            Merchant::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))
                ->firstWhere('merchant_number', $merchant_number)?->name_json
        );
        $data['model']->merchant_number = $merchant_number;
        $data['model']->merchant_name = $merchant_name;
        $data['model']->payment_vendor = $request->old('payment_vendor', $data['model']->payment_vendor);
        $data['model']->price = $request->old('price', filled($data['model']->price) ? (float)bcdiv($data['model']->price, 100, 1) : null);
        $data['model']->points = $request->old('points', filled($data['model']->points) ? (float)bcdiv($data['model']->points, 100, 1) : null);
        $data['model']->is_enable = $request->old('is_enable', $data['model']->is_enable);
        $data['model']->sort_order = $request->old('sort_order', $data['model']->sort_order);

        $data['payment_vendor_list'] = PaymentVendor::asSelectArray();

        return view("pages.{$data['module_name']}.form", $data);
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @param PointsTopUpTier $model
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    protected function modelValidateAndSave(Request $request, PointsTopUpTier $model): RedirectResponse
    {
        $request->validate(self::rules($request, $model), [], self::attributes());

        if ((isSuperAdministrator() || auth()->user()->merchant_number_list->count() > 1) && blank($model?->merchant_number)) {
            // 如果是新增，并且是超级管理员或者管理员的商户大于1的情况下就直接获取页面传入的商户编号
            $model->merchant_number = $request->input('merchant_number');
        } else if (blank($model?->merchant_number) && auth()->user()->merchant_number_list->count() === 1) {
            // 如果是新增并且管理员商户只有一个就直接赋值
            $model->merchant_number = auth()->user()->merchant_number_list->first();
        }

        // 如果是新增并且商户有数据就把商户的编号赋值给商户编号
        if (filled($merchant = Merchant::firstWhere('merchant_number', $model?->merchant_number)) && blank($model?->merchant_number)) {
            $model->merchant_number = $merchant->merchant_number;
        }
        $model->payment_vendor = $request->input('payment_vendor');
        $model->price = $request->input('price');
        $model->points = $request->input('points');
        $model->price *= 100;
        $model->points *= 100;
        $model->is_enable = $request->input('is_enable', 0);
        $model->sort_order = $request->input('sort_order', 0);
        $model->save();

        return redirect()->action(
            [self::class, 'showPage'], self::getUrlParams($request)
        );
    }

    /**
     * 删除
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function delete(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        $id = $request->post('id');

        $model = PointsTopUpTier::when(!isSuperAdministrator(), fn($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))->firstWhere('points_top_up_tier_id', $id);
        if (empty($id) || blank($model)) {
            $this->notFoundData(__("$module_name.points_top_up_tier_id"));
            return $this->returnJson();
        }
        $model->delete();

        return $this->returnJson();
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function rules(?Request $request, ?Model $model): array
    {
        $module_name = self::$module_name;

        $merchant_number = $model->merchant_number;
        if ((isSuperAdministrator() || auth()->user()->merchant_number_list->count() > 1) && blank($model?->merchant_number)) {
            // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下就直接获取页面传入的场地编号
            $merchant_number = $request->input('merchant_number');
        } else if (blank($model?->merchant_number) && auth()->user()->merchant_number_list->count() === 1) {
            // 如果是新增并且管理员场地只有一个就直接赋值
            $merchant_number = auth()->user()->merchant_number_list->first();
        }

        $rules = array(
            'payment_vendor' => 'nullable|enum_value:' . PaymentVendor::class,
            'price' => 'required|numeric|min:0|max:999999|decimal:0,1',
            'points' => 'required|numeric|min:0|max:999999|decimal:0,1',
            'sort_order' => 'required|integer|min:0|max:999999',
            'is_enable' => 'boolean',
        );

        // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下才需要判断场地编号是否存在
        if ((isSuperAdministrator() || auth()->user()->merchant_number_list->count() > 1) && blank($model?->merchant_number)) {
            $rules['merchant_number'] = [
                'required',
                'exists:App\Models\Modules\Merchant,merchant_number',
                function ($attr, $value, $fail) use ($module_name, $request) {
                    // 判断选择的merchant是否为当前管理员权限下的
                    $merchant = Merchant::when(!isSuperAdministrator(), fn($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))->firstWhere('merchant_number', $value);
                    if (blank($merchant)) $fail(__('common.text_not_found', ['field' => __("$module_name.merchant")]));
                },
            ];
        }

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'merchant_number' => __("$module_name.merchant"),
            'payment_vendor' => __("$module_name.payment_vendor"),
            'price' => __("$module_name.price"),
            'points' => __("$module_name.points"),
            'is_enable' => __("$module_name.is_enable"),
            'sort_order' => __("$module_name.sort_order"),
        ];
    }

    /**
     * 返回地址栏参数
     *
     * @param Request|null $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => 'true',
            'merchant_search' => $request->get('merchant_search'),
        );
    }

    /**
     * 获取搜索数据
     *
     * @param Request $request
     * @return mixed
     * @Description
     * @example
     * @date 2022-11-08
     */
    protected function _searchList(Request $request): mixed
    {
        // dataTable字段
        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $merchant_search = $request->input('merchant_search');

        if ($order == 'merchant_name') $order = 'merchant.name_json';

        return PointsTopUpTier::query()
            ->select('points_top_up_tier.*', 'merchant.name_json as merchant_name_json_init')
            ->leftJoin('merchant', 'points_top_up_tier.merchant_number', 'merchant.merchant_number')
            ->when(filled($merchant_search), fn($query) => $query->where('points_top_up_tier.merchant_number', 'like', "%$merchant_search%"))
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('points_top_up_tier.merchant_number', auth()->user()->merchant_number_list))
            ->orderBy($order, $sort)
            ->latest('gmt_modified');
    }
}

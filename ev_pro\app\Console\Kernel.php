<?php

namespace App\Console;

use App\Services\ErpService;
use App\Enums\{
    ChargePointVendor,
    ChargeTariffScheme,
    ConnectorStatus,
    EventTypeEnum,
    OctopusDataExchangeOperationMode,
    OctopusDataExchangeType,
};
use App\Http\Controllers\Common\CommonController;
use App\Jobs\ScheduleSendEmailJob;
use App\Jobs\ScheduleSendSMSJob;
use App\Models\Modules\{
    ChargePoint,
    ChargeRecord,
    Connector,
    EmailSendRecord,
    ErpRecord,
    EventLog,
    MaintenanceRecord,
    OctopusDataExchangeProgramRecord,
    SmsSendRecord,
};
use App\Services\LogManager;
use Carbon\Carbon;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Console\Scheduling\Schedule;

use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Support\Facades\{Blade, Http, Log, Redis};

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //
    ];

    /**
     * Define the application's command schedule.
     *
     * @param \Illuminate\Console\Scheduling\Schedule $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // 检查存在闲置时间的充电枪
        $schedule->call(function () {
            if (env('IS_ENABLE_SEND_REMIND_IDLING_SMS', false) !== true) return;
            logger('检查闲置的充电枪任务启动开始');
            // 检查存在闲置时间的充电枪
            $this->checkHasIdlingTimeConnector();
            logger('检查闲置的充电枪任务启动结束');
        })
            ->everyMinute()
            ->name('check_has_idling_time_connector_schedule')
            ->withoutOverlapping(5);

        // 检查需要重发的邮件和短信记录
        $schedule->call(function () {
            // 获取当前时间
            $current_date = date('Y-m-d H:i:s');

            logger('检查邮件或短信记录任务启动开始：' . $current_date);

            // 查询可发送邮件记录
            $send_email_list = EmailSendRecord::whereNull('gmt_sent')->where('gmt_expiry', '>', $current_date)->get();
            // 查询可发送短信记录
            $send_sms_list = SmsSendRecord::whereNull('gmt_sent')->where('gmt_expiry', '>', $current_date)->get();

            // 设置发送邮件请求数组
            foreach ($send_email_list as $send_email_item) {
                logger('邮件可发送记录Id：' . $send_email_item);
                // 检查redis是否存在发送中的key
                if (
                    !Redis::exists('send_email_' . $send_email_item->email_send_record_id) && !empty($send_email_item->recipient) &&
                    !empty($send_email_item->message)
                ) {
                    Redis::set('send_email_' . $send_email_item->email_send_record_id, $send_email_item->email_send_record_id);

                    // 将发送邮件任务分发给队列
                    ScheduleSendEmailJob::dispatch($send_email_item);
                }
            }

            // 设置发送短信请求数组
            foreach ($send_sms_list as $send_sms_item) {
                logger('短信可发送记录Id：' . $send_sms_item);
                // 检查redis是否存在发送中的key
                if (
                    !Redis::exists('send_sms_' . $send_sms_item->sms_send_record_id) && !empty($send_sms_item->recipient) &&
                    !empty($send_sms_item->message)
                ) {
                    Redis::set('send_sms_' . $send_sms_item->sms_send_record_id, $send_sms_item->sms_send_record_id);

                    // 将发送短信任务分发给队列
                    ScheduleSendSMSJob::dispatch($send_sms_item);
                }
            }

            logger('检查邮件或短信记录任务启动结束：' . date('Y-m-d H:i:s'));
        })
            ->everyMinute()
            ->name('project_send_email_and_sms_schedule')
            ->withoutOverlapping(5);

        // Maintenance Schedules
        $schedule->call(function () {
            logger('Maintenance Schedules');
            // 查詢到時間了的維修記錄
            $current_time = now();
            $maintenance_record_list = MaintenanceRecord::where('gmt_start', '<=', $current_time)
                ->where('gmt_end', '>=', $current_time)
                ->where('is_bypass_mode', 1)
                ->get();
            // 查詢所有的Charge Point
            $charge_point_list = ChargePoint::with(['connector', 'chargePointCs'])
                ->where('vendor', ChargePointVendor::EVPowerCS)
                ->get();
            // 先處理有維修記錄的數據，返回無記錄的數據
            $charge_point_list = $this->setIsBypassModeWithMaintenanceRecord($maintenance_record_list, $charge_point_list);
            // 處理無維修記錄但是is_bypass_mode=1的數據
            $this->setIsBypassModeWithOutMaintenanceRecord($charge_point_list);
        })
            ->everyMinute()
            ->name('ev_pro_maintenance_schedule')
            ->withoutOverlapping(5);

        // octopus
        /* $schedule->call(function () {
            // 是否开启定时任务显示日志
            $is_enable_schedule_log = env('IS_ENABLE_SCHEDULE_LOG', false);

            $api_url = env('APP_URL') . '/octopusDataExchange/octopusUploadAndDownload';

            $client = new Client;

            try {
                $response = $client->request('POST', $api_url);
                if ($is_enable_schedule_log) {
                    Log::info('Octopus Schedule Response: ' . $response->getStatusCode());
                }
            } catch (ClientException $e) {
                if ($is_enable_schedule_log) {
                    Log::info('Octopus Schedule ClientException: ' . Psr7\Message::toString($e->getResponse()));
                }
            } catch (Exception $e) {
                if ($is_enable_schedule_log) {
                    Log::info('Octopus Schedule Exception: ' . $e->getMessage());
                }
            }
        })
            ->dailyAt('00:00'); */

        // 八达通数据交换: 下载
        $schedule->call(function () {
            logger('检查八达通定时下载任务');
            $this->startOctopusCrontab(OctopusDataExchangeType::Download);
        })
            ->name('octopus_data_exchange:download')
            ->everyTwoMinutes()
            ->withoutOverlapping();
        // 八达通数据交换: 上数
        $schedule->call(function () {
            logger('检查八达通定时上数任务');
            $this->startOctopusCrontab(OctopusDataExchangeType::Upload);
        })
            ->name('octopus_data_exchange:upload')
            ->everyTwoMinutes()
            ->withoutOverlapping();
        // 每日23:59:59,每种付款方式的每日交易记录摘要上传到 ERP 系统
        $schedule->call(function () {
            $this->uploadDailyTransactionRecordSchedule();
        })
            ->name('upload_transaction_record_schedule')
            ->everyMinute()
            ->withoutOverlapping()
            ->skip(function () {
                $erp_record_enable = env('ERP_RECORD_ENABLE', false);
                return !$erp_record_enable;
            });
        // HUAWEI IOC
        $schedule->call(function () {
            $this->huaweiIOC();
        })
            ->name('huawei_ioc_schedule')
            ->everyMinute()
            ->withoutOverlapping()
            ->skip(function () {
                $huawei_ioc_enable = env('HUAWEI_IOC_ENABLE', false);
                return !$huawei_ioc_enable;
            });
        /*需要根据配置檔中，定義時間，例如每30分鐘執行一次，則在每30分鐘內，只執行一次，避免重複執行
         * ->skip(
            function () {
                // 若Redis中存在huawei_ioc_next_runtime,且当前时间小于huawei_ioc_next_runtime，則返回true, 任务将不会执行
                return Redis::exists('huawei_ioc_next_runtime') && time() < Redis::get('huawei_ioc_next_runtime');
            }
        )*/
        // 每日凌晨1点执行日志压缩和清理
        $schedule->call(function () {
            $logPath = storage_path('logs');
            $retentionDays = env('LOG_RETENTION_DAYS', 30); // 日志保留天数
            $channelsToCompress = explode(',', env('LOG_CHANNELS')); // 需要压缩的日志通道
            $yesterday = Carbon::yesterday()->format('Y-m-d');

            // 使用服务容器实例化 LogManager
            $logManager = new LogManager($logPath, $channelsToCompress, $retentionDays);

            try {
                // 压缩指定通道的日志
                $logManager->compressLogs($yesterday);

                // 清理过期的日志文件
                $logManager->deleteOldLogs();
            } catch (\Exception $e) {
                // 如果发生异常，记录错误日志
                logger()->error('Error in log compression and cleanup task', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
            }
        })->dailyAt('01:00');
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }

    /**
     * 處理維修記錄關聯的Charge Point下的Connector，設置他們的is_bypass_mode
     * @param $maintenance_record_list
     * @param $charge_point_list
     * @return mixed
     */
    public function setIsBypassModeWithMaintenanceRecord($maintenance_record_list, $charge_point_list)
    {
        // 從維修記錄的site_id_list(,隔開)中提取所有的Site Id
        $site_id_list = [];
        foreach ($maintenance_record_list as $maintenance_record) {
            $site_id_list = array_merge($site_id_list, explode(',', $maintenance_record->site_id_list));
        }
        // 如果沒有Site Id，則不處理
        if (blank($site_id_list)) return $charge_point_list;
        // 篩選出Site下的Charge Point
        $maintenance_charge_point_list = $charge_point_list->whereIn('site_id', $site_id_list);

        // 獲取要處理的api_url_list
        $api_url_list = $this->getApiUrlList($maintenance_charge_point_list, 0);
        // Curl設置is_bypass_mode
        if (!empty($api_url_list)) $this->curlSetConnectorIsBypassMode($api_url_list, 1);
        // 去除charge_point_list中的maintenance_charge_point_list，返回沒有維修記錄的Charge Point
        return $charge_point_list->diff($maintenance_charge_point_list);
    }

    /**
     * 處理無維修記錄但是is_bypass_mode=1的數據
     *
     * @param [type] $charge_point_list
     * @return void
     * @Description
     * @example
     * @date 2023-09-13
     */
    public function setIsBypassModeWithOutMaintenanceRecord($charge_point_list)
    {
        // 獲取要處理的api_url_list
        $api_url_list = $this->getApiUrlList($charge_point_list, 1);
        // Curl設置is_bypass_mode
        if (!empty($api_url_list)) $this->curlSetConnectorIsBypassMode($api_url_list, 0);
    }

    /**
     * 獲取要處理的api_url_list
     *
     * @param [type] $charge_point_list
     * @param integer $is_bypass_mode
     * @return array
     * @Description
     * @example
     * @date 2023-09-13
     */
    public function getApiUrlList($charge_point_list, int $is_bypass_mode): array
    {
        // 查詢這些Charge Point下的Connector是否存在狀態非離線且is_bypass_mode對應的數據。如果有，獲取Charge Point的Charge Point Cs的api_url
        $api_url_list = [];
        foreach ($charge_point_list as $charge_point) {
            $maintenance_connector_count = $charge_point->connector()
                ->where('status', '!=', ConnectorStatus::Offline)
                ->where('is_bypass_mode', $is_bypass_mode)
                ->count();

            if (($maintenance_connector_count > 0) && filled($charge_point->chargePointCs->api_url) && filled($charge_point->chargePointCs->api_token)) {
                // 拿到Connector所屬的Charge Point的Charge Point Cs的api_url
                $api_url_list[$charge_point->chargePointCs->charge_point_cs_id] = [
                    'api_url' => $charge_point->chargePointCs->api_url,
                    'api_token' => $charge_point->chargePointCs->api_token,
                    'name' => $charge_point->chargePointCs->name,
                    'number' => $charge_point->chargePointCs->charge_point_cs_number,
                ];
            }
        }

        // 重置鍵名
        return array_values($api_url_list);
    }

    public function curlSetConnectorIsBypassMode($api_url_list, int $is_bypass_mode)
    {
        foreach ($api_url_list as $api_url) {
            try {
                $params = array(
                    'key' => $api_url['api_token'],
                    'mode' => $is_bypass_mode === 1 ? 'on' : 'off',
                );

                // 获取返回信息
                $response = CommonController::curlPostJson(
                    $api_url['api_url'] . '/command/setbypassmode',
                    json_encode($params),
                    array('Content-Type: application/json; charset=utf-8'),
                );

                $format_result = json_decode(htmlspecialchars_decode($response), true);

                // 失败时保存EventLog
                if (is_null($format_result) || (isset($format_result['status']) && $format_result['status'] != 200) || (isset($format_result['response']) && strtolower($format_result['response']) != 'success')) {
                    $event_log_model = new EventLog;
                    $event_log_model->target = 'CMS';
                    $event_log_model->target_number = $api_url['number'];
                    $event_log_model->target_name = $api_url['name'];
                    $event_log_model->event_type = EventTypeEnum::PostChargePointCSBypassFailure;
                    $event_log_model->description = 'CMS Change Bypass Mode Error: ' . $response;

                    CommonController::saveEventLogOrRules($event_log_model, true);
                }
            } catch (\Exception $e) {
                // 失败时保存EventLog
                $event_log_model = new EventLog;
                $event_log_model->target = 'CMS';
                $event_log_model->target_number = $api_url['number'];
                $event_log_model->target_name = $api_url['name'];
                $event_log_model->event_type = EventTypeEnum::PostChargePointCSBypassFailure;
                $event_log_model->description = 'CMS Change Bypass Mode Error: ' . $e->getMessage();

                CommonController::saveEventLogOrRules($event_log_model, true);
            }
        }
    }

    public function startOctopusCrontab($job_type)
    {
        // 定时任务的时间列表
        $execution_time = env("OCTOPUS_{$job_type}_TIME");
        // 如果没有配置时间，不执行直接返回
        if (blank($execution_time)) {
            return;
        }
        $execution_time_list = collect(explode(',', $execution_time)); // 转数组集合
        // 当前时间
        $current_time = time();
        // 上一个定时任务执行时间
        $last_execution_time = strtotime($execution_time_list->filter(function ($item) use ($current_time) {
            return strtotime(date('Y-m-d') . ' ' . $item) <= $current_time;
        })->last());

        // 判断上一个定时任务执行时间到现在是否有任务记录
        $is_exchange_record_exist =
            OctopusDataExchangeProgramRecord::where('octopus_data_exchange_type', $job_type)
                ->where('octopus_data_exchange_operation_mode', OctopusDataExchangeOperationMode::Auto)
                ->where('gmt_create', '>=', date('Y-m-d H:i:s', $last_execution_time))
                ->exists();

        // 如果没有任务记录，执行定时任务
        if (!$is_exchange_record_exist) {
            logger('开始八达通定时任务' . $job_type);
            // 执行下载
            $execute_log = $this->executeOctopusJob($job_type);
            if (blank($execute_log)) {
                $execute_log = 'No log';
            }
            // 保存记录
            $octopus_data_exchange_program_record = new OctopusDataExchangeProgramRecord;
            $octopus_data_exchange_program_record->octopus_data_exchange_type = $job_type;
            $octopus_data_exchange_program_record->octopus_data_exchange_operation_mode = OctopusDataExchangeOperationMode::Auto;
            $octopus_data_exchange_program_record->execute_log = $execute_log;
            $octopus_data_exchange_program_record->gmt_create = date('Y-m-d H:i:s', $current_time);
            $octopus_data_exchange_program_record->save();
            logger('结束八达通定时任务' . $job_type);
        }
    }

    /**
     * 执行八达通上数/下载脚本
     *
     * @param string $job_type download/upload
     * @return string
     * @Description
     * @example
     * @date 2024-05-23
     */
    public function executeOctopusJob(string $job_type): string
    {
        // 如果是win，需要使用调接口的方式实现
        if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
            return $this->executeOctopusJobWin($job_type);
        }
        // 以下是linux的实现方式
        $command_path = base_path('job/command/');
        $job_type = strtolower($job_type);
        $command = 'sh ' . $command_path . '/linux/' . $job_type . '.sh';

        $descriptorspec = array(
            0 => array("pipe", "r"),  // 标准输入，子进程从此处读取
            1 => array("pipe", "w"),  // 标准输出，子进程向此处写入
            2 => array("pipe", "w") // 标准错误，写入到一个文件
        );
        $process = proc_open($command, $descriptorspec, $pipes);

        if (is_resource($process)) {
            // $pipes 现在看起来是这样的：
            // 0 => 可以向子进程标准输入写入的句柄
            // 1 => 可以从子进程标准输出读取的句柄
            // 错误输出将被追加到文件 /tmp/error-output.txt

            // 等待子进程结束
            do {
                $status = proc_get_status($process);
                usleep(100000); // 等待100毫秒
            } while ($status['running']);

            // 读取子进程的输出和错误
            $output = stream_get_contents($pipes[1]);
            $error = stream_get_contents($pipes[2]);

            // 别忘了关闭所有的管道以避免死锁
            fclose($pipes[0]);
            fclose($pipes[1]);
            fclose($pipes[2]);

            // 最后，你必须调用 proc_close 以确保你不会造成僵尸进程。
            // 这个函数也会返回子进程的退出码
            $return_value = proc_close($process);

            // 返回执行日志
            return $output ?: $error;
        }
    }

    public function executeOctopusJobWin(string $job_type): string
    {
        // 请求路由名称为cronOctopus的接口，拼接上参数type=DOWNLOAD。如果请求失败，重复五次，每次间隔10秒
        retry(5, function () use ($job_type) {
            $response = Http::get(config('app.url') . '/octopusDataExchange/cronOctopus?type=' . $job_type);
            if ($response->status() !== 200 || $response->json()['code'] !== 200 || $response->json()['data'] !== true) {
                throw new Exception('Request failed');
            }
        }, 10000);
        $job_type = strtolower($job_type);
        // 循环每秒请求日志，去除最后的空行，判断日志最后一行是否是 octopus download bat execution completed 。如果是，返回日志内容。最长等待一个小时
        $log_file_path = base_path("job/command/win/$job_type.log");
        if (!file_exists($log_file_path)) {
            return '';
        }
        $completed_message = "octopus $job_type bat execution completed";

        for ($i = 0; $i < 3600; $i++) {
            $lines = file($log_file_path);
            if ($lines === false) {
                return '';
            }

            // 移除所有的空行
            $lines = array_filter($lines, 'trim');

            $last_line = end($lines);
            if (strpos($last_line, $completed_message) !== false) {
                break;
            }

            sleep(1);
        }

        // 无论是否超时，都返回日志内容，去除最后的空行和最后一行的文字 octopus $job_type bat execution completed
        $log = file_get_contents($log_file_path);
        $log = trim($log);
        $last_pos = strrpos($log, $completed_message);
        if ($last_pos !== false) {
            $log = substr($log, 0, $last_pos);
        }
        $log = trim($log);
        // log转为UTF8
        $log = iconv('gbk', 'utf-8', $log);

        return $log;
    }

    /**
     * 检查存在闲置时间的充电枪
     *
     * @return void
     * @throws GuzzleException
     */
    protected function checkHasIdlingTimeConnector(): void
    {
        // 获取管理员短信通知列表数据
        $telephone_list = array();
        $notification_list = getArrayFromJsonFile('admin_sms_notification') ?: array();
        foreach ($notification_list as $notification) {
            if (!isset($notification['telephone']) || blank($notification['telephone'])) continue;
            // 移除key，并将电话去除+和空格
            $telephone_list[] = str_replace([' ', '+'], '', $notification['telephone']);
        }
        // 不存在电话列表不处理
        if (blank($telephone_list)) return;

        // 查询状态为finishing且current_charge_record_number不为空的充电枪
        $connector_list = Connector::with('chargeRecord')
            ->where('status', ConnectorStatus::Finishing)
            ->has('chargeRecord')
            ->get();

        // 不存在存在闲置时间的充电枪不处理
        if (blank($connector_list)) return;

        // 闲置提醒时间，分钟数
        $idling_remind_time = env('SEND_REMIND_IDLING_TIME', 30);

        foreach ($connector_list as $connector) {
            // 未有充电记录跳过
            if (blank($connector->current_charge_record_number) || blank($connector->chargeRecord)) continue;
            // 获取当前闲置时间，秒数
            $idling_time = $this->getConnectorIdlingTime($connector->chargeRecord);
            // 需要大于指定时间才发送短信
            if ($idling_time < ($idling_remind_time * 60)) continue;
            // 闲置提醒发送短信内容
            $idling_remind_sms_message = "車位{$connector->name}，已佔用超時多於{$idling_remind_time}分鐘，請處理";
            // 检查该充电记录是否已发送闲置提醒。如果已发送，跳过
            $sms_send_record_exist = SmsSendRecord::where('charge_record_number', $connector->current_charge_record_number)
                ->where('message', 'like', "%$idling_remind_sms_message%")
                ->exists();
            if ($sms_send_record_exist) continue;
            foreach ($telephone_list as $telephone) {
                // 发送短信
                $send_result = CommonController::sendSMS($telephone, $idling_remind_sms_message);
                // 并创建发送记录
                SmsSendRecord::create([
                    'charge_record_number' => $connector->current_charge_record_number,
                    'recipient' => $telephone,
                    'message' => $idling_remind_sms_message,
                    'gmt_sent' => isset($send_result['code']) && $send_result['code'] == 200
                        ? date('Y-m-d H:i:s')
                        : null,
                    'gmt_expiry' => date('Y-m-d H:i:s', strtotime('+30 minute')),
                ]);
            }
        }
    }

    /**
     * 获取充电枪闲置时间
     *
     * @param ChargeRecord $charge_record
     * @return int
     */
    protected function getConnectorIdlingTime(ChargeRecord $charge_record): int
    {
        return match ($charge_record->charge_tariff_scheme) {
            ChargeTariffScheme::PrePaid => $this->getConnectorPrePaidIdlingTime($charge_record),
            ChargeTariffScheme::PostPaid => $this->getConnectorPostPaidIdlingTime($charge_record),
            default => 0,
        };
    }

    /**
     * 获取预付闲置时间
     *
     * @param ChargeRecord $charge_record
     * @return int
     */
    protected function getConnectorPrePaidIdlingTime(ChargeRecord $charge_record): int
    {
        $current_date = date('Y-m-d H:i');
        $start_date = date('Y-m-d H:i', strtotime($charge_record->gmt_power_on ?: ($charge_record->gmt_start ?: 0)));
        // 闲置开始日期 = 开始日期 + 预付充电量
        $idling_start_date =
            date('Y-m-d H:i', strtotime($start_date . ' + ' . ($charge_record->pre_paid_charge_value ?: 0) . ' seconds'));
        // 当前时间不超过闲置时间不显示
        if ($current_date <= $idling_start_date) return 0;
        // 闲置时间，当前时间减去闲置开始时间
        $idling_time = ceil((strtotime($current_date) - strtotime($idling_start_date)) / 60) * 60;
        return max($idling_time, 0);
    }

    /**
     * 获取后付闲置时间
     *
     * @param ChargeRecord $charge_record
     * @return int
     */
    protected function getConnectorPostPaidIdlingTime(ChargeRecord $charge_record): int
    {
        $current_date = date('Y-m-d H:i');
        $start_date = date('Y-m-d H:i', strtotime($charge_record->gmt_power_on ?: ($charge_record->gmt_start ?: 0)));
        // 闲置开始时间 = 开始时间 + 后付最大充电时间
        $idling_start_date =
            date('Y-m-d H:i', strtotime($start_date . ' + ' . ($charge_record->post_paid_maximum_charge_time ?: 0) . ' seconds'));
        // 当前时间不超过闲置时间不显示
        if ($current_date <= $idling_start_date) return 0;
        // 闲置时间，当前时间减去闲置开始时间
        $idling_time = floor((strtotime($current_date) - strtotime($idling_start_date)) / 60) * 60;
        return max($idling_time, 0);
    }

    /**
     * 判断日期/时间是否符合指定格式
     * @param $time //指定的日期/时间
     * @param $formats //格式数组
     * @return bool //true=通过验证
     */
    public function validTime($time, $formats): bool
    {
        foreach ($formats as $format) {
            $dateTime = \DateTime::createFromFormat($format, $time);
            if ($dateTime && $dateTime->format($format) === $time) {
                return true;
            }
        }
        return false;
    }

    /**
     * 上传每种付款方式的汇总金额数据任务
     * @return void
     */
    public function uploadDailyTransactionRecordSchedule(): void
    {
        // 每日交易记录上传
        $this->uploadDailyTransactionRecord();
        // 失敗數據重新請求
        $this->failedDataRetry();
    }

    /**
     * 每日交易记录上传
     * @return void
     */
    function uploadDailyTransactionRecord(): void
    {
        // venue
        $erp_venue = env('ERP_VENUE');
        // 上传时间
        $execution_time = env('ERP_UPLOAD_TRANSACTION_RECORD_TIME');
        // 请求链接
        $erpBaseUrl = env('ERP_BASE_URL');
        // 请求token client id
        $erpClientId = env('ERP_CLIENT_ID');
        // 请求token client secret
        $erpClientSecret = env('ERP_CLIENT_SECRET');
        // 场地编号
        $erp_site_code = env('ERP_SITE_CODE');
        // event code
        $erp_event_code = env('ERP_EVENT_CODE');
        // 时间格式
        $timeFormats = ['H:i:s', 'H:i'];
        // 如果没有配置时间 / 请求链接，不执行直接返回
        if (blank($execution_time) || blank($erpBaseUrl) || blank($erpClientId) || blank($erpClientSecret) || blank($erp_site_code) || !$this->validTime($execution_time, $timeFormats) || blank($erp_venue) || blank($erp_event_code)) {
            Log::channel('erpRecord')->info("upload_transaction_record: 配置参数错误
            ERP_UPLOAD_TRANSACTION_RECORD_TIME: $execution_time; ERP_BASE_URL: $erpBaseUrl; ERP_CLIENT_ID: $erpClientId; ERP_CLIENT_SECRET: $erpClientSecret; ERP_SITE_CODE: $erp_site_code; ERP_VENUE: $erp_venue; ERP_EVENT_CODE: $erp_event_code");
            return;
        }
        // 获取H:i
        $execution_time_check = substr($execution_time, 0, 5);
        // 获取当前时间戳(秒数)
        $now = time();
        // 当前时间小于设定的时间,直接return
        if (strtotime(date('Y-m-d' . ' ' . $execution_time_check)) > $now) {
            return;
        }
        // 开始时间
        $date = date('Y-m-d', $now);
        // 查询今日是否有记录
        $existJson = ErpRecord::whereDate('gmt_data', $date)
            ->orderBy('gmt_create', 'desc')
            ->first();
        // 當前時間小於下次重試請求時間跳過
        if ($existJson && date('Y-m-d H:i:s') < $existJson->gmt_next_retry_request) {
            return;
        }
        // 获取当天设定时间秒数
        $sleepEnd = strtotime('today ' . $execution_time);
        // 计算需要睡眠的秒数
        $sleepSeconds = $sleepEnd - $now;
        // erp 记录id
        $erpRecordId = 0;
        // json数据, 若使用已生成的json,需要decode
        $dataJson = array();
        // 异常信息
        $exception = null;
        try {
            if ($sleepSeconds > 0) {
                sleep($sleepSeconds);
            }
            $dailyTransactionData = $this->getDailyTransactionData($sleepEnd);
            $erpRecordId = $dailyTransactionData['erpRecordId'];
            $dataJson = $dailyTransactionData['dataJson'];
            $exception = $dailyTransactionData['exception'];
        } catch (Exception $e) {
            $exception .= $exception . "&&" . $e->getMessage();
        }
        $this->sendDataWithRetry($erpRecordId, $dataJson, $exception);
    }

    /**
     * 获取当日每种付款方式的总交易金额
     * @param $now
     * @return array
     * @throws Exception
     */
    function getDailyTransactionData($now): array
    {
        // 日期时间 生成指定日期时间的数据
        $dateTime = date('Y-m-d H:i:s', $now);
        // 异常信息
        $exception = null;
        // 初始化 $result 和 $payment 数组
        $dataJson = null;
        // 發送請求間隔
        $retryInterval = env('ERP_RETRY_INTERVAL', 28800);
        try {
            // 生成json数据
            $erpService = new ErpService();
            $dataJson = $erpService->getErpDataJson($now);
        } catch (Exception $e) {
            $exception = $e->getMessage();
        } finally {
            // 插入记录表, 组装dataJson过程中出现异常,dataJson必定为null
            // 保存记录
            $newErpRecord = ErpRecord::create([
                'gmt_data' => $dateTime,
                'data_json' => json_encode($dataJson, JSON_PRESERVE_ZERO_FRACTION),
                'exception' => $exception,
                'is_request_success' => 0,
                'gmt_next_retry_request' => date('Y-m-d H:i:s', $now + $retryInterval),
            ]);
        }
        Log::channel('erpRecord')->info("get_daily_transaction_data: 插入记录表数据成功-" . json_encode($newErpRecord));

        return [
            'erpRecordId' => $newErpRecord->erp_record_id,
            'dataJson' => $dataJson,
            'exception' => $exception
        ];
    }

    /**
     * 将每日付款方式汇总数据post到指定接口, 最大可重试3次
     * @param $postUrl
     * @param $erpRecordId
     * @param $dataJson
     * @param $exception
     * @return void
     */
    function sendDataWithRetry($erpRecordId, $dataJson, $exception): void
    {
        $erpService = new ErpService();
        // 发送请求返回结果
        $curlData = $erpService->sendDataWithRetry($dataJson, $exception);
        // 更新记录表
        ErpRecord::where('erp_record_id', $erpRecordId)
            ->update([
                'gmt_request' => $curlData['gmt_request'],
                'response_json' => $curlData['response_json'],
                'exception' => $curlData['exception'],
                'is_request_success' => $curlData['is_request_success'],
                'gmt_next_retry_request' => $curlData['gmt_next_retry_request'],
            ]);
    }

    /**
     * 失败数据重试
     * @return void
     */
    function failedDataRetry(): void
    {
        // 重试間隔
        $retryInterval = env('ERP_RETRY_INTERVAL');
        // 如果没有配置时间 / 请求链接，不执行直接返回
        if (blank($retryInterval)) {
            Log::channel('erpRecord')->info("erp_failed_data_retry_schedule: 配置参数错误. ERP_RETRY_INTERVAL: $retryInterval");
            return;
        }
        // 查询请求失败且请求时间小于今日的数据
        $failDataList = ErpRecord::where('is_request_success', 0)
            ->where(function ($query) {
                $query->where('gmt_next_retry_request', '<=', date('Y-m-d H:i:59'))
                    ->orWhereNull('gmt_next_retry_request');
            })
            ->orderby('gmt_next_retry_request', 'desc')
            ->get();
        // 没有数据直接返回
        if ($failDataList->isEmpty()) return;
        // 打印 SQL 查询
        Log::channel('erpRecord')->info('erp_failed_data_retry_schedule: 获取请求失败数据成功-' . json_encode($failDataList));
        foreach ($failDataList as $record) {
            $this->sendDataWithRetry($record->erp_record_id, json_decode($record->data_json ?? array()), null);
        }
    }

    /**
     *
     */
    function huaweiIOC(): void
    {
        // 读取环境变量
        $baseUrl = env('HUAWEI_IOC_BASE_URL');
        $hwId = env('HUAWEI_IOC_HW_ID');
        $hwAppKey = env('HUAWEI_IOC_APP_KEY');
        $hwSiteCode = env('HUAWEI_IOC_SITE_CODE');
        if (blank($baseUrl) || blank($hwId) || blank($hwAppKey)) {
            Log::channel('huaweiIOC')->info("HuaweiIOC-请求参数错误: baseUrl: $baseUrl, id:$hwId, hwAppKey: $hwAppKey, hwSiteCode: $hwSiteCode");
            return;
        }
        // 去除末尾/
        $baseUrl = rtrim($baseUrl, '/');
        // 请求url
        $endpoints = [
            'pushOverallChargeInformation' => '/io/io.ktspioc.EVCharger/north/0.1.0/overviewInfo/receive',
            'pushIndividualChargerInformation' => '/io/io.ktspioc.EVCharger/north/0.1.0/EVChargerInfo/receive',
            'pushVehicleFlowRecords' => '/io/io.ktspioc.EVCharger/north/0.1.0/flowRecord/receive',
            'heartbeat' => '/io/io.ktspioc/north/0.1.0/system/status/query'
        ];
        // 完整的请求url
        $urls = array_map(fn($path) => $baseUrl . $path, $endpoints);
        // 请求头
        $headers = [
            'Content-Type: application/json',   // 设置内容类型为application/json
            'X-HW-ID: ' . $hwId,
            'X-HW-APPKEY: ' . $hwAppKey
        ];

        $languageCode = 'en_US';
        // 获取充电枪数据,以及正在充电的充电记录
        $connectorList = Connector::with('chargeRecord:charge_record_number,charge_point_number,charged_energy,gmt_start,gmt_stop,pre_paid_charge_value')
            ->select(
                'connector.site_number',
                'connector.zone_number',
                'connector.parking_bay_number',
                'connector.charge_point_number',
                'connector.status',
                'connector.current_charge_record_number',
                'zone_description.name as zone_name'
            )
            ->leftJoin('zone_description', function ($join) use ($languageCode) {
                $join->on('connector.zone_number', '=', 'zone_description.zone_number')
                    ->where('zone_description.language_code', $languageCode);
            })
            ->where('connector.is_enable', 1)
            ->get();
        $delimiter = env('DELIMITER', '-');
        $responses = [
            'pushOverallChargeInformation' => $this->pushOverallChargeInformation($urls['pushOverallChargeInformation'], $headers, $connectorList, $hwSiteCode),
            'pushIndividualChargerInformation' => $this->pushIndividualChargerInformation($urls['pushIndividualChargerInformation'], $headers, $connectorList, $delimiter, $hwSiteCode),
            'pushVehicleFlowRecords' => $this->pushVehicleFlowRecords($urls['pushVehicleFlowRecords'], $headers, $connectorList, $delimiter, $hwSiteCode),
            'heartbeat' => $this->heartbeat($urls['heartbeat'], $headers)
        ];
        // 输出log
        foreach ($responses as $key => $response) {
            Log::channel('huaweiIOC')->info("HuaweiIOC-{$key}-response: " . $response);
        }
    }

    /**
     * 获取每个场地不同状态充电枪的数量
     * 1.AVAILABLE 2.PREPARING,CHARGING, FINISHING 3.Other
     * @param $url // 请求链接
     * @param $headers // 请求头
     * @param $connectorList //充电枪列表
     * @return string
     */
    function pushOverallChargeInformation($url, $headers, $connectorList, $cpkId): string
    {
        $nowDateTime = date('Y-m-d H:i:s');
        $huaweiIOCOverviewInfoRequestList = [];
        foreach ($connectorList as $connection) {
            $siteNumber = $connection->site_number;
            // 初始化数组项，使用 ?? 运算符
            $huaweiIOCOverviewInfoRequestList[$siteNumber] = $huaweiIOCOverviewInfoRequestList[$siteNumber] ?? [
                'cpkID' => $cpkId,
                'cpkName' => $cpkId,
                'availableChargerTotal' => 0,
                'occupiedChargerTotal' => 0,
                'damageChargerTotal' => 0,
                'timeStamp' => $nowDateTime
            ];
            // 根据状态,统计充电枪数量
            switch ($connection->status) {
                case ConnectorStatus::Available:
                    $huaweiIOCOverviewInfoRequestList[$siteNumber]['availableChargerTotal'] += 1;
                    break;
                case ConnectorStatus::Preparing:
                case ConnectorStatus::Charging:
                case ConnectorStatus::Finishing:
                    $huaweiIOCOverviewInfoRequestList[$siteNumber]['occupiedChargerTotal'] += 1;
                    break;
                default:
                    $huaweiIOCOverviewInfoRequestList[$siteNumber]['damageChargerTotal'] += 1;
                    break;
            }
        }
        $requestJson['body'] = array_values($huaweiIOCOverviewInfoRequestList);
        $requestData = json_encode($requestJson);
        Log::channel('huaweiIOC')->info('HuaweiIOC-pushOverallChargeInformation-Url:' . $url . PHP_EOL . ' requestData:' . $requestData . PHP_EOL . ' Request Headers' . json_encode($headers) . PHP_EOL);
        return CommonController::curlPostJson($url, $requestData, $headers);
    }

    /**
     * 获取充电枪的状态
     * 1.AVAILABLE->available 2.(PREPARING,CHARGING, FINISHING)->occupied 3.Other->damaged
     * @param $url // 请求链接
     * @param $headers // 请求头
     * @param $connectorList //充电枪列表
     * @param $delimiter
     * @return string
     */
    function pushIndividualChargerInformation($url, $headers, $connectorList, $delimiter, $cpkId): string
    {
        // 使用数组代替for中的switch,简化代码
        $statusMapping = [
            ConnectorStatus::Available => "available",
            ConnectorStatus::Preparing => "occupied",
            ConnectorStatus::Charging => "occupied",
            ConnectorStatus::Finishing => "occupied",
        ];
        $nowDateTime = date('Y-m-d H:i:s');
        $huaweiIOCEVChargerInfoRequestList = [];
        foreach ($connectorList as $connection) {
            $status = $statusMapping[$connection->status] ?? "damaged";
            $huaweiIOCEVChargerInfoRequestList[] = [
                'chargId' => $this->getLastStr($delimiter, $connection->charge_point_number),
                'cpkID' => $cpkId,
                'parkingSpotCode' => $connection->parking_bay_number,
                'zone' => $connection->zone_name,
                'chargeType' => 'MC',
                'status' => $status,
                'timeStamp' => $nowDateTime
            ];
        }
        $requestJson['body'] = array_values($huaweiIOCEVChargerInfoRequestList);
        $requestData = json_encode($requestJson);
        Log::channel('huaweiIOC')->info('HuaweiIOC-pushIndividualChargerInformation-Url:' . $url . PHP_EOL . ' requestData:' . $requestData . PHP_EOL . ' Request Headers:' . json_encode($headers) . PHP_EOL);
        return CommonController::curlPostJson($url, $requestData, $headers);
    }

    /**
     * 获取正在充电的充电记录的充电量, 充电开始时间, 充电结束时间
     * @param $url // 请求链接
     * @param $headers // 请求头
     * @param $connectorList //充电枪列表
     * @param $delimiter
     * @return string
     */
    function pushVehicleFlowRecords($url, $headers, $connectorList, $delimiter, $cpkId): string
    {
        $huaweiIOCFlowRecordRequestList = [];
        $nowDateTime = date('Y-m-d H:i:s');
        foreach ($connectorList as $connect) {
            $chargeRecord = $connect->chargeRecord;
            if (!$chargeRecord) continue;
            $chargedEnergy = (float)bcdiv($chargeRecord->charged_energy ?? 0, 1000, 3);
            // 充电开始时间
            $gmtStart = $chargeRecord->gmt_start;
            // 若gmt_stop为null,计算停止时间
            $prePaidChargeValue = $chargeRecord->pre_paid_charge_value ?? 0;
            $gmtStop = $chargeRecord->gmt_stop ?: ($gmtStart ? date('Y-m-d H:i:s', strtotime($gmtStart . '+' . $prePaidChargeValue . ' seconds')) : null);
            $chargeRecordId = $this->getLastStr($delimiter, $chargeRecord->charge_record_number);
            $chargerId = $this->getLastStr($delimiter, $chargeRecord->charge_point_number);
            $huaweiIOCFlowRecordRequestList[] = [
                'id' => $chargeRecordId,
                'cpkID' => $cpkId,
                'chargingElectricQuantity' => $chargedEnergy,
                'chargId' => $chargerId,
                'charAttribute' => 'MC',
                'entTime' => $gmtStart,
                'outTime' => $gmtStop,
                'timeStamp' => $nowDateTime
            ];
        }
        $requestJson['body'] = array_values($huaweiIOCFlowRecordRequestList);
        $requestData = json_encode($requestJson);
        Log::channel('huaweiIOC')->info('HuaweiIOC-pushVehicleFlowRecords-Url:' . $url . PHP_EOL . ' requestData:' . $requestData . PHP_EOL . ' Request Headers:' . json_encode($headers) . PHP_EOL);
        return CommonController::curlPostJson($url, $requestData, $headers);
    }

    /**
     * 发送get请求
     * @param $url
     * @param $headers
     * @return string
     */
    function heartbeat($url, $headers): string
    {
        // 设置内容类型为text/plain
        $headers['Content-Type'] = 'text/plain';
        Log::channel('huaweiIOC')->info('HuaweiIOC-heartbeat-Url:' . $url . PHP_EOL . ' Request Headers:' . json_encode($headers) . PHP_EOL);
        return CommonController::curlGetWithHeaders($url, $headers);
    }

    /**
     * 根据分隔符获取最后一个字符串
     * @param $delimiter
     * @param $str
     * @return string|null
     */
    function getLastStr($delimiter, $str): ?string
    {
        // 分隔符为空或者为空字符串，直接返回null
        if (empty($delimiter) || empty($str)) return null;
        $strArray = explode($delimiter, $str);
        return array_pop($strArray);
    }
}

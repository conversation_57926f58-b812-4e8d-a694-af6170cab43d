<?php

namespace App\Models\Modules;

use App\Enums\TariffTableType;
use Illuminate\Database\Eloquent\Model;
use App\Models\Traits\Emoji;

class IdlingPenaltyTariffTable extends Model
{
    use emoji;

    protected $table = 'idling_penalty_tariff_table'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'idling_penalty_tariff_table_id'; //定义主键，默认为id
    // 表的唯一number
    public static $table_number = 'idling_penalty_tariff_table_number';
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    // protected $fillable = [];

    /**
     * 类型转化器
     */
    protected $casts = [
        'idling_penalty_tariff_table_item_json' => 'array',
        'member_card_group_idling_penalty_tariff_table_item_json' => 'array',
        'user_group_idling_penalty_tariff_table_item_json' => 'array',
    ];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * 一对多关联IdlingPenaltyTariffTableItem
     */
    public function item()
    {
        return $this->hasMany(IdlingPenaltyTariffTableItem::class, 'idling_penalty_tariff_table_number', 'idling_penalty_tariff_table_number');
    }


    // deleting event
    public static function boot()
    {
        parent::boot();

        static::deleting(function ($model) {
            $model->item()->delete();
        });

        static::updating(function ($model) {
            $model->item()->delete();
        });
    }

    /**
     * 查找多个connector setting
     */
    public function connectorSetting()
    {
        return $this->hasMany(ConnectorSetting::class, 'idling_penalty_tariff_table_number', 'idling_penalty_tariff_table_number')
            ->whereIn('tariff_table_type', [TariffTableType::SimpleTariffTable, TariffTableType::ComplexEnergyTariffTable, TariffTableType::ComplexTimeTariffTable]);
    }
}

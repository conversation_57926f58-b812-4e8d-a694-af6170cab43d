<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Redis;
use Illuminate\Http\{
    JsonResponse,
    RedirectResponse,
};
use Illuminate\Http\Request;
use App\Models\Modules\{
    TimeTariffTable,
    MemberCardGroup,
    Site,
    UserGroup,
};
use App\Enums\{
    ChargeTariffScheme,
    IdentityType,
    DayType,
    RemainChargeValueGenerationTrigger,
};

class TimeTariffTableController extends CommonController
{
    protected ?TimeTariffTable $model = null;
    protected static string $module_name = 'timeTariffTable'; // 模块名称

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new TimeTariffTable;
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'add_url' => action([self::class, 'add']),
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'name_search' => $request->get('name_search'),
            'charge_tariff_scheme_search' => $request->get('charge_tariff_scheme_search'),
            'site_search' => $request->get('site_search'),
        );

        $data['charge_tariff_list'] = array();
        foreach (ChargeTariffScheme::asSelectArray() as $value => $name) {
            $data['charge_tariff_list'][] = array(
                'name' => $name,
                'value' => $value,
            );
        }

        // 场地下拉列表
        $data['site_list'] = $this->getSiteOptionList();

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $length = (int)$request->input('length', 10);
        $name_search = $request->input('name_search');
        $charge_tariff_scheme_search = $request->input('charge_tariff_scheme_search');
        $site_search = $request->input('site_search');

        if ($order == 'site_name') $order = 'site.name_json';

        $data_list = TimeTariffTable::select('time_tariff_table.*', 'site.name_json as site_name_json_init')
            ->leftJoin('site', 'time_tariff_table.site_number', '=', 'site.site_number')
            ->when(filled($name_search), fn ($query) => $query->where('time_tariff_table.name', 'like', "%$name_search%"))
            ->when(filled($charge_tariff_scheme_search), fn ($query) => $query->where('time_tariff_table.charge_tariff_scheme', $charge_tariff_scheme_search))
            ->when(filled($site_search), fn ($query) => $query->where('time_tariff_table.site_number', $site_search))
            ->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('time_tariff_table.site_number', auth()->user()->site_number_list))
            ->orderBy($order, $sort)
            ->latest('gmt_modified')
            ->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $post_paid_identity_type_list = $data->post_paid_identity_type_list ?? '';
            // 将，分隔的字符串转为数组
            $post_paid_identity_type_list = explode(',', $post_paid_identity_type_list);
            // 将数组中的元素转换为对应的描述
            $post_paid_identity_type_list = implode(',', array_map(fn ($item) => IdentityType::getDescription($item), $post_paid_identity_type_list));
            $result[] = array(
                'time_tariff_table_id' => $data->time_tariff_table_id, // 时间收费方案ID
                'time_tariff_table_number' => $data->time_tariff_table_number, // 时间收费方案编号
                'name' => $data->name, // 名称
                'site_name' => $this->getValueFromLanguageArray($data->site_name_json_init) ?? '—/—', // 場地名称
                'charge_tariff_scheme' => ChargeTariffScheme::getDescription($data->charge_tariff_scheme), // 收费方案
                'charge_value_interval' => ($data->charge_value_interval / 60) . __('common.unit_mins'), // 收费值间隔
                'pre_paid_charge_value_maximum_selection' => ($data->pre_paid_charge_value_maximum_selection / 60) . __('common.unit_mins'), // 预付充电量最大选择
                'post_paid_identity_type_list' => $post_paid_identity_type_list, // 后付身份类型
                'post_paid_maximum_charge_time' => ($data->post_paid_maximum_charge_time / 60) . __('common.unit_mins'), // 后付最大充电时间
                'is_enable_admin_octopus_card_free_deduct' => $data->is_enable_admin_octopus_card_free_deduct, // 是否开启管理员八达通卡免费扣款
                'is_enable_free_octopus_card' => $data->is_enable_free_octopus_card, // 是否启用免费八达通卡
                'is_enable_top_up' => $data->is_enable_top_up, // 是否启用续充
                'top_up_buffer_limit' => ($data->top_up_buffer_limit ?: 0) . __('common.unit_second'), // 续充缓冲限制 - 剩余充电量需大于此数才可续充
                'is_top_up_need_confirm_identity' => $data->is_top_up_need_confirm_identity, // 是否续充需要确认身份
                'remain_charge_value_generation_trigger' => RemainChargeValueGenerationTrigger::getDescription($data->remain_charge_value_generation_trigger), // 剩余充电量生成触发条件
                'remain_charge_value_validity_period' => (($data->remain_charge_value_validity_period ?: 0) / 60) . __('common.unit_mins'), // 剩余充电量有效期
                'remain_charge_value_minimum_limit' => (($data->remain_charge_value_minimum_limit ?: 0) / 60) . __('common.unit_mins'), // 剩余充电量最小限制
                'is_enable_use_remain_charge_value' => $data->is_enable_use_remain_charge_value, // 是否允许使用剩余充电量
                'is_enable_charge_value_adjust_selected_base_on_remain' => $data->is_enable_charge_value_adjust_selected_base_on_remain, // 是否启用根据剩余充电量调整已选充电量
                'remark' => $data->remark ?? '—/—', // 备注
                'sort_order' => $data->sort_order, // 排序
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );

        return response()->json($json);
    }

    public function add(Request $request): View|Factory|Application|RedirectResponse
    {
        $data = array();

        $model = $this->model;

        // 因为post提交表单和get链接都会传入收费表编号参数，所以用下划线标识该编号是copyToAdd携带的
        if (filled($time_tariff_table_number = $request->input('_time_tariff_table_number'))) {
            $model = $model->with('item.itemSpecial')
                ->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->where('time_tariff_table_number', $time_tariff_table_number)
                ->firstOrFail();

            // 排序规则映射
            $sort_map = [
                null => 0,
                DayType::Monday => 1,
                DayType::Tuesday => 2,
                DayType::Wednesday => 3,
                DayType::Thursday => 4,
                DayType::Friday => 5,
                DayType::Saturday => 6,
                DayType::Sunday => 7,
                DayType::PublicHoliday => 8,
            ];
            $time_tariff_table_item_list = $model->item
                ->sortBy(fn ($item) => $sort_map[$item->day_type] ?? 9);
            // 默认/会员卡组/用户组
            $default_item = [];
            $member_card_group_item_list = [];
            $user_group_item_list = [];
            foreach ($time_tariff_table_item_list as $index => $item) {
                $item_special_list = $item->itemSpecial->sortBy('start_time');
                // 整理数据格式和结构给前端使用
                foreach ($item_special_list as &$item_special) {
                    $item_special['start_time'] = self::convertTo24HourFormat($item_special['start_time']);
                    $item_special['end_time'] = self::convertTo24HourFormat($item_special['end_time']);
                    $item_special['normal_rate'] = isset($item_special['normal_rate']) ? (float)bcdiv($item_special['normal_rate'], 100, 1) : 0;
                    $item_special['concessionary_rate'] = isset($item_special['concessionary_rate']) ? (float)bcdiv($item_special['concessionary_rate'], 100, 1) : 0;
                }
                // 当前处理的数据数组
                unset($current_item_list); // 因为是引用变量，此处用unset删除变量
                $current_item_list = null; // 初始化当前处理数据
                if (blank($item->member_card_group_id) && blank($item->user_group_id)) {
                    // 默认
                    $current_item_list = &$default_item;
                } elseif (filled($item->member_card_group_id)) {
                    // 会员卡组
                    $current_item_list = &$member_card_group_item_list[$index];
                } elseif (filled($item->user_group_id)) {
                    // 用户组
                    $current_item_list = &$user_group_item_list[$index];
                }
                if (blank($current_item_list)) {
                    $current_item_list = [
                        'data' => [],
                        'member_card_group_id' => $item->member_card_group_id ?? null,
                        'user_group_id' => $item->user_group_id ?? null,
                        'member_card_group_name' => $this->getValueFromLanguageArray(MemberCardGroup::find($item->member_card_group_id)?->name_json) ?? '—/—',
                        'user_group_name' => $this->getValueFromLanguageArray(UserGroup::find($item->user_group_id)?->name_json) ?? '—/—',
                    ];
                }
                $current_item_list['data'][$item->day_type ?: 'DEFAULT'] = [
                    'day_type' => $item->day_type,
                    'default_normal_rate' => isset($item->default_normal_rate) ? (float)bcdiv($item->default_normal_rate, 100, 1) : 0,
                    'default_concessionary_rate' => isset($item->default_concessionary_rate) ? (float)bcdiv($item->default_concessionary_rate, 100, 1) : 0,
                    'item_special_list' => $item_special_list,
                ];

            }
            $data['default_item'] = $default_item;
            $data['member_card_group_item_list'] = array_values($member_card_group_item_list);
            $data['user_group_item_list'] = array_values($user_group_item_list);

            $model->time_tariff_table_id = $model->time_tariff_table_number = $model->name = null;
            // 秒轉為分鐘
            $model->charge_value_interval = $model->charge_value_interval ? $model->charge_value_interval / 60 : $model->charge_value_interval;
            $model->pre_paid_charge_value_maximum_selection = $model->pre_paid_charge_value_maximum_selection ? $model->pre_paid_charge_value_maximum_selection / 60 : $model->pre_paid_charge_value_maximum_selection;
            $model->remain_charge_value_minimum_limit = $model->remain_charge_value_minimum_limit ? $model->remain_charge_value_minimum_limit / 60 : $model->remain_charge_value_minimum_limit;
            $model->post_paid_maximum_charge_time = $model->post_paid_maximum_charge_time ? $model->post_paid_maximum_charge_time / 60 : $model->post_paid_maximum_charge_time;
        } else {
            $data['default_item'] = [
                'data' => [
                    'DEFAULT' => [
                        'day_type' => null,
                        'default_normal_rate' => 0,
                        'default_concessionary_rate' => 0,
                        'member_card_group_id' => null,
                        'user_group_id' => null,
                        'item_special_list' => []
                    ]
                ],
                'member_card_group_id' => null,
                'user_group_id' => null,
                'member_card_group_name' => '—/—',
                'user_group_name' => '—/—',
            ];
            $data['member_card_group_item_list'] = [];
            $data['user_group_item_list'] = [];
            $model->charge_value_interval = 15;
        }

        if ($request->isMethod('post')) {
            return $this->modelValidateAndSave($request, $model);
        }

        $data['model'] = $model;

        return $this->getForm($request, $data);
    }

    public function edit(Request $request, $time_tariff_table_number): Factory|Application|View|RedirectResponse
    {
        $module_name = self::$module_name;
        $data = array();

        $model = TimeTariffTable::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->where('time_tariff_table_number', $time_tariff_table_number)
            ->firstOrFail();
        if ($request->isMethod('post')) {
            return $this->modelValidateAndSave($request, $model);
        }

        // 秒轉為分鐘
        $model->charge_value_interval = $model->charge_value_interval ? $model->charge_value_interval / 60 : $model->charge_value_interval;
        $model->pre_paid_charge_value_maximum_selection = $model->pre_paid_charge_value_maximum_selection ? $model->pre_paid_charge_value_maximum_selection / 60 : $model->pre_paid_charge_value_maximum_selection;
        $model->remain_charge_value_minimum_limit = $model->remain_charge_value_minimum_limit ? $model->remain_charge_value_minimum_limit / 60 : $model->remain_charge_value_minimum_limit;
        $model->post_paid_maximum_charge_time = $model->post_paid_maximum_charge_time ? $model->post_paid_maximum_charge_time / 60 : $model->post_paid_maximum_charge_time;

        $model->load('item.itemSpecial');
        // 排序规则映射
        $sort_map = [
            null => 0,
            DayType::Monday => 1,
            DayType::Tuesday => 2,
            DayType::Wednesday => 3,
            DayType::Thursday => 4,
            DayType::Friday => 5,
            DayType::Saturday => 6,
            DayType::Sunday => 7,
            DayType::PublicHoliday => 8,
        ];

        $time_tariff_table_item_list = $model->item
                ->sortBy(fn ($item) => $sort_map[$item->day_type] ?? 9);
        // 默认/会员卡组/用户组
        $default_item = [];
        $member_card_group_item_list = [];
        $user_group_item_list = [];
        foreach ($time_tariff_table_item_list as $index => $item) {
            $item_special_list = $item->itemSpecial->sortBy('start_time');
            // 整理数据格式和结构给前端使用
            foreach ($item_special_list as &$item_special) {
                $item_special['start_time'] = self::convertTo24HourFormat($item_special['start_time']);
                $item_special['end_time'] = self::convertTo24HourFormat($item_special['end_time']);
                $item_special['normal_rate'] = isset($item_special['normal_rate']) ? (float)bcdiv($item_special['normal_rate'], 100, 1) : 0;
                $item_special['concessionary_rate'] = isset($item_special['concessionary_rate']) ? (float)bcdiv($item_special['concessionary_rate'], 100, 1) : 0;
            }
            // 当前处理的数据数组
            unset($current_item_list); // 因为是引用变量，此处用unset删除变量
            $current_item_list = null; // 初始化当前处理数据
            if (blank($item->member_card_group_id) && blank($item->user_group_id)) {
                // 默认
                $current_item_list = &$default_item;
            } elseif (filled($item->member_card_group_id)) {
                // 会员卡组
                $current_item_list = &$member_card_group_item_list[$item->member_card_group_id];
            } elseif (filled($item->user_group_id)) {
                // 用户组
                $current_item_list = &$user_group_item_list[$item->user_group_id];
            }
            if (blank($current_item_list)) {
                $current_item_list = [
                    'data' => [],
                    'member_card_group_id' => $item->member_card_group_id ?? null,
                    'user_group_id' => $item->user_group_id ?? null,
                    'member_card_group_name' => $this->getValueFromLanguageArray(MemberCardGroup::find($item->member_card_group_id)?->name_json) ?? '—/—',
                    'user_group_name' => $this->getValueFromLanguageArray(UserGroup::find($item->user_group_id)?->name_json) ?? '—/—',
                ];
            }
            $current_item_list['data'][$item->day_type ?: 'DEFAULT'] = [
                'day_type' => $item->day_type,
                'default_normal_rate' => isset($item->default_normal_rate) ? (float)bcdiv($item->default_normal_rate, 100, 1) : 0,
                'default_concessionary_rate' => isset($item->default_concessionary_rate) ? (float)bcdiv($item->default_concessionary_rate, 100, 1) : 0,
                'item_special_list' => $item_special_list,
            ];

        }
        $data['default_item'] = $default_item;
        $data['member_card_group_item_list'] = array_values($member_card_group_item_list);
        $data['user_group_item_list'] = array_values($user_group_item_list);

        $data['model'] = $model;

        return $this->getForm($request, $data);
    }

    protected function getForm(Request $request, array $data): View|Application|Factory
    {
        $module_name = $data['module_name'] = self::$module_name;
        $data['memberCardGroupNotCheckDataUrl'] = action([ModalController::class, 'memberCardGroupNotCheckCheckbox']);
        $data['userGroupNotCheckDataUrl'] = action([ModalController::class, 'userGroupNotCheckCheckbox']);
        $data['cancel_url'] = action(
            [self::class, 'showPage'],
            self::getUrlParams($request)
        );
        $data['checkDataUrl'] = action([self::class, 'checkData']);

        $data['day_type_list'] = [
            'DEFAULT' => __("$module_name.default_day"),
            DayType::Monday => DayType::getDescription(DayType::Monday),
            DayType::Tuesday => DayType::getDescription(DayType::Tuesday),
            DayType::Wednesday => DayType::getDescription(DayType::Wednesday),
            DayType::Thursday => DayType::getDescription(DayType::Thursday),
            DayType::Friday => DayType::getDescription(DayType::Friday),
            DayType::Saturday => DayType::getDescription(DayType::Saturday),
            DayType::Sunday => DayType::getDescription(DayType::Sunday),
            DayType::PublicHoliday => DayType::getDescription(DayType::PublicHoliday),
        ];

        $data['model']->name = $request->old('name', $data['model']->name);
        // 新增时才回显收费表编号和场地编号
        if (blank($data['model']->time_tariff_table_id)) {
            $data['model']->time_tariff_table_number = $request->old('time_tariff_table_number', $data['model']->time_tariff_table_number); // 时间收费表编号
            // 超级管理员或者多场地能选场地，否则只取所属的单场地
            $site_number = isSuperAdministrator() || auth()->user()->site_number_list->count() > 1
                ? $request->old('site_number', $data['model']->site_number)
                : (auth()->user()->site_number_list->first() ?? null);
            $site_name = $this->getValueFromLanguageArray(Site::firstWhere('site_number', $site_number)?->name_json);
            $data['model']->site_number = $site_number; // 场地编号
            $data['model']->site_name = $site_name; // 场地名称
        }
        $data['model']->charge_tariff_scheme = $request->old('charge_tariff_scheme', $data['model']->charge_tariff_scheme);
        $data['model']->charge_value_interval = $request->old('charge_value_interval', $data['model']->charge_value_interval);
        $data['model']->pre_paid_charge_value_maximum_selection = $request->old('pre_paid_charge_value_maximum_selection', $data['model']->pre_paid_charge_value_maximum_selection);
        $data['model']->post_paid_identity_type_list = $request->old('post_paid_identity_type_list', explode(',', $data['model']->post_paid_identity_type_list ?? ''));
        $data['model']->post_paid_maximum_charge_time = $request->old('post_paid_maximum_charge_time', $data['model']->post_paid_maximum_charge_time);
        $data['model']->is_enable_admin_octopus_card_free_deduct = $request->old('is_enable_admin_octopus_card_free_deduct', $data['model']->is_enable_admin_octopus_card_free_deduct);
        $data['model']->is_enable_free_octopus_card = $request->old('is_enable_free_octopus_card', $data['model']->is_enable_free_octopus_card);

        $data['model']->is_enable_top_up = $request->old('is_enable_top_up', $data['model']->is_enable_top_up);
        $data['model']->top_up_buffer_limit = $request->old('top_up_buffer_limit', $data['model']->top_up_buffer_limit);
        $data['model']->is_top_up_need_confirm_identity = $request->old('is_top_up_need_confirm_identity', $data['model']->is_top_up_need_confirm_identity);
        $data['model']->remain_charge_value_generation_trigger = $request->old('remain_charge_value_generation_trigger', $data['model']->remain_charge_value_generation_trigger); // 剩余充电量生成触发器
        $data['model']->remain_charge_value_validity_period = $request->old('remain_charge_value_validity_period', $data['model']->remain_charge_value_validity_period / 60);
        $data['model']->remain_charge_value_minimum_limit = $request->old('remain_charge_value_minimum_limit', $data['model']->remain_charge_value_minimum_limit);
        $data['model']->is_enable_use_remain_charge_value = $request->old('is_enable_use_remain_charge_value', $data['model']->is_enable_use_remain_charge_value);
        $data['model']->is_enable_charge_value_adjust_selected_base_on_remain = $request->old('is_enable_charge_value_adjust_selected_base_on_remain', $data['model']->is_enable_charge_value_adjust_selected_base_on_remain);

        $data['model']->remark = $request->old('remark', $data['model']->remark);
        $data['model']->sort_order = $request->old('sort_order', $data['model']->sort_order);

        // 默认
        if ($request->old('default_item')) {
            $data['default_item'] = [];
            $default_item = $request->old('default_item');
            $data['default_item'] = $this->generateItemList($data['day_type_list'], $default_item);
        }
        // 会员卡组
        if ($request->old('member_card_group_item_list')) {
            $data['member_card_group_item_list'] = [];
            $member_card_group_item_list = $request->old('member_card_group_item_list');
            foreach ($member_card_group_item_list as $index => $member_card_group_item) {
                $data['member_card_group_item_list'][$index] = $this->generateItemList($data['day_type_list'], $member_card_group_item);
                $data['member_card_group_item_list'][$index]['member_card_group_id'] = $member_card_group_item['member_card_group_id'] ?? null;
                $data['member_card_group_item_list'][$index]['member_card_group_name'] = $member_card_group_item['member_card_group_name'] ?? '—/—';
            }
        }
        // 用户组
        if ($request->old('user_group_item_list')) {
            $data['user_group_item_list'] = [];
            $user_group_item_list = $request->old('user_group_item_list');
            dump(json_encode($user_group_item_list));
            foreach ($user_group_item_list as $index => $user_group_item) {
                $data['user_group_item_list'][$index] = $this->generateItemList($data['day_type_list'], $user_group_item);
                $data['user_group_item_list'][$index]['user_group_id'] = $user_group_item['user_group_id'] ?? null;
                $data['user_group_item_list'][$index]['user_group_name'] = $user_group_item['user_group_name'] ?? '—/—';
            }
        }


        $data['charge_tariff_scheme_list'] = array();
        foreach (ChargeTariffScheme::asSelectArray() as $value => $name) {
            $data['charge_tariff_scheme_list'][] = array(
                'name' => $name,
                'value' => $value,
            );
        }

        $data['identity_type_list'] = array();
        foreach (IdentityType::asSelectArray() as $value => $name) {
            $data['identity_type_list'][] = array(
                'name' => $name,
                'value' => $value,
            );
        }

        $module_name = self::$module_name;
        $data['day_type_enum'] = $data['day_description'] = array();
        $data['day_description']['DEFAULT'] = __("$module_name.default_day");

        // 用于copyToOther保证foreach顺序先从SUNDAY开始获取数据
        foreach (DayType::asSelectArray() as $value => $name) {
            $data['day_type_enum'][$value] = $name;
            $data['day_description'][$value] = $name;
        }

        return view("pages.{$data['module_name']}.form", $data);
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @param TimeTariffTable $model
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    protected function modelValidateAndSave(Request $request, TimeTariffTable $model): RedirectResponse
    {
        // 判断是否是新增
        if (blank($model->time_tariff_table_id)) {
            $model = $this->model;
        }
        $request->validate(self::rules($request, $model), [], self::attributes());
        // 公共方法定义，因为需要先刪除子表数据，防止sql执行错误时可以回滚
        deleteItemAndSaveModel(function () use ($request, $model) {
            // 防止修改编号导致原本的无法被刪除，所以现在最上面关联刪除
            $model->item()->each(function ($item) {
                $item->itemSpecial()->delete();
            });
            $model->item()->delete();

            // 新增时才保存收费表、场地和商户编号
            if (blank($model->time_tariff_table_id)) {
                $model->time_tariff_table_number = $request->input('time_tariff_table_number');
                $model->site_number = $request->input('site_number');
                if (filled($model->site_number) && filled($site = Site::firstWhere('site_number', $model->site_number))) {
                    $model->merchant_number = $site->merchant_number;
                }
            }

            $charge_tariff_scheme = $request->input('charge_tariff_scheme');
            $is_post_paid = $charge_tariff_scheme == ChargeTariffScheme::PostPaid;
            $is_pre_paid = $charge_tariff_scheme == ChargeTariffScheme::PrePaid;
            $model->name = $request->input('name'); // 名称
            $model->charge_tariff_scheme = $charge_tariff_scheme; // 收费方案
            $model->is_enable_admin_octopus_card_free_deduct = $request->input('is_enable_admin_octopus_card_free_deduct', 0); // 是否开启管理员八达通卡免费扣款
            $model->is_enable_free_octopus_card = $request->input('is_enable_free_octopus_card', 0); // 是否启用免费八达通卡
            $model->sort_order = $request->input('sort_order', 0);
            $model->remark = $request->input('remark', 0);
            $charge_value_interval = $request->input('charge_value_interval', 15);

            $model->post_paid_identity_type_list = $is_post_paid ? $request->input('post_paid_identity_type_list', array()) : [];
            // 将数组转换为字符串
            $model->post_paid_identity_type_list = implode(',', $model->post_paid_identity_type_list);
            $model->post_paid_maximum_charge_time = $is_post_paid ? $request->input('post_paid_maximum_charge_time', 0) : 0; // 后付最大充电时间

            $model->pre_paid_charge_value_maximum_selection = $is_pre_paid ? $request->input('pre_paid_charge_value_maximum_selection') : 0; // 预付充电量最大选择
            $model->is_enable_top_up = $is_pre_paid ? $request->input('is_enable_top_up', 0) : 0;
            $model->remain_charge_value_generation_trigger = $is_pre_paid ? $request->input('remain_charge_value_generation_trigger') : null;
            $model->remain_charge_value_validity_period = $is_pre_paid ? $request->input('remain_charge_value_validity_period') : null;
            $model->remain_charge_value_minimum_limit = $is_pre_paid ? $request->input('remain_charge_value_minimum_limit') : null;
            $model->is_enable_use_remain_charge_value = $is_pre_paid ? $request->input('is_enable_use_remain_charge_value', 0) : 0;
            $model->is_enable_charge_value_adjust_selected_base_on_remain = $is_pre_paid ? $request->input('is_enable_charge_value_adjust_selected_base_on_remain', 1) : 0;
            $model->top_up_buffer_limit = ($is_pre_paid && $model->is_enable_top_up == true) ? $request->input('top_up_buffer_limit') : null;
            $model->is_top_up_need_confirm_identity = ($is_pre_paid && $model->is_enable_top_up == true) ? $request->input('is_top_up_need_confirm_identity', 0) : 0;
            // 获取item列表
            $default_item_list = $request->input('default_item', array());
            $member_card_group_item_list = $request->input('member_card_group_item_list', array());
            $user_group_item_list = $request->input('user_group_item_list', array());
            $item_list = array_merge([$default_item_list], $member_card_group_item_list, $user_group_item_list);


            $charge_value_interval *= 60; // 将间隔时间(min)转为秒(s)
            $model->charge_value_interval = $charge_value_interval; // 充电量间隔
            $model->post_paid_maximum_charge_time *= 60;
            $model->pre_paid_charge_value_maximum_selection *= 60;
            $model->remain_charge_value_validity_period = $model->remain_charge_value_validity_period ? $model->remain_charge_value_validity_period * 60 : $model->remain_charge_value_validity_period;
            $model->remain_charge_value_minimum_limit = $model->remain_charge_value_minimum_limit ? $model->remain_charge_value_minimum_limit * 60 : $model->remain_charge_value_minimum_limit;

            // 处理item
            $item_data_list = [];
            date_default_timezone_set('GMT');
            foreach ($item_list as $item_group) {
                foreach ($item_group['data'] as $key => $value) {
                    $item_special_list = [];
                    if (isset($value['item_special_list'])) {
                        foreach ($value['item_special_list'] as $item_special) {
                            $start = explode(':', $item_special['start_time']);
                            $start_time = $start[0] * 3600 + $start[1] * 60; // 将开始时间 H:i 转为秒
                            $end = explode(':', $item_special['end_time']);
                            $end_time = $end[0] * 3600 + $end[1] * 60; // 将结束时间 H:i 转为秒
                            $item_special_list[] = [
                                'start_time' => $start_time,
                                'end_time' => $end_time,
                                'normal_rate' => (int)bcmul($item_special['normal_rate'], 100),
                                'concessionary_rate' => (int)bcmul($item_special['concessionary_rate'], 100),
                            ];
                        }
                    }
                    $item_data_list[] = [
                        'day_type' => DayType::hasValue($key) ? $key : null,
                        'default_normal_rate' => (int)bcmul($value['default_normal_rate'], 100),
                        'default_concessionary_rate' => (int)bcmul($value['default_concessionary_rate'], 100),
                        'member_card_group_id' => $item_group['member_card_group_id'] ?? null,
                        'user_group_id' => $item_group['user_group_id'] ?? null,
                        'time_tariff_table_item_special_list' => $item_special_list
                    ];
                }
            }

            date_default_timezone_set(config('app.timezone'));
            // 排序规则映射
            $sort_map = [
                null => 0,
                DayType::Monday => 1,
                DayType::Tuesday => 2,
                DayType::Wednesday => 3,
                DayType::Thursday => 4,
                DayType::Friday => 5,
                DayType::Saturday => 6,
                DayType::Sunday => 7,
                DayType::PublicHoliday => 8,
            ];
            // 默认
            $model->time_tariff_table_item_json = array_values(collect($item_data_list)
                ->whereNull('member_card_group_id')
                ->whereNull('user_group_id')
                ->sortBy(fn ($item) => $sort_map[$item['day_type']])
                ->values()
                ->toArray());
            // 会员卡组
            $model->member_card_group_time_tariff_table_item_json = array_values(collect($item_data_list)
                ->whereNotNull('member_card_group_id')
                ->whereNull('user_group_id')
                ->sortBy(fn ($item) => $sort_map[$item['day_type']])
                ->sortBy('member_card_group_id')
                ->values()
                ->toArray());
            // 用户组
            $model->user_group_time_tariff_table_item_json = array_values(collect($item_data_list)
                ->whereNotNull('user_group_id')
                ->whereNull('member_card_group_id')
                ->sortBy(fn ($item) => $sort_map[$item['day_type']])
                ->sortBy('user_group_id')
                ->values()
                ->toArray());
            // 保存收费表模型
            $model->save();
            // 保存item及item_special模型
            $save_item_and_special_data = function ($item_list) use (&$model) {
                $model->item()->createMany($item_list)->each(function ($item) use ($item_list) {
                    $day_type_item_list = collect($item_list)
                        ->where('day_type', $item->day_type)
                        ->first();
                    $item_special_data = $day_type_item_list['time_tariff_table_item_special_list'];
                    if (isset($item_special_data) && filled($item_special_data)) {
                        foreach ($item_special_data as &$item_special) {
                            $item_special['time_tariff_table_item_number'] = $item->time_tariff_table_item_number;
                        }
                        $item->itemSpecial()->createMany($item_special_data);
                    }
                });
            };
            // 保存默认收费表item及item_special模型
            $save_item_and_special_data($model->time_tariff_table_item_json);
            // 保存会员卡组收费表item及item_special模型
            $save_item_and_special_data($model->member_card_group_time_tariff_table_item_json);
            // 保存用户组收费表item及item_special模型
            $save_item_and_special_data($model->user_group_time_tariff_table_item_json);
        });
        self::delRedis($model->time_tariff_table_number);
        self::setConnectorTokenByTariffTable($model);
        self::sendInitPushByKioskNumberList();

        return redirect()->action(
            [self::class, 'showPage'],
            self::getUrlParams($request)
        );
    }

    /**
     * 格式化时间
     */
    protected static function convertTo24HourFormat($number)
    {
        $hours = floor($number / 3600);
        $minutes = floor(($number % 3600) / 60);
        return sprintf('%02d:%02d', $hours, $minutes);
    }

    /**
     * 获取数据库中数据进行合并输出
     * @param Request $request
     * @return JsonResponse
     */
    public function checkData(Request $request): JsonResponse
    {
        $time_tariff_table_number = $request->input('time_tariff_table_number');

        if (
            filled($time_tariff_table_number) &&
            filled($model = TimeTariffTable::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->find($time_tariff_table_number))
        ) {
            $time_tariff_table_item_special_list = $data = $data['item'] = array();

            foreach ($model->item()
                ->oldest('day_type')
                ->oldest('start_time')
                ->get() as $item) {
                // 两个收费字段的值不与默认收费值相等
                if (((float)bcdiv($item->normal_rate, 100, 1) != (float)$model->time_tariff_table_item_rule_json[$item->day_type]['default_normal_rate']) || ((float)bcdiv($item->concessionary_rate, 100, 1) != (float)$model->time_tariff_table_item_rule_json[$item->day_type]['default_concessionary_rate'])) {
                    $time_tariff_table_item_special_list[$item->day_type][] = array(
                        'day_type' => $item->day_type,
                        'start_time' => $item->start_time,
                        'end_time' => $item->end_time,
                        'normal_rate' => (float)bcdiv($item->normal_rate, 100, 1),
                        'concessionary_rate' => (float)bcdiv($item->concessionary_rate, 100, 1),
                    );
                }
            }

            date_default_timezone_set('GMT');
            foreach (DayType::asArray() as $value) {
                $data['item'][$value] = array(
                    'default_normal_rate' => isset($model->time_tariff_table_item_rule_json[$value]['default_normal_rate']) ? (float)bcdiv($model->time_tariff_table_item_rule_json[$value]['default_normal_rate'], 1, 1) : null,
                    'default_concessionary_rate' => isset($model->time_tariff_table_item_rule_json[$value]['default_concessionary_rate']) ? (float)bcdiv($model->time_tariff_table_item_rule_json[$value]['default_concessionary_rate'], 1, 1) : null,
                    'time_tariff_table_item_special_list' => array()
                );

                $start_time = $end_time = $normal_rate = $concessionary_rate = null;

                if (isset($time_tariff_table_item_special_list[$value])) {
                    foreach ($time_tariff_table_item_special_list[$value] as $rule) {
                        if (
                            $start_time !== null &&
                            ($end_time != $rule['start_time'] ||
                                $rule['normal_rate'] != $normal_rate ||
                                $rule['concessionary_rate'] != $concessionary_rate)
                        ) {
                            /* start_time不为null(即已开始记录)，
                            且正常收费或优惠收费与默认值不相等，
                            则结束记录，并立即开始一条新纪录 */
                            $data['item'][$value]['time_tariff_table_item_special_list'][] = array(
                                'start_time' => date('H:i', $start_time),
                                'end_time' => date('H:i', $end_time),
                                'normal_rate' => $normal_rate,
                                'concessionary_rate' => $concessionary_rate,
                            );
                            $normal_rate = $rule['normal_rate'];
                            $concessionary_rate = $rule['concessionary_rate'];
                            $start_time = $rule['start_time'];
                        } elseif ($start_time === null) {
                            /* start_time为null，开始记录 */
                            $normal_rate = $rule['normal_rate'];
                            $concessionary_rate = $rule['concessionary_rate'];
                            $start_time = $rule['start_time'];
                        }
                        $end_time = $rule['end_time'];
                    }

                    // 循环结束后还需手动记录下最后一条数据
                    $data['item'][$value]['time_tariff_table_item_special_list'][] = array(
                        'start_time' => date('H:i', $start_time),
                        'end_time' => date('H:i', $end_time),
                        'normal_rate' => $normal_rate,
                        'concessionary_rate' => $concessionary_rate,
                    );
                }
            }
            date_default_timezone_set(config('app.timezone'));

            $this->data = $data['item'];
        }

        return $this->returnJson();
    }


    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function rules(?Request $request, ?Model $model): array
    {
        $charge_value_interval = $request->input('charge_value_interval', 15);
        $charge_value_interval *= 60; // 将间隔时间(min)转为秒(s)
        $module_name = self::$module_name;
        $rules = array(
            'name' => 'required|max:45',
            'charge_tariff_scheme' => 'required|string|max:20',
            'charge_value_interval' => 'required|integer|min:1|max:999999',
            'pre_paid_charge_value_maximum_selection' => 'exclude_unless:charge_tariff_scheme,' . ChargeTariffScheme::PrePaid . '|nullable|integer|min:1|max:999999|multiple_of:' . $request->charge_value_interval,
            'post_paid_maximum_charge_time' => 'exclude_unless:charge_tariff_scheme,' . ChargeTariffScheme::PostPaid . '|nullable|integer|min:1|max:999999',
            'is_enable_admin_octopus_card_free_deduct' => 'bool',
            'is_enable_free_octopus_card' => 'bool',
            'post_paid_identity_type_list' => 'exclude_unless:charge_tariff_scheme,' . ChargeTariffScheme::PostPaid . '|required|array',
            'is_enable_top_up' => 'exclude_unless:charge_tariff_scheme,' . ChargeTariffScheme::PrePaid . '|bool',
            'top_up_buffer_limit' => 'exclude_unless:charge_tariff_scheme,' . ChargeTariffScheme::PrePaid . '|exclude_unless:is_enable_top_up,"1"|required|integer|min:0|max:999999',
            'is_top_up_need_confirm_identity' => 'exclude_unless:charge_tariff_scheme,' . ChargeTariffScheme::PrePaid . '|exclude_unless:is_enable_top_up,"1"|bool',
            'remain_charge_value_generation_trigger' => 'enum_value:' . RemainChargeValueGenerationTrigger::class,
            'remain_charge_value_validity_period' => 'required_with:remain_charge_value_generation_trigger|nullable|integer|max:999999',
            'remain_charge_value_minimum_limit' => 'required_with:remain_charge_value_generation_trigger|nullable|integer|max:999999',
            'is_enable_use_remain_charge_value' => 'exclude_unless:charge_tariff_scheme,' . ChargeTariffScheme::PrePaid . '|bool',
            'is_enable_charge_value_adjust_selected_base_on_remain' => 'exclude_unless:charge_tariff_scheme,' . ChargeTariffScheme::PrePaid . '|bool',
            'remark' => 'nullable|string',
            'sort_order' => 'integer|min:0',
        );

        // 只有新增时才校验收费表和场地编号
        if (blank($model->time_tariff_table_id)) {
            $rules['time_tariff_table_number'] = [
                'required',
                'max:30',
                'regex:/^[a-zA-Z0-9]+$/', // 只能填写字母和数字
                'unique:App\Models\Modules\TimeTariffTable,time_tariff_table_number',
            ];
            if (isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) {
                $rules['site_number'] = [
                    'required',
                    'exists:App\Models\Modules\Site,site_number',
                    function ($attr, $value, $fail) use ($request, $model, $module_name) {
                        // 新增时校验非超级管理员且未拥有该场地提示错误
                        if (!isSuperAdministrator() && !in_array($value, auth()->user()->site_number_list->toArray())) {
                            $fail(__('common.text_not_found', ['field' => __("$module_name.site")]));
                        }
                    },
                ];
            }
        }

        // 费率规则
        $rate_rule = function ($attribute, $value, $fail) use ($module_name) {
            if (!preg_match("/^\d+(\.\d{0,1})?$/", $value)) {
                $message = __("$module_name.text_most_one_decimal", [
                    'rate' => $value,
                ]);
                $fail($message);
            }
        };
        // 会员卡组规则
        $member_card_group_rule = function ($attribute, $value, $fail) use ($module_name, $request) {
            // 判断选择的Member Card Group是否为当前场地下的
            $member_card_group = MemberCardGroup::when(
                !isSuperAdministrator(),
                fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list)
            )
            ->where('site_number', $request->input('site_number'))
            ->where('member_card_group_id', $value)
            ->first();
            if (blank($member_card_group)) $fail(__("$module_name.no_such_member_card_group"));
        };
        // 用户组规则
        $user_group_rule = function ($attribute, $value, $fail) use ($module_name, $request) {
            // 判断选择的User Group是否为当前商户下的
            $site = Site::firstWhere('site_number', $request->input('site_number'));
            $user_group = UserGroup::when(
                !isSuperAdministrator(),
                fn ($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list)
            )
            ->where('merchant_number', $site?->merchant_number)
            ->where('user_group_id', $value)
            ->first();
            if (blank($user_group)) $fail(__("$module_name.no_such_user_group"));
        };
        // 收费表item_special规则
        $item_special_rule = function ($attribute, $value, $fail) use ($module_name, $charge_value_interval) {
            date_default_timezone_set('GMT');
            $array = array();
            foreach ($value as $v) {
                // 时间转换成秒
                $start = explode(':', $v['start_time']);
                $end = explode(':', $v['end_time']);
                $start_time = $start[0] * 3600 + $start[1] * 60;
                $end_time = $end[0] * 3600 + $end[1] * 60;
                // 更新进新列表
                $array[] = array(
                    'start_s' => $start_time,
                    'end_s' => $end_time
                );
            }
            foreach ($array as $k => $v) {
                // 判断是否有效
                if ((($v['end_s'] - $v['start_s']) < 0) || ($v['start_s'] % $charge_value_interval != 0 && $v['start_s'] != 0) || ($v['end_s'] % $charge_value_interval != 0 && $v['end_s'] != 0)) {
                    $message = __("$module_name.validate_time_valid", [
                        'start_time' => date('H:i', $v['start_s']),
                        'end_time' => date('H:i', $v['end_s'])
                    ]);
                    $fail($message);
                    return;
                }
                foreach ($array as $kk => $vv) {
                    if ($kk != $k) {
                        if (self::isTimeCross($v['start_s'], $v['end_s'], $vv['start_s'], $vv['end_s'])) {
                            $message = __("$module_name.validate_time_repetition", [
                                'start_time' => date('H:i', $v['start_s']),
                                'end_time' => date('H:i', $v['end_s']),
                                'start_time2' => date('H:i', $vv['start_s']),
                                'end_time2' => date('H:i', $vv['end_s'])
                            ]);
                            $fail($message);
                            return;
                        }
                    }
                }
            }
            date_default_timezone_set(config('app.timezone'));
        };
        // 费率规则数组
        $rate_rule_array = ['required', 'numeric', 'min:0', 'max:999999', $rate_rule];
        // 默认收费表规则
        $rules['default_item'] = [
            'required',
            'array'
        ];
        $rules['default_item.data.DEFAULT'] = 'required';
        $rules['default_item.data.*.default_normal_rate'] = $rate_rule_array;
        $rules['default_item.data.*.default_concessionary_rate'] = $rate_rule_array;
        $rules['default_item.data.*.item_special_list'] = [
            'nullable',
            'array',
            $item_special_rule
        ];
        $rules['default_item.data.*.item_special_list.*.start_time'] = 'required';
        $rules['default_item.data.*.item_special_list.*.end_time'] = 'required';
        $rules['default_item.data.*.item_special_list.*.normal_rate'] = $rate_rule_array;
        $rules['default_item.data.*.item_special_list.*.concessionary_rate'] = $rate_rule_array;

        // 会员卡组规则
        $rules['member_card_group_item_list'] = ['nullable', 'array'];
        $rules['member_card_group_item_list.*.data.DEFAULT'] = 'required';
        $rules['member_card_group_item_list.*.data.*.default_normal_rate'] = $rate_rule_array;
        $rules['member_card_group_item_list.*.data.*.default_concessionary_rate'] = $rate_rule_array;
        $rules['member_card_group_item_list.*.data.*.item_special_list'] = [
            'nullable',
            'array',
            $item_special_rule
        ];
        $rules['member_card_group_item_list.*.data.*.item_special_list.*.start_time'] = 'required';
        $rules['member_card_group_item_list.*.data.*.item_special_list.*.end_time'] = 'required';
        $rules['member_card_group_item_list.*.data.*.item_special_list.*.normal_rate'] = $rate_rule_array;
        $rules['member_card_group_item_list.*.data.*.item_special_list.*.concessionary_rate'] = $rate_rule_array;
        $rules['member_card_group_item_list.*.member_card_group_id'] = [
            'nullable',
            'exists:App\Models\Modules\MemberCardGroup,member_card_group_id',
            $member_card_group_rule,
        ];

        // 用户组规则
        $rules['user_group_item_list'] = ['nullable', 'array'];
        $rules['user_group_item_list.*.data.DEFAULT'] = 'required';
        $rules['user_group_item_list.*.data.*.default_normal_rate'] = $rate_rule_array;
        $rules['user_group_item_list.*.data.*.default_concessionary_rate'] = $rate_rule_array;
        $rules['user_group_item_list.*.data.*.item_special_list'] = [
            'nullable',
            'array',
            $item_special_rule
        ];
        $rules['user_group_item_list.*.data.*.item_special_list.*.start_time'] = 'required';
        $rules['user_group_item_list.*.data.*.item_special_list.*.end_time'] = 'required';
        $rules['user_group_item_list.*.data.*.item_special_list.*.normal_rate'] = $rate_rule_array;
        $rules['user_group_item_list.*.data.*.item_special_list.*.concessionary_rate'] = $rate_rule_array;
        $rules['user_group_item_list.*.user_group_id'] = [
            'nullable',
            'exists:App\Models\Modules\UserGroup,user_group_id',
            $user_group_rule,
        ];
        $rules['user_group_item_list.*.data.*.item_special_list'] = [
            'nullable',
            'array',
            $item_special_rule
        ];

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'time_tariff_table_number' => __("$module_name.time_tariff_table_number"),
            'site_number' => __("$module_name.site"),
            'name' => __("$module_name.name"),
            'charge_tariff_scheme' => __("$module_name.charge_tariff_scheme"),
            'charge_value_interval' => __("$module_name.charge_value_interval"),
            'pre_paid_charge_value_maximum_selection' => __("$module_name.pre_paid_charge_value_maximum_selection"),
            'post_paid_maximum_charge_time' => __("$module_name.post_paid_maximum_charge_time"),
            'post_paid_identity_type_list' => __("$module_name.post_paid_identity_type_list"),
            'is_enable_top_up' => __("$module_name.is_enable_top_up"),
            'is_enable_admin_octopus_card_free_deduct' => __("$module_name.is_enable_admin_octopus_card_free_deduct"),
            'is_enable_free_octopus_card' => __("$module_name.is_enable_free_octopus_card"),
            'top_up_buffer_limit' => __("$module_name.top_up_buffer_limit"),
            'is_top_up_need_confirm_identity' => __("$module_name.is_top_up_need_confirm_identity"),
            'remain_charge_value_generation_trigger' => __("$module_name.remain_charge_value_generation_trigger"),
            'remain_charge_value_validity_period' => __("$module_name.remain_charge_value_validity_period"),
            'remain_charge_value_minimum_limit' => __("$module_name.remain_charge_value_minimum_limit"),
            'is_enable_use_remain_charge_value' => __("$module_name.is_enable_use_remain_charge_value"),
            'is_enable_charge_value_adjust_selected_base_on_remain' => __("$module_name.is_enable_charge_value_adjust_selected_base_on_remain"),
            'sort_order' => __("$module_name.sort_order"),
            'remark' => __("$module_name.remark"),
            'default_item.data.*.default_normal_rate' => __("$module_name.default_normal_rate"),
            'default_item.data.*.default_concessionary_rate' => __("$module_name.default_concessionary_rate"),
            'default_item.data.*.item_special_list.*.start_time' => __("$module_name.start_time"),
            'default_item.data.*.item_special_list.*.end_time' => __("$module_name.end_time"),
            'default_item.data.*.item_special_list.*.normal_rate' => __("$module_name.normal_rate"),
            'default_item.data.*.item_special_list.*.concessionary_rate' => __("$module_name.concessionary_rate"),
            'member_card_group_item_list.*.member_card_group_id' => __("$module_name.member_card_group"),
            'member_card_group_item_list.*.data.*.default_normal_rate' => __("$module_name.default_normal_rate"),
            'member_card_group_item_list.*.data.*.default_concessionary_rate' => __("$module_name.default_concessionary_rate"),
            'member_card_group_item_list.*.data.*.item_special_list.*.start_time' => __("$module_name.start_time"),
            'member_card_group_item_list.*.data.*.item_special_list.*.end_time' => __("$module_name.end_time"),
            'member_card_group_item_list.*.data.*.item_special_list.*.normal_rate' => __("$module_name.normal_rate"),
            'member_card_group_item_list.*.data.*.item_special_list.*.concessionary_rate' => __("$module_name.concessionary_rate"),
            'user_group_item_list.*.user_group_id' => __("$module_name.user_group"),
            'user_group_item_list.*.data.*.default_normal_rate' => __("$module_name.default_normal_rate"),
            'user_group_item_list.*.data.*.default_concessionary_rate' => __("$module_name.default_concessionary_rate"),
            'user_group_item_list.*.data.*.item_special_list.*.start_time' => __("$module_name.start_time"),
            'user_group_item_list.*.data.*.item_special_list.*.end_time' => __("$module_name.end_time"),
            'user_group_item_list.*.data.*.item_special_list.*.normal_rate' => __("$module_name.normal_rate"),
            'user_group_item_list.*.data.*.item_special_list.*.concessionary_rate' => __("$module_name.concessionary_rate"),
        ];
    }

    /**
     * 返回地址栏参数
     *
     * @param Request $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(Request $request): array
    {
        return array(
            'is_page' => 'true',
            'name_search' => $request->get('name_search'),
            'charge_tariff_scheme_search' => $request->get('charge_tariff_scheme_search'),
            'site_search' => $request->get('site_search'),
        );
    }

    /**
     * PHP计算两个时间段是否有交集（边界重叠不算）
     *
     * @param string $beginTime1 开始时间1
     * @param string $endTime1 结束时间1
     * @param string $beginTime2 开始时间2
     * @param string $endTime2 结束时间2
     * @return bool
     */
    protected static function isTimeCross(string $beginTime1 = '', string $endTime1 = '', string $beginTime2 = '', string $endTime2 = ''): bool
    {
        $status = $beginTime2 - $beginTime1;
        if ($status > 0) {
            $status2 = $beginTime2 - $endTime1;
            if ($status2 >= 0) {
                return false;
            } else {
                return true;
            }
        } else {
            $status2 = $endTime2 - $beginTime1;
            if ($status2 > 0) {
                return true;
            } else {
                return false;
            }
        }
    }

    public function delete(Request $request): JsonResponse
    {
        $time_tariff_table_number = $request->input('time_tariff_table_number');
        $module_name = self::$module_name;

        $time_tariff_table = TimeTariffTable::with('connectorSetting')
            ->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->firstWhere('time_tariff_table_number', $time_tariff_table_number);
        if (filled($time_tariff_table_number) && filled($time_tariff_table)) {
            if (blank($connector_setting_list = $time_tariff_table->connectorSetting)) {
                // 先删除关联表数据
                $time_tariff_table->item()->each(function ($item) {
                    $item->itemSpecial()->delete();
                });

                $time_tariff_table->item()->delete();
                $time_tariff_table->delete();
                self::delRedis($time_tariff_table_number);
                self::sendInitPushByKioskNumberList();
            } else {
                $connector_setting_str = '';
                foreach ($connector_setting_list as $connector_setting) {
                    $connector_setting_str .= '<li>' . $connector_setting->name . '</li>';
                }
                $this->code = 201;
                $this->message = __('common.error_has_binding_unable_to_delete_tariff_table', [
                    'connector_setting' => $connector_setting_str
                ]);
            }
        } else {
            $this->notFoundData(__("$module_name.web_title"));
        }

        return $this->returnJson();
    }

    public static function delRedis($time_tariff_table_number): void
    {
        foreach (['defaultFee', 'memberCardGroupFee', 'userGroupFee'] as $type) {
            foreach (config('languages') as $language_code => $language) {
                Redis::del("timeTariffTable:$time_tariff_table_number:$type:$language_code");
            }
            Redis::del("timeTariffTable:$time_tariff_table_number:$type:en_US_zh_HK");
        }
    }

    protected function generateItemList(array $day_type_list, $form_item)
    {
        $item = [
            'data' => [],
        ];
        foreach ($day_type_list as $day_type => $day_type_description) {
            if (!isset($form_item['data'][$day_type])) {
                continue;
            }
            $item['data'][$day_type] = [
                'day_type' => $day_type,
                'default_normal_rate' => $form_item['data'][$day_type]['default_normal_rate'] ?? 0,
                'default_concessionary_rate' => $form_item['data'][$day_type]['default_concessionary_rate'] ?? 0,
            ];
            if (isset($form_item['data'][$day_type]['item_special_list']) && filled($form_item['data'][$day_type]['item_special_list'])) {
                foreach ($form_item['data'][$day_type]['item_special_list'] as $item_special) {
                    $item['data'][$day_type]['item_special_list'][] = [
                        'start_time' => $item_special['start_time'] ?? null,
                        'end_time' => $item_special['end_time'] ?? null,
                        'normal_rate' => $item_special['normal_rate'] ?? null,
                        'concessionary_rate' => $item_special['concessionary_rate'] ?? null,
                    ];
                }
            }
        }
        return $item;
    }
}

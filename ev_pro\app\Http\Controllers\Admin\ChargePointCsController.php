<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\{
    JsonResponse,
    RedirectResponse,
    Request,
};
use App\Http\Controllers\Admin\Traits\{
    Add,
    Edit,
    Delete,
};
use Illuminate\Foundation\Application;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use App\Models\Modules\{
    ChargePointCs,
    Site,
    ChargePoint,
};


class ChargePointCsController extends CommonController
{
    use Edit;

    protected static string $module_name = 'chargePointCs'; // 模块名称
    protected static bool $module_check_site = true; // 标记该模块校验场地

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new ChargePointCs;
    }

    /**
     * 初始页
     *
     * @param Request $request
     * @return View|Application|Factory
     */
    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'add_url' => action([self::class, 'add']),
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'site_search' => $request->get('site_search'),
            'name_search' => $request->get('name_search'),
            'site_list' => $this->getSiteOptionList(),
        );

        return view("pages.{$data['module_name']}.list", $data);
    }

    /**
     * 列表接口
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'sort_order');
        $sort = $request->input('sort', 'asc');
        $length = (int)$request->input('length', 10);
        $site_search = $request->input('site_search');
        $name_search = $request->input('name_search');

        $where = array();

        if ($order == 'site_name') $order = 'site.name_json';

        $data_list = ChargePointCs::select('charge_point_cs.*', 'site.name_json as site_name_json_init')
            ->leftJoin('site', 'charge_point_cs.site_number', '=', 'site.site_number')
            ->when(filled($site_search), fn($query) => $query->where('charge_point_cs.site_number', $site_search))
            ->when(filled($name_search), fn ($query) => $query->where('charge_point_cs.name', 'like', "%$name_search%"))
            ->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('charge_point_cs.site_number', auth()->user()->site_number_list))
            ->orderBy($order, $sort)
            ->latest('gmt_modified')
            ->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $result[] = array(
                'charge_point_cs_id' => $data->charge_point_cs_id, // 充电机中央服务器ID
                'charge_point_cs_number' => $data->charge_point_cs_number, // 充电机中央服务器编码
                'site_name' => $this->getValueFromLanguageArray($data->site_name_json_init) ?? '—/—', // 场地名称
                'name' => $data->name, // 名称
                'vendor' => $data->vendor, // 供应商
                'api_url' => $data->api_url, // api url
                'ip_address' => $data->ip_address ?? '—/—', // IP地址
                'gmt_last_boot' => $data->gmt_last_boot ?? '—/—', // 最后启动时间
                'gmt_last_alive' => $data->gmt_last_alive ?? '—/—', // 最后活跃时间
                'remark' => $data->remark ?? '—/—', // 备注
                'sort_order' => $data->sort_order, // 排序
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result
        );
        return response()->json($json);
    }

    /**
     * 最后启动时间和最后活跃时间没有写在表单页面
     *
     * @param Request $request
     * @param array $data
     * @return View|Application|Factory
     */
    protected function getForm(Request $request, array $data): View|Application|Factory
    {
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'],
            self::getUrlParams($request)
        );
        if (blank($data['model']->charge_point_cs_id)) $data['model']->charge_point_cs_number = $request->old('charge_point_cs_number', $data['model']->charge_point_cs_number); // 充电机中央服务器编号
        $site_number = $data['model']->site_number;
        if ((isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) && blank($data['model']?->charge_point_cs_id)) {
            // 如果是新增，并且是超级管理员或者管理员的商户大于1的情况下就直接获取页面传入的商户编号
            $site_number = $request->old('site_number', $data['model']->site_number);
        } else if (blank($data['model']?->charge_point_cs_id) && auth()->user()->site_number_list->count() === 1) {
            // 如果是新增并且管理员商户只有一个就直接赋值
            $site_number = auth()->user()->site_number_list->first();
        }

        $data['model']->site_number = $site_number; // 场地编号
        $site_name = $this->getValueFromLanguageArray(
            Site::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->firstWhere('site_number', $site_number)?->name_json
        );
        $data['model']->site_name = $site_name; // 场地名称
        $data['model']->name = $request->old('name', $data['model']->name); // 名称
        $data['model']->vendor = $request->old('vendor', $data['model']->vendor); // 供应商
        $data['model']->api_url = $request->old('api_url', $data['model']->api_url); // api url
        $data['model']->remark = $request->old('remark', $data['model']->remark); // 备注
        $data['model']->sort_order = $request->old('sort_order', $data['model']->sort_order); // 排序

        return view("pages.{$data['module_name']}.form", $data);
    }

    /**
     * 添加
     *
     * @param Request $request
     * @return Factory|Application|View|RedirectResponse
     */
    public function add(Request $request): Factory|Application|View|RedirectResponse
    {
        $data = array();

        $model = $this->model;

        // 如果有charge_point_cs_number的存在就是复制除了charge_point_cs_number和name以外的收费表数据输出到新增页面
        if (filled($charge_point_cs_number = $request->input('_charge_point_cs_number'))) {
            $model = $model->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->where('charge_point_cs_number', $charge_point_cs_number)->firstOrFail();
            $model->charge_point_cs_id = $model->name = $model->charge_point_cs_number = null;
        }

        if ($request->isMethod('post')) {
            return $this->modelValidateAndSave($request, $model);
        }

        $data['model'] = $model;
        return $this->getForm($request, $data);
    }

    /**
     * 删除
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function delete(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        $charge_point_cs_number = $request->input('charge_point_cs_number');

        if (!empty($charge_point_cs_number)) {
            $model = $this->model::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('charge_point_cs_number', $charge_point_cs_number);
            if (filled($model)) {
                if (($model->chargePoint?->count() ?? 0) === 0) {
                    $model->delete();
                    self::sendInitPushByKioskNumberList();
                } else {
                    $this->code = 40001;
                    $this->message = __("$module_name.error_charge_point_cs_mismatching");
                }
            } else {
                $this->notFoundData(__("$module_name.charge_point_cs_number"));
            }
        } else {
            $this->notFoundData(__("$module_name.charge_point_cs_number"));
        }

        return $this->returnJson();
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @param ChargePointCs $model
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    protected function modelValidateAndSave(Request $request, ChargePointCs $model): RedirectResponse
    {
        $request->validate(self::rules($request, $model), [], self::attributes());
        // 判断是否是新增还是编辑
        if (blank($model->charge_point_cs_id)) {
            $model = $this->model;
            $model->charge_point_cs_number = $request->input('charge_point_cs_number');
            $site_number = $model->site_number;

            if (isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) {
                // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下就直接获取页面传入的场地编号
                $site_number = $request->input('site_number');
            } else if (auth()->user()->site_number_list->count() === 1) {
                // 如果是新增并且管理员场地只有一个就直接赋值
                $site_number = auth()->user()->site_number_list->first();
            }
            $model->site_number = $site_number;
            $model->merchant_number = Site::firstWhere('site_number', $model->site_number)?->merchant_number;
        }
        $model->name = $request->input('name');
        $model->vendor = $request->input('vendor');
        $model->api_url = $request->input('api_url');
        $model->sort_order = $request->input('sort_order', 0);
        $model->remark = $request->input('remark');

        // charge point number加密生成许可码
        if (config('app.is_enable_automatic_encryption_of_license_code')) $model->license_code = self::licenseCodeEncryption($model->charge_point_cs_number);

        $model->save();

        self::sendInitPushByKioskNumberList();

        return redirect()->action(
            [self::class, 'showPage'],
            self::getUrlParams($request)
        );
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function rules(?Request $request, ?Model $model): array
    {
        $module_name = self::$module_name;
        $site_number = $model->site_number;
        if ((isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) && blank($model?->charge_point_cs_id)) {
            // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下就直接获取页面传入的场地编号
            $site_number = $request->input('site_number');
        } else if (blank($model?->charge_point_cs_id) && auth()->user()->site_number_list->count() === 1) {
            // 如果是新增并且管理员场地只有一个就直接赋值
            $site_number = auth()->user()->site_number_list->first();
        }
        
        $rules = array(
            'name' => 'required|max:45',
            'vendor' => 'required|max:45',
            'api_url' => 'required|max:1000',
            'sort_order' => 'required|integer|min:0',
            'remark' => 'nullable|max:1000',
        );

        // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下才需要判断场地编号是否存在
        if (blank($model->charge_point_cs_id)) {
            $rules['charge_point_cs_number'] = [
                'required',
                'max:30',
                'regex:/^[a-zA-Z0-9]+$/', // 只能填写字母和数字
                'unique:App\Models\Modules\ChargePointCs,charge_point_cs_number',
            ];
            if (isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) {
                $rules['site_number'] = [
                    'required',
                    'exists:App\Models\Modules\Site,site_number',
                    function ($attr, $value, $fail) use ($module_name, $model) {
                        // 判断当前用户是否有该场地权限
                        $site = Site::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('site_number', $value);
                        if (blank($site)) {
                            $fail(__('common.text_not_found', ['field' => __("$module_name.site")]));
                        }
                    },
                ];
            }
        }

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'charge_point_cs_number' => __("$module_name.charge_point_cs_number"),
            'site_number' => __("$module_name.site"),
            'name' => __("$module_name.name"),
            'sort_order' => __("$module_name.sort_order"),
            'vendor' => __("$module_name.vendor"),
            'api_url' => __("$module_name.api_url"),
            'remark' => __("$module_name.remark"),
        ];
    }

    /**
     * 返回地址栏参数
     *
     * @param Request|null $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => 'true',
            'site_search' => $request->get('site_search'),
            'name_search' => $request->get('name_search'),
        );
    }

    /**
     * 批量更新license_code
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2023-05-30
     */
    public function updateLicenseCode(): JsonResponse
    {
        if (config('app.is_enable_automatic_encryption_of_license_code')) {
            $model = $this->model;
            $charge_point_css = $model->all(['charge_point_cs_id', 'charge_point_cs_number']);
            foreach ($charge_point_css as $charge_point_cs) {
                $model->where('charge_point_cs_id', $charge_point_cs->charge_point_cs_id)
                    ->update([
                        'license_code' => self::licenseCodeEncryption($charge_point_cs->charge_point_cs_number)
                    ]);
            }
        }

        return $this->returnJson();
    }
}

<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\{
    Request,
    JsonResponse,
    RedirectResponse,
};
use App\Http\Controllers\Admin\Traits\{
    Add,
    Edit,
};
use App\Models\Modules\{Merchant, PublicHoliday, Site};
use Exception;

class PublicHolidayController extends CommonController
{
    use Add, Edit;

    protected static string $module_name = 'publicHoliday'; // 模块名称
    protected static bool $module_check_site = true; // 标记该模块校验场地
    protected static string $delimiter; // 分隔符

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new PublicHoliday;
        self::$delimiter = env('DELIMITER', '-');
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'add_url' => action([self::class, 'add']),
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'name_search' => $request->input('name_search'),
            'site_search' => $request->get('site_search'),
            'start_date_search' => $request->input('start_date_search'),
            'end_date_search' => $request->input('end_date_search'),
        );

        // 场地下拉列表
        $data['site_list'] = $this->getSiteOptionList();

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $length = (int)$request->input('length', 10);
        $search = $this->_searchList($request);
        $data_list = $search->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $site_name = $this->getValueFromLanguageArray($data->site_name_json_init) ?? '—/—';
            $result[] = array(
                'public_holiday_id' => $data->public_holiday_id, // ID
                'public_holiday_number' => $data->public_holiday_number, // 公众假期编号
                'name' => $data->name, // 名称
                'site_name' => $site_name, // 场地名称
                'start_date' => $data->start_date, // 开始时间
                'end_date' => $data->end_date, // 结束时间
                'remark' => $data->remark ?? '—/—', // 备注
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );
        return response()->json($json);
    }

    protected function getForm(Request $request, array $data): View|Application|Factory
    {
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'], self::getUrlParams($request)
        );

        // 新增时才回显公众假期编号和场地编号
        if (blank($data['model']->public_holiday_id)) {
            $data['model']->public_holiday_number = $request->old('public_holiday_number', $data['model']->public_holiday_number); // 公众假期编号
            $site_number = isSuperAdministrator() || auth()->user()->site_number_list->count() > 1
                ? $request->old('site_number', $data['model']->site_number)
                : (auth()->user()->site_number_list->first() ?? null);
            $site_name = $this->getValueFromLanguageArray(Site::firstWhere('site_number', $site_number)?->name_json);
            $data['model']->site_number = $site_number; // 场地编号
            $data['model']->site_name = $site_name; // 场地名称
        } else {
            // 编辑时将模型的公众假期编号拆分
            $public_holiday_number = filled($data['model']->public_holiday_number) && count($number = explode(self::$delimiter, $data['model']->public_holiday_number)) > 1
                ? $number[1]
                : null;
            $data['model']->public_holiday_number = $public_holiday_number; // 公众假期编号
        }
        $data['model']->name = $request->old('name', $data['model']->name); // 名称
        $data['model']->start_date = $request->old('start_date', $data['model']->start_date); // 开始时间
        $data['model']->end_date = $request->old('end_date', $data['model']->end_date); // 结束时间
        $data['model']->remark = $request->old('remark', $data['model']->remark); // 备注

        return view("pages.{$data['module_name']}.form", $data);
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @param PublicHoliday $model
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    protected function modelValidateAndSave(Request $request, PublicHoliday $model): RedirectResponse
    {
        $request->validate(self::rules($request, $model), [], self::attributes());
        // 新增时才保存公众假期、场地和商户编号
        if (blank($model->public_holiday_id)) {
            $model->site_number = $request->input('site_number');
            $model->public_holiday_number = $request->input('public_holiday_number');
            if (filled($model->site_number) && filled($site = Site::firstWhere('site_number', $model->site_number))) {
                $model->merchant_number = $site->merchant_number;
            }
        }
        $model->name = $request->input('name');
        $model->start_date = $request->input('start_date');
        $model->end_date = $request->input('end_date');
        $model->remark = $request->input('remark');
        $model->save();

        self::setSiteAllConnectorToken($model->site_number);
        self::sendInitPushByKioskNumberList();

        return redirect()->action(
            [self::class, 'showPage'], self::getUrlParams($request),
        );
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function rules(?Request $request, ?Model $model): array
    {
        $module_name = self::$module_name;

        $site_number = $request->input('site_number', $model->site_number);

        $rules = array(
            'name' => 'required|max:45',
            'start_date' => 'required|date|date_format:Y-m-d',
            'end_date' => 'required|date|date_format:Y-m-d|after_or_equal:start_date',
            'remark' => 'nullable|max:1000',
        );

        // 只有新增时才校验收费表和场地编号
        if (blank($model->public_holiday_id)) {
            if (isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) {
                $rules['site_number'] = [
                    'required',
                    'exists:App\Models\Modules\Site,site_number',
                    function ($attr, $value, $fail) use ($request, $model, $module_name) {
                        // 新增时校验非超级管理员且未拥有该场地提示错误
                        if (!isSuperAdministrator() && !in_array($value, auth()->user()->site_number_list->toArray())) {
                            $fail(__('common.text_not_found', ['field' => __("$module_name.site")]));
                        }
                    },
                ];
            }
            $rules['public_holiday_number'] = [
                'required',
                'max:30',
                'regex:/^[a-zA-Z0-9]+$/', // 只能填写字母和数字
                function ($attribute, $value, $fail) use ($site_number, $model, $module_name) {
                    if (PublicHoliday::where('public_holiday_number', $site_number . self::$delimiter . $value)->exists()) {
                        $fail(__("$module_name.public_holiday_number_already"));
                    }
                },
            ];
        }

        return $rules;
    }

    protected static function importRules(): array
    {
        $module_name = self::$module_name;

        return array(
            '*.site_number' => [
                'required',
                'exists:App\Models\Modules\Site,site_number',
                function ($attr, $value, $fail) use ($module_name) {
                    // 导入时无论新增还是更新都需要校验非超级管理员且未拥有该场地提示错误
                    if (!isSuperAdministrator() && !in_array($value, auth()->user()->site_number_list->toArray())) {
                        $fail(__('common.text_not_found', ['field' => __("$module_name.site")]));
                    }
                },
            ],
            '*.public_holiday_number' => [
                'required',
                'max:30',
                'regex:/^[a-zA-Z0-9]+$/', // 只能填写字母和数字
            ],
            '*.name' => 'required|max:45',
            '*.start_date' => 'required|date|date_format:Y-m-d',
            '*.end_date' => 'required|date|date_format:Y-m-d|after_or_equal:start_date',
            '*.remark' => 'nullable|max:1000',
        );
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'name' => __("$module_name.name"),
            'site_number' => __("$module_name.site"),
            'public_holiday_number' => __("$module_name.public_holiday_number"),
            'start_date' => __("$module_name.start_date"),
            'end_date' => __("$module_name.end_date"),
            'remark' => __("$module_name.remark"),
        ];
    }

    /**
     * 返回地址栏参数
     *
     * @param Request|null $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => 'true',
            'name_search' => $request->get('name_search'),
            'start_date_search' => $request->get('start_date_search'),
            'end_date_search' => $request->get('end_date_search'),
            'site_search' => $request->get('site_search'),
        );
    }

    /**
     * 获取搜索数据
     *
     * @param Request $request
     * @return mixed
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-11-08
     */
    protected function _searchList(Request $request): mixed
    {
        // dataTable字段
        $order = $request->input('order', 'start_date');
        $sort = $request->input('sort', 'asc');
        $name_search = $request->input('name_search');
        $site_search = $request->input('site_search');
        $start_date_search = $request->input('start_date_search');
        $end_date_search = $request->input('end_date_search');

        if ($order == 'site_name') $order = 'site.name_json';

        return PublicHoliday::select('public_holiday.*', 'site.name_json as site_name_json_init')
            ->leftJoin('site', 'public_holiday.site_number', '=', 'site.site_number')
            ->when(filled($name_search), fn($query) => $query->where('public_holiday.name', 'like', "%$name_search%"))
            ->when(filled($site_search), fn($query) => $query->where('public_holiday.site_number', $site_search))
            ->when(filled($start_date_search), fn($query) => $query->where('public_holiday.start_date', '>=', $start_date_search))
            ->when(filled($end_date_search), fn($query) => $query->where('public_holiday.end_date', '<=', $end_date_search))
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('public_holiday.site_number', auth()->user()->site_number_list))
            ->orderBy($order, $sort)
            ->latest('gmt_modified');
    }

    /**
     * 导入
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-11-08
     */
    public function import(Request $request): JsonResponse
    {
        try {
            // 获取文件数据
            $data = $this->_import($request);
            // 判断code及数据是否为空，如果只有一行标题数据说明无数据
            if ($data['code'] != 200 || blank($data['data']) || count($data['data']) <= 1) {
                $this->code = 40101;
                $this->message = $data['message'] ?: __('common.import_error_format');
                return $this->returnJson();
            }

            // 解析数据
            $result_list = [];
            // 解析出来的数据从第二行开始输出
            foreach ($data['data'] as $index => $item) {
                if ($index == 0) {
                    continue;
                }
                $result_list[] = array(
                    'site_number' => $item[0],
                    'name' => $item[1],
                    'start_date' => $item[2],
                    'end_date' => $item[3],
                    'remark' => $item[4],
                    'public_holiday_number' => $item[5],
                );
            }

            $validator = Validator::make($result_list, self::importRules(), [], self::attributes());
            if ($validator->fails()) {
                // 获取未通过校验下标数组
                $invalid_index_list = array_keys($validator->invalid());
                // 读取数据
                $invalid_result_list = collect($result_list)->only($invalid_index_list)->toArray();
                $this->code = 40104;
                $this->data = $invalid_result_list;
                $this->message = __('common.fill_data_correctly_or_time_error_format');
                return $this->returnJson();
            }

            // 将所有需要更新site提出来
            $update_site_number_list = array();

            // 校验成功后需更改部分数据
            $result_list = array_map(function ($item) use (&$update_site_number_list) {
                if (filled($site = Site::firstWhere('site_number', $item['site_number']))) {
                    $merchant_number = $site->merchant_number;
                }
                $update_site_number_list[$item['site_number']] = $item['site_number'];
                return array(
                    'site_number' => $item['site_number'],
                    'merchant_number' => $merchant_number ?? null,
                    'name' => $item['name'],
                    'start_date' => $item['start_date'],
                    'end_date' => $item['end_date'],
                    'remark' => $item['remark'],
                    'public_holiday_number' => $item['site_number'] . self::$delimiter . $item['public_holiday_number'],
                );
            }, $result_list);

            PublicHoliday::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->upsert($result_list, ['public_holiday_number', 'site_number']);

            self::setSiteAllConnectorToken($update_site_number_list);
            self::sendInitPushByKioskNumberList();

            return $this->returnJson();
        } catch (Exception) {
            $this->code = 40103;
            $this->data = null;
            // 提示信息
            $this->message = __('common.import_upload_failed');
            return $this->returnJson();
        }
    }

    /**
     * 导出
     *
     * @param Request $request
     * @return JsonResponse
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-11-08
     */
    public function export(Request $request): JsonResponse
    {
        // 获取数据
        $search = $this->_searchList($request);
        $data_list = $search->get();

        $result = array();
        // 添加表头
        $result[] = array(
            '*' . __(self::$module_name . '.site_number'),
            '*' . __(self::$module_name . '.name'),
            '*' . __(self::$module_name . '.start_date'),
            '*' . __(self::$module_name . '.end_date'),
            __(self::$module_name . '.remark'),
            '*' . __(self::$module_name . '.public_holiday_number'),
        );
        foreach ($data_list as $data) {
            $result[] = array(
                'site_number' => $data->site_number, // 场地编号
                'name' => $data->name, // 名称
                'start_date' => $data->start_date, // 开始日期
                'end_date' => $data->end_date, // 结束日期
                'remark' => $data->remark, // 备注
                'public_holiday_number' => filled($data->public_holiday_number) && count($number = explode(self::$delimiter, $data->public_holiday_number)) > 1
                    ? $number[1]
                    : '',
            );
        }
        // 获取文件数据
        $this->_export($result, __(self::$module_name . '.web_title'));
        return $this->returnJson();
    }

    public function delete(Request $request): JsonResponse
    {
        $public_holiday_number = $request->input('public_holiday_number');
        $module_name = self::$module_name;

        if (filled($public_holiday_number) &&
            filled($public_holiday = PublicHoliday::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->firstWhere('public_holiday_number', $public_holiday_number))
        ) {
            $public_holiday->delete();
            self::setSiteAllConnectorToken($public_holiday->site_number);
            self::sendInitPushByKioskNumberList();
        } else {
            $this->notFoundData(__("$module_name.web_title"));
        }

        return $this->returnJson();
    }

}

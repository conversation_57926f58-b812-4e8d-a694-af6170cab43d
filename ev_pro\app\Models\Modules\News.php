<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class News extends Model
{
    protected $table = 'news';
    protected $primaryKey = 'news_id';

    const CREATED_AT = 'gmt_create'; //默认是小写的模型类名复数格式，此处为自定义表名
    const UPDATED_AT = 'gmt_modified'; //定义主键，默认为id

    // protected $fillable = [];
    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 类型转化器
     */
    protected $casts = [
        'is_enable' => 'bool',
        'is_main_page_display_text_content' => 'bool', // 是否在首页显示文字内容
    ];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [
        'is_enable' => 0,
        'view_count' => 0,
        'is_main_page_display_text_content' => 1, // 是否在首页显示文字内容
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    // 用户
    public function user()
    {
        return $this->hasOne(AppUser::class, 'user_id', 'user_id');
    }

    public function getFirstDescriptionAttribute()
    {
        $description = $this->description->first();
        if (filled($description)) return $description;

        $description = new NewsDescription;
        foreach ($description->fillable as $key) {
            $description->$key = null;
        }
        return $description;
    }

    /**
     * 一对多关联description
     */
    public function description()
    {
        return $this->hasMany(NewsDescription::class, 'news_id', 'news_id');
    }

    // 新闻所属的分类
    public function category(): BelongsToMany
    {
        return $this->belongsToMany(NewsCategory::class, 'news_category_to_news', 'news_id', 'news_category_id')->withPivot('sort_order')->withTimestamps();
    }
}

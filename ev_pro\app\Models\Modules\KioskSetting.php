<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;

class KioskSetting extends Model
{
    protected $table = 'kiosk_setting'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'kiosk_setting_id'; //定义主键，默认为id
    // 表的唯一number
    public static $table_number = 'kiosk_setting_number';
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    // protected $fillable = [];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [
        'is_display_idle_remain_time' => false, // 是否显示闲置剩余时间
        'is_enable_user_charge_record_bill_payment' => false, // 是否启用用户充电记录账单支付
        'is_display_fee_page' => false, // 是否显示收费页面
        'is_display_support_page' => false, // 是否显示支援页面
        'is_enable_print_receipt_required_count_update' => false, // 是否启用打印收据必须计数更新
        'is_display_available_payment_method' => false, // 是否显示可用支付方式
        'is_enable_zone_selection' => false, // 是否启用区域选择
        'connector_menu_row_count' => 0, // 充电机菜单行数
        'connector_menu_column_count' => 0, // 充电机菜单列数
        'connector_menu_preparing_row_count' => 0, // 充电机菜单准备中行数
        'connector_menu_preparing_column_count' => 0, // 充电机菜单准备中列数
        'is_display_connector_tag' => false, // 是否显示充电枪标签
        'clear_cache_charge_record_buffer_time' => 0, // 清除缓存充电记录缓冲时间
        'is_enable_enter_receiver' => false, // 是否启用输入接收者
        'is_enable_record_screen_operation' => false, // 是否启用记录屏幕操作
        'is_enable_print_receipt' => false, // 是否启用打印收据
        'is_enable_cms_calculate_user_charge_record_bill_total_amount' => false, // 是否启用CMS计算用户充电记录账单金额
        'is_enable_synchronize_system_date_time' => false, // 是否启用同步系统日期时间
        'websocket_check_alive_interval' => 0, // Websocket检查活跃间隔
        'websocket_alive_timeout' => 0, // WebSocket活跃超时
        'heartbeat_interval' => 0, // 心跳间隔
        'pos_check_health_interval' => 0, // POS检测健康间隔
        'self_check_interval' => 0, // 自检间隔
        'check_octopus_data_exchange_interval' => 0, // 检查八达通数据交换间隔
        'screensaver_waiting_time' => 0, // 屏保等待时间
        'screensaver_image_auto_run_interval' => 0, // 屏保图片自动轮播间隔
        'is_screensaver_top_priority' => false, // 是否屏保最优先
        'synchronize_system_date_time_offset' => -28800, // 同步系统日期时间偏移量 (负8小时)
        'sort_order' => 0, // 排序
    ];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 类型转化器
     */
    protected $casts = [
        'is_display_idle_remain_time' => 'bool',
        'is_enable_user_charge_record_bill_payment' => 'bool',
        'is_display_fee_page' => 'bool',
        'is_display_support_page' => 'bool',
        'is_enable_settle_charge_arrears' => 'bool',
        'is_enable_charge_pre_authorization_refund' => 'bool',
        'is_enable_print_receipt_required_count_update' => 'bool',
        'is_display_available_payment_method' => 'bool',
        'is_enable_zone_selection' => 'bool',
        'is_display_connector_tag' => 'bool',
        'is_enable_record_screen_operation' => 'bool',
        'is_enable_enter_receiver' => 'bool',
        'is_enable_print_receipt' => 'bool',
        'is_enable_cms_calculate_user_charge_record_bill_total_amount' => 'bool',
        'is_enable_octopus_cancel' => 'bool',
        'is_enable_synchronize_system_date_time' => 'bool',
        'is_screensaver_top_priority' => 'bool',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * 一对多关联KioskScreenTime
     */
    public function screenTime()
    {
        return $this->hasMany(KioskSettingScreenTime::class, 'kiosk_setting_number', 'kiosk_setting_number');
    }

    /**
     * 一对多关联description
     */
    public function description()
    {
        return $this->hasMany(KioskSettingDescription::class, 'kiosk_setting_number', 'kiosk_setting_number');
    }

    /**
     * 一对多关联screensaver
     */
    public function screensaver()
    {
        return $this->hasMany(KioskSettingScreensaverResourceDescription::class, 'kiosk_setting_number', 'kiosk_setting_number');
    }

    /**
     * 一对多关联 kiosk
     */
    public function kiosk()
    {
        return $this->belongsTo(Kiosk::class, 'kiosk_setting_number', 'kiosk_setting_number');
    }
}

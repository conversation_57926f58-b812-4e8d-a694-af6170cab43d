<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Validation\Rule;
use Illuminate\Http\{
    JsonResponse,
    RedirectResponse,
    Request,
};
use App\Http\Controllers\Admin\Traits\{
    Edit,
};
use Illuminate\Foundation\Application;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use App\Models\Modules\{
    ChargePoint,
    ChargePointSetting,
    Merchant,
    Site,
};

class ChargePointSettingController extends CommonController
{
    use Edit;

    protected static string $module_name = 'chargePointSetting'; // 模块名称
    protected static bool $module_check_site = true; // 标记该模块校验场地

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new ChargePointSetting;
    }

    /**
     * 初始页
     *
     * @param Request $request
     * @return View|Application|Factory
     */
    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'add_url' => action([self::class, 'add']),
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'name_search' => $request->get('name_search'),
            'site_search' => $request->get('site_search'),
            'site_list' => $this->getSiteOptionList(),
        );

        return view("pages.{$data['module_name']}.list", $data);
    }

    /**
     * 列表接口
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'sort_order');
        $sort = $request->input('sort', 'asc');
        $length = (int)$request->input('length', 10);
        $name_search = $request->input('name_search');
        $site_search = $request->input('site_search');

        if ($order == 'site_name') $order = 'site.name_json';

        $data_list = ChargePointSetting::select('charge_point_setting.*', 'site.name_json as site_name_json_init')
            ->leftJoin('site', 'charge_point_setting.site_number', '=', 'site.site_number')
            ->when(filled($name_search), fn($query) => $query->where('charge_point_setting.name', 'like', "%$name_search%"))
            ->when(filled($site_search), fn($query) => $query->where('charge_point_setting.site_number', $site_search))
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('charge_point_setting.site_number', auth()->user()->site_number_list))
            ->orderBy($order, $sort)
            ->latest('gmt_modified')
            ->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $site_name = $this->getValueFromLanguageArray($data->site_name_json_init) ?? '—/—';
            $result[] = array(
                'charge_point_setting_id' => $data->charge_point_setting_id, // 充电机设置ID
                'charge_point_setting_number' => $data->charge_point_setting_number, // 充电机设置编号
                'site_name' => $site_name, // 场地名称
                'name' => $data->name, // 名称
                'is_enable_verify_rfid_card' => $data->is_enable_verify_rfid_card, // 是否启用校验RFID卡
                'sort_order' => $data->sort_order, // 排序
                'remark' => $data->remark ?? '—/—', // 备注
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );
        return response()->json($json);
    }

    /**
     * 表单页
     *
     * @param Request $request
     * @param array $data
     * @return View|Application|Factory
     */
    protected function getForm(Request $request, array $data): View|Application|Factory
    {
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'], self::getUrlParams($request)
        );
        if (blank($data['model']->charge_point_setting_id)) $data['model']->charge_point_setting_number = $request->old('charge_point_setting_number', $data['model']->charge_point_setting_number); // 充电机设置编号
        $site_number = $data['model']->site_number;
        if ((isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) && blank($data['model']?->charge_point_setting_id)) {
            // 如果是新增，并且是超级管理员或者管理员的商户大于1的情况下就直接获取页面传入的商户编号
            $site_number = $request->old('site_number', $data['model']->site_number);
        } else if (blank($data['model']?->charge_point_setting_id) && auth()->user()->site_number_list->count() === 1) {
            // 如果是新增并且管理员商户只有一个就直接赋值
            $site_number = auth()->user()->site_number_list->first();
        }

        $data['model']->site_number = $site_number; // 场地编号
        $site_name = $this->getValueFromLanguageArray(
            Site::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->firstWhere('site_number', $site_number)?->name_json
        );
        $data['model']->site_name = $site_name; // 场地名称
        $data['model']->name = $request->old('name', $data['model']->name); // 名称
        $data['model']->is_enable_verify_rfid_card = $request->old('is_enable_verify_rfid_card', $data['model']->is_enable_verify_rfid_card); // 是否启用校验RFID卡
        $data['model']->remark = $request->old('remark', $data['model']->remark); // 备注
        $data['model']->sort_order = $request->old('sort_order', $data['model']->sort_order); // 排序

        return view("pages.{$data['module_name']}.form", $data);
    }

    /**
     * 添加
     *
     * @param Request $request
     * @return Factory|Application|View|RedirectResponse
     */
    public function add(Request $request): Factory|Application|View|RedirectResponse
    {
        $data = array();

        $model = $this->model;

        // 如果有charge_point_setting_number的存在就是复制除了charge_point_setting_number和name以外的收费表数据输出到新增页面
        $charge_point_setting_number = $request->input('_charge_point_setting_number');
        if (filled($charge_point_setting_number)) {
            $model = $model->when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->where('charge_point_setting_number', $charge_point_setting_number)->firstOrFail();
            $model->charge_point_setting_id = $model->charge_point_setting_number = $model->name = null;
        }

        if ($request->isMethod('post')) {
            return $this->modelValidateAndSave($request, $model);
        }

        $data['model'] = $model;
        return $this->getForm($request, $data);
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @param ChargePointSetting $model
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    protected function modelValidateAndSave(Request $request, ChargePointSetting $model): RedirectResponse
    {
        $request->validate(self::rules($request, $model), [], self::attributes());
        // 判断是否是新增还是编辑
        if (blank($model->charge_point_setting_id)) {
            $model = $this->model;
            $model->charge_point_setting_number = $request->input('charge_point_setting_number');

            if (isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) {
                // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下就直接获取页面传入的场地编号
                $model->site_number = $request->input('site_number');
            } else if (auth()->user()->site_number_list->count() === 1) {
                // 如果是新增并且管理员场地只有一个就直接赋值
                $model->site_number = auth()->user()->site_number_list->first();
            }
            $model->merchant_number = Site::firstWhere('site_number', $model->site_number)?->merchant_number;
        }
        $model->name = $request->input('name');
        $model->is_enable_verify_rfid_card = $request->input('is_enable_verify_rfid_card', 0);
        $model->remark = $request->input('remark');
        $model->sort_order = $request->input('sort_order', 0);
        $model->save();

        self::sendInitPushByKioskNumberList();

        return redirect()->action(
            [self::class, 'showPage'], self::getUrlParams($request)
        );
    }

    /**
     * 删除
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function delete(Request $request): JsonResponse
    {
        $charge_point_setting_number = $request->input('charge_point_setting_number');

        $charge_point_setting = ChargePointSetting::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('charge_point_setting_number', $charge_point_setting_number);
        if (!empty($charge_point_setting_number) && filled($charge_point_setting)) {
            if (blank($charge_point_list = $charge_point_setting->chargePoint()->get())) {
                $charge_point_setting->delete();

                self::sendInitPushByKioskNumberList();
            } else {
                $charge_point_str = '';
                foreach ($charge_point_list as $charge_point) {
                    $charge_point_str .= '<li>' . $charge_point->name . '</li>';
                }
                $this->code = 201;
                $this->message = __('common.error_has_binding_unable_to_delete_charge_point_setting', [
                    'charge_point' => $charge_point_str
                ]);
            }
        } else {
            $this->code = 404;
            $this->message = __('common.text_not_found', ['field' => 'Number']);
        }

        return $this->returnJson();
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function rules(?Request $request, ?Model $model): array
    {
        $module_name = self::$module_name;
        $rules = array(
            'name' => 'required|string|max:45',
            'is_enable_verify_rfid_card' => 'boolean',
            'remark' => 'nullable|max:1000',
            'sort_order' => 'integer|min:0',
        );

        // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下才需要判断场地编号是否存在
        if (blank($model->charge_point_setting_id)) {
            $rules['charge_point_setting_number'] = [
                'required',
                'unique:App\Models\Modules\ChargePointSetting,charge_point_setting_number',
                'max:30', 
                'regex:/^[a-zA-Z0-9]+$/', // 只能填写字母和数字
            ];
            if (isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) {
                $rules['site_number'] = [
                    'required',
                    'exists:App\Models\Modules\Site,site_number',
                    function ($attr, $value, $fail) use ($module_name) {
                        // 判断选择的site是否包含在当前角色的场地内
                        $site = Site::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('site_number', $value);
                        if (blank($site)) $fail(__('common.text_not_found', ['field' => __("$module_name.site")]));
                    }
                ];
            };
        }

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'site_number' => __("$module_name.site"),
            'charge_point_setting_number' => __("$module_name.charge_point_setting_number"),
            'name' => __("$module_name.name"),
            'remark' => __("$module_name.remark"),
            'sort_order' => __("$module_name.sort_order"),
            'gmt_activate' => __("$module_name.gmt_activate"),
            'gmt_expiry' => __("$module_name.gmt_expiry"),
            'is_enable' => __("$module_name.is_enable"),
        ];
    }

    /**
     * 返回地址栏参数
     *
     * @param Request|null $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => 'true',
            'name_search' => $request->get('name_search'),
            'site_search' => $request->get('site_search'),
        );
    }

}

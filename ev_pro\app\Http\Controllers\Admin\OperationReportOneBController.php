<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use App\Models\Modules\ChargeRecord;
use App\Enums\{
    ChargeTariffScheme,
    ChargeValueType,
    LmsMode,
};
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use Illuminate\Foundation\Application;
use Illuminate\Http\{
    JsonResponse,
    Request,
};

class OperationReportOneBController extends CommonController
{
    protected static string $module_name = 'operationReportOneB'; // 模块名称

    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'time_frame_search' => $request->get('time_frame_search')
        );

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'year');
        $sort = $request->input('sort', 'asc');
        $time_frame_search = $request->input('time_frame_search');
        $result = array();
        // 获取数据
        $list_data = $this->listData($time_frame_search);
        // 数据长度
        $list_data_total = count($list_data);

        // 正序、倒序
        $sort_order = strtolower($sort) == 'desc' ? SORT_DESC : SORT_ASC;
        // 年月一起排序
        if (strtolower($order) == 'year' || strtolower($order) == 'month') {
            $year_sort_list = array_column($list_data, 'year');
            $month_sort_list = array_column($list_data, 'month');
            // 多字段排序
            array_multisort($year_sort_list, $sort_order, $month_sort_list, $sort_order, $list_data);
        } else {
            // 取出对应排序key的数据
            $order_sort_list = array_column($list_data, $order);
            array_multisort($order_sort_list, $sort_order, $list_data);
        }

        // 后统一格式化对应数据
        foreach ($list_data as $item) {
            $result[] = array(
                'year' => $item['year'],
                'month' => $item['month'],
                'pre_without_lms' => $item['pre_without_lms'],
                'pre_even_distribution' => $item['pre_even_distribution'],
                'pre_queuing' => $item['pre_queuing'],
                'post_time_without_lms' => $item['post_time_without_lms'],
                'post_time_even_distribution' => $item['post_time_even_distribution'],
                'post_time_queuing' => $item['post_time_queuing'],
                'post_energy_without_lms' => $item['post_energy_without_lms'],
                'post_energy_even_distribution' => $item['post_energy_even_distribution'],
                'post_energy_queuing' => $item['post_energy_queuing'],
                'total_time_in_bypass_mode' => $item['total_time_in_bypass_mode'],
                'total_time_in_maintenance_period' => $item['total_time_in_maintenance_period']
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $list_data_total,
            'recordsFiltered' => $list_data_total,
            "data" => $result,
        );

        return response()->json($json);

    }

    public function excelExport(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        // dataTable字段
        $time_frame_search = $request->input('time_frame_search');

        // 获取数据
        $list_data = $this->listData($time_frame_search);

        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet(); //实例化 Spreadsheet 对象
        $worksheet = $spreadsheet->getActiveSheet();

        $web_title = __("operationReportOneB.web_title");
        // 工作簿名称为 "Operation Report 1B"
        $worksheet->setTitle($web_title);
        $excel_title = array(
            __("$module_name.pre_paid_time_charge_scheme"),
            __("$module_name.post_paid_time_charge_scheme"),
            __("$module_name.post_paid_energy_charge_scheme"),
            __("$module_name.year"),
            __("$module_name.month"),
            __("$module_name.pre_without_lms"),
            __("$module_name.pre_even_distribution"),
            __("$module_name.pre_queuing"),
            __("$module_name.post_time_without_lms"),
            __("$module_name.post_time_even_distribution"),
            __("$module_name.post_time_queuing"),
            __("$module_name.post_energy_without_lms"),
            __("$module_name.post_energy_even_distribution"),
            __("$module_name.post_energy_queuing")
        );

        // 设置表头名
        $worksheet->setCellValueByColumnAndRow(1, 1, '');
        $worksheet->setCellValueByColumnAndRow(2, 1, '');
        $worksheet->setCellValueByColumnAndRow(3, 1, $excel_title[0]);
        $worksheet->setCellValueByColumnAndRow(6, 1, $excel_title[1]);
        $worksheet->setCellValueByColumnAndRow(9, 1, $excel_title[2]);
        $worksheet->setCellValueByColumnAndRow(1, 2, $excel_title[3]);
        $worksheet->setCellValueByColumnAndRow(2, 2, $excel_title[4]);
        $worksheet->setCellValueByColumnAndRow(3, 2, $excel_title[5]);
        $worksheet->setCellValueByColumnAndRow(4, 2, $excel_title[6]);
        $worksheet->setCellValueByColumnAndRow(5, 2, $excel_title[7]);
        $worksheet->setCellValueByColumnAndRow(6, 2, $excel_title[8]);
        $worksheet->setCellValueByColumnAndRow(7, 2, $excel_title[9]);
        $worksheet->setCellValueByColumnAndRow(8, 2, $excel_title[10]);
        $worksheet->setCellValueByColumnAndRow(9, 2, $excel_title[11]);
        $worksheet->setCellValueByColumnAndRow(10, 2, $excel_title[12]);
        $worksheet->setCellValueByColumnAndRow(11, 2, $excel_title[13]);
        $worksheet->mergeCells('C1:E1');
        $worksheet->mergeCells('F1:H1');
        $worksheet->mergeCells('I1:K1');

        // 计算数组长度便于后面遍历
        $len = count($list_data);

        // 因为前面两格表头样式占了两行，所以要加2用于设置单元格样式
        $total_rows = $len + 2;

        //设置单元格样式
        $styleArray = [
            'font' => [
                'bold' => false
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['argb' => '666666'],
                ],
            ],
        ];
        $worksheet->getStyle("A1:K$total_rows")->applyFromArray($styleArray);
        $worksheet->getStyle('A1:K2')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('c0c0c0');

        // 设置单元格的宽度
        $width_value = 2;
        foreach (range('A', 'K') as $index => $value) {
            if ($index > 1) $width_value = 10;                      // 头三个是多级表头的表头
            $worksheet->getColumnDimension($value)->setWidth(strlen($excel_title[$index + 3]) + $width_value);
        }
        $index = 0;
        foreach ($list_data as $item) {
            $line = $index + 3; //从表格第3行开始
            $worksheet->setCellValueByColumnAndRow(1, $line, $item['year']);
            $worksheet->setCellValueByColumnAndRow(2, $line, (int)$item['month']);
            $worksheet->setCellValueByColumnAndRow(3, $line, $item['pre_without_lms']);
            $worksheet->setCellValueByColumnAndRow(4, $line, $item['pre_even_distribution']);
            $worksheet->setCellValueByColumnAndRow(5, $line, $item['pre_queuing']);
            $worksheet->setCellValueByColumnAndRow(6, $line, $item['post_time_without_lms']);
            $worksheet->setCellValueByColumnAndRow(7, $line, $item['post_time_even_distribution']);
            $worksheet->setCellValueByColumnAndRow(8, $line, $item['post_time_queuing']);
            $worksheet->setCellValueByColumnAndRow(9, $line, $item['post_energy_without_lms']);
            $worksheet->setCellValueByColumnAndRow(10, $line, $item['post_energy_even_distribution']);
            $worksheet->setCellValueByColumnAndRow(11, $line, $item['post_energy_queuing']);
            $index++;
        }


        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $file_name = $web_title . ' ' . date('Y-m-d H:i:s') . '.xlsx';

        // 客户端文件下载
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $file_name . '"');
        header('Cache-Control: max-age=0');
        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
        exit;
    }

    /**
     * 查询数据
     * @param $time_frame_search
     * @return array
     */
    protected function listData($time_frame_search = null): array
    {
        // 结束日期即当前月份的最后一天
        $end_date = date('Y-m-t 23:59:59');
        // 起始日期即开始时间的第一天 - 当前时间往前推12月
        $start_date = date('Y-m-01 00:00:00', strtotime('-11 month'));
        // 实际结果集
        $real_result_list = array();
        // 默认填充数据
        $default_result_list = array(
            'pre_without_lms' => 0,
            'pre_even_distribution' => 0,
            'pre_queuing' => 0,
            'post_time_without_lms' => 0,
            'post_time_even_distribution' => 0,
            'post_time_queuing' => 0,
            'post_energy_without_lms' => 0,
            'post_energy_even_distribution' => 0,
            'post_energy_queuing' => 0,
            'total_time_in_bypass_mode' => 0,
            'total_time_in_maintenance_period' => 0,
        );

        // 搜索时间不为空
        if (filled($time_frame_search)) {
            // 得到时间区间，分割成开始时间和结束时间
            $time_frame_range = explode(' - ', $time_frame_search);
            // 判断是否是时间类型且为开始时间结束时间类型
            if (count($time_frame_range) == 2 && strtotime($time_frame_range[0]) && strtotime($time_frame_range[1]) && (strtotime($time_frame_range[0]) <= strtotime($time_frame_range[1]))) {
                $start_date = date('Y-m-01 00:00:00', strtotime($time_frame_range[0]));
                $end_date = date('Y-m-t 23:59:59', strtotime($time_frame_range[1]));
            }
        }

        // 获取充电记录结果集
        $charge_record_result_list = ChargeRecord::selectRaw('DATE_FORMAT(gmt_start,"%Y-%m") AS month_year_key')
            ->selectRaw('SUM(CASE WHEN lms_mode= "' . LmsMode::NoLms . '" and charge_tariff_scheme="' . ChargeTariffScheme::PrePaid . '" THEN 1 ELSE 0 END) as pre_without_lms')
            ->selectRaw('SUM(CASE WHEN lms_mode= "' . LmsMode::EvenDistribution . '" and charge_tariff_scheme="' . ChargeTariffScheme::PrePaid . '" THEN 1 ELSE 0 END) as pre_even_distribution')
            ->selectRaw('SUM(CASE WHEN lms_mode= "' . LmsMode::MaximumOutput . '" and charge_tariff_scheme="' . ChargeTariffScheme::PrePaid . '" THEN 1 ELSE 0 END) as pre_queuing')
            ->selectRaw('SUM(CASE WHEN lms_mode= "' . LmsMode::NoLms . '" and charge_tariff_scheme="' . ChargeTariffScheme::PostPaid . '" and charge_value_type = "' . ChargeValueType::Time . '" THEN 1 ELSE 0 END) as post_time_without_lms')
            ->selectRaw('SUM(CASE WHEN lms_mode= "' . LmsMode::EvenDistribution . '" and charge_tariff_scheme="' . ChargeTariffScheme::PostPaid . '" and charge_value_type = "' . ChargeValueType::Time . '" THEN 1 ELSE 0 END) as post_time_even_distribution')
            ->selectRaw('SUM(CASE WHEN lms_mode= "' . LmsMode::MaximumOutput . '" and charge_tariff_scheme="' . ChargeTariffScheme::PostPaid . '" and charge_value_type = "' . ChargeValueType::Time . '" THEN 1 ELSE 0 END) as post_time_queuing')
            ->selectRaw('SUM(CASE WHEN lms_mode= "' . LmsMode::NoLms . '" and charge_tariff_scheme="' . ChargeTariffScheme::PostPaid . '" and charge_value_type = "' . ChargeValueType::Energy . '" THEN 1 ELSE 0 END) as post_energy_without_lms')
            ->selectRaw('SUM(CASE WHEN lms_mode= "' . LmsMode::EvenDistribution . '" and charge_tariff_scheme="' . ChargeTariffScheme::PostPaid . '" and charge_value_type = "' . ChargeValueType::Energy . '" THEN 1 ELSE 0 END) as post_energy_even_distribution')
            ->selectRaw('SUM(CASE WHEN lms_mode= "' . LmsMode::MaximumOutput . '" and charge_tariff_scheme="' . ChargeTariffScheme::PostPaid . '" and charge_value_type = "' . ChargeValueType::Energy . '" THEN 1 ELSE 0 END) as post_energy_queuing')
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('charge_record.site_number', auth()->user()->site_number_list))
            ->whereNotNull('gmt_stop')
            ->whereBetween('gmt_start', [$start_date, $end_date])
            ->groupBy('month_year_key')
            ->get()
            ->toArray();

        $new_result_list = array_column($charge_record_result_list, null, 'month_year_key');

        // 获取开始结束时间的时间戳
        $start_date_timestamp = strtotime($start_date);
        $end_date_timestamp = strtotime($end_date);
        // 根据开始结束日期区间的时间戳循环获取月份 - 例如2022-03 ~ 2023-02
        while ($start_date_timestamp <= $end_date_timestamp) {
            // 当前月份
            $current_month = date('Y-m', $start_date_timestamp);
            // 当前月份结果 - 当有搜索时间且数据为空时即填充0
            $default_result_list['year'] = date('Y', $start_date_timestamp);
            $default_result_list['month'] = date('m', $start_date_timestamp);
            // 将每月填充数据，默认为0
            $real_result_list[$current_month] = $default_result_list;

            if (!empty($new_result_list)) {
                // 如果查询数据存在该月，赋值数据
                if (array_key_exists($current_month, $new_result_list)) {
                    // 移除key
                    unset($new_result_list[$current_month]['month_year_key']);
                    // 将查询数据全部转成number类型
                    $new_result_list[$current_month] = array_map(function ($v) {
                        // 转化成数字
                        return (int)$v;
                    }, $new_result_list[$current_month]);
                    // 合并覆盖默认数据
                    $new_result_list[$current_month] = array_merge($real_result_list[$current_month], $new_result_list[$current_month]);
                    $new_result_list[$current_month]['year'] = date('Y', $start_date_timestamp);
                    $new_result_list[$current_month]['month'] = date('m', $start_date_timestamp);
                    $real_result_list[$current_month] = $new_result_list[$current_month];
                }
            }

            // 每次+1月份作为循环出口
            $start_date_timestamp = strtotime("+1 month", $start_date_timestamp);
        }

        return $real_result_list;
    }
}

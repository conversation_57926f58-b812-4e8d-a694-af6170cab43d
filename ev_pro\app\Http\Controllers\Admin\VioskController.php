<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Carbon\CarbonPeriod;
use Exception;
use Illuminate\Contracts\View\{
    Factory,
    View
};
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Redis;
use Illuminate\Validation\Rule;
use Illuminate\Http\{
    JsonResponse,
    RedirectResponse,
    Request
};
use App\Enums\{
    WebsocketType,
    PaymentMethod,
    PosVendorEnum,
};
use App\Models\Modules\{
    Viosk,
    VioskSetting,
    Site,
    Zone,
    ChargePoint,
    VioskPaymentMethod,
};

class VioskController extends CommonController
{
    protected ?Viosk $model = null;
    protected static string $module_name = 'viosk'; // 模块名称

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new Viosk;
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $viosk_number_search = $request->input('viosk_number_search');
        $name_search = $request->input('name_search');

        $data = array(
            'module_name' => self::$module_name,
            'add_url' => action([self::class, 'add']),
            'list_url' => action([self::class, 'list']),
            'setting_list_url' => action([VioskSettingController::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'download_template_url' => existsImage('template', 'viosk_template.xlsx'),
            'name_search' => $name_search,
            'viosk_number_search' => $viosk_number_search,
        );

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'sort_order');
        $sort = $request->input('sort', 'asc');
        $length = (int)$request->input('length', 10);
        $viosk_number_search = $request->input('viosk_number_search');
        $name_search = $request->input('name_search');

        if ($order == 'viosk_setting_name') $order = 'viosk_setting.name';
        if ($order == 'site_name') $order = 'site.name_json';
        if ($order == 'zone_name') $order = 'zone.name_json';

        $data_list = Viosk::query()
            ->select('viosk.*', 'viosk_setting.name as viosk_setting_name_init', 'site.name_json as site_name_json_init', 'zone.name_json as zone_name_json_init')
            ->leftJoin('viosk_setting', 'viosk.viosk_setting_number', '=', 'viosk_setting.viosk_setting_number')
            ->leftJoin('site', 'viosk.site_number', '=', 'site.site_number')
            ->leftJoin('zone', 'viosk.zone_number', '=', 'zone.zone_number')
            ->when(filled($viosk_number_search), fn($query) => $query->where('viosk_number', 'like', "%$viosk_number_search%"))
            ->when(filled($name_search), fn($query) => $query->where('viosk.name', 'like', "%$name_search%"))
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('viosk.site_number', auth()->user()->site_number_list))
            ->orderBy($order, $sort)
            ->orderBy('gmt_modified', 'desc')
            ->paginate($length, ['*'], 'start');

        $result = array();

        foreach ($data_list as $data) {
            $result[] = array(
                'viosk_id' => $data->viosk_id, // Viosk ID
                'viosk_number' => $data->viosk_number, // Viosk编号
                'viosk_setting_name' => $data->viosk_setting_name_init ?? '—/—', // 设置名称
                'viosk_setting_number' => $data->viosk_setting_number, // 设置编号
                'charge_point_number' => $data->charge_point_number, // 充电机编号
                'name' => $data->name, // 名称
                'site_name' => $this->getValueFromLanguageArray($data->site_name_json_init) ?? '—/—', // 场地
                'zone_name' => $this->getValueFromLanguageArray($data->zone_name_json_init) ?? '—/—', // 区域
                'ip_address' => $data->ip_address ?? '—/—', // IP地址
                'current_build_version' => $data->current_build_version ?? '—/—', // 当前构建版本
                'gmt_last_boot' => $data->gmt_last_boot ?? '—/—', // 最后启动时间
                'gmt_last_alive' => $data->gmt_last_alive ?? '—/—', // 最后活跃时间
                'charge_point_com_port' => $data->charge_point_com_port ?? '—/—', // 最后活跃时间
                'charge_point_baud_rate' => $data->charge_point_baud_rate ?? '—/—', // 最后活跃时间
                'octopus_com_port' => $data->octopus_com_port, // 八达通串口
                'octopus_device_number' => $data->octopus_device_number ?? '—/—', // 八达通设备编号
                'gmt_octopus_last_upload' => $data->gmt_octopus_last_upload ?? '—/—', // 八达通最后上传时间
                'gmt_octopus_last_download' => $data->gmt_octopus_last_download ?? '—/—', // 八达通最后下载时间
                'pos_vendor' => PosVendorEnum::getDescription($data->pos_vendor), // POS供应商
                'soe_pay_pos_com_port' => $data->soe_pay_pos_com_port ?? '—/—', // SoePay POS机串口
                'soe_pay_pos_baud_rate' => $data->soe_pay_pos_baud_rate ?? '—/—', // SoePay POS机波特率
                'soe_pay_pos_api_key' => $data->soe_pay_pos_api_key ?? '—/—', // SoePay POS机API键
                'soe_pay_pos_api_token' => $data->soe_pay_pos_api_token ?? '—/—', // SoePay POS机API Token
                'soe_pay_pos_pre_authorization_amount' => is_null($data->soe_pay_pos_pre_authorization_amount) ? '—/—' : (__('common.unit_hk') . (float)bcdiv($data->soe_pay_pos_pre_authorization_amount, 100, 1)), // SoePay POS机预授权金额
                'sort_order' => $data->sort_order, // 排序
                'remark' => $data->remark ?? '—/—', // 备注
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
                'status' => false,   // 连接状态 - 是否在线
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );

        return response()->json($json);
    }

    protected function getForm(Request $request, array $data): View|Application|Factory
    {
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'],
            self::getUrlParams($request)
        );
        if (blank($data['model']->viosk_id)) {
            $data['model']->viosk_number = $request->old('viosk_number', $data['model']->viosk_number); // Viosk编号
        }

        $site_number = $data['model']->site_number;
        if ((isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) && blank($data['model']?->viosk_id)) {
            // 如果是新增，并且是超级管理员或者管理员的商户大于1的情况下就直接获取页面传入的商户编号
            $site_number = $request->old('site_number', $data['model']->site_number);
        } else if (blank($data['model']?->viosk_id) && auth()->user()->site_number_list->count() === 1) {
            // 如果是新增并且管理员商户只有一个就直接赋值
            $site_number = auth()->user()->site_number_list->first();
        }
        $data['model']->site_number = $site_number; // 场地编号
        $site_name = $this->getValueFromLanguageArray(
            Site::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->firstWhere('site_number', $site_number)?->name_json
        );
        $data['model']->site_name = $site_name; // 场地名称
        $data['model']->name = $request->old('name', $data['model']->name); // 名称
        $data['model']->zone_number = $request->old('zone_number', $data['model']->zone_number); // 区域编号
        $zone_name = $this->getValueFromLanguageArray(
            Zone::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->firstWhere('zone_number', $data['model']->zone_number)?->name_json);
        $data['model']->zone_name = $zone_name; // 区域名称
        $data['model']->viosk_setting_number = $request->old('viosk_setting_number', $data['model']->viosk_setting_number); // 设置ID
        $viosk_setting_name =
            VioskSetting::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->firstWhere('viosk_setting_number', $data['model']->viosk_setting_number)?->name;
        $data['model']->viosk_setting_name = $viosk_setting_name; // viosk group名称
        $data['model']->charge_point_number = $request->old('charge_point_number', $data['model']->charge_point_number); // 充电机编号
        $charge_point_name =
            ChargePoint::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->firstWhere('charge_point_number', $data['model']->charge_point_number)?->name;
        $data['model']->charge_point_name = $charge_point_name; // 充电机名称
        $data['model']->ip_address = $request->old('ip_address', $data['model']->ip_address); // IP地址
        $data['model']->sort_order = $request->old('sort_order', $data['model']->sort_order); // 排序
        $data['model']->remark = $request->old('remark', $data['model']->remark); // 备注
        $data['model']->charge_point_com_port = $request->old('charge_point_com_port', $data['model']->charge_point_com_port); // 充电机串口
        $data['model']->charge_point_baud_rate = $request->old('charge_point_baud_rate', $data['model']->charge_point_baud_rate); // 充电机波特率
        $data['model']->octopus_com_port = $request->old('octopus_com_port', $data['model']->octopus_com_port); // 八达通串口
        $data['model']->octopus_device_number = $request->old('octopus_device_number', $data['model']->octopus_device_number); // 八达通设备编号
        $data['model']->pos_vendor = $request->old('pos_vendor', $data['model']->pos_vendor); // POS供应商
        $data['model']->yedpay_pos_api_url = $request->old('yedpay_pos_api_url', $data['model']->yedpay_pos_api_url); // Yedpay POS机API路径
        $data['model']->yedpay_pos_api_key = $request->old('yedpay_pos_api_key', $data['model']->yedpay_pos_api_key); // Yedpay POS机API 键
        $data['model']->yedpay_pos_pre_authorization_amount = $request->old('yedpay_pos_pre_authorization_amount', (float)bcdiv($data['model']->yedpay_pos_pre_authorization_amount, 100, 1)); // Yedpay POS机预授权金额
        $data['model']->soe_pay_pos_com_port = $request->old('soe_pay_pos_com_port', $data['model']->soe_pay_pos_com_port); // SoePay POS机串口
        $data['model']->soe_pay_pos_baud_rate = $request->old('soe_pay_pos_baud_rate', $data['model']->soe_pay_pos_baud_rate); // SoePay POS机波特率
        $data['model']->soe_pay_pos_api_key = $request->old('soe_pay_pos_api_key', $data['model']->soe_pay_pos_api_key); // SoePay POS机API键
        $data['model']->soe_pay_pos_api_token = $request->old('soe_pay_pos_api_token', $data['model']->soe_pay_pos_api_token); // SoePay POS机API Token
        $data['model']->soe_pay_pos_pre_authorization_amount = $request->old('soe_pay_pos_pre_authorization_amount', (float)bcdiv($data['model']->soe_pay_pos_pre_authorization_amount, 100, 1));; // SoePay POS机预授权金额
        // 需要顯示所有POS Vendor: $data['pos_vendor_list'] = PosVendorEnum::asSelectArray();
        $data['pos_vendor_list'] = array_filter(PosVendorEnum::asSelectArray(), function ($vendor) {
            return strcasecmp($vendor, 'Yedpay') !== 0; // 忽略大小写匹配
        });
        $data['yedpay'] = PosVendorEnum::Yedpay;
        $data['yedpay_pcg'] = PosVendorEnum::Yedpay_PCG;
        $data['soe_pay'] = PosVendorEnum::SoePay;

        return view("pages.{$data['module_name']}.form", $data);
    }

    public function add(Request $request): Application|View|Factory|RedirectResponse
    {
        $data = array();

        $model = $this->model;
        // 如果有viosk_number的存在就是复制除了viosk_number和name以外的收费表数据输出到新增页面
        if (filled($viosk_number = $request->input('_viosk_number'))) {
            $model = $model->when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->where('viosk_number', $viosk_number)->firstOrFail();
            $model->viosk_number = $model->viosk_id = $model->name = null;
        }

        if ($request->isMethod('post')) {
            return $this->modelValidateAndSave($request, $model);
        }

        $data['model'] = $model;

        return $this->getForm($request, $data);
    }

    public function edit(Request $request, string $viosk_number): View|Application|Factory|RedirectResponse|null
    {
        $data = array();

        $model = $this->model->when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->where('viosk_number', $viosk_number)->firstOrFail();

        if ($request->isMethod('post')) {
            return $this->modelValidateAndSave($request, $model);
        }
        $data['model'] = $model;

        return $this->getForm($request, $data);
    }

    public function delete(Request $request): JsonResponse
    {
        $viosk_number = $request->post('viosk_number');
        $module_name = self::$module_name;

        $viosk = Viosk::with('chargePoint')->when(!isSuperAdministrator(), fn($query) => $query->whereIn('viosk.site_number', auth()->user()->site_number_list))->firstWhere('viosk_number', $viosk_number);
        if (filled($viosk_number) && filled($viosk)) {
            // 如果未绑定ChargePoint才可以删除
            if (($viosk->chargePoint?->count() ?? 0) === 0 && ($viosk->vioskPaymentMethod?->count() ?? 0) === 0) {
                // 删除viosk
                $viosk->delete();
            } else {
                $this->code = 40001;
                $this->message = __("$module_name.error_has_binding_unable_to_delete_viosk");
            }
        } else {
            $this->code = 404;
            $this->message = __('common.text_not_found', ['field' => __("$module_name.viosk_number")]);
        }

        return $this->returnJson();
    }

    public function editSetting(Request $request): JsonResponse
    {
        $module_name = self::$module_name;

        $viosk_number = $request->input('viosk_number');
        $viosk_setting_number = $request->input('viosk_setting_number');

        $viosk_setting = VioskSetting::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('viosk_setting_number', $viosk_setting_number);
        $viosk = Viosk::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('viosk_number', $viosk_number);
        if (blank($viosk_number) || blank($viosk) || blank($viosk_setting_number) || blank($viosk_setting)) {
            $this->missingField(__("$module_name.web_title") . '|' . __("$module_name.viosk_setting"));
            return $this->returnJson();
        }

        // site不一致无法修改
        if ($viosk_setting->site_number != $viosk->site_number) {
            $this->code = 201;
            $this->message = __('common.site_inconsistent_with_field', ['field' => 'Viosk: ' . $viosk->name]);
            return $this->returnJson();
        }

        $viosk->viosk_setting_number = $viosk_setting_number;
        $viosk->save();

        return $this->returnJson();
    }

    // 初始化Viosk
    public function initViosks(Request $request): JsonResponse
    {
        $viosk_action_type = "ReloadInitializationDataNotification";
        $viosk_number = $request->input('viosk_number');
        $isSuperAdmin = isSuperAdministrator();
        // 不传viosk_number,并且不是超级管理员
        if (blank($viosk_number) && !$isSuperAdmin) {
            return $this->returnJson();
        }
        // 没有传入viosk_number, 是超级管理员则全推送
        if (blank($viosk_number) && $isSuperAdmin) {
            Viosk::query()->each(function ($viosk) use ($viosk_action_type) {
                self::redisAddUnique('viosk_action_type_list:' . $viosk->viosk_number, $viosk_action_type);
            });
            return $this->returnJson();
        }
        // 传入了 viosk_number，检查权限
        $hasPermission = Viosk::when(!$isSuperAdmin, function ($query) {
            return $query->whereIn('site_number', auth()->user()->site_number_list);
        })->where('viosk_number', $viosk_number)->exists();

        if ($hasPermission) {
            self::redisAddUnique('viosk_action_type_list:' . $viosk_number, $viosk_action_type);
            return $this->returnJson();
        }

        $this->code = 201;
        $this->message = __('common.site_inconsistent_with_field', ['field' => 'Viosk']);
        return $this->returnJson();
    }

    /**
     * 获取viosk绑定的charge point
     *
     * @param Request $request
     * @param [type] $number
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-18
     */
    public function getChargePoint(Request $request, $number): JsonResponse
    {
        $module_name = self::$module_name;

        $viosk = Viosk::with('chargePoint')->when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('viosk_number', $number);
        if (blank($viosk)) {
            $this->missingField(__("$module_name.web_title"));
        } else {
            // 查出选中的充电机用于左右拖拽
            $viosk_charge_point_list = $viosk->chargePoint?->sortBy('pivot.sort_order') ?? array();
            foreach ($viosk_charge_point_list as $viosk_charge_point) {
                $this->data['viosk_charge_point'][] = array(
                    'name' => $viosk_charge_point->name,
                    'charge_point_number' => $viosk_charge_point->charge_point_number,
                );
            }

            // 取出未绑定的充电机编号
            $other_charge_point_number_list = array();

            // 判断是否有数据
            if (isset($this->data['viosk_charge_point'])) {
                for ($i = 0; $i < count($this->data['viosk_charge_point']); $i++) {
                    $charge_point_number = $this->data['viosk_charge_point'][$i]['charge_point_number'];
                    $other_charge_point_number_list[] = $charge_point_number;
                }
            }
            // 排除掉选中的充电机用于左右拖拽
            $other_charge_point_list = ChargePoint::whereNotIn('charge_point_number', $other_charge_point_number_list)
                ->where('site_number', $viosk->site_number)
                ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->orderBy('sort_order')
                ->get();
            foreach ($other_charge_point_list as $other_charge_point) {
                $this->data['other_charge_point'][] = array(
                    'name' => $other_charge_point->name,
                    'charge_point_number' => $other_charge_point->charge_point_number,
                );
            }
        }

        return $this->returnJson();
    }

    /**
     * 提交保存viosk关联的charge point
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-18
     */
    public function submitChargePoint(Request $request): JsonResponse
    {
        $module_name = self::$module_name;

        $viosk_number = $request->input('viosk_number');
        $charge_point_number = $request->input('charge_point_number');

        $viosk = Viosk::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('viosk_number', $viosk_number);
        // 判断充电机与viosk的场地是否一致
        $charge_point = ChargePoint::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->firstWhere('charge_point_number', $charge_point_number);

        if (blank($viosk_number) || blank($viosk) || blank($charge_point_number) || blank($charge_point)) {
            $this->missingField(__("$module_name.web_title") . '|' . __("$module_name.charge_point"));
            return $this->returnJson();
        }

        // site不一致无法修改
        if ($charge_point->site_number != $viosk->site_number) {
            $this->code = 201;
            $this->message = __('common.site_inconsistent_with_field', ['field' => 'Viosk: ' . $viosk->name]);
            return $this->returnJson();
        }
        $viosk->charge_point_number = $charge_point_number;
        $viosk->save();

        return $this->returnJson();
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @param Viosk $model
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    protected function modelValidateAndSave(Request $request, Viosk $model): RedirectResponse
    {
        $request->validate(self::rules($request, $model), [], self::attributes());
        // 判断是否是新增
        if (blank($model->viosk_id)) {
            $model = $this->model;
            $viosk_number = $request->input('viosk_number');
            $model->viosk_number = $viosk_number;
            $site_number = $model->site_number;
            // 安卓移动设备无法获取 mac_address, license_code 由 viosk_number 加密生成
            if (config('app.is_enable_automatic_encryption_of_license_code')) $model->license_code = self::licenseCodeEncryption($viosk_number);

            if (isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) {
                // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下就直接获取页面传入的场地编号
                $site_number = $request->input('site_number');
            } else if (auth()->user()->site_number_list->count() === 1) {
                // 如果是新增并且管理员场地只有一个就直接赋值
                $site_number = auth()->user()->site_number_list->first();
            }
            $model->site_number = $site_number;
            $model->merchant_number = Site::firstWhere('site_number', $model->site_number)?->merchant_number;
        }
        $model->charge_point_number = $request->input('charge_point_number');
        $pos_vendor = $request->input('pos_vendor');
        // 拼接年月日
        $year_month_day = date('Y') . '-01-01 ';
        $model->name = $request->input('name');
        $model->zone_number = $request->input('zone_number');
        $model->octopus_com_port = $request->input('octopus_com_port');
        $model->pos_vendor = $pos_vendor;
        if ($pos_vendor == PosVendorEnum::Yedpay) {
            $model->yedpay_pos_api_url = $request->input('yedpay_pos_api_url');
            $model->yedpay_pos_api_key = $request->input('yedpay_pos_api_key');
        } else if ($pos_vendor == PosVendorEnum::Yedpay_PCG) {
            $model->yedpay_pos_api_url = $request->input('yedpay_pos_api_url');
            $model->yedpay_pos_api_key = $request->input('yedpay_pos_api_key');
            $model->yedpay_pos_pre_authorization_amount = bcmul($request->input('yedpay_pos_pre_authorization_amount', 0), 100);
        } else if ($pos_vendor == PosVendorEnum::SoePay) {
            $model->soe_pay_pos_com_port = $request->input('soe_pay_pos_com_port');
            $model->soe_pay_pos_baud_rate = $request->input('soe_pay_pos_baud_rate');
            $model->soe_pay_pos_api_key = $request->input('soe_pay_pos_api_key');
            $model->soe_pay_pos_api_token = $request->input('soe_pay_pos_api_token');
            $model->soe_pay_pos_pre_authorization_amount = bcmul($request->input('soe_pay_pos_pre_authorization_amount', 0), 100);
        } else {
            $model->yedpay_pos_api_url = null;
            $model->yedpay_pos_api_key = null;
            $model->yedpay_pos_pre_authorization_amount = null;
            $model->soe_pay_pos_com_port = null;
            $model->soe_pay_pos_baud_rate = null;
            $model->soe_pay_pos_api_key = null;
            $model->soe_pay_pos_api_token = null;
            $model->soe_pay_pos_pre_authorization_amount = null;
        }
        $model->sort_order = $request->input('sort_order', 0);
        $model->remark = $request->input('remark');
        $model->viosk_setting_number = $request->input('viosk_setting_number');

        $model->save();

        return redirect()->action(
            [self::class, 'showPage'],
            self::getUrlParams($request)
        );
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function rules(?Request $request, ?Model $model): array
    {
        $module_name = self::$module_name;
        $site_number = $model->site_number;
        if ((isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) && blank($model?->viosk_id)) {
            // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下就直接获取页面传入的场地编号
            $site_number = $request->input('site_number');
        } else if (blank($model?->viosk_id) && auth()->user()->site_number_list->count() === 1) {
            // 如果是新增并且管理员场地只有一个就直接赋值
            $site_number = auth()->user()->site_number_list->first();
        }
        $rules = array(
            'name' => 'required|max:45',
            'zone_number' => [
                'nullable',
                'exists:App\Models\Modules\Zone,zone_number',
                function ($attr, $value, $fail) use ($site_number, $module_name) {
                    // 如果存在site判断是否商户一致
                    if (filled($value) && filled($site_number)) {
                        $zone = Zone::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('zone_number', $value);
                        if (blank($zone)) {
                            $fail(__('common.text_not_found', ['field' => __("$module_name.zone")]));
                        } else if ($site_number != $zone->site_number) {
                            $fail(__('common.site_inconsistent_with_field', ['field' => __("$module_name.zone")]));
                        }
                    }
                },
            ],
            'viosk_setting_number' => [
                'required',
                'exists:App\Models\Modules\VioskSetting,viosk_setting_number',
                function ($attr, $value, $fail) use ($site_number, $module_name) {
                    // 如果存在site判断是否商户一致
                    if (filled($site_number)) {
                        $viosk_setting = VioskSetting::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('viosk_setting_number', $value);
                        if (blank($viosk_setting)) {
                            $fail(__('common.text_not_found', ['field' => __("$module_name.viosk_setting")]));
                        } else if ($site_number != $viosk_setting->site_number) {
                            $fail(__('common.site_inconsistent_with_field', ['field' => __("$module_name.viosk_setting")]));
                        }
                    }
                },
            ],
            'charge_point_number' => [
                // 判断是否已经和Viosk绑定,
                'nullable',
                // charge 和 viosk 一對一綁定
                Rule::unique('viosk')->ignore($model->viosk_number, 'viosk_number'),
                // 是否在同一个场地下
                function ($attr, $value, $fail) use ($model, $site_number, $module_name) {
                    $charge_point = ChargePoint::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                        ->firstWhere('charge_point_number', $value);
                    if (blank($charge_point) || $site_number != $charge_point->site_number) {
                        $fail(__('common.site_inconsistent_with_field', ['field' => __("$module_name.charge_point")]));
                    }
                },
            ],
            'octopus_com_port' => '|required|max:255',
            'pos_vendor' => 'nullable|enum_value:' . PosVendorEnum::class,
            // YEDPAY和YEDPAY_PCG需要填写
            'yedpay_pos_api_url' => 'exclude_unless:pos_vendor,' . PosVendorEnum::Yedpay . ',' . PosVendorEnum::Yedpay_PCG . '|required|max:255',
            'yedpay_pos_api_key' => 'exclude_unless:pos_vendor,' . PosVendorEnum::Yedpay . ',' . PosVendorEnum::Yedpay_PCG . '|required|max:255',
            // YEDPAY_PCG需要填写
            'yedpay_pos_pre_authorization_amount' => 'exclude_unless:pos_vendor,' . PosVendorEnum::Yedpay_PCG . '|required|numeric|min:0|max:999999',
            'soe_pay_pos_com_port' => 'exclude_unless:pos_vendor,' . PosVendorEnum::SoePay . '|required|max:255',
            'soe_pay_pos_baud_rate' => 'exclude_unless:pos_vendor,' . PosVendorEnum::SoePay . '|required|integer|min:0|max:999999',
            'soe_pay_pos_api_key' => 'exclude_unless:pos_vendor,' . PosVendorEnum::SoePay . '|required|max:255',
            'soe_pay_pos_api_token' => 'exclude_unless:pos_vendor,' . PosVendorEnum::SoePay . '|required|max:255',
            'soe_pay_pos_pre_authorization_amount' => 'exclude_unless:pos_vendor,' . PosVendorEnum::SoePay . '|required|numeric|min:0|max:999999',
            'sort_order' => 'required|integer|min:0',
            'remark' => 'nullable|max:1000',
        );
        // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下才需要判断场地编号是否存在
        if (blank($model?->viosk_id)) {
            $rules['viosk_number'] = [
                'required',
                'unique:App\Models\Modules\Viosk,viosk_number',
                'max:30',
                'regex:/^[a-zA-Z0-9]+$/', // 只能填写字母和数字
            ];
            if (isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) {
                $rules['site_number'] = [
                    'required',
                    'exists:App\Models\Modules\Site,site_number',
                    function ($attr, $value, $fail) use ($module_name) {
                        // 判断选择的site是否为当前管理员的场地下的
                        $site = Site::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('site_number', $value);
                        if (blank($site)) $fail(__('common.text_not_found', ['field' => __("$module_name.site")]));
                    },
                ];
            }
        }

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'viosk_number' => __("$module_name.viosk_number"),
            'name' => __("$module_name.name"),
            'mac_address' => __("$module_name.mac_address"),
            'site_number' => __("$module_name.site"),
            'zone_number' => __("$module_name.zone"),
            'viosk_setting_number' => __("$module_name.viosk_setting"),
            'octopus_com_port' => __("$module_name.octopus_com_port"),
            'octopus_baud_rate' => __("$module_name.octopus_baud_rate"),
            'octopus_data_exchange_program_local_path' => __("$module_name.octopus_data_exchange_program_local_path"),
            'pos_vendor' => __("$module_name.pos_vendor"),
            'yedpay_pos_api_url' => __("$module_name.yedpay_pos_api_url"),
            'yedpay_pos_api_key' => __("$module_name.yedpay_pos_api_key"),
            'yedpay_pos_pre_authorization_amount' => __("$module_name.yedpay_pos_pre_authorization_amount"),
            'soe_pay_pos_api_url' => __("$module_name.soe_pay_pos_api_url"),
            'soe_pay_pos_api_key' => __("$module_name.soe_pay_pos_api_key"),
            'soe_pay_pos_api_token' => __("$module_name.soe_pay_pos_api_token"),
            'torch_port_name' => __("$module_name.torch_port_name"),
            'torch_baud_rate' => __("$module_name.torch_baud_rate"),
            'torch_data_bits' => __("$module_name.torch_data_bits"),
            'sort_order' => __("$module_name.sort_order"),
            'remark' => __("$module_name.remark"),
        ];
    }

    /**
     * 返回地址栏参数
     *
     * @param Request $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => 'true',
            'viosk_number_search' => $request->get('viosk_number_search'),
            'name_search' => $request->get('name_search'),
        );
    }

    /**
     * 软重启
     *
     * @param Request $request
     * @param [type] $number
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-10-28
     */
    public function softReboot(Request $request, $number): JsonResponse
    {
        $module_name = self::$module_name;
        if (!empty($number)) {
            $model = Viosk::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('viosk_number', $number);
            if (filled($model)) {
                self::websocketPush(WebsocketType::SoftRebootNotification, null, $model->viosk_number);
            } else {
                $this->notFoundData(__("$module_name.viosk_number"));
            }
        } else {
            $this->notFoundData(__("$module_name.viosk_number"));
        }
        return $this->returnJson();
    }

    /**
     * 硬重启
     *
     * @param Request $request
     * @param [type] $number
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-10-28
     */
    public function hardReboot(Request $request, $number): JsonResponse
    {
        $module_name = self::$module_name;
        if (!empty($number)) {
            $model = Viosk::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('viosk_number', $number);
            if (filled($model)) {
                self::websocketPush(WebsocketType::HardRebootNotification, null, $model->viosk_number);
            } else {
                $this->notFoundData(__("$module_name.viosk_number"));
            }
        } else {
            $this->notFoundData(__("$module_name.viosk_number"));
        }
        return $this->returnJson();
    }

    /**
     * 软关机
     *
     * @param Request $request
     * @param [type] $number
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-10-28
     */
    public function softShutdown(Request $request, $number): JsonResponse
    {
        $module_name = self::$module_name;
        if (!empty($number)) {
            $model = Viosk::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('viosk_number', $number);
            if (filled($model)) {
                self::websocketPush(WebsocketType::SoftShutdownNotification, null, $model->viosk_number);
            } else {
                $this->notFoundData(__("$module_name.viosk_number"));
            }
        } else {
            $this->notFoundData(__("$module_name.viosk_number"));
        }
        return $this->returnJson();
    }

    /**
     * 硬关机
     *
     * @param Request $request
     * @param [type] $number
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-10-28
     */
    public function hardShutdown(Request $request, $number): JsonResponse
    {
        $module_name = self::$module_name;
        if (!empty($number)) {
            $model = Viosk::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('viosk_number', $number);
            if (filled($model)) {
                self::websocketPush(WebsocketType::HardShutdownNotification, null, $model->viosk_number);
            } else {
                $this->notFoundData(__("$module_name.viosk_number"));
            }
        } else {
            $this->notFoundData(__("$module_name.viosk_number"));
        }
        return $this->returnJson();
    }

    /**
     * 向 Redis 中添加值, 并且去重
     * @param $redis_key
     * @param $value *str || array*
     * @return void
     */
    private function redisAddUnique($redis_key, $value)
    {
        $redis_list = Redis::get($redis_key);
        $value_list = filled($redis_list) ? json_decode($redis_list, true) : [];
        // 确保 value_list 是数组
        if (!is_array($value_list)) {
            $value_list = [];
        }
        // 统一处理 $value 为数组
        $new_values = is_array($value) ? $value : [$value];
        // 追加新值并去重
        $value_list = array_unique(array_merge($value_list, $new_values));

        // 加入到Redis中
        Redis::set($redis_key, json_encode(array_values($value_list)));
    }

    /**
     * 检查App版本更新
     *
     * @param Request $request
     * @param [type] $number
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-10-28
     */
    public function checkVioskAppVersion(Request $request, $viosk_number): JsonResponse
    {
        $module_name = self::$module_name;
        if (!empty($viosk_number)) {
            $model = Viosk::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('viosk_number', $viosk_number);
            if (filled($model)) {
                // 从Redis中获取viosk action type list
                $redis_key = 'viosk_action_type_list:' . $viosk_number;
                self::redisAddUnique($redis_key, "CheckVioskAppVersion");
            } else {
                $this->notFoundData(__("$module_name.viosk_number"));
            }
        } else {
            $this->notFoundData(__("$module_name.viosk_number"));
        }
        return $this->returnJson();
    }

    public function uploadLogFileRequest(Request $request, $viosk_number): JsonResponse
    {
        $module_name = self::$module_name;
        // 获取开始时间和结束时间
        $gmt_data_request = $request->upload_log_file_request_date;
        // 传入日期不能为空
        if (blank($gmt_data_request)) {
            $this->code = 40001;
            $this->message = __("$module_name.fill_required_data");
            return $this->returnJson();
        }
        $gmt_data_array = $this->getRangeDateTimeArray($gmt_data_request);
        $start_date = $gmt_data_array[0];
        $end_date = $gmt_data_array[1];

        // 开始和结束日期不能为空
        if (blank($start_date) || blank($end_date)) {
            $this->code = 40001;
            return $this->returnJson();
        }
        // 开始时间不能大于结束时间
        if (strtotime($start_date) > strtotime($end_date)) {
            $this->code = 40001;
            return $this->returnJson();
        }
        // 向 Viosk 操作列表中添加 "UploadLogFileRequest"
        $redis_action_key = 'viosk_action_type_list:' . $viosk_number;
        self::redisAddUnique($redis_action_key, "UploadLogFileRequest");

        // 添加日志上傳文件時間
        $redis_upload_log_file_key = 'need_upload_log_file_request_list:' . $viosk_number;
        $need_upload_log_file_request_list = [];
        // 獲取兩個時間點間隔中的每一天
        $period = CarbonPeriod::create($start_date, $end_date);
        foreach ($period as $date) {
            $need_upload_log_file_request_list[] = $date->format('Y-m-d');
        }
        self::redisAddUnique($redis_upload_log_file_key, $need_upload_log_file_request_list);
        return $this->returnJson();
    }

    public function deleteAppRecord(Request $request, $viosk_number): JsonResponse
    {
        $module_name = self::$module_name;
        // visok number 不能為空
        if (blank($viosk_number)) {
            return $this->returnJson();
        }
        // 向 Viosk 操作列表中添加 "DeleteAppRecord"
        $redis_action_key = 'viosk_action_type_list:' . $viosk_number;
        self::redisAddUnique($redis_action_key, "DeleteAppRecord");

        $delete_app_record_param = $request->delete_app_record;
        // 參數不能为空
        if (blank($delete_app_record_param)) {
            $this->code = 40001;
            $this->message = __("$module_name.fill_required_data");
            return $this->returnJson();
        }
        $is_number = preg_match('/^\d+$/', $delete_app_record_param);
        if (!$is_number) {
            $this->code = 40001;
            $this->message = __("$module_name.fill_number");
            return $this->returnJson();
        }
        // 添加 删除App记录 参数
        $redis_delete_app_recorde_key = 'need_delete_app_months_ago_record:' . $viosk_number;
        // 直接覆盖对应 key 的值
        Redis::set($redis_delete_app_recorde_key, $delete_app_record_param);
        $this->data = Redis::get($redis_delete_app_recorde_key);

        return $this->returnJson();
    }

    /**
     * 获取viosk绑定的charge point
     *
     * @param Request $request
     * @param [type] $number
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-18
     */
    public function getVioskPayment(Request $request, $number): JsonResponse
    {
        $module_name = self::$module_name;
        if (
            empty($number) ||
            blank($model = Viosk::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('viosk_number', $number))
        ) {
            $this->missingField(__("$module_name.viosk_number"));
        } else {
            $viosk_payments = VioskPaymentMethod::select('payment_method', 'viosk_number', 'is_enable')
                ->where('viosk_number', $model->viosk_number)
                ->orderBy('sort_order')
                ->get();

            $this->data['viosk_payment'] = array();
            $this->data['payment_method'] = array();

            // 获取已选择的支付方式
            foreach ($viosk_payments as $viosk_payment) {
                $this->data['viosk_payment'][] = array(
                    'value' => $viosk_payment->payment_method,
                    'name' => PaymentMethod::getDescription($viosk_payment->payment_method),
                    'image' => existsImage('icon', 'payment_methods/' . strtolower($viosk_payment->payment_method) . '.png') ?? '',
                    'is_enable' => $viosk_payment->is_enable,
                );
            }

            // 获取除去已选择的支付方式枚举
            $payment_methods = PaymentMethod::asArray();
            foreach ($payment_methods as $value) {
                $viosk_payment_method = VioskPaymentMethod::select('payment_method', 'viosk_number')
                    ->where('viosk_number', $model->viosk_number)
                    ->where('payment_method', $value)
                    ->first();
                if (!$viosk_payment_method) {
                    $this->data['payment_method'][] = array(
                        'value' => $value,
                        'name' => PaymentMethod::getDescription($value),
                        'image' => existsImage('icon', 'payment_methods/' . strtolower($value) . '.png') ?? '',
                    );
                }
            }
        }

        return $this->returnJson();
    }

    /**
     * 提交保存viosk关联的charge point
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-18
     */
    public function submitVioskPayment(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        $number = $request->input('number');
        $viosk_payment_method_list = $request->input('viosk_payment_method_list', []);

        $model = Viosk::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('viosk_number', $number);
        if (empty($number) || blank($model)) {
            $this->missingField(__("$module_name.viosk_number"));
        } else {
            // 先清除原付款方式
            VioskPaymentMethod::where('viosk_number', $model->viosk_number)->delete();
            // 再添加付款方式
            foreach ($viosk_payment_method_list as $viosk_payment_method) {
                $vioskPaymentMethod = new VioskPaymentMethod;
                $vioskPaymentMethod->viosk_number = $model->viosk_number;
                $vioskPaymentMethod->payment_method = $viosk_payment_method['payment_method'];
                $vioskPaymentMethod->is_enable = $viosk_payment_method['is_enable'];
                $vioskPaymentMethod->sort_order = $viosk_payment_method['sort_order'];
                $vioskPaymentMethod->save();
            }
        }

        return $this->returnJson();
    }

    /**
     * 上传 TODO确定成型后更改
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-11-08
     */
    public function uploadViosk(Request $request): JsonResponse
    {
        try {
            $result_keys = array(
                'viosk_number',
                'name',
                'viosk_setting_number',
                'site_number',
                'zone_number',
                'charge_point_number',
                'charge_point_com_port',
                'charge_point_baud_rate',
                'octopus_com_port',
                'octopus_device_number',
                'pos_vendor',
                'yedpay_pos_api_url',
                'yedpay_pos_api_key',
                'yedpay_pos_pre_authorization_amount',
                'soe_pay_pos_com_port',
                'soe_pay_pos_baud_rate',
                'soe_pay_pos_api_key',
                'soe_pay_pos_api_token',
                'soe_pay_pos_pre_authorization_amount',
                'sort_order',
                'remark',
            );

            // 获取文件数据
            $format_result = $this->_importDataAndKeys($request, $result_keys);
            if ($format_result['code'] != 200) {
                $this->code = 20001;
                $this->data = null;
                // 提示信息
                $this->message = __('common.import_error_format');
                return $this->returnJson();
            }
            // 获取文件数据结果集
            $format_result_list = $format_result['data'];
            // 移除第一行表头
            array_shift($format_result_list);

            // 是否可上传
            $enable_upload = true;
            // 结果集
            $result = array();

            // 枚举值用于判断是否是枚举值
            $pos_vendor = PosVendorEnum::asArray();
            // 当前用户所拥有的场地权限
            $site_array = isSuperAdministrator() ? Site::pluck('site_number')->toArray() : auth()->user()->site_number_list->toArray();
            foreach ($format_result_list as $item) {
                $check_rules = $this->importVioskRules($item, $result_keys, $pos_vendor, $site_array);
                if (!$check_rules['check_result']) $enable_upload = false;

                $result_item = array(
                    'check_result' => $check_rules['check_result'],
                    'error_message' => $check_rules['error_message'],
                    'viosk_number' => $item['viosk_number'] ?? '',
                    'name' => $item['name'] ?? '',
                    'viosk_setting_number' => $item['viosk_setting_number'] ?? '',
                    'site_number' => $item['site_number'] ?? '',
                    'zone_number' => $item['zone_number'] ?? '',
                    'charge_point_number' => $item['charge_point_number'] ?? '',
                    'charge_point_com_port' => $item['charge_point_com_port'] ?? '',
                    'charge_point_baud_rate' => $item['charge_point_baud_rate'] ?? '',
                    'octopus_com_port' => $item['octopus_com_port'] ?? '',
                    'octopus_device_number' => $item['octopus_device_number'] ?? '',
                    'pos_vendor' => $item['pos_vendor'] ?? '',
                    'yedpay_pos_api_url' => $item['yedpay_pos_api_url'] ?? '',
                    'yedpay_pos_api_key' => $item['yedpay_pos_api_key'] ?? '',
                    'yedpay_pos_pre_authorization_amount' => $item['yedpay_pos_pre_authorization_amount'] ?? '',
                    'soe_pay_pos_com_port' => $item['soe_pay_pos_com_port'] ?? '',
                    'soe_pay_pos_baud_rate' => $item['soe_pay_pos_baud_rate'] ?? '',
                    'soe_pay_pos_api_key' => $item['soe_pay_pos_api_key'] ?? '',
                    'soe_pay_pos_api_token' => $item['soe_pay_pos_api_token'] ?? '',
                    'soe_pay_pos_pre_authorization_amount' => $item['soe_pay_pos_pre_authorization_amount'] ?? '',
                    'sort_order' => $item['sort_order'] ?? 0,
                    'remark' => $item['remark'] ?? '',
                );

                $result[] = $result_item;
            }

            $this->data = array(
                'result_list' => $result,
                'enable_upload' => $enable_upload,
            );

            return $this->returnJson();
        } catch (Exception) {
            $this->code = 20001;
            $this->data = null;
            // 提示信息
            $this->message = __('common.import_error_format');
            return $this->returnJson();
        }
    }

    /**
     * 导入
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-11-08
     */
    public function importViosk(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        // viosk列表
        $viosk_list = $request->input('viosk_list', array());

        if (empty($viosk_list)) {
            $this->missingField('VIOSK');
            return $this->returnJson();
        }

        $result_keys = array(
            'viosk_number',
            'name',
            'viosk_setting_number',
            'site_number',
            'zone_number',
            'charge_point_number',
            'charge_point_com_port',
            'charge_point_baud_rate',
            'octopus_com_port',
            'octopus_device_number',
            'pos_vendor',
            'yedpay_pos_api_url',
            'yedpay_pos_api_key',
            'yedpay_pos_pre_authorization_amount',
            'soe_pay_pos_com_port',
            'soe_pay_pos_baud_rate',
            'soe_pay_pos_api_key',
            'soe_pay_pos_api_token',
            'soe_pay_pos_pre_authorization_amount',
            'sort_order',
            'remark',
        );
        // 枚举值用于判断是否是枚举值
        $pos_vendor_list = PosVendorEnum::asArray();
        $illegal_viosk_number = array();
        // 当前用户所拥有的场地权限
        $site_array = isSuperAdministrator() ? Site::pluck('site_number')->toArray() : auth()->user()->site_number_list->toArray();
        // 当前用户所拥有的场地编号和商户编号，用于后面直接获取对应场地的商户编号而不需要多次查询数据库
        $site_merchant_map = array();
        if (isSuperAdministrator()) {
            $site_merchant_map = Site::pluck('merchant_number', 'site_number');
        } else {
            auth()->user()->loadMissing('roles.site');
            foreach (auth()->user()->roles as $role) {
                foreach ($role->site as $site) {
                    $site_merchant_map[$site->site_number] = $site->merchant_number;
                }
            }
        }
        foreach ($viosk_list as $item) {
            $check_rules = $this->importVioskRules($item, $result_keys, $pos_vendor_list, $site_array);
            if (!$check_rules['check_result']) {
                $this->code = 40001;
                $this->message = __('viosk.data_has_modified');
                continue;
            }
            // 存在更新，不存在新增 因为编号唯一所以这里不用判断商户
            $viosk_model = Viosk::firstOrNew(['viosk_number' => $item['viosk_number']]);

            // 如果用firstOrNew的话要用exists，如果用exists()会出现都为true的情况
            if ($viosk_model->exists) {
                // 如果存在并且Viosk场地编号不在权限范围内就不给修改
                if (!in_array($viosk_model->site_number, $site_array)) {
                    $illegal_viosk_number[] = $item['viosk_number'];
                    continue;
                }
                // 如果存在，并且相关设置被更改为不同场地时就不给修改
                if ($viosk_model->site_number != $item['site_number']) {
                    $site_no_relevant_setting_data[] = $item['viosk_number'];
                    continue;
                }
            } else {
                // 只有新增才能修改Viosk编号和场地及商户编号
                $viosk_model->viosk_number = $item['viosk_number'];
                $viosk_model->site_number = $item['site_number'];
                $viosk_model->merchant_number = $site_merchant_map[$item['site_number']];
            }
            $viosk_model->license_code = config('app.is_enable_automatic_encryption_of_license_code') && filled($item['viosk_number']) ? self::licenseCodeEncryption($item['viosk_number']) : null;
            $viosk_model->zone_number = $item['zone_number'];
            $viosk_model->name = $item['name'];
            $viosk_model->viosk_setting_number = $item['viosk_setting_number'];
            $viosk_model->charge_point_number = $item['charge_point_number'];
            $viosk_model->charge_point_com_port = $item['charge_point_com_port'];
            $viosk_model->charge_point_baud_rate = $item['charge_point_baud_rate'];
            $viosk_model->octopus_com_port = $item['octopus_com_port'];
            $viosk_model->octopus_device_number = $item['octopus_device_number'];
            $pos_vendor = $item['pos_vendor'] ?? '';
            $viosk_model->pos_vendor = $pos_vendor;
            if ($pos_vendor == PosVendorEnum::SoePay) {
                $viosk_model->soe_pay_pos_com_port = $item['soe_pay_pos_com_port'];
                $viosk_model->soe_pay_pos_baud_rate = $item['soe_pay_pos_baud_rate'];
                $viosk_model->soe_pay_pos_api_key = $item['soe_pay_pos_api_key'];
                $viosk_model->soe_pay_pos_api_token = $item['soe_pay_pos_api_token'];
                $viosk_model->soe_pay_pos_pre_authorization_amount = bcmul($item['soe_pay_pos_pre_authorization_amount'], 100);
            } else if ($pos_vendor == PosVendorEnum::Yedpay_PCG) {
                $viosk_model->yedpay_pos_api_url = $item['yedpay_pos_api_url'];
                $viosk_model->yedpay_pos_api_key = $item['yedpay_pos_api_key'];
                $viosk_model->yedpay_pos_pre_authorization_amount = bcmul($item['yedpay_pos_pre_authorization_amount'], 100);
            } else if ($pos_vendor == PosVendorEnum::Yedpay) {
                $viosk_model->yedpay_pos_api_url = $item['yedpay_pos_api_url'];
                $viosk_model->yedpay_pos_api_key = $item['yedpay_pos_api_key'];
            } else {
                $viosk_model->yedpay_pos_api_url = null;
                $viosk_model->yedpay_pos_api_key = null;
                $viosk_model->yedpay_pos_pre_authorization_amount = null;
                $viosk_model->soe_pay_pos_com_port = null;
                $viosk_model->soe_pay_pos_baud_rate = null;
                $viosk_model->soe_pay_pos_api_key = null;
                $viosk_model->soe_pay_pos_api_token = null;
                $viosk_model->soe_pay_pos_pre_authorization_amount = null;
            }
            $viosk_model->sort_order = $item['sort_order'] ?? 0;
            $viosk_model->remark = $item['remark'];
            $viosk_model->save();
        }

        if (!empty($site_no_relevant_setting_data)) {
            $this->code = 40002;
            $this->message = __("$module_name.viosk_number_no_relevant_setting_data") . ': ' . implode(', ', $site_no_relevant_setting_data);
        }

        if (!empty($illegal_viosk_number)) {
            $this->code = 40002;
            $this->message = __("$module_name.viosk_number_already_exists") . ': ' . implode(', ', $illegal_viosk_number);
        }

        return $this->returnJson();
    }

    public function importVioskRules($item, $result_keys, $pos_vendor_list, $site_array): array
    {
        $module_name = self::$module_name;
        if (empty($item) || array_diff($result_keys, array_keys($item))) return array(
            'check_result' => false,
        );
        // 校验结果
        $check_result = true;
        // 错误信息
        $error_message = null;
        // 如果存在空值不通过
        // viosk number, name, visok_setting_number, site_number
        $check_item_1 = array_slice($item, 0, 4);
        // octopus_com_port
        $check_item_2 = array_slice($item, 8, 1);

        // 校验是否为1到30位数字和字母组成的字符串
        $check_viosk_number = (isset($item['viosk_number']) && !empty($item['viosk_number']) && !preg_match('/^[a-zA-Z0-9]{1,30}$/', $item['viosk_number']));
        // 可能不填，不填就直接判断为false不用判断枚举值是否合法
        $check_enum_pos_vendor = false;
        if (!empty($item['pos_vendor'])) {
            // 判断是否是枚举值
            $check_enum_pos_vendor = (!in_array($item['pos_vendor'], $pos_vendor_list));
            if ($item['pos_vendor'] == PosVendorEnum::SoePay) {
                $check_item_2[14] = $item['soe_pay_pos_com_port'] ?? null;
                $check_item_2[15] = $item['soe_pay_pos_baud_rate'] ?? null;
                $check_item_2[16] = $item['soe_pay_pos_api_key'] ?? null;
                $check_item_2[17] = $item['soe_pay_pos_api_token'] ?? null;
                $check_item_2[18] = $item['soe_pay_pos_pre_authorization_amount'] ?? null;

            } else if ($item['pos_vendor'] == PosVendorEnum::Yedpay_PCG) {
                $check_item_2[11] = $item['yedpay_pos_api_url'] ?? null;
                $check_item_2[12] = $item['yedpay_pos_api_key'] ?? null;
                $check_item_2[13] = $item['yedpay_pos_pre_authorization_amount'] ?? null;
            } else if ($item['pos_vendor'] == PosVendorEnum::Yedpay) {
                $check_item_2[11] = $item['yedpay_pos_api_url'] ?? null;
                $check_item_2[12] = $item['yedpay_pos_api_key'] ?? null;
            }
        }
        // 必填
        $check_empty = in_array(null, $check_item_1, true) ||
            in_array('null', $check_item_1, true) ||
            in_array(null, $check_item_2, true) ||
            in_array('null', $check_item_2, true);
        // 校验非必填时如果填写是否为数字
        $check_sort_order = (isset($item['sort_order']) && !is_null($item['sort_order']) && !preg_match('/^\d+$/', $item['sort_order']));
        $check_charge_point_baud_rate = (isset($item['charge_point_baud_rate']) && !is_null($item['charge_point_baud_rate']) && !preg_match('/^\d+$/', $item['charge_point_baud_rate']));
        $check_soe_pay_pos_baud_rate = (isset($item['soe_pay_pos_baud_rate']) && !is_null($item['soe_pay_pos_baud_rate']) && !preg_match('/^\d+$/', $item['soe_pay_pos_baud_rate']));
        // 判断该场地是否有权限并且存在
        $check_site = !in_array($item['site_number'], $site_array);
        $check_viosk_setting = $check_zone = false;
        /* 判断该场地下是否存在对应的设置，并且判断仅在有场地编号时才进入判断*/
        if (!$check_site) {
            $check_viosk_setting = VioskSetting::where('site_number', $item['site_number'])->where('viosk_setting_number', $item['viosk_setting_number'])->doesntExist();

            $check_zone = (filled($item['zone_number']) && Zone::where('site_number', $item['site_number'])->where('zone_number', $item['zone_number'])->doesntExist());
        }

        // 校验不通过disable
        if ($check_empty || $check_sort_order || $check_charge_point_baud_rate || $check_soe_pay_pos_baud_rate || $check_viosk_number || $check_enum_pos_vendor || $check_site || $check_zone || $check_viosk_setting) {
            $check_result = false;
            $error_messages = [];

            if ($check_empty) {
                $error_messages[] = __("$module_name.fill_required_data");
            }
            if ($check_sort_order) {
                $error_messages[] = __("$module_name.sort_order") . __("$module_name.fill_number");
            }
            if ($check_charge_point_baud_rate) {
                $error_messages[] = __("$module_name.charge_point_baud_rate") . __("$module_name.fill_number");
            }
            if ($check_soe_pay_pos_baud_rate) {
                $error_messages[] = __("$module_name.soe_pay_pos_baud_rate") . __("$module_name.fill_number");
            }
            if ($check_viosk_number) {
                $error_messages[] = __("$module_name.viosk_number_must_letter_number");
            }
            if ($check_enum_pos_vendor) {
                $error_messages[] = __("$module_name.pos_vendor_does_not_exist");
            }
            if ($check_viosk_setting) {
                $error_messages[] = __("$module_name.viosk_setting_does_not_exist");
            }
            if ($check_site) {
                $error_messages[] = __("$module_name.site_does_not_exist");
            }
            if ($check_zone) {
                $error_messages[] = __("$module_name.zone_does_not_exist");
            }

            // 将错误消息数组连接成一个字符串，用 <br> 分隔
            $error_message = implode('<br>', $error_messages);
        }

        return array(
            'check_result' => $check_result,
            'error_message' => $error_message
        );
    }

}

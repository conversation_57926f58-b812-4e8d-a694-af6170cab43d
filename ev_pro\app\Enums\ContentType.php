<?php

namespace App\Enums;

use BenSampo\Enum\{
    Enum,
    Contracts\LocalizedEnum
};
use App\Enums\Traits\Tools;

/**
 * @method static Text
 * @method static Html
 * @method static Markdown
 * @method static InAppLink
 * @method static ExternalLink
 */
final class ContentType extends Enum implements LocalizedEnum
{
    use Tools;

    const Text = 'TEXT';
    const Html = 'HTML';
    const Markdown = 'MARKDOWN';
    const InAppLink = 'IN_APP_LINK';
    const ExternalLink = 'EXTERNAL_LINK';
}

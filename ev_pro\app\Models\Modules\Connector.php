<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;

class Connector extends Model
{
    protected $table = 'connector'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'connector_id'; //定义主键，默认为id
    // 表的唯一number
    public static $table_number = 'connector_number';
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    // protected $fillable = [];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 类型转化器
     */
    protected $casts = [
        'is_bypass_mode' => 'bool', // 是否旁路模式
        'is_display' => 'bool',
        'is_enable' => 'bool',
        'is_support_unlock_connector' => 'bool',
        'is_fixed_maximum_current' => 'bool',
    ];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [
        'is_bypass_mode' => false, // 是否旁路模式
        'is_display' => true, // 是否显示
        'is_enable' => true, // 是否启用
        'sort_order' => 0, // 排序
        'is_support_unlock_connector' => 0, // 是否支持解锁充电枪
        'is_fixed_maximum_current' => false, // 是否固定最大电流
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * 一对一关联 关联 Charge Record
     */
    public function chargeRecord()
    {
        return $this->hasOne(ChargeRecord::class, 'charge_record_number', 'current_charge_record_number');
    }

    /**
     * 一对一关联 关联 Charge Point
     */
    public function chargePoint()
    {
        return $this->hasOne(ChargePoint::class, 'charge_point_number', 'charge_point_number');
    }

    /**
     * 一对一关联 Connector Setting
     */
    public function setting()
    {
        return $this->hasOne(ConnectorSetting::class, 'connector_setting_number', 'connector_setting_number');
    }

    // 一对一关联zone
    public function zone()
    {
        return $this->hasOne(Zone::class, 'zone_number', 'zone_number');
    }

    /**
     * 一对多关联description
     */
    public function description()
    {
        return $this->hasMany(ConnectorDescription::class, 'connector_number', 'connector_number');
    }

    // 一对一关联site
    public function site()
    {
        return $this->hasOne(Site::class, 'site_number', 'site_number');
    }
}

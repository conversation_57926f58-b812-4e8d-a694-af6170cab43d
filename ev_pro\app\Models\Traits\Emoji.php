<?php

namespace App\Models\Traits;

trait Emoji
{
    public static function bootEmoji()
    {
//        static::saving(function ($model) {// 保存事件(添加和編輯)
//            foreach ($model->attributesToArray() as $attribute => $value) {
//                if (is_string($model->$attribute)) $model->$attribute = emojiEncode($model->$attribute);
//            }
//        });

//        static::retrieved(function ($model) {// 查詢事件
//            foreach ($model->attributesToArray() as $attribute => $value) {
//                if (is_string($model->$attribute)) $model->$attribute = html_entity_decode($model->$attribute);
//            }
//        });
    }
}

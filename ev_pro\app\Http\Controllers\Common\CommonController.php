<?php

namespace App\Http\Controllers\Common;

use App\Http\Controllers\Controller;
use App\Models\Modules\{
    ChargeRecord,
    Connector,
    EventLog,
    EventLogNotifyRule,
    Kiosk,
    Merchant,
    NotifyEventLog,
    Site,
};
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Foundation\Application;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use Illuminate\Http\{
    JsonResponse,
    Request,
};
use Illuminate\Support\Facades\{
    App,
    Config,
};
use GatewayClient\Gateway;
use PhpOffice\PhpSpreadsheet\Reader\Exception;
use Spatie\Permission\PermissionRegistrar;
use App\Enums\{
    EventTypeEnum,
    WebsocketType,
};
use GuzzleHttp\{
    Client,
    Exception\GuzzleException,
    Psr7,
    Exception\ClientException,
};
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Mail;

class CommonController extends Controller
{
    public int $code = 200;
    public mixed $data = null;
    public string $message = '';
    public int $limit = 10;
    public string $language_code = 'en_US';
    // 默认商户标识 - EVPro
    public static int $default_merchant_id = -1;
    protected static string $websocket_push_path = '';
    protected static string $websocket_web_push_path = '';

    public function __construct(Request $request)
    {
        $language = $request->headers->get('language') ? $request->headers->get('language') : 'en_US'; //获取header中设置的语言
        if (array_key_exists($language, Config::get('languages'))) { //判断code是否存在
            App::setLocale($language); //设置为当前语言
            $this->language_code = App::getLocale();
        }
        // websocket路径赋值
        self::$websocket_push_path = env('WEBSOCKET_HOST') . ':' . env('WEBSOCKET_KIOSK_PUSH_PORT');
        self::$websocket_web_push_path = env('WEBSOCKET_HOST') . ':' . env('WEBSOCKET_WEB_PUSH_PORT');
    }

    /**
     * 设置语言
     * @param string|null $language_code
     * @return void
     * @Description
     * @example
     * @date 2023-06-08
     */
    protected static function setLanguage(?string $language_code): void
    {
        if (filled($language_code) && array_key_exists($language_code, config('languages'))) {
            App::setLocale($language_code);
        }
    }

    /**
     * Onesignal 推送 最新版
     *
     * @param array $title
     * ['en' => 'English', 'zh-Hans' => '簡體中文', 'zh-Hant' => '繁體中文']
     * @param array $content
     * ['en' => 'English', 'zh-Hans' => '簡體中文', 'zh-Hant' => '繁體中文']
     * @param array $data 自定義傳入的數據
     * @param array $include_player_ids 需要發送推送的用戶的ID
     * @param array $include_external_user_ids 自定义用户ID
     * @param string $push_time 推送時間，格式：2022-02-22 12:00:00
     * @return array
     * @Description
     * @example
     */
    public static function push(array $title = [], array $content = [], array $data = [], array $include_player_ids = [], array $include_external_user_ids = [], string $push_time = null): array
    {
        $return = [
            'code' => 200,
            'data' => null,
            'message' => null
        ];

        $app_id = env('PUSH_ONESIGNAL_APP_ID');
        $authorization = env('PUSH_ONESIGNAL_AUTHORIZATION');

        // 未配置推送參數，返回錯誤
        if (blank($app_id) || blank($authorization)) {
            $error_msg = 'Push failed! Onesignal app_id or authorization is empty.';
            logger($error_msg);
            $return['code'] = 500;
            $return['message'] = $error_msg;
            return $return;
        }

        if (empty($data)) $data = ['foo' => 'bar'];

        $fields = [
            'app_id' => $app_id,
            'data' => $data,
            'contents' => $content,
            'target_channel' => 'push',
        ];

        // 推送给指定用戶的token
        if (filled($include_player_ids)) {
            $fields['include_subscription_ids'] = $include_player_ids;
        }
        // 推送給指定用戶的
        if (filled($include_external_user_ids)) {
            $fields['include_aliases']['external_id'] = $include_external_user_ids;
        }
        // 沒有指定用戶，發送給訂閱用戶
        if (blank($include_player_ids) && blank($include_external_user_ids)) {
            $fields['included_segments'] = [
                'Subscribed Users'
            ];
        }

        if (filled($title)) {
            $headings = $title;
            $fields['headings'] = $headings;
        }

        // 设定推送时间
        if ($push_time) {
            // 拼接时区，默认为香港时区
            $push_time .= ' GMT+0800';
            $fields['send_after'] = $push_time;
        }

        $fields = json_encode($fields);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://api.onesignal.com/notifications");
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json; charset=utf-8', 'Authorization: KEY ' . $authorization));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($ch, CURLOPT_HEADER, FALSE);
        curl_setopt($ch, CURLOPT_POST, TRUE);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $fields);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);

        $response = curl_exec($ch);
        $err = curl_error($ch);
        curl_close($ch);

        // 请求错误
        if ($err) {
            $error_msg = 'Onesignal request failed! ' . $err;
            logger($error_msg);
            logger('params: ' . json_encode($fields));
            $return['code'] = 501;
            $return['message'] = $error_msg;
            return $return;
        }

        $response = json_decode($response, true);

        // 解析返回结果JSON失败
        if (!is_array($response)) {
            $error_msg = 'Onesignal response is not a valid json!';
            logger($error_msg);
            logger('params: ' . json_encode($fields));
            $return['code'] = 502;
            $return['message'] = $error_msg;
            return $return;
        }

        // 推送返回结果不存在id字段，返回错误
        if (!isset($response['id'])) {
            $error_msg = 'Onesignal response errors! ' . json_encode($response['errors']);
            logger($error_msg);
            logger('params: ' . json_encode($fields));
            $return['code'] = 503;
            $return['message'] = $error_msg;
            return $return;
        }

        // 推送返回结果存在errors字段，记录日志
        if (isset($response['errors'])) {
            logger('Onesignal response has errors!But response code is 200! Response: ' . json_encode($response));
        }

        $return['data'] = $response;

        return $return;
    }

    /**
     * Onesignal 推送
     *
     * @param array $title
     * ['en' => 'English', 'zh-Hans' => '簡體中文', 'zh-Hant' => '繁體中文']
     * @param array $content
     * ['en' => 'English', 'zh-Hans' => '簡體中文', 'zh-Hant' => '繁體中文']
     * @param array $data 自定義傳入的數據
     * @param array $include_player_ids 需要發送推送的用戶的ID
     * @param array $include_external_user_ids 自定义用户ID
     * @return array
     * @Description
     * @example
     */
    public static function _push(array $title = [], array $content = [], array $data = [], array $include_player_ids = [], array $include_external_user_ids = []): array
    {
        $return = [
            'code' => 200,
            'data' => null,
            'message' => null
        ];

        $app_id = env('PUSH_ONESIGNAL_APP_ID');
        $authorization = env('PUSH_ONESIGNAL_AUTHORIZATION');

        // 未配置推送參數，返回錯誤
        if (blank($app_id) || blank($authorization)) {
            $error_msg = 'Push failed! Onesignal app_id or authorization is empty.';
            logger($error_msg);
            $return['code'] = 500;
            $return['message'] = $error_msg;
            return $return;
        }

        if (empty($data)) $data = ['foo' => 'bar'];

        $fields = [
            'app_id' => $app_id,
            'data' => $data,
            'contents' => $content,
        ];

        // 推送给指定用戶的token
        if (filled($include_player_ids)) {
            $fields['include_player_ids'] = $include_player_ids;
        }
        // 推送給指定用戶的
        if (filled($include_external_user_ids)) {
            $fields['include_external_user_ids'] = $include_external_user_ids;
        }
        // 沒有指定用戶，發送給訂閱用戶
        if (blank($include_player_ids) && blank($include_external_user_ids)) {
            $fields['included_segments'] = [
                'Subscribed Users'
            ];
        }

        if ($title) {
            $headings = $title;
            $fields['headings'] = $headings;
        }

        $fields = json_encode($fields);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://onesignal.com/api/v1/notifications");
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json; charset=utf-8', 'Authorization: Basic ' . $authorization));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($ch, CURLOPT_HEADER, FALSE);
        curl_setopt($ch, CURLOPT_POST, TRUE);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $fields);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);

        $response = curl_exec($ch);
        $err = curl_error($ch);
        curl_close($ch);

        // 请求错误
        if ($err) {
            $error_msg = 'Onesignal request failed! ' . $err;
            logger($error_msg);
            logger('params: ' . json_encode($fields));
            $return['code'] = 501;
            $return['message'] = $error_msg;
            return $return;
        }

        $response = json_decode($response, true);

        // 解析返回结果JSON失败
        if (!is_array($response)) {
            $error_msg = 'Onesignal response is not a valid json!';
            logger($error_msg);
            logger('params: ' . json_encode($fields));
            $return['code'] = 502;
            $return['message'] = $error_msg;
            return $return;
        }

        // 推送返回结果不存在id字段，返回错误
        if (!isset($response['id'])) {
            $error_msg = 'Onesignal response errors! ' . json_encode($response['errors']);
            logger($error_msg);
            logger('params: ' . json_encode($fields));
            $return['code'] = 503;
            $return['message'] = $error_msg;
            return $return;
        }

        // 推送返回结果存在errors字段，记录日志
        if (isset($response['errors'])) {
            logger('Onesignal response has errors!But response code is 200! Response: ' . json_encode($response));
        }

        return $return;
    }

    /**
     * Websocket发送重新请求初始化数据
     */
    public static function sendInitPushWeb($data = null): void
    {
        self::websocketPushWeb(WebsocketType::NotifyUpdatedNotification, $data);
    }

    /**
     * Websocket 推送消息 web
     */
    public static function websocketPushWeb($type = null, $data = null): void
    {
        if (WebsocketType::hasValue($type)) {
            try {
                Gateway::$registerAddress = self::$websocket_web_push_path;
                $push_data = array($type);
                if ($data) {
                    $push_data[] = $data;
                }
                Gateway::sendToAll(json($push_data));
            } catch (\Throwable $e) {
                report($e);
            }
        }
    }

    /**
     * Websocket发送重新请求初始化数据
     */
    public static function sendInitPush($data = null, $kiosk_number = null): void
    {
        self::websocketPush(WebsocketType::ReloadInitializationDataNotification, $data, $kiosk_number);
    }

    /**
     * Websocket 推送消息
     */
    public static function websocketPush($type = null, $data = null, $kiosk_number = null, $client_id = null): void
    {
        if (WebsocketType::hasValue($type)) {
            try {
                Gateway::$registerAddress = self::$websocket_push_path;
                $push_data = array($type);
                if (filled($data)) $push_data[] = $data;
                if (filled($client_id)) $push_data[] = $client_id;

                // 有傳入 kiosk_number，則只推送給該 kiosk_number 的客戶端，否則推送給所有客戶端
                if (filled($kiosk_number)) {
                    if (env('WEBSOCKET_PUSH_API_URL')) {
                        $url = env('WEBSOCKET_PUSH_API_URL') . '/webSocketCallKiosk';
                        if (!is_array($kiosk_number)) $kiosk_number = [$kiosk_number];
                        // 转为字符串
                        $kiosk_number = implode(',', $kiosk_number);
                        $post_data = [
                            'kiosk_number_list_string' => $kiosk_number,
                            'message' => json($push_data),
                        ];
                        $response = self::curlPost($url, $post_data);
                        if (Str::isJson($response)) {
                            $response = json_decode($response, true);
                        }
                        if (!isset($response['code']) || $response['code'] !== 200) {
                            report('Websocket push error, send: ' . json($post_data) . ', response' . json($response));
                        }
                    }
                    Gateway::sendToUid($kiosk_number, json($push_data));
                } else {
                    if (env('WEBSOCKET_PUSH_API_URL')) {
                        $url = env('WEBSOCKET_PUSH_API_URL') . '/webSocketCallAllKiosk';
                        $post_data = [
                            'message' => json($push_data),
                        ];
                        $response = self::curlPost($url, $post_data);
                        if (Str::isJson($response)) {
                            $response = json_decode($response, true);
                        }
                        if (!isset($response['code']) || $response['code'] !== 200) {
                            report('Websocket push error, send: ' . json($post_data) . ', response' . json($response));
                        }
                    }
                    Gateway::sendToAll(json($push_data));
                }
            } catch (\Throwable $e) {
                report($e);
            }
        }
    }

    /**
     * Websocket 推送消息
     */
    public static function websocketAllUid(): array
    {
        try {
            Gateway::$registerAddress = self::$websocket_push_path;

            $uid_list = array();

            foreach (Gateway::getAllUidList() as $key => $value) {
                $uid_list[] = $value;
            }

            return $uid_list;
        } catch (\Throwable $e) {
            report($e);

            return array();
        }
    }

    /**
     * Websocket 判断是否在线
     */
    public static function websocketIsUidOnline($uid): bool
    {
        try {
            Gateway::$registerAddress = self::$websocket_push_path;

            return Gateway::isUidOnline($uid);
        } catch (\Throwable $e) {
            report($e);

            return false;
        }
    }

    /**
     * Websocket 判断KIOSK是否在线
     */
    public static function websocketIsUidOnlineByKioskList($kiosk_list): array
    {
        // 为空不处理
        if (blank($kiosk_list)) return [];

        $online_kiosk_list = array();

        try {
            // 连接websocket
            Gateway::$registerAddress = self::$websocket_push_path;

            foreach ($kiosk_list as $kiosk) {
                // 判断是否在线
                if (Gateway::isUidOnline($kiosk->kiosk_number)) {
                    $online_kiosk_list[] = $kiosk;
                }
            }
        } catch (\Throwable $e) {
            report($e);

            return [];
        }

        return $online_kiosk_list;
    }

    /**
     * Websocket 判断kiosk列表是否在线及是否可用
     *
     * @param $kiosks
     * @return array // 不可编辑kiosk列表
     */
    public static function websocketIsUidOnlineByKiosks($kiosk_list): array
    {
        // 为空不处理
        if (blank($kiosk_list)) return [];

        $online_kiosk_list = array();

        try {
            // 连接websocket
            Gateway::$registerAddress = self::$websocket_push_path;

            foreach ($kiosk_list as $kiosk) {
                // 判断是否在线
                if (Gateway::isUidOnline($kiosk->kiosk_number)) {
                    // 当前显示页面为首页或holdMessage时返回不可编辑的kiosk
                    match ($kiosk->current_display_screen) {
                        'Home', 'Hold Message' => '',
                        default => $online_kiosk_list[] = $kiosk,
                    };
                }
            }
        } catch (\Throwable $e) {
            report($e);
        }

        return $online_kiosk_list;
    }

    /**
     * Websocket 判断是否全部在线及是否可用
     *
     * @return array // 不可编辑kiosk列表
     */
    public static function websocketIsAllUidOnline(): array
    {
        $kiosk_list = array();
        try {
            // 连接websocket
            Gateway::$registerAddress = self::$websocket_push_path;

            foreach (Kiosk::get() as $kiosk) {
                // 判断是否在线
                if (Gateway::isUidOnline($kiosk->kiosk_number)) {
                    // 当前显示页面为首页或holdMessage时返回不可编辑的kiosk
                    match ($kiosk->current_display_screen) {
                        'Home', 'Hold Message' => '',
                        default => $kiosk_list[] = $kiosk,
                    };
                }
            }
        } catch (\Throwable $e) {
            report($e);
        }

        return $kiosk_list;
    }

    /**
     * 根据收费表获取connector并更改token
     *
     * @param $model // 收费表模型
     */
    public static function setConnectorTokenByTariffTable($model): void
    {
        if (blank($model)) return;

        $connector_number_list = array();
        foreach ($model->connectorSetting as $connector_setting) {
            foreach ($connector_setting->connector as $connector) {
                $connector_number_list[$connector->connector_number] = $connector->connector_number;
            }
        }

        Connector::whereIn('connector_number', $connector_number_list)
            ->update(['setting_token' => (int)(microtime(true) * 1000)]);
    }

    /**
     * 根据connector setting获取connector并更改token
     *
     * @param $model // 收费表模型
     */
    public static function setConnectorTokenBySetting($model): void
    {
        // 获取关联的connector
        // 一层一层判断
        if (empty($model?->connector)) return;

        $model->connector()->update(['setting_token' => (int)(microtime(true) * 1000)]);
    }

    /**
     * 获取所有connector并更改token
     */
    public static function setSiteAllConnectorToken($site_number): void
    {
        if (blank($site_number)) return;

        if (!is_array($site_number)) $site_number = [$site_number];

        Connector::whereIn('site_number', $site_number)->update(['setting_token' => (int)(microtime(true) * 1000)]);
    }

    protected static function _404Page(): View|Application|Factory
    {
        $pageConfigs = ['bodyCustomClass' => 'bg-full-screen-image'];
        return view('pages.error-404', ['pageConfigs' => $pageConfigs]);
    }

    /**
     * 将一个 datetime - datetime 格式的字符串转换为数组
     *
     * @param string $range_datetime
     * @return array|false
     * @Description 格式正确返回数组;否则返回false
     * @example
     * <AUTHOR>
     * @date 2022-01-22
     */
    public function getRangeDateTimeArray(string $range_datetime): false|array
    {
        if (!empty($range_datetime)) {
            $datetime_arr = explode(' - ', $range_datetime);
            if (count($datetime_arr) === 2) {
                return $datetime_arr;
            }
        }

        return false;
    }

    /**
     * 計算時間差
     *
     * @param int $begin_time
     * @param int $end_time
     * @return array
     * @Description 計算兩個時間日期相隔的天數、時、分、秒
     * @example
     * <AUTHOR>
     * @date 2022-03-10
     */
    public function timeDiff(int $begin_time, int $end_time): array
    {
        if ($begin_time < $end_time) {
            $start_time = $begin_time;
            //$end_time = $end_time;
        } else {
            $start_time = $end_time;
            $end_time = $begin_time;
        }
        $time_diff = $end_time - $start_time;
        $days = intval($time_diff / 86400);
        $remain = $time_diff % 86400;
        $hours = intval($remain / 3600);
        $remain = $remain % 3600;
        $mins = intval($remain / 60);
        $secs = $remain % 60;
        return array("day" => $days, "hour" => $hours, "min" => $mins, "sec" => $secs);
    }

    /**
     * Excel导入
     * @param Request $request
     * @return array
     * @throws Exception
     */
    public function _import(Request $request, $keys = null): array
    {
        set_time_limit(0);

        if ($request->hasFile('import') && $request->file('import')->isValid()) {
            $accept_formats = array('xls', 'xlsx');
            if (in_array($request->file('import')->extension(), $accept_formats)) {
                if ($request->file('import')->extension() == 'xlsx') {
                    $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader('Xlsx');
                } else {
                    $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader('Xls');
                }
                // 创建读操作
                $spreadsheet = $reader->setReadDataOnly(true)
                    ->setReadEmptyCells(false)
                    ->load($request->file('import')->path());            // 打开文件、载入excel表格
                $sheet = $spreadsheet->getActiveSheet();        // 获取活动工作簿

                $raw_data = $sheet->toArray(); // 原数据
                $import_data = array(); // 导出数据
                // 如果传入的key为空就直接输出原数据
                if ($keys == null || count($keys) <= 0) {
                    $import_data = $raw_data;
                } else {
                    foreach ($raw_data as $row) {
                        $row_data = array();
                        foreach ($keys as $index => $key) {
                            $row_data[$key] = $row[$index] ?? null;
                        }
                        $import_data[] = $row_data;
                    }
                }

                $this->data = $import_data;
            } else {
                $this->code = 40101;
                $this->data = null;
                $this->message = __('common.import_error_format');
            }
        } else {
            $this->code = 40102;
            $this->data = null;
            $this->message = __('common.import_upload_failed');
        }

        return array(
            'code' => $this->code,
            'data' => $this->data,
            'message' => $this->message
        );
    }

    public function _importDataAndKeys(Request $request, $keys = null): array
    {
        set_time_limit(0);

        if ($request->hasFile('import') && $request->file('import')->isValid()) {
            $accept_formats = array('xls', 'xlsx');
            if (in_array($request->file('import')->extension(), $accept_formats)) {
                if ($request->file('import')->extension() == 'xlsx') {
                    $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader('Xlsx');
                } else {
                    $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader('Xls');
                }
                // 创建读操作
                $spreadsheet = $reader->setReadDataOnly(true)
                    ->setReadEmptyCells(false)
                    ->load($request->file('import')->path());            // 打开文件、载入excel表格
                $sheet = $spreadsheet->getActiveSheet();        // 获取活动工作簿

                $raw_data = $sheet->toArray(); // 原数据
                $import_data = array(); // 导出数据
                // 如果传入的key为空就直接输出原数据
                if ($keys == null || count($keys) <= 0) {
                    $import_data = $raw_data;
                } else {
                    foreach ($raw_data as $row) {
                        $row_data = array();
                        foreach ($keys as $index => $key) {
                            $row_data[$key] = $row[$index] ?? null;
                        }
                        $import_data[] = $row_data;
                    }
                }

                $this->data = $import_data;
            } else {
                $this->code = 40101;
                $this->data = null;
                $this->message = __('common.import_error_format');
            }
        } else {
            $this->code = 40102;
            $this->data = null;
            $this->message = __('common.import_upload_failed');
        }

        return array(
            'code' => $this->code,
            'data' => $this->data,
            'message' => $this->message
        );
    }

    /**
     * 手动重置缓存
     *
     * @return void
     * @Description
     * @throws BindingResolutionException
     * <AUTHOR>
     * @date 2022-02-17
     * @example
     */
    public function clearPermissionCache(): void
    {
        app()->make(PermissionRegistrar::class)->forgetCachedPermissions();
    }

    /**
     * @param $data
     * @param $file_name
     * @return void
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function _export($data, $file_name)
    {
        set_time_limit(0);

        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();     // 实例化 Spreadsheet 对象
        $sheet = $spreadsheet->getActiveSheet();                        // 获取活动工作簿

        // array_unshift($data, $excel_header);//将表头插入数组第一个

        //批量赋值
        $sheet->fromArray($data);
        //设置单元格的宽度
        $the_last_one = 'B';
        $index = 0;
        foreach ($sheet->getColumnIterator() as $column) {
            $the_last_one = $column->getColumnIndex();
            // 通过表头计算长度
            $sheet->getColumnDimension($the_last_one)->setWidth(strlen($data[0][$index]) + 2);
            $index++;
        }

        //设置单元格样式
        $styleArray = [
            'font' => [
                'bold' => false
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['argb' => '666666'],
                ],
            ],
        ];
        $total_rows = 1;

        if (count($data) != 0) $total_rows = count($data);

        // 转为字符串
        $set_array_style = "A1:$the_last_one$total_rows";
        $set_title_style = "A1:$the_last_one" . "1";

        $sheet->getStyle($set_array_style)->applyFromArray($styleArray);
        $sheet->getStyle($set_title_style)->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('c0c0c0');


        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $file_name .= ' ' . date('Y-m-d H:i:s') . '.xlsx';
        // $writer->save(DIR_DOWNLOAD . $file_name);

        // 客户端文件下载
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $file_name . '"');
        header('Cache-Control: max-age=0');
        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
        exit;
    }

    protected function returnJson(): JsonResponse
    {
        $json = array(
            'code' => $this->code,
            'data' => $this->data,
            'message' => $this->message,
            'gmt_current' => date('Y-m-d H:i:s'),
        );

        return response()->json($json);
    }

    protected function modelSaveFail(): void
    {
        $this->code = 40001;
        $this->message = __('common.model_save_fail');
    }

    protected function missingField(string $field_name): void
    {
        $this->code = 40002;
        $this->message = __('common.the_field_is_missing_the_correct_value', ['field' => $field_name]);
    }

    protected function notFoundData(string $name): void
    {
        $this->code = 40003;
        $this->message = __('common.text_not_found', ['field' => $name]);
    }

    protected function alreadyExists(string $field_name): void
    {
        $this->code = 40004;
        $this->message = __('common.text_already_exists', ['field' => $field_name]);
    }

    protected function verificationCodeError(): void
    {
        $this->code = 40005;
        $this->message = __('common.the_verification_code_is_incorrect_or_expired');
    }

    protected function usernameOrPasswordError(): void
    {
        $this->code = 40006;
        $this->message = __('common.the_account_or_password_is_incorrect');
    }

    protected function checkTelephoneValid(string $telephone): bool
    {
        if (blank($telephone)) {
            $this->missingField('Telephone');
            return false;
        }
        $pattern = '/\+[0-9]{1,3}[\s.-]?[0-9]{4,11}$/';
        if (preg_match($pattern, $telephone)) {
            return true;
        }
        $this->code = 40007;
        $this->message = __('common.the_field_is_format_error', ['field' => 'telephone']);
        return false;
    }

    protected function checkEmailValid(string $email): bool
    {
        if (blank($email)) {
            $this->missingField('Email');
            return false;
        }
        list($email_name, $email_domain) = explode('@', $email);
        if (filled($email_name) && filled($email_domain) && checkdnsrr($email_domain)) {
            return true;
        }
        $this->code = 40007;
        $this->message = __('common.the_field_is_format_error', ['field' => 'email']);
        return false;
    }

    protected function returnPageData($data): void
    {
        $this->data['currentPage'] = $data->currentPage();
        $this->data['pageCount'] = $data->lastPage();
        $this->data['total'] = $data->total();
        $this->data['limit'] = $data->perPage();
    }

    protected function dataTablesNull($draw): JsonResponse
    {
        $dataTablesNull = array(
            'draw' => $draw,
            'recordsTotal' => 0,
            'recordsFiltered' => 0,
            "data" => array()
        );
        return response()->json($dataTablesNull);
    }

    /**
     * curl post
     *
     * @param string $url
     * @param array $post_data
     * @param array $headers
     * @return string
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-02-23
     */
    public static function curlPost(string $url, array $post_data = array(), array $headers = array()): string
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        // post数据
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        // 头部
        if (!empty($headers)) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        }

        /* curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        curl_setopt($ch, CURLOPT_USERPWD, "$username:$password"); */
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($post_data));
        // 执行请求
        $output = curl_exec($ch);
        $err = curl_error($ch);
        /*$status_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        echo($status_code);*/
        curl_close($ch);

        return !empty($err) ? $err : $output;
//        return $output;
    }

    public static function curlPostJson(string $url, string $post_data = '', array $headers = array()): string
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        // post数据
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);// 忽略验证证书
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);// 忽略验证主机
        // 头部
        if (!empty($headers)) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        }

        /* curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        curl_setopt($ch, CURLOPT_USERPWD, "$username:$password"); */
        // 设置请求体为JSON格式
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
        // 执行请求
        $output = curl_exec($ch);
        $err = curl_error($ch);
        /* $status_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        echo($status_code); */
        curl_close($ch);
        //打印获得的数据

        return !empty($err) ? $err : $output;
        // return $output;
    }

    /**
     * curl get
     *
     * @param string $url
     * @return string
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-02-23
     */
    protected static function curlGet(string $url): string
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        // post数据
        // curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        /* curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        curl_setopt($ch, CURLOPT_USERPWD, "$username:$password"); */
        // curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($post_data));
        $output = curl_exec($ch);
        $err = curl_error($ch);
        /* $status_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        echo($status_code); */
        curl_close($ch);
        //打印获得的数据

        return !empty($err) ? $err : $output;
        // return $output;
    }

    /**
     * @param string $url
     * @param array $headers
     * @param int $connectTimeout
     * @param int $timeout
     * @return string
     */
    public static function curlGetWithHeaders(string $url, array $headers = array()): string
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 忽略验证主机
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 忽略验证证书
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");  // 设置请求方法为GET
        // 头部
        if (!empty($headers)) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        }
        $output = curl_exec($ch);
        $err = curl_error($ch);
        curl_close($ch);
        //打印获得的数据
        return !empty($err) ? $err : $output;
    }

    /**
     * api用，判断是否拿到的是空数据，空数据返回NULL
     * @param $data_list
     * @return mixed|null
     */
    public function getArray($data_list = null): mixed
    {
        return filled($data_list) ? $data_list : null;
    }

    /**
     * 通过http client发送请求
     *
     * @param string $url
     * @param array $params
     * @param array $headers
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public static function request(string $url, array $params = array(), array $headers = array()): array
    {
        $client = new Client;
        $code = 201;

        try {
            $response = $client->request('POST', $url, array(
                'headers' => $headers,
                'query' => $params,
            ));

            if ($response->getStatusCode() == 200) {
                $code = $response->getStatusCode();
                $message = $response->getBody()->getContents();
            }
        } catch (ClientException $e) {
            $message = Psr7\Message::toString($e->getResponse());
        } catch (\Exception $e) {
            $message = $e->getMessage();
        }

        return array(
            'code' => $code,
            'data' => null,
            'message' => $message ?? null,
        );
    }

    /**
     * 通过http client发送短信
     *
     * @param $msg
     * @param array $mobile_list
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public static function sendSMSByClient($msg, array $mobile_list = array()): array
    {
        $client = new Client();
        $code = 201;

        // 设置url
        $url = env('SMS_URL') . 'outgoing_msg';
        // 设置参数
        $params = array(
            'channel' => 'sms',
            'recipient_domain' => 'msisdn',
            'recipient' => $mobile_list,
            'text' => array(
                'content' => $msg,
            ),
            'uemo' => 'n',
            'whitelist_filter' => 'n',
            'blacklist_filter' => 'n',
            'sender' => env('SMS_SENDER'),
        );

        try {
            $response = $client->request('POST', $url, array(
                'curl' => array(
                    CURLOPT_SSL_VERIFYPEER => false,    // 验证curl对等证书(一般只要此项)
                    CURLOPT_SSL_VERIFYHOST => false,    // 检查服务器SSL证书中是否存在一个公用名
                    CURLOPT_SSLVERSION => 0,            // 传递一个包含SSL版本的长参数
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_POSTFIELDS => json_encode($params),
                    CURLOPT_HTTPHEADER => array(
                        'accept: application/json',
                        'accept-language: en_US',
                        'Authorization: Basic ' . base64_encode(env('SMS_ACCOUNT_PASSWORD')),
                        'content-type: application/json'
                    ),
                ),
            ));

            // 200成功连接到接口，但不代表发送成功
            if ($response->getStatusCode() == 200) {
                $body = json_decode($response->getBody(), true);
                $message = $body;
                // 短信内部返回格式
                if (isset($body['response'][0]['status']) && $body['response'][0]['status'] == 'OK') {
                    $code = 200;
                }
            }
        } catch (ClientException $e) {
            $message = Psr7\Message::toString($e->getResponse());
        } catch (\Exception $e) {
            $message = $e->getMessage();
        }

        return array(
            'code' => $code,
            'data' => null,
            'message' => $message ?? null
        );
    }

    /**
     * 通过http client发送邮件
     *
     * @param string $reminder_email
     * @param string $mail_title
     * @param string $mail_content
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public static function sendEmailByClient(string $reminder_email, string $mail_title, string $mail_content): array
    {
        // 设置url
        $url = env('EMAIL_SERVER_API_URL_TESTING_HTTPS');
        // 设置curl及options
        $client = new Client;
        $code = 201;

        try {
            $response = $client->request('POST', $url, array(
                'headers' => array(
                    'Accept' => 'application/json',
                    'charset' => 'utf-8',
                ),
                'verify' => base_path('egis.crt'), // 指定證書位置
                // 關閉證書的驗證
                'curl' => [
                    CURLOPT_SSL_VERIFYHOST => false,
                    CURLOPT_SSL_VERIFYPEER => false,
                ],
                // 'debug' => true,
                'multipart' => array(
                    [
                        'name' => 'req',
                        'contents' => json_encode([
                            'contentDetail' => [
                                'subject' => $mail_title,
                                'content' => $mail_content
                            ],
                            'recipientDetail' => [
                                'chanAddr' => [$reminder_email]
                            ]
                        ]),
                        'headers' => [
                            'Content-Type' => 'application/json'
                        ]
                    ]
                )
            ));

            // 200成功连接到接口，但不代表发送成功
            if ($response->getStatusCode() === 200) {
                $body = $response->getBody()->getContents();
                $message = $body;
                // 截取body字符串7到11位作为状态码, 0000是成功，其他为失败
                $status_code = substr($body, 7, 4);
                if ($status_code === '0000') {
                    $code = 200;
                } else {
                    $message = $body;
                }
            } else {
                // 獲取請求的報錯詳細信息
                $message = $response->getReasonPhrase();
            }
        } catch (ClientException $e) {
            $message = Psr7\Message::toString($e->getResponse());
        } catch (\Exception $e) {
            $message = $e->getMessage();
        }
        if ($code !== 200) logger($message);

        return array(
            'code' => $code,
            'data' => null,
            'message' => $message ?? null
        );
    }

    public static function sendEmailByClientVeeotech(string $reminder_email, string $mail_title, string $mail_content)
    {
        try {
            Mail::raw($mail_content, function ($message) use ($reminder_email, $mail_title) {
                $message->from(env('MAIL_USERNAME'), env('MAIL_FROM_NAME'));
                $message->to($reminder_email)->subject($mail_title);
            });
        } catch (\Exception $e) {
            // 错误处理
            logger($e->getMessage());
            return array(
                'code' => 201,
                'data' => null,
                'message' => $e->getMessage()
            );
        }

        return array(
            'code' => 200,
            'data' => null,
            'message' => null
        );
    }


    /**
     * 保存日志，根据规则保存对应日志
     *
     * @param EventLog $event_log_model
     * @param bool $is_create_notify_event_log
     * @param ChargeRecord|null $charge_record
     * @return void
     */
    public static function saveEventLogOrRules(
        EventLog     $event_log_model,
        bool         $is_create_notify_event_log = false,
        ChargeRecord $charge_record = null
    ): void
    {
        if (filled($event_log_model)) {
            $event_log_model->save();
        }

        // 匹配规则并创建通知记录
        if ($is_create_notify_event_log) {
            if (
                filled($event_log_notify_rule_model = EventLogNotifyRule::where('event_type', $event_log_model->event_type)->first()) &&
                $event_log_notify_rule_model->is_enable
            ) {
                $title = $event_log_model->target . '-' . $event_log_model->target_number . '-' . $event_log_model->target_name . ' ' . EventTypeEnum::getDescription($event_log_model->event_type);
                $notify_event_log_model = new NotifyEventLog;
                $notify_event_log_model->event_log_id = $event_log_model->event_log_id;
                $notify_event_log_model->title = $title;
                $notify_event_log_model->description = $event_log_model->description;
                $notify_event_log_model->save();
            }
        }
    }

    /**
     * 加密许可码
     * @param string $string
     * @return string
     */
    public static function licenseCodeEncryption(string $string): string
    {
        $license_code = 'VEEO' . $string . 'TECH';
        // 字符串转大写
        $license_code = strtoupper($license_code);
        return strtoupper(hash('sha512', $license_code));
    }

    /**
     * 刪除redis hash
     *
     * @param string $key
     * @param string|array $field
     * @return void
     * @Description
     * @example
     * @date 2023-09-22
     */
    public static function delRedisHash(string $key, string|array $field): void
    {
        Redis::hdel($key, $field);
    }

    /**
     * 删除所有收费表相关redis
     *
     * @return void
     * @Description
     * @example
     * @date 2023-10-12
     */
    public static function delAllRedisTariffTable(): void
    {
        $redis_prefix = config('database.redis.options.prefix'); // 获取后缀过滤前缀

        $del_redis_key_list = [];
        foreach (Redis::keys('simpleTariffTable*') as $key) {
            $key = str_replace($redis_prefix, '', $key);
            $del_redis_key_list[] = $key;
        }
        foreach (Redis::keys('timeTariffTable*') as $key) {
            $key = str_replace($redis_prefix, '', $key);
            $del_redis_key_list[] = $key;
        }
        foreach (Redis::keys('energyTariffTable*') as $key) {
            $key = str_replace($redis_prefix, '', $key);
            $del_redis_key_list[] = $key;
        }
        foreach (Redis::keys('idlingPenaltyTariffTable*') as $key) {
            $key = str_replace($redis_prefix, '', $key);
            $del_redis_key_list[] = $key;
        }
        foreach (Redis::keys('peakTimeTable*') as $key) {
            $key = str_replace($redis_prefix, '', $key);
            $del_redis_key_list[] = $key;
        }

        Redis::pipeline(function ($pipe) use ($del_redis_key_list) {
            foreach ($del_redis_key_list as $key) {
                $pipe->del($key);
            }
        });
    }

    /**
     * 生成随机数字
     *
     * @param integer $length
     * @return integer
     * @Description
     * @example
     * @date 2023-10-24
     */
    public static function getRandomNumber(int $length = 6): int
    {
        $base = pow(10, $length);
        $min = $base / 10;
        $max = $base - 1;

        return random_int($min, $max);
    }

    /**
     * 从语言数组中获取值
     *
     * @param mixed $language
     * @param string|null $language_code
     * @return array|string|null
     * @Description
     * @example
     * @date 2023-12-23
     */
    public static function getValueFromLanguageArray(mixed $language, ?string $language_code = null): array|string|null
    {
        $language_code = $language_code ?? app()->getLocale();

        if (is_string($language) && Str::isJson($language)) {
            $language = json_decode($language, true);
        }
        if (!is_array($language)) {
            return $language;
        }
        if (filled($language_code) && isset($language[$language_code])) {
            return $language[$language_code];
        }
        if (isset($language['en_US'])) {
            return $language['en_US'];
        }

        return reset($language);
    }

    /**
     * 生成随机字符串
     *
     * @param integer $length
     * @param callable|null $func
     * @return string
     * @Description
     * @example
     * @date 2023-12-27
     */
    public static function getRandomString(int $length = 6, ?callable $func = null): string
    {
        return retry(5, function () use ($length, $func) {
            $str = Str::random($length);
            if ($func !== null) {
                $func($str);
            }
            return $str;
        }, 100);
    }

    /**
     * 使用AccessYou平台发送短信验证码
     *
     * @param string $telephone
     * @param string $msg
     * @return array
     * @throws \Exception
     */
    public static function sendAccessYouSMSVerificationCode(string $telephone, string $msg): array
    {
        $code = 201;
        $message = '';

        try {
            $account_no = env('API_ACCESS_YOU_SMS_ACCOUNT');
            $user = env('API_ACCESS_YOU_SMS_USER');
            $pwd = env('API_ACCESS_YOU_SMS_PASSWORD');
            $url = env('API_ACCESS_YOU_SMS_URL');

            $context = stream_context_create([
                'http' => [
                    'ignore_errors' => true, // 忽略 HTTP 错误
                ]
            ]);

            $handle = fopen("$url?user=$user&msg=$msg&phone=$telephone&pwd=$pwd&accountno=$account_no", 'r', false, $context);

            if ($handle === false) {
                $error = error_get_last(); // 获取最近的错误信息
                if ($error) {
                    logger()->error($error['message']);
                    return array(
                        'code' => $code,
                        'message' => $error['message'],
                    );
                }
            } else {
                // 处理文件读取
                $contents = trim(fread($handle, 8192));
                libxml_use_internal_errors(true);
                $result = simplexml_load_string($contents);
                if ($result === false) {
                    $errors = libxml_get_errors();
                    foreach ($errors as $error) {
                        logger()->error("XML parsing error: {$error->message}");
                    }
                    libxml_clear_errors();
                } else {
                    $result = new \SimpleXMLElement($contents);
                }
                if (is_object($result) && isset($result->msg->msg_status[0])) {
                    $msg_status = $result->msg->msg_status[0];
                    if ($msg_status == '100') {
                        $code = 200;
                    }
                }
                if ($code != 200) {
                    logger()->error('Send SMS Verification Code Failed');
                    logger()->error($contents);
                    $message = $contents;
                }
            }
        } catch (Exception $e) {
            logger()->error($e->getMessage());
            logger()->error('Send SMS Verification Code Error');
            $message = $e->getMessage();
        }
        return array(
            'code' => $code,
            'message' => $message,
        );
    }

    /**
     * 整合发送短信方式，根据参数切换不同发短信方式
     * 使用ACCESS_YOU平台需单发(如果传了数组只拿第一个)
     * 使用GW_SMS平台可以单发也可以群发
     *
     * @param $telephone
     * @param string $msg
     * @return array
     * @throws GuzzleException
     * @throws \Exception
     */
    public static function sendSMS($telephone, string $msg): array
    {
        $sms_send_platform = env('SMS_SEND_PLATFORM');

        switch ($sms_send_platform) {
            case 'GW_SMS':
                if (is_string($telephone)) $telephone = [$telephone];
                return self::sendSMSByClient($msg, $telephone);
            default:
                if (is_array($telephone) && count($telephone) > 1) $telephone = $telephone[0];
                return self::sendAccessYouSMSVerificationCode($telephone, $msg);
        }
    }

    /**
     * 数据存入redis
     *
     * @param string $key
     * @param mixed $data
     * @return void
     * @Description
     * @example
     * @date 2024-01-04
     */
    public static function saveToRedis(string $key, mixed $data): void
    {
        // 判断data类型
        if (is_array($data) || is_object($data)) {
            $data = json_encode($data);
        }
        Redis::set($key, $data);
    }

    /**
     * 从redis获取数据(json文件缓存用)
     *
     * @param string $key
     * @return mixed
     * @Description
     * @example
     * @date 2024-01-04
     */
    public static function getJsonDataFromRedis(string $key): mixed
    {
        $data = Redis::get($key);
        // 判断data类型
        if (is_string($data) && Str::of($data)->isJson()) {
            $data = json_decode($data, true);
        }
        return $data;
    }

    /**
     * 获取商户下拉数据
     *
     * @return array
     */
    public static function getMerchantOptionList(): array
    {
        $merchant_list = array();
        $merchant_list_result = Merchant::when(!isSuperAdministrator(), fn($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))->get();
        foreach ($merchant_list_result as $value) {
            $merchant_list[] = array(
                'value' => $value->merchant_number,
                'name' => self::getValueFromLanguageArray($value->name_json) ?? '—/—',
            );
        }
        return $merchant_list;
    }

    /**
     * 获取场地下拉数据
     *
     * @return array
     */
    public static function getSiteOptionList(): array
    {
        $site_list = array();
        $site_list_result = Site::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->oldest('sort_order')->get();
        foreach ($site_list_result as $value) {
            $site_list[] = array(
                'value' => $value->site_number,
                'name' => self::getValueFromLanguageArray($value->name_json) ?? '—/—',
            );
        }
        return $site_list;
    }

    /**
     * 获取场地列表，按商户分组
     *
     * @return array
     * @Description
     * @example
     * @date 2024-03-15
     */
    public static function getSiteListGroupByMerchant(): array
    {
        $return = []; // 场地列表
        $site_list = Site::with('merchant')
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->oldest('sort_order')
            ->get();
        $no_merchant_site_list = []; // 未分配商户的场地列表
        foreach ($site_list as $site) {
            if (blank($site->merchant)) {
                $no_merchant_site_list[] = array(
                    'site_number' => $site->site_number,
                    'name' => self::getValueFromLanguageArray($site->name_json),
                );
                continue;
            }
            $merchant_name = self::getValueFromLanguageArray($site->merchant->name_json);
            $merchant_number = $site->merchant->merchant_number;
            if (!isset($return[$merchant_number])) {
                $return[$merchant_number] = [
                    'merchant_name' => $merchant_name,
                    'merchant_number' => $merchant_number,
                    'site_list' => [],
                ];
            }
            $return[$merchant_number]['site_list'][] = array(
                'site_number' => $site->site_number,
                'name' => self::getValueFromLanguageArray($site->name_json),
            );
        }
        // 重置索引
        $return = array_values($return);
        // 如果有未分配商户的场地
        if (filled($no_merchant_site_list)) {
            $return[] = [
                'merchant_name' => __('role.unassigned_site'),
                'merchant_number' => 0,
                'site_list' => $no_merchant_site_list,
            ];
        }

        return $return;
    }

    /**
     * 根据site编号数组获取对应kiosk列表，默认为当前角色
     * @param array|null $site_number_list
     * @return array
     */
    public static function getKioskListBySiteNumberList(?array $site_number_list = null): array
    {
        // 不管什么管理员如果传入的是非null且空数组直接返回空数组
        if ($site_number_list !== null && count($site_number_list) === 0) {
            return [];
        }

        // 不管什么管理员如果传入的非null且存在数组直接查询
        if ($site_number_list !== null && count($site_number_list) > 0) {
            // 根据site编号数组查询对应kiosk
            return Kiosk::whereIn('site_number', $site_number_list)->pluck('kiosk_number')->toArray();
        }

        // 如果非超级管理员且为null时读取当前管理员的site_number_list
        if (($not_super_admin = !isSuperAdministrator()) && $site_number_list === null) {
            $site_number_list = auth()->user()->site_number_list;
        }

        // 根据site编号数组查询对应kiosk
        return Kiosk::when($not_super_admin, fn($query) => $query->whereIn('site_number', $site_number_list))->pluck('kiosk_number')->toArray();
    }

    /**
     * 根据Kiosk编号发送推送
     *
     * @return void
     * @Description
     * @example
     * @date 2024-03-19
     */
    public static function sendInitPushByKioskNumberList(): void
    {
        if (isSuperAdministrator()) {
            self::sendInitPush();
        } else {
            $kiosk_number_list = self::getKioskListBySiteNumberList();
            if (filled($kiosk_number_list)) self::sendInitPush(kiosk_number: $kiosk_number_list);
        }
    }
}

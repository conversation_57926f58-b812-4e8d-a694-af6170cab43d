<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;

class ChargeAccountingRecord extends Model
{

    protected $table = 'charge_accounting_record';
    protected $primaryKey = 'charge_accounting_record_id';

    const CREATED_AT = 'gmt_create'; //默认是小写的模型类名复数格式，此处为自定义表名
    const UPDATED_AT = 'gmt_modified'; //定义主键，默认为id


    // protected $fillable = [];
    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 类型转化器
     */
    protected $casts = [

    ];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [

    ];

    /**
     * 全部字段
     *
     * @var array
     */
    public array $columns = [
        'charge_accounting_record_id',
        'merchant_number',
        'site_number',
        'payment_device',
        'payment_method',
        'transaction_type',
        'transaction_category',
        'source_transaction_number',
        'amount',
        'gmt_deduct',
        'octopus_card_number',
        'pos_payment_method_name',
        'pos_card_number',
        'remark',
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    // 一对一关联Site
    public function site()
    {
        return $this->hasOne(Site::class, 'site_number', 'site_number');
    }

}

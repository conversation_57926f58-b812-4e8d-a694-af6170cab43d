<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\{
    JsonResponse,
    RedirectResponse,
    Request,
};
use App\Http\Controllers\Admin\Traits\{
    Add,
    Edit,
};
use Illuminate\Foundation\Application;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use App\Models\Modules\{
    UserNotify,
    UserNotifyDescription,
};
use App\Enums\{
    UserNotifyCategory,
};
use Illuminate\Support\Str;


class UserNotifyController extends CommonController
{
    use Add, Edit;
    protected static string $module_name = 'userNotify'; // 模块名称

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new UserNotify;
    }

    /**
     * 初始页
     *
     * @param Request $request
     * @return View|Application|Factory
     */
    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'list_url' => action([self::class, 'list']),
            'add_url' => action([self::class, 'add']),
            'show_page_url' => action([self::class, 'showPage']),
            'gmt_release' => $request->gmt_release,
            'title' => $request->title,
            'content' => $request->content,
        );
        // 多语言
        $language_list = config('languages');
        $data['language_list'] = $language_list;

        return view("pages.{$data['module_name']}.list", $data);
    }

    /**
     * 列表接口
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        $draw = (int)$request->input('draw', 1);
        $length = (int)$request->input('length', 10);
        $search = $this->_searchList($request);
        $data_list = $search->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $current_language_description = $data->description->firstWhere('language_code', app()->getLocale());
            $description_list = [];
            foreach ($data->description as $description) {
                $description_list[$description->language_code] = $description;
            }
            $result[] = array(
                'user_notify_id' => $data->user_notify_id, // 用户通知ID
                'title' => $current_language_description->title ?? '—/—', // 标题
                'image_url' => $current_language_description?->image_url, // 图片地址
                'content' => $current_language_description->content ?? '—/—', // 内容
                'gmt_release' => $data->gmt_release ?? '—/—', // 发布时间
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
                'description_list' => $description_list, // 描述列表
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result
        );
        
        return response()->json($json);
    }

    /**
     * 表单页
     *
     * @param Request $request
     * @param array $data
     * @return View|Application|Factory
     */
    protected function getForm(Request $request, array $data): View|Application|Factory
    {
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'], self::getUrlParams($request)
        );

        $data['model']->gmt_release = $request->old('gmt_release', $data['model']->gmt_release); // 发布时间

        // 先加载description关联
        if (filled($data['model']->user_notify_id)) $data['model']->load(['description']);
        // 处理description
        $data['description_list'] = [];
        // 多语言
        $language_list = config('languages');
        $data['language_list'] = $language_list;
        // old数据
        $old_description_list = $request->old('description_list');
        foreach ($language_list as $language_code => $language_name) {
            // 先处理old数据
            if (filled($old_description_list)) {
                $old_description = $old_description_list[$language_code];
                $description = new UserNotifyDescription([
                    'user_notify_description_id' => $old_description['user_notify_description_id'] ?? null,
                    'language_code' => $language_code,
                    'title' => $old_description['title'] ?? null,
                    'content' => $old_description['content'] ?? null,
                    'image_url' => $old_description['image_url'] ?? null,
                ]);
                $description->skipMutators = true; // 关闭模型的修改器
                $data['description_list'][$language_code] = $description;
                continue;
            }
            // 再处理数据库中的数据
            $description = $data['model']?->description->firstWhere('language_code', $language_code);
            if (blank($description)) {
                // 如果数据库中没有数据，则创建一个空的
                $description = new UserNotifyDescription([
                    'user_notify_description_id' => null,
                    'language_code' => $language_code,
                    'title' => null,
                    'content' => null,
                    'image_url' => null,
                ]);
            }
            $description->skipMutators = true; // 关闭模型的修改器
            $data['description_list'][$language_code] = $description;
        }

        return view("pages.{$data['module_name']}.form", $data);
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @param UserNotify $model
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    protected function modelValidateAndSave(Request $request, UserNotify $model): RedirectResponse
    {
        $request->validate(self::rules($request, $model), [], self::attributes());
        $model->gmt_release = $request->gmt_release;
        $model->category = UserNotifyCategory::System;
        $model->save();

        foreach ($request->description_list as $description) {
            if (filled($description['user_notify_description_id'])) {
                $model->description()->where('user_notify_description_id', $description['user_notify_description_id'])->update($description);
                continue;
            } else {
                $model->description()->create($description);
            }
        }

        return redirect()->action(
            [self::class, 'showPage'], self::getUrlParams($request)
        );
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function rules(?Request $request, ?Model $model): array
    {
        // 获取配置值
        $language_list = config('languages');
        $rules = array(
            'gmt_release' => 'nullable|date',
            'description_list' => 'required|array',
            'description_list.*.language_code' => 'required|string|in:' . implode(',', array_keys($language_list)),
            // title 最长45个字符
            'description_list.*.title' => 'nullable|string|max:45',
        );

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'gmt_release' => __("$module_name.gmt_release"),
            'description_list' => __('common.text_description'),
            'description_list.*.title' => __("$module_name.title"),
        ];
    }

    public function delete(Request $request): JsonResponse
    {
        $id = $request->post('id');

        if (blank($id)) {
            $this->notFoundData('ID');
            return $this->returnJson();
        }

        $model = $this->model::find($id);

        if (blank($model)) {
            $this->notFoundData('ID');
            return $this->returnJson();
        }

        $model->description()?->delete();
        $model->userNotifyToUser()?->forceDelete();
        $model->delete();

        return $this->returnJson();
    }

    /**
     * 返回地址栏参数
     *
     * @param Request|null $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => 'true',
            'gmt_release' => $request->get('gmt_release'),
            'title' => $request->get('title'),
            'content' => $request->get('content'),
        );
    }

    /**
     * 获取搜索数据
     *
     * @param Request $request
     * @return mixed
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-11-08
     */
    protected function _searchList(Request $request): mixed
    {
        // dataTable字段

        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $gmt_release = $request->gmt_release;
        $title = $request->title;
        $content = $request->content;

        $gmt_release_list = self::getRangeDateTimeArray($gmt_release ?: '') ?: null;

        return UserNotify::with(['description'])
        ->whereNull('user_id')
        ->where('category', UserNotifyCategory::System)
        ->when(filled($gmt_release_list), function ($query) use ($gmt_release_list) {
            return $query->whereBetween('gmt_release', $gmt_release_list);
        })
        ->when(filled($title), function ($query) use ($title) {
            return $query->whereHas('description', function ($query) use ($title) {
                return $query->where('title', 'like', "%{$title}%");
            });
        })
        ->when(filled($content), function ($query) use ($content) {
            return $query->whereHas('description', function ($query) use ($content) {
                return $query->where('content', 'like', "%{$content}%");
            });
        })
        ->orderBy($order, $sort)
        ->latest('gmt_modified')
        ->latest('user_notify_id');
    }
}

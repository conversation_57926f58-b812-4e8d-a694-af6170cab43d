<?php

namespace App\Enums;

use BenSampo\Enum\{
    Enum,
    Contracts\LocalizedEnum
};
use App\Enums\Traits\Tools;

/**
 * @method static ChargePayment
 * @method static SystemDeduct
 * @method static UserTopUp
 * @method static SystemTopUp
 * @method static YedpayOnlinePaymentTopUp
 * @method static YedpayOnlinePaymentRefunded
 * @method static SpectraOnlinePaymentTopUp
 * @method static SpectraOnlinePaymentRefunded
 */
final class TransactionCategory extends Enum implements LocalizedEnum
{
    use Tools;

    const ChargePayment = 'CHARGE_PAYMENT';
    const SystemDeduct = 'SYSTEM_DEDUCT';
    const UserTopUp = 'USER_TOP_UP';
    const SystemTopUp = 'SYSTEM_TOP_UP';
    const YedpayOnlinePaymentTopUp = 'YEDPAY_ONLINE_PAYMENT_TOP_UP';
    const YedpayOnlinePaymentRefunded = 'YEDPAY_ONLINE_PAYMENT_REFUNDED';
    const SpectraOnlinePaymentTopUp = 'SPECTRA_ONLINE_PAYMENT_TOP_UP';
    const SpectraOnlinePaymentRefunded = 'SPECTRA_ONLINE_PAYMENT_REFUNDED';
}

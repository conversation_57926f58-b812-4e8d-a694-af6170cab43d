<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use App\Models\Modules\{
    ChargeRecord,
};
use Illuminate\Support\Facades\DB;
use Illuminate\Contracts\View\{
    Factory,
    View
};
use Illuminate\Foundation\Application;
use Illuminate\Http\{
    JsonResponse,
    Request
};

class OperationReportTwoController extends CommonController
{
    protected static string $module_name = 'operationReportTwo'; // 模块名称
    protected ChargeRecord $model;

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new ChargeRecord;
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'time_frame_search' => $request->get('time_frame_search')
        );

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'year');
        $sort = $request->input('sort', 'asc');
        $time_frame_search = $request->input('time_frame_search');
        // 获取数据
        $list_data = $this->listData($time_frame_search);
        // 数据长度
        $list_data_total = count($list_data);

        // 正序、倒序
        $sort_order = strtolower($sort) == 'desc' ? SORT_DESC : SORT_ASC;
        // 年月一起排序
        if (strtolower($order) == 'year' || strtolower($order) == 'month') {
            $year_sort_list = array_column($list_data, 'year');
            $month_sort_list = array_column($list_data, 'month');
            // 多字段排序
            array_multisort($year_sort_list, $sort_order, $month_sort_list, $sort_order, $list_data);
        } else {
            // 取出对应排序key的数据
            $list_data_children = array_column($list_data, 'data');
            $order_sort_list = array_column($list_data_children, $order);
            array_multisort($order_sort_list, $sort_order, $list_data);
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $list_data_total,
            'recordsFiltered' => $list_data_total,
            "data" => array_values($list_data),
        );

        return response()->json($json);
    }

    public function excelExport(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        // dataTable字段
        $time_frame_search = $request->input('time_frame_search');

        $list_data = $this->listData($time_frame_search);

        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet(); //实例化 Spreadsheet 对象
        $worksheet = $spreadsheet->getActiveSheet();
        $web_title = __("operationReportTwo.web_title");
        // 工作簿名称为 "Operation Report 2"
        $worksheet->setTitle($web_title);

        $year = __("$module_name.year");
        $month = __("$module_name.month");
        $number_of_starting_charging_session = __("$module_name.number_of_starting_charging_session");
        // 工作簿名称为 "Operation Report 2"
        $worksheet->setTitle('Operation Report 2');

        // 设置表头名
        $worksheet->setCellValueByColumnAndRow(1, 1, '');
        $worksheet->setCellValueByColumnAndRow(2, 1, '');
        $worksheet->setCellValueByColumnAndRow(3, 1, $number_of_starting_charging_session);
        $worksheet->setCellValueByColumnAndRow(1, 2, $year);
        $worksheet->setCellValueByColumnAndRow(2, 2, $month);
        $worksheet->setCellValueByColumnAndRow(3, 2, '00 - 01');
        $worksheet->setCellValueByColumnAndRow(4, 2, '01 - 02');
        $worksheet->setCellValueByColumnAndRow(5, 2, '02 - 03');
        $worksheet->setCellValueByColumnAndRow(6, 2, '03 - 04');
        $worksheet->setCellValueByColumnAndRow(7, 2, '04 - 05');
        $worksheet->setCellValueByColumnAndRow(8, 2, '05 - 06');
        $worksheet->setCellValueByColumnAndRow(9, 2, '06 - 07');
        $worksheet->setCellValueByColumnAndRow(10, 2, '07 - 08');
        $worksheet->setCellValueByColumnAndRow(11, 2, '08 - 09');
        $worksheet->setCellValueByColumnAndRow(12, 2, '09 - 10');
        $worksheet->setCellValueByColumnAndRow(13, 2, '10 - 11');
        $worksheet->setCellValueByColumnAndRow(14, 2, '11 - 12');
        $worksheet->setCellValueByColumnAndRow(15, 2, '12 - 13');
        $worksheet->setCellValueByColumnAndRow(16, 2, '13 - 14');
        $worksheet->setCellValueByColumnAndRow(17, 2, '14 - 15');
        $worksheet->setCellValueByColumnAndRow(18, 2, '15 - 16');
        $worksheet->setCellValueByColumnAndRow(19, 2, '16 - 17');
        $worksheet->setCellValueByColumnAndRow(20, 2, '17 - 18');
        $worksheet->setCellValueByColumnAndRow(21, 2, '18 - 19');
        $worksheet->setCellValueByColumnAndRow(22, 2, '19 - 20');
        $worksheet->setCellValueByColumnAndRow(23, 2, '20 - 21');
        $worksheet->setCellValueByColumnAndRow(24, 2, '21 - 22');
        $worksheet->setCellValueByColumnAndRow(25, 2, '22 - 23');
        $worksheet->setCellValueByColumnAndRow(26, 2, '23 - 24');
        $worksheet->mergeCells('C1:Y1');

        // 计算数组长度便于后面遍历
        $len = count($list_data);

        // 因为前面两格表头样式占了两行，所以要加2用于设置单元格样式
        $total_rows = $len + 2;

        //设置单元格样式
        $styleArray = [
            'font' => [
                'bold' => false
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['argb' => '666666'],
                ],
            ],
        ];
        $worksheet->getStyle("A1:Z$total_rows")->applyFromArray($styleArray);
        $worksheet->getStyle('A1:Z2')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('c0c0c0');

        foreach (range('A', 'Z') as $value) {
            $worksheet->getColumnDimension($value)->setWidth(10);
        }
        $index = 0;
        foreach ($list_data as $item) {
            $line = $index + 3; //从表格第3行开始
            $worksheet->setCellValueByColumnAndRow(1, $line, $item['year']);
            $worksheet->setCellValueByColumnAndRow(2, $line, (int)$item['month']);
            if (isset($item['data'])) {
                foreach ($item['data'] as $key => $datum) {
                    $worksheet->setCellValueByColumnAndRow(3 + $key, $line, $datum);
                }
            }
            $index++;
        }

        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $file_name = $web_title . ' ' . date('Y-m-d H:i:s') . '.xlsx';

        // 客户端文件下载
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $file_name . '"');
        header('Cache-Control: max-age=0');
        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
        exit;
    }

    /**
     * 查询数据
     * @param $time_frame_search
     * @return array
     */
    protected function listData($time_frame_search = null): array
    {
        // 结束日期即当前月份的最后一天
        $end_date = date('Y-m-t 23:59:59');
        // 起始日期即开始时间的第一天 - 当前时间往前推12月
        $start_date = date('Y-m-01 00:00:00', strtotime('-11 month'));
        // 实际结果集
        $real_result_list = array();
        // 默认填充数据
        $default_result_list = array_fill(0, 24, 0);

        // 搜索时间不为空
        if (filled($time_frame_search)) {
            // 得到时间区间，分割成开始时间和结束时间
            $time_frame_range = explode(' - ', $time_frame_search);
            // 判断是否是时间类型且为开始时间结束时间类型
            if (count($time_frame_range) == 2 && strtotime($time_frame_range[0]) && strtotime($time_frame_range[1]) && (strtotime($time_frame_range[0]) <= strtotime($time_frame_range[1]))) {
                $start_date = date('Y-m-01 00:00:00', strtotime($time_frame_range[0]));
                $end_date = date('Y-m-t 23:59:59', strtotime($time_frame_range[1]));
            }
        }

        // 获取充电记录结果集
        $charge_record_result_list = ChargeRecord::selectRaw('DATE_FORMAT(gmt_start,"%Y-%m") AS month_year_key')
            ->selectRaw('DATE_FORMAT(gmt_start,"%H") as hours')
            ->selectRaw("SUM(CASE WHEN DATE_FORMAT(gmt_start,'%H') THEN 1 WHEN DATE_FORMAT(gmt_start,'%H') = '00' THEN 1 ELSE 0 END) as start")
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->whereNotNull('gmt_stop')
            ->whereBetween('gmt_start', [$start_date, $end_date])
            ->groupBy('month_year_key', 'hours')
            ->get()
            ->toArray();

        $new_charge_record_result_list = array();
        // 循环原先一维数组，相同键值的进行存储
        foreach ($charge_record_result_list as $key => $value) {
            $new_charge_record_result_list[$value['month_year_key']][] = $value;
        }

        // 获取开始结束时间的时间戳
        $start_date_timestamp = strtotime($start_date);
        $end_date_timestamp = strtotime($end_date);
        // 根据开始结束日期区间的时间戳循环获取月份 - 例如2022-03 ~ 2023-02
        while ($start_date_timestamp <= $end_date_timestamp) {
            // 当前月份
            $current_month = date('Y-m', $start_date_timestamp);
            // 将每月填充数据，默认为0
            $real_result_list[$current_month]['data'] = $default_result_list;
            // 当前月份结果 - 当有搜索时间且数据为空时即填充0
            $real_result_list[$current_month]['year'] = date('Y', $start_date_timestamp);
            $real_result_list[$current_month]['month'] = date('m', $start_date_timestamp);

            if (!empty($new_charge_record_result_list)) {
                // 如果查询数据存在该月，赋值数据
                if (array_key_exists($current_month, $new_charge_record_result_list) && !empty($new_charge_record_result_list[$current_month])) {
                    foreach ($new_charge_record_result_list[$current_month] as &$item) {
                        // 移除key
                        unset($item['month_year_key']);
                        // 将查询数据全部转成number类型
                        $item = array_map(function ($v) {
                            // 转化成数字
                            return (int)$v;
                        }, $item);
                        // 将每月填充数据，默认为0
                        if (isset($item['hours'])) {
                            $real_result_list[$current_month]['data'][$item['hours']] = $item['start'];
                        }
                    }
                }
            }

            // 每次+1月份作为循环出口
            $start_date_timestamp = strtotime("+1 month", $start_date_timestamp);
        }

        return $real_result_list;
    }

}

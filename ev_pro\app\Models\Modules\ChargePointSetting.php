<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;

class ChargePointSetting extends Model
{
    protected $table = 'charge_point_setting'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'charge_point_setting_id'; //定义主键，默认为id
    // 表的唯一number
    public static $table_number = 'charge_point_setting_number';
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    // protected $fillable = [];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [
        'is_enable_verify_rfid_card' => false, // 是否启用校验RFID卡
        'sort_order' => 0, // 排序
    ];

    /**
     * 类型转化器
     */
    protected $casts = [
        'is_enable_verify_rfid_card' => 'bool',
    ];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * 一对多关联 charge point
     */
    public function chargePoint()
    {
        return $this->hasOne(ChargePoint::class, 'charge_point_setting_number', 'charge_point_setting_number');
    }

    /**
     * 一对一关联商户
     */
    public function merchant()
    {
        return $this->hasOne(Merchant::class, 'merchant_number', 'merchant_number');
    }
}

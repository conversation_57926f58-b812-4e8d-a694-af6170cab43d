<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;
use App\Models\Traits\Emoji;

class MaintenanceRecord extends Model
{
    use emoji;

    protected $table = 'maintenance_record'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'maintenance_record_id'; //定义主键，默认为id
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    // protected $fillable = [];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 类型转化器
     */
    protected $casts = [
        'is_bypass_mode' => 'bool',
    ];
    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [

        'is_bypass_mode' => false, // 是否为手动操作充电机模式
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];


    /**
     * Maintenance Record 关联 Kiosk
     */
//    public function kiosk()
//    {
//        return $this->hasOne(Kiosk::class, 'kiosk_id', 'kiosk_id');
//    }

}

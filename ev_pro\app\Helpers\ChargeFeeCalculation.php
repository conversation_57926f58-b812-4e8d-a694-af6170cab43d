<?php
namespace App\Helpers;

use DateTime;
use DateInterval;
use Illuminate\Support\Arr;
use App\Enums\{
    DayType,
    LmsMode,
};

/**
 * 充电费用计算
 *
 * @Description
 * @example
 * @date 2023-06-15
 */
Class ChargeFeeCalculation
{
    // 计算简单时间收费
    public static function calculateSimpleTimeTariff(DateTime $start_date_time, DateTime $stop_date_time, array $simple_tariff_table): Array
    {
        // 去除秒数
        $start_date_time = DateTime::createFromFormat('Y/m/d H:i', $start_date_time->format('Y/m/d H:i'));
        $stop_date_time = DateTime::createFromFormat('Y/m/d H:i', $stop_date_time->format('Y/m/d H:i'));
        // 间隔数量
        $interval_count = (int) ceil(($stop_date_time->getTimestamp() - $start_date_time->getTimestamp()) / $simple_tariff_table['charge_value_interval']);
        // 防0值及负数
        $interval_count = max($interval_count, 1);
        // 金额
        $amount = $simple_tariff_table['rate'] * $interval_count;
        // 组装计算数组
        $simple_tariff_calculation_record = [
            'gmt_calculation_start' => $start_date_time->format('Y-m-d\TH:i:s'),
            'gmt_calculation_stop' => $stop_date_time->format('Y-m-d\TH:i:s'),
            'charge_value_interval' => $simple_tariff_table['charge_value_interval'],
            'interval_count' => $interval_count,
            'rate' => $simple_tariff_table['rate'],
            // 去除分钱
            'amount' => (int)floor($amount / 10) * 10,
        ];

        return $simple_tariff_calculation_record;
    }

    // 计算简单电量收费
    public static function calculateSimpleEnergyTariff(int $energy, array $simple_tariff_table): Array
    {
        // 间隔数量
        $interval_count = (int) ceil($energy / $simple_tariff_table['charge_value_interval']);
        // 防0值及负数
        $interval_count = max($interval_count, 1);
        // 金额
        $amount = $simple_tariff_table['rate'] * $interval_count;
        // 组装计算数组
        $simple_tariff_calculation_record = [
            'calculation_energy' => $energy,
            'charge_value_interval' => $simple_tariff_table['charge_value_interval'],
            'interval_count' => $interval_count,
            'rate' => $simple_tariff_table['rate'],
            // 去除分钱
            'amount' => (int)floor($amount / 10) * 10,
        ];

        return $simple_tariff_calculation_record;
    }

    // 获取日期类型
    private static function getDayType(DateTime $date_time, array $public_holiday_list): String
    {
        // 判断公众假期
        if (self::isPublicHoliday($date_time, $public_holiday_list)) {
            return DayType::PublicHoliday;
        }

        $day_of_week = $date_time->format('w');
        $week_enum_array = [
            '0' => DayType::Sunday,
            '1' => DayType::Monday,
            '2' => DayType::Tuesday,
            '3' => DayType::Wednesday,
            '4' => DayType::Thursday,
            '5' => DayType::Friday,
            '6' => DayType::Saturday,
        ];

        return $week_enum_array[$day_of_week];
    }

    // 判断是否为假期
    private static function isPublicHoliday(DateTime $date_time, array $public_holiday_list): Bool
    {
        // 只取日期
        $date_time_clone = clone $date_time;
        $date_time_clone->setTime(0, 0, 0);
        $date_Time_timestamp = $date_time_clone->getTimestamp();

        // 参数去空
        $public_holiday_list = $public_holiday_list ?? [];

        foreach ($public_holiday_list as $public_holiday) {
            if ($date_Time_timestamp >= strtotime($public_holiday['start_date']) && $date_Time_timestamp <= strtotime($public_holiday['end_date'])) {
                return true;
            }
        }

        return false;
    }

    // 计算时间收费
    public static function calculateTimeTariff(DateTime $start_date_time, DateTime $stop_date_time, int $charge_value_interval, array $time_tariff_table_item_list, array $public_holiday_list, string $lms_mode_enum) : array
    {
        // 参数去空
        $time_tariff_table_item_list = $time_tariff_table_item_list ?? [];
        $public_holiday_list = $public_holiday_list ?? [];
        // 去除秒数
        $start_date_time = DateTime::createFromFormat('Y/m/d H:i', $start_date_time->format('Y/m/d H:i'));
        $stop_date_time = DateTime::createFromFormat('Y/m/d H:i', $stop_date_time->format('Y/m/d H:i'));
        // 时间收费表间隔项
        $time_tariff_table_interval_item_list = self::assembleTimeTariffTableIntervalItemList($start_date_time, $stop_date_time, $charge_value_interval, $time_tariff_table_item_list, $public_holiday_list);
        // 间隔数量
        $interval_count = (int) ceil(($stop_date_time->getTimestamp() - $start_date_time->getTimestamp()) / $charge_value_interval);
        // 防0值及负数
        $interval_count = max($interval_count, 1);
        // 合共金额
        $total_amount = 0;
        // 组装计算数组
        $time_tariff_calculation_record = [
            'gmt_calculation_start' => $start_date_time->format('Y-m-d\TH:i:s'),
            'gmt_calculation_stop' => $stop_date_time->format('Y-m-d\TH:i:s'),
            'charge_value_interval' => $charge_value_interval,
            'interval_count' => $interval_count,
        ];
        $time_tariff_calculation_record_item_list = [];
        for ($i = 0; $i < $interval_count; $i++) {
            /* PT 是 ISO 8601 标准中定义的时间间隔格式
            P 表示时间间隔的格式，必须出现在时间间隔字符串的开头。
            T 表示时间分隔符，必须出现在日期和时间之间。*/
            $start_date_time_clone = clone $start_date_time;
            $date_time = $start_date_time_clone->add(new DateInterval('PT' . $i * $charge_value_interval . 'S'));
            $day_type = self::getDayType($date_time, $public_holiday_list);
            $date_time_seconds = (int) ($date_time->getTimestamp() - $date_time->setTime(0, 0, 0)->getTimestamp()); // 当天秒数
            // dump($date_time_seconds);
            $time_tariff_table_interval_item = Arr::first($time_tariff_table_interval_item_list, fn($value) => $value['day_type'] === $day_type && ($value['start_time'] <= $date_time_seconds && $value['end_time'] > $date_time_seconds));
            if (is_null($time_tariff_table_interval_item)) {
                $time_tariff_table_interval_item = Arr::first($time_tariff_table_interval_item_list, fn($value) => is_null($value['day_type']) && ($value['start_time'] <= $date_time_seconds && $value['end_time'] > $date_time_seconds));
            }
            if (!is_null($time_tariff_table_interval_item)) {
                // 费率
                $rate = $lms_mode_enum === LmsMode::EvenDistribution ? $time_tariff_table_interval_item['concessionary_rate'] : $time_tariff_table_interval_item['normal_rate'];
                $total_amount += $rate;
                // 组装计算数组
                $time_tariff_calculation_record_item = [
                    'gmt_calculation' => $date_time->format('Y-m-d\TH:i:s'),
                    'day_type' => $day_type,
                    'tariff_table_item_start_time' => $time_tariff_table_interval_item['start_time'],
                    'tariff_table_item_end_time' => $time_tariff_table_interval_item['end_time'],
                    'rate' => $rate,
                    'amount' => $rate,
                    'total_amount' => $total_amount,
                ];
                $time_tariff_calculation_record_item_list[] = $time_tariff_calculation_record_item;
            }
        }

        //去掉分钱
        $time_tariff_calculation_record['amount'] = (int)floor($total_amount / 10) * 10;
        $time_tariff_calculation_record['time_tariff_calculation_record_item_list'] = $time_tariff_calculation_record_item_list;

        return $time_tariff_calculation_record;
    }

    private static function assembleTimeTariffTableIntervalItemList(Datetime $start_date_time, DateTime $stop_date_time, int $charge_value_interval, array $time_tariff_table_item_list, array $public_holiday_list) : array
    {
        // 天最大秒数
        $day_maximum_seconds = 24 * 60 * 60;
        // 只取日期
        $start_date_time = new DateTime($start_date_time->format('Y-m-d'));
        $stop_date_time = new DateTime($stop_date_time->format('Y-m-d'));
        // 天数量
        $day_count = (int)ceil(($stop_date_time->getTimestamp() - $start_date_time->getTimestamp()) / (24 * 60 * 60));
        $time_tariff_table_interval_item_list = [];
        $day_type_list = [];

        // 组装时间收费表间隔项数组
        for ($day = 0; $day <= $day_count; $day++) {
            $date_time = clone $start_date_time;
            $date_time->modify("+$day day");
            $day_type = self::getDayType($date_time, $public_holiday_list);

            if (in_array($day_type, $day_type_list)) {
                continue;
            }

            $time_tariff_table_item = Arr::first($time_tariff_table_item_list, fn($value) => $value['day_type'] === $day_type);
            if (is_null($time_tariff_table_item)) {
                $day_type = null;
                $time_tariff_table_item = Arr::first($time_tariff_table_item_list, fn($value) => $value['day_type'] === $day_type);
            }
            if (is_null($time_tariff_table_item)) {
                continue;
            }

            $interval_start_time = 0;
            $interval_end_time = 0;
            $interval_count = (int)ceil($day_maximum_seconds / $charge_value_interval);

            for ($interval = 0; $interval < $interval_count; $interval++) {
                $interval_start_time = $interval * $charge_value_interval;
                $interval_end_time = $interval_start_time + $charge_value_interval;
                $normal_rate = 0;
                $concessionary_rate = 0;

                if (!is_null($time_tariff_table_item)) {
                    $normal_rate = $time_tariff_table_item['default_normal_rate'];
                    $concessionary_rate = $time_tariff_table_item['default_concessionary_rate'];
                    if (isset($time_tariff_table_item['time_tariff_table_item_special_list'])) {
                        $time_tariff_table_item_special = Arr::first($time_tariff_table_item['time_tariff_table_item_special_list'], fn($value) => $interval_start_time >= $value['start_time'] && $interval_end_time <= $value['end_time']);
                        if (!is_null($time_tariff_table_item_special)) {
                            $normal_rate = $time_tariff_table_item_special['normal_rate'];
                            $concessionary_rate = $time_tariff_table_item_special['concessionary_rate'];
                        }
                    }
                }

                $time_tariff_table_interval_item = [
                    'day_type' => $day_type,
                    'start_time' => $interval_start_time,
                    'end_time' => $interval_end_time,
                    'normal_rate' => $normal_rate,
                    'concessionary_rate' => $concessionary_rate
                ];
                $time_tariff_table_interval_item_list[] = $time_tariff_table_interval_item;
            }

        }

        return $time_tariff_table_interval_item_list;
    }

    // 计算电量收费
    public static function calculateEnergyTariff(array $charged_energy_record_list, array $energy_tariff_table_item_list, array $peak_time_table_item_list, array $public_holiday_list, bool $is_enable_round_up_tail_charge_value_calculation, string $lms_mode_enum) : array
    {
        // 参数去空
        $charged_energy_record_list = $charged_energy_record_list ?? [];
        $energy_tariff_table_item_list = $energy_tariff_table_item_list ?? [];
        $peak_time_table_item_list = $peak_time_table_item_list ?? [];
        $public_holiday_list = $public_holiday_list ?? [];

        // 合共金额
        $total_amount = 0;
        // 启用向上取整尾部充电量计算
        if ($is_enable_round_up_tail_charge_value_calculation) {
            $maximum_charged_energy = 0;
            $maximum_charged_energy_date_time = new DateTime();

            foreach ($charged_energy_record_list as $charged_energy_record) {
                // 取已充电量最大值
                $charged_energy = $charged_energy_record['charge_record_charged_energy'] + $charged_energy_record['this_time_record_energy'];
                if ($charged_energy > $maximum_charged_energy) {
                    $maximum_charged_energy = $charged_energy;
                    $maximum_charged_energy_date_time = $charged_energy_record['gmt_create'];
                }
            }

            // 有最大已充电量时 && 取余1000后没有余数(代表需要向上取整)
            if ($maximum_charged_energy > 0 && $maximum_charged_energy % 1000 > 0) {
                // 取向上取整后的差值, 计算出此次已充电量
                $this_time_record_energy = 1000 - $maximum_charged_energy % 1000;
                // 创建向上取整后的已充电量虚拟记录
                $round_up_charged_energy_record = [
                    'charge_record_charged_energy' => $maximum_charged_energy,
                    'this_time_record_energy' => $this_time_record_energy,
                    'gmt_create' => $maximum_charged_energy_date_time,
                    'is_generated_by_round_up' => true
                ];
                $charged_energy_record_list[] = $round_up_charged_energy_record;
            }
        }
        // 组装计算数组
        $energy_tariff_calculation_record = [];
        $energy_tariff_calculation_record_item_list = [];
        foreach ($charged_energy_record_list as $charged_energy_record) {
            // 是否为高峰期
            $date_time = new DateTime($charged_energy_record['gmt_create']);
            $day_type = self::getDayType($date_time, $public_holiday_list);
            $date_time_seconds = (int)($date_time->getTimestamp() - $date_time->getOffset()); // 当天秒数
            $peak_time_table_item = Arr::first($peak_time_table_item_list, fn($value) => $value['day_type'] === $day_type);
            if (!is_null($peak_time_table_item)) {
                $peak_time_table_item = Arr::first($peak_time_table_item_list, fn($value) => $value['day_type'] === $day_type && ($value['start_time'] <= $date_time_seconds && $value['end_time'] > $date_time_seconds));
            } else {
                $peak_time_table_item = Arr::first($peak_time_table_item_list, fn($value) => is_null($value['day_type']) && ($value['start_time'] <= $date_time_seconds && $value['end_time'] > $date_time_seconds));
            }
            $is_on_peak = !is_null($peak_time_table_item);
            // 开始已充电量
            $start_charged_energy = $charged_energy_record['charge_record_charged_energy'];
            // 结束已充电量
            $end_charged_energy = $charged_energy_record['charge_record_charged_energy'] + $charged_energy_record['this_time_charged_energy'];
            // 计算区间收费
            foreach ($energy_tariff_table_item_list as $energy_tariff_table_item) {
                // 开始区间
                $start_range = $energy_tariff_table_item['start_range'];
                // 结束区间
                $end_range = $energy_tariff_table_item['end_range'];
                // 计算电量
                $calculate_energy = 0;
                if ($start_charged_energy <= $start_range && $end_charged_energy >= $end_range) {
                    // 开始电量 <= 开始区间 && 结束电量 >= 结束区间
                    // 电量包含整段区间 - 取整段区间
                    $calculate_energy = $end_range - $start_range;
                } else if ($start_charged_energy >= $start_range && $end_charged_energy <= $end_range) {
                    // 开始电量 >= 开始区间 && 结束电量 <= 结束区间
                    // 区间包含整段电量 - 取整段电量
                    $calculate_energy = $end_charged_energy - $start_charged_energy;
                } else if ($start_charged_energy >= $start_range && $start_charged_energy < $end_range) {
                    // 开始电量 >= 开始区间 && 开始电量 < 结束区间
                    // 只占了区间后半段 - 取开始电量 至 结束区间
                    $calculate_energy = $end_range - $start_charged_energy;
                } else if ($end_charged_energy > $start_range && $end_charged_energy <= $end_range) {
                    // 结束电量 > 开始区间 && 结束电量 <= 结束区间
                    // 只占了区间前半段 - 取开始区间 至 结束电量
                    $calculate_energy = $end_charged_energy - $start_range;
                }
                // 不符合区间不计算
                if ($calculate_energy > 0) {
                    // 计算费用
                    // 费率
                    $rate = 0;
                    if ($is_on_peak) {
                        // 高峰
                        $rate = $lms_mode_enum === LmsMode::EvenDistribution ? $energy_tariff_table_item['on_peak_concessionary_rate'] : $energy_tariff_table_item['on_peak_normal_rate'];
                    } else {
                        //非高峰
                        $rate = $lms_mode_enum === LmsMode::EvenDistribution ? $energy_tariff_table_item['off_peak_concessionary_rate'] : $energy_tariff_table_item['off_peak_normal_rate'];
                    }
                    // 此处未瓦时*分钱
                    // 计算是按每千瓦时收费
                    // 如果此处就 / 1000的话误差太大. 进行调整最后总价再 / 1000
                    $total_amount += $calculate_energy * $rate;
                    // 组装计算数组
                    $energy_tariff_calculation_record_item = [
                        'start_charged_energy' => $start_charged_energy,
                        'end_charged_energy' => $end_charged_energy,
                        'tariff_table_item_start_range' => $start_range,
                        'tariff_table_item_end_range' => $end_range,
                        'calculate_energy' => $calculate_energy,
                        'gmt_charged_energy' => $charged_energy_record['gmt_create'],
                        'day_type' => $day_type,
                        'is_on_peak' => $is_on_peak,
                        'rate' => $rate,
                        'amount' => $calculate_energy * $rate / 1000,
                        'total_amount' => $total_amount / 1000,
                        'is_generated_by_round_up' => $charged_energy_record['is_generated_by_round_up'] ?? false,
                    ];
                    $energy_tariff_calculation_record_item_list[] = $energy_tariff_calculation_record_item;
                }
            }
        }
        // 算总价时再 / 1000
        $energy_tariff_calculation_record['amount'] = $total_amount / 1000;
        // 去除分钱
        $energy_tariff_calculation_record['amount'] = (int)floor($energy_tariff_calculation_record['amount'] / 10) * 10;
        $energy_tariff_calculation_record['energy_tariff_calculation_record_item_list'] = $energy_tariff_calculation_record_item_list;

        return $energy_tariff_calculation_record;
    }

    // 计算闲置罚款收费
    public static function calculateIdlingPenalty(DateTime $start_date_time, DateTime $stop_date_time, array $idling_penalty_tariff_table_item_list, array $peak_time_table_item_list, array $public_holiday_list)
    {
        // 参数去空
        $idling_penalty_tariff_table_item_list = $idling_penalty_tariff_table_item_list ?? [];
        $peak_time_table_item_list = $peak_time_table_item_list ?? [];
        $public_holiday_list = $public_holiday_list ?? [];
        // 去除秒数
        $start_date_time = DateTime::createFromFormat('Y/m/d H:i', $start_date_time->format('Y/m/d H:i'));
        $stop_date_time = DateTime::createFromFormat('Y/m/d H:i', $stop_date_time->format('Y/m/d H:i'));
        // 分钟数量
        $minute_count = (int)($stop_date_time->getTimestamp() - $start_date_time->getTimestamp()) / 60;
        // 合共金额
        $total_amount = 0;
        // 组装计算模型
        $idling_penalty_tariff_calculation_record = array(
            'gmt_calculation_start' => $start_date_time->format('Y-m-d\TH:i:s'),
            'gmt_calculation_stop' => $stop_date_time->format('Y-m-d\TH:i:s'),
            'minute_count' => $minute_count,
        );
        $idling_penalty_tariff_calculation_record_item_list = array();
        for ($i = 0; $i < $minute_count; $i++) {
            $start_date_time_clone = clone $start_date_time;
            $date_time = DateTime::createFromFormat('Y/m/d H:i', $start_date_time_clone->add(new DateInterval('PT' . $i * 60 . 'S'))->format('Y/m/d H:i'));
            $day_type = self::getDayType($date_time, $public_holiday_list);
            $date_time_seconds = (int)($date_time->getTimestamp() - strtotime($date_time->format('Y-m-d')));
            $peak_time_table_item = Arr::first($peak_time_table_item_list, fn($value) => $value['day_type'] === $day_type);
            if (!is_null($peak_time_table_item)) {
                $peak_time_table_item = Arr::first($peak_time_table_item_list, fn($value) => $value['day_type'] === $day_type && ($value['start_time'] <= $date_time_seconds && $value['end_time'] > $date_time_seconds));
            } else {
                $peak_time_table_item = Arr::first($peak_time_table_item_list, fn($value) => is_null($value['day_type']) && ($value['start_time'] <= $date_time_seconds && $value['end_time'] > $date_time_seconds));
            }
            $is_on_peak = !is_null($peak_time_table_item);
            $idling_penalty_tariff_table_item = Arr::first($idling_penalty_tariff_table_item_list, fn($value) => $value['start_range'] <= $i * 60 && $value['end_range'] > $i * 60);
            if (!is_null($idling_penalty_tariff_table_item)) {
                $rate = $is_on_peak ? $idling_penalty_tariff_table_item['on_peak_rate'] : $idling_penalty_tariff_table_item['off_peak_rate'];
                $total_amount += $rate;
                // 组装计算模型
                $idling_penalty_tariff_calculation_record_item = array(
                    'gmt_calculation' => $date_time->format('Y-m-d\TH:i:s'),
                    'day_type' => $day_type,
                    'tariff_table_item_start_range' => $idling_penalty_tariff_table_item['start_range'],
                    'tariff_table_item_end_range' => $idling_penalty_tariff_table_item['end_range'],
                    'is_on_peak' => $is_on_peak,
                    'rate' => $rate,
                    'amount' => $rate,
                    'total_amount' => $total_amount,
                );
                $idling_penalty_tariff_calculation_record_item_list[] = $idling_penalty_tariff_calculation_record_item;
            }
        }

        // 去除分钱
        $idling_penalty_tariff_calculation_record['amount'] = (int)floor($total_amount / 10) * 10;
        $idling_penalty_tariff_calculation_record['idling_penalty_tariff_calculation_record_item_list'] = $idling_penalty_tariff_calculation_record_item_list;
        return $idling_penalty_tariff_calculation_record;
    }
}

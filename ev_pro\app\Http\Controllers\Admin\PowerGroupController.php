<?php

namespace App\Http\Controllers\Admin;

use App\Enums\LoadBalanceAlgorithmEnum;
use App\Http\Controllers\Common\CommonController;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Application;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Admin\Traits\{
    Add,
    Edit
};
use App\Models\Modules\{
    PowerGroup,
};

class PowerGroupController extends CommonController
{
    use Add, Edit;

    protected static string $module_name = 'powerGroup'; // 模块名称

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new PowerGroup;
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'add_url' => action([self::class, 'add']),
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'name_search' => $request->get('name_search'),
            'load_balance_algorithm_search' => $request->get('load_balance_algorithm_search'),
        );

        $data['load_balance_algorithm_list'] = array();

        foreach (LoadBalanceAlgorithmEnum::asSelectArray() as $value => $name) {
            $data['load_balance_algorithm_list'][] = array(
                'name' => $name,
                'value' => $value,
            );
        }

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'name');
        $sort = $request->input('gmt_modified', 'desc');
        $length = (int)$request->input('length', 10);
        $name_search = $request->input('name_search');
        $load_balance_algorithm_search = $request->input('load_balance_algorithm_search');

        $where = array();

        if (filled($name_search)) {
            $where[] = ['name', 'like', "%$name_search%"];
        }

        if (filled($load_balance_algorithm_search)) {
            $where[] = ['load_balance_algorithm', '=', "$load_balance_algorithm_search"];
        }

        $data_list = PowerGroup::where($where)
            ->orderBy($order, $sort)
            ->latest('gmt_modified')
            ->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $result[] = array(
                'power_group_id' => $data->power_group_id, // 设置ID
                'power_group_number' => $data->power_group_number, // 电源组编号
                'name' => $data->name, // 名称
                'load_balance_algorithm' => LoadBalanceAlgorithmEnum::getDescription($data->load_balance_algorithm), // 负载均衡算法
                'current_voltage' => $data->current_voltage ? (float)(bcdiv($data->current_voltage ?? 0, 100, 2)) . __('common.unit_voltage') : '—/—', // 当前電壓
                'maximum_current' => $data->maximum_current ? (float)(bcdiv($data->maximum_current ?? 0, 100, 2)) . __('common.unit_ampere') : '—/—', // 当前電流
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result
        );
        return response()->json($json);
    }

    protected function getForm(Request $request, array $data): View|Application|Factory
    {
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'], self::getUrlParams($request)
        );
        // 新增时才回显场地编号和商户编号
        if (blank($data['model']->power_group_id)) {
            $data['model']->power_group_number = $request->old('power_group_number', $data['model']->power_group_number); // 电源组编号
        }

        $data['model']->name = $request->old('name', $data['model']->name); // 键
        $data['model']->load_balance_algorithm = $request->old('load_balance_algorithm', $data['model']->load_balance_algorithm); // 值
        $data['model']->maximum_current = $request->old('maximum_current') ?? ($data['model']->maximum_current ? (float)bcdiv($data['model']->maximum_current, 100, 2) : null); // 最大电流

        $data['load_balance_algorithm_list'] = LoadBalanceAlgorithmEnum::asSelectArray();
        return view("pages.{$data['module_name']}.form", $data);
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @param PowerGroup $model
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    protected function modelValidateAndSave(Request $request, PowerGroup $model): RedirectResponse
    {
        $request->validate(self::rules($request, $model), [], self::attributes());
//        $current_voltage = $request->input('current_voltage');
        $maximum_current = $request->input('maximum_current');
        $model->power_group_number = $request->input('power_group_number'); // 電源組編號
        $model->name = $request->input('name'); // 名稱
        $model->load_balance_algorithm = $request->input('load_balance_algorithm');// 負載均衡算法
//        $model->current_voltage = $current_voltage ? intval(bcmul($current_voltage, 100)) : null;// 当前电压
        $model->maximum_current = $maximum_current ? intval(bcmul($maximum_current, 100)) : null;// 最大電流
        $model->save();

        self::sendInitPushByKioskNumberList();

        return redirect()->action(
            [self::class, 'showPage'], self::getUrlParams($request)
        );
    }

    public function delete(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        $power_group_number = $request->post('power_group_number');

        if (filled($power_group_number) && filled($model = PowerGroup::where('power_group_number', $power_group_number)->first())) {
            $model->delete();
            self::sendInitPushByKioskNumberList();
        } else {
            $this->notFoundData(__("$module_name.web_title"));
        }

        return $this->returnJson();
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function rules(?Request $request, ?Model $model): array
    {
        $rules = array(
            'name' => 'required|max:45',
            'load_balance_algorithm' => 'required|nullable|enum_value:' . LoadBalanceAlgorithmEnum::class,
//            'current_voltage' => 'nullable|numeric|min:0|max:999999|decimal:0,2',
            'maximum_current' => 'required|nullable|numeric|min:0|max:999999|decimal:0,2',
        );
        // 只有新增时才校验电源组编号
        if (blank($model->power_group_id)) {
            $rules['power_group_number'] = [
                'required',
                'max:10',
                'regex:/^[a-zA-Z0-9]+$/', // 只能填写字母和数字
                'unique:App\Models\Modules\PowerGroup,power_group_number',
            ];
        }

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'name' => __("$module_name.name"),
            'maximum_power' => __("$module_name.maximum_power"),
            'load_balance_algorithm' => __("$module_name.load_balance_algorithm"),
        ];
    }

    /**
     * 返回地址栏参数
     *
     * @param Request|null $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => 'true',
            'name_search' => $request->get('name_search'),
            'load_balance_algorithm_search' => $request->get('load_balance_algorithm_search'),
        );
    }

}


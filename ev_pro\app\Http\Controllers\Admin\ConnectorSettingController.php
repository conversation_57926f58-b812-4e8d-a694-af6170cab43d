<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Validation\Rule;
use Illuminate\Http\{
    JsonResponse,
    RedirectResponse,
    Request,
};
use App\Http\Controllers\Admin\Traits\{
    Edit,
};
use Illuminate\Foundation\Application;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use App\Models\Modules\{
    ConnectorSetting,
    ConnectorSettingTrialChargeRule,
    ConnectorSettingDescription,
    SimpleTariffTable,
    Site,
    TimeTariffTable,
    EnergyTariffTable,
    IdlingPenaltyTariffTable,
    PeakTimeTable,
};
use App\Enums\{
    ChargePointVendor,
    TariffTableType,
    ReadingContextEnum,
    MeasurandEnum,
};

class ConnectorSettingController extends CommonController
{
    use Edit;

    protected static string $module_name = 'connectorSetting'; // 模块名称
    protected static bool $module_check_site = true; // 标记该模块校验场地

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new ConnectorSetting;
    }

    /**
     * 初始页
     *
     * @param Request $request
     * @return View|Application|Factory
     */
    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'member_module_name' => 'memberOctopusCard',
            'add_url' => action([self::class, 'add']),
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'site_search' => $request->get('site_search'),
            'name_search' => $request->get('name_search'),
            'site_list' => $this->getSiteOptionList(),
        );

        $data['tariff_table_type_list'] = array();
        foreach (TariffTableType::asSelectArray() as $value => $name) {
            $data['tariff_table_type_list'][] = array(
                'name' => $name,
                'value' => $value,
            );
        }

        return view("pages.{$data['module_name']}.list", $data);
    }

    /**
     * 列表接口
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'sort_order');
        $sort = $request->input('sort', 'asc');
        $length = (int)$request->input('length', 10);
        $site_search = $request->input('site_search');
        $name_search = $request->input('name_search');

        if ($order == 'site_name') $order = 'site.name_json';
        if ($order == 'simple_tariff_table_name') $order = 'simple_tariff_table.name';
        if ($order == 'time_tariff_table_name') $order = 'time_tariff_table.name';
        if ($order == 'energy_tariff_table_name') $order = 'energy_tariff_table.name';
        if ($order == 'idling_penalty_tariff_table_name') $order = 'idling_penalty_tariff_table.name';
        if ($order == 'peak_time_table_name') $order = 'peak_time_table.name';

        $data_list = ConnectorSetting::query()
            ->select('connector_setting.*', 'simple_tariff_table.name as simple_tariff_table_name_init', 'time_tariff_table.name as time_tariff_table_name_init', 'energy_tariff_table.name as energy_tariff_table_name_init', 'idling_penalty_tariff_table.name as idling_penalty_tariff_table_name_init', 'peak_time_table.name as peak_time_table_name_init', 'site.name_json as site_name_json_init')
            ->leftJoin('simple_tariff_table', 'connector_setting.simple_tariff_table_number', '=', 'simple_tariff_table.simple_tariff_table_number')
            ->leftJoin('time_tariff_table', 'connector_setting.time_tariff_table_number', '=', 'time_tariff_table.time_tariff_table_number')
            ->leftJoin('energy_tariff_table', 'connector_setting.energy_tariff_table_number', '=', 'energy_tariff_table.energy_tariff_table_number')
            ->leftJoin('idling_penalty_tariff_table', 'connector_setting.idling_penalty_tariff_table_number', '=', 'idling_penalty_tariff_table.idling_penalty_tariff_table_number')
            ->leftJoin('peak_time_table', 'connector_setting.peak_time_table_number', '=', 'peak_time_table.peak_time_table_number')
            ->leftJoin('site', 'connector_setting.site_number', '=', 'site.site_number')
            ->when(filled($site_search), fn ($query) => $query->where('connector_setting.site_number', $site_search))
            ->when(filled($name_search), fn ($query) => $query->where('connector_setting.name', 'like', "%$name_search%"))
            ->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('connector_setting.site_number', auth()->user()->site_number_list))
            ->orderBy($order, $sort)
            ->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $site_name = $this->getValueFromLanguageArray($data->site_name_json_init) ?? '—/—';
            $result[] = array(
                'connector_setting_id' => $data->connector_setting_id, // 主键ID
                'connector_setting_number' => $data->connector_setting_number, // 充电枪设置编号
                'site_name' => $site_name, // 场地名称
                'name' => $data->name, // 名称
                'simple_tariff_table_name' => $data->simple_tariff_table_name_init ?? '—/—', // 简单收费表名称
                'time_tariff_table_name' => $data->time_tariff_table_name_init ?? '—/—', // 时间收费表名称
                'energy_tariff_table_name' => $data->energy_tariff_table_name_init ?? '—/—', // 电量收费表名称
                'idling_penalty_tariff_table_name' => $data->idling_penalty_tariff_table_name_init ?? '—/—', // 闲置罚款收费表名称
                'peak_time_table_name' => $data->peak_time_table_name_init ?? '—/—', // 高峰时间表名称
                'is_record_charge_record_meter_value' => $data->is_record_charge_record_meter_value, // 是否记录充电记录仪表值
                'is_remote_stop_when_no_charge_record' => $data->is_remote_stop_when_no_charge_record, // 是否在没有充电记录时远程停止充电
                'is_display_menu_extra_information' => $data->is_display_menu_extra_information, // 是否显示菜单额外信息
                'is_display_charging_extra_information' => $data->is_display_charging_extra_information, // 是否显示充电额外信息
                'is_enable_estimate_amount' => $data->is_enable_estimate_amount, // 是否启用预估金额
                'is_enable_verify_setting_token' => $data->is_enable_verify_setting_token, // 是否启用校验设置Token
                'is_enable_charge_arrears' => $data->is_enable_charge_arrears, // 是否启用充电欠款
                'is_enable_verify_park_sensor' => $data->is_enable_verify_park_sensor, // 是否启用校验停车传感器
                'park_sensor_allow_start_charge_timeout' => filled($data->park_sensor_allow_start_charge_timeout) ? (($data->park_sensor_allow_start_charge_timeout / 60) . __('common.unit_mins')) : '—/—', // 停车传感器允许开始充电超时
                'is_enable_push_notification' => $data->is_enable_push_notification, // 是否启用推送通知
                'is_allow_points_overdraft' => $data->is_allow_points_overdraft, // 是否允许积分透支
                'start_charge_minimum_points_limit' => filled($data->start_charge_minimum_points_limit) ? ($data->start_charge_minimum_points_limit / 100) : '—/—', // 开始充电最小积分限制
                'start_operation_timeout' => isset($data->start_operation_timeout) ? ($data->start_operation_timeout . __('common.unit_s')) : '—/—', // 开始操作超时时间
                'trial_charge_timeout' => ($data->trial_charge_timeout ?? 0) . __('common.unit_s'), // 试充超时时间
                'charge_maximum_vehicle_soc_limit' => isset($data->charge_maximum_vehicle_soc_limit) ? ($data->charge_maximum_vehicle_soc_limit . __('common.unit_percent_blank')) : '—/—', // 充电最大车辆SoC限制
                'tariff_table_type' => TariffTableType::getDescription($data->tariff_table_type), // 收费表类型
                'is_enable_member_card_group_tariff_table' => $data->is_enable_member_card_group_tariff_table, // 是否启用会员卡组收费表
                'is_enable_user_group_tariff_table' => $data->is_enable_user_group_tariff_table, // 是否启用用户组收费表
                'idling_penalty_grace_period' => filled($data->idling_penalty_grace_period) ? (($data->idling_penalty_grace_period / 60) . __('common.unit_mins')) : '—/—', // 闲置罚款宽限期
                'stop_operation_timeout' => isset($data->stop_operation_timeout) ? ($data->stop_operation_timeout . __('common.unit_s')) : '—/—', // 停止操作超时时间
                'merchant_handling_fee_rate' => isset($data->merchant_handling_fee_rate) ? ($data->merchant_handling_fee_rate . __('common.unit_percent_blank')) : '—/—', // 商户手续费率
                'sort_order' => $data->sort_order, // 排序
                'remark' => $data->remark ?? '—/—', // 备注
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );

        return response()->json($json);
    }

    /**
     * 获取设置名称
     *
     * @param $connector_setting_number
     * @return JsonResponse
     */
    public function getName($connector_setting_number): JsonResponse
    {
        $module_name = self::$module_name;
        $model = ConnectorSetting::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('connector_setting_number', $connector_setting_number);
        if (filled($connector_setting_number) && filled($model)) {
            $this->data = array(
                'name' => $model->name,
                'tariff_table_type' => $model->tariff_table_type ?? '',
                'simple_tariff_table_number' => $model->simple_tariff_table_number,
                'simple_tariff_table_name' => $model->simpleTariffTable?->name ?? '',
                'time_tariff_table_number' => $model->time_tariff_table_number,
                'time_tariff_table_name' => $model->timeTariffTable?->name ?? '',
                'energy_tariff_table_number' => $model->energy_tariff_table_number,
                'energy_tariff_table_name' => $model->energyTariffTable?->name ?? '',
                'idling_penalty_tariff_table_number' => $model->idling_penalty_tariff_table_number,
                'idling_penalty_tariff_table_name' => $model->idlingPenaltyTariffTable?->name ?? '',
                'peak_time_table_number' => $model->peak_time_table_number,
                'peak_time_table_name' => $model->peakTimeTable?->name ?? '',
                'idling_penalty_grace_period' => filled($model->idling_penalty_grace_period) ? ($model->idling_penalty_grace_period / 60) : null,
            );
        } else {
            $this->notFoundData(__("$module_name.connector_setting_number"));
        }

        return $this->returnJson();
    }

    /**
     * 获取connectorSetting
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getConnectorSetting(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        $connector_setting_number = $request->input('connector_setting_number');

        $connector_setting_model = ConnectorSetting::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('connector_setting_number', $connector_setting_number);
        if (blank($connector_setting_number) || blank($connector_setting_model)) {
            $this->missingField(__("$module_name.web_title"));
            return $this->returnJson();
        }

        $this->data = $connector_setting_model;

        return $this->returnJson();
    }

    /**
     * 表单页
     *
     * @param Request $request
     * @param array $data
     * @return View|Application|Factory
     */
    protected function getForm(Request $request, array $data): View|Application|Factory
    {
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action([self::class, 'showPage'], self::getUrlParams($request));

        if (blank($data['model']->connector_setting_id)) $data['model']->connector_setting_number = $request->old('connector_setting_number', $data['model']->connector_setting_number); // 充电枪设置编号
        $site_number = $data['model']->site_number;
        if ((isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) && blank($data['model']?->connector_setting_id)) {
            // 如果是新增，并且是超级管理员或者管理员的商户大于1的情况下就直接获取页面传入的商户编号
            $site_number = $request->old('site_number', $data['model']->site_number);
        } else if (blank($data['model']?->connector_setting_id) && auth()->user()->site_number_list->count() === 1) {
            // 如果是新增并且管理员商户只有一个就直接赋值
            $site_number = auth()->user()->site_number_list->first();
        }

        $data['model']->site_number = $site_number; // 场地编号
        $site_name = $this->getValueFromLanguageArray(
            Site::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->firstWhere('site_number', $site_number)?->name_json
        );
        $data['model']->site_name = $site_name; // 场地名称
        $data['model']->name = $request->old('name', $data['model']->name); // 名称
        $data['model']->is_record_charge_record_meter_value = $request->old('is_record_charge_record_meter_value', $data['model']->is_record_charge_record_meter_value); // 是否记录充电记录仪表值
        $data['model']->is_remote_stop_when_no_charge_record = $request->old('is_remote_stop_when_no_charge_record', $data['model']->is_remote_stop_when_no_charge_record); // 是否在没有充电记录时远程停止充电
        $data['model']->is_display_menu_extra_information = $request->old('is_display_menu_extra_information', $data['model']->is_display_menu_extra_information); // 是否显示菜单额外信息
        $data['model']->is_display_charging_extra_information = $request->old('is_display_charging_extra_information', $data['model']->is_display_charging_extra_information); // 是否显示充电额外信息
        $data['model']->is_enable_estimate_amount = $request->old('is_enable_estimate_amount', $data['model']->is_enable_estimate_amount); // 是否启用预估金额
        $data['model']->is_enable_verify_setting_token = $request->old('is_enable_verify_setting_token', $data['model']->is_enable_verify_setting_token); // 是否启用校验设置Token
        $data['model']->is_enable_charge_arrears = $request->old('is_enable_charge_arrears', $data['model']->is_enable_charge_arrears); // 是否启用充电欠款
        $data['model']->is_enable_verify_park_sensor = $request->old('is_enable_verify_park_sensor', $data['model']->is_enable_verify_park_sensor); // 是否启用校验停车传感器
        $data['model']->park_sensor_allow_start_charge_timeout = $request->old('park_sensor_allow_start_charge_timeout', filled($data['model']->park_sensor_allow_start_charge_timeout) ? ($data['model']->park_sensor_allow_start_charge_timeout / 60) : null); // 停车传感器允许开始充电超时
        $data['model']->is_enable_push_notification = $request->old('is_enable_push_notification', $data['model']->is_enable_push_notification); // 是否启用推送通知
        $data['model']->is_allow_points_overdraft = $request->old('is_allow_points_overdraft', $data['model']->is_allow_points_overdraft); // 是否允许积分透支
        $data['model']->start_charge_minimum_points_limit = $request->old('start_charge_minimum_points_limit', filled($data['model']->start_charge_minimum_points_limit) ? ($data['model']->start_charge_minimum_points_limit / 100) : null); // 开始充电最小积分限制
        $data['model']->start_operation_timeout = $request->old('start_operation_timeout', $data['model']->start_operation_timeout); // 开始操作超时时间
        $data['model']->trial_charge_timeout = $request->old('trial_charge_timeout', $data['model']->trial_charge_timeout); // 试充超时时间
        $data['model']->charge_maximum_vehicle_soc_limit = $request->old('charge_maximum_vehicle_soc_limit', $data['model']->charge_maximum_vehicle_soc_limit); // 充电最大车辆SoC限制
        $data['model']->tariff_table_type = $request->old('tariff_table_type', $data['model']->tariff_table_type); // 收费表类型
        $data['model']->is_enable_member_card_group_tariff_table = $request->old('is_enable_member_card_group_tariff_table', $data['model']->is_enable_member_card_group_tariff_table); // 是否启用会员卡组收费表
        $data['model']->is_enable_user_group_tariff_table = $request->old('is_enable_user_group_tariff_table', $data['model']->is_enable_user_group_tariff_table); // 是否启用用户组收费表

        $data['model']->stop_operation_timeout = $request->old('stop_operation_timeout', $data['model']->stop_operation_timeout); // 停止操作超时时间
        $data['model']->merchant_handling_fee_rate = $request->old('merchant_handling_fee_rate', $data['model']->merchant_handling_fee_rate); // 商户手续费率

        // 获取收费表类型对应表数据
        $tariff_table_type_result_list = $this->getTariffTableTypeInfo($data['model']->tariff_table_type, $request->old() ?: $data['model']);
        $data['model']->simple_tariff_table_number = $tariff_table_type_result_list['simple_tariff_table_number'] ?: $data['model']->simple_tariff_table_number;
        $data['model']->time_tariff_table_number = $tariff_table_type_result_list['time_tariff_table_number'] ?: $data['model']->time_tariff_table_number;
        $data['model']->energy_tariff_table_number = $tariff_table_type_result_list['energy_tariff_table_number'] ?: $data['model']->energy_tariff_table_number;
        $data['model']->idling_penalty_tariff_table_number = $tariff_table_type_result_list['idling_penalty_tariff_table_number'] ?: $data['model']->idling_penalty_tariff_table_number;
        $data['model']->peak_time_table_number = $tariff_table_type_result_list['peak_time_table_number'] ?: $data['model']->peak_time_table_number;
        $data['model']->idling_penalty_grace_period = $request->old('idling_penalty_grace_period', filled($data['model']->idling_penalty_grace_period) ? ($data['model']->idling_penalty_grace_period / 60) : null); // 闲置罚款宽限期

        $data['model']->remark = $request->old('remark', $data['model']->remark); // 备注
        $data['model']->sort_order = $request->old('sort_order', $data['model']->sort_order); // 排序

        // 查询设置收费表对应名称,提交失败时显示值不会被刷新
        if (filled($data['model']->simple_tariff_table_number)) {
            $simpleTariffTable = SimpleTariffTable::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->firstWhere('simple_tariff_table_number', $data['model']->simple_tariff_table_number);
            $data['model']->simple_tariff_table_name = $simpleTariffTable?->name ?? '';
        }
        if (filled($data['model']->time_tariff_table_number)) {
            $timeTariffTable = TimeTariffTable::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->firstWhere('time_tariff_table_number', $data['model']->time_tariff_table_number);
            $data['model']->time_tariff_table_name = $timeTariffTable?->name ?? '';
        }
        if (filled($data['model']->energy_tariff_table_number)) {
            $energyTariffTable = EnergyTariffTable::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->firstWhere('energy_tariff_table_number', $data['model']->energy_tariff_table_number);
            $data['model']->energy_tariff_table_name = $energyTariffTable?->name ?? '';
        }
        if (filled($data['model']->idling_penalty_tariff_table_number)) {
            $idlingPenaltyTariffTable = IdlingPenaltyTariffTable::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->firstWhere('idling_penalty_tariff_table_number', $data['model']->idling_penalty_tariff_table_number);
            $data['model']->idling_penalty_tariff_table_name = $idlingPenaltyTariffTable?->name ?? '';
        }
        if (filled($data['model']->peak_time_table_number)) {
            $peakTimeTable = PeakTimeTable::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->firstWhere('peak_time_table_number', $data['model']->peak_time_table_number);
            $data['model']->peak_time_table_name = $peakTimeTable?->name ?? '';
        }

        // 是否更改试充规则
        $data['is_modified_trial_charge_rule'] = $request->old('is_modified_trial_charge_rule', 0);
        /* 获取当前试充规则数据 根据是否更改试充规则来判断需要旧数据还是模型里的数据，
        防止编辑时试充规则原本有数据，然后清空试充规则并且有些字段没过验证的情况下回填数据时把试充规则模型里的数据回填了 */
        if (filled($data['model']->connector_setting_id) || filled($request->input('_connector_setting_number'))) {
            // 编辑的情况下 和 复制到新增的情况下
            $data['trial_charge_rule_list'] = $data['is_modified_trial_charge_rule'] ? $request->old('trial_charge_rule', []) : $data['model']->trialChargeRule;
        } else {
            // 新增的情况下
            $data['trial_charge_rule_list'] = $request->old('trial_charge_rule', []);
        }

        // 防止在复制到新增的时候因为is_modified_trial_charge_rule第一次进去默认为false导致数据无法新增进去，所以如果是复制到新增的话就直接为true，为true就是已修改
        if (filled($request->input('_connector_setting_number'))) $data['is_modified_trial_charge_rule'] = 1;

        $config_languages = config('languages');
        // 单独加入双语语言，只有kiosk需要显示双语，cms不需要双语
        $config_languages['en_US_zh_HK'] = __('common.en_US_zh_HK');
        foreach ($config_languages as $language_code => $language_name) {
            // 有旧数据
            $connector_setting_description_old_list = $request->old('item');
            if (filled($connector_setting_description_old_list)) {
                $data['item'][$language_code] = array(
                    'connector_setting_description_id' => $connector_setting_description_old_list[$language_code]['connector_setting_description_id'] ?? null, // ID
                    'language_name' => $language_name,
                    'language_code' => $language_code,
                    'tariff_table_name' => $connector_setting_description_old_list[$language_code]['tariff_table_name'] ?? null,
                );
            } else {
                $connector_setting_description_result = $data['model']->description->where('language_code', $language_code)->first();
                $data['item'][$language_code] = array(
                    'connector_setting_description_id' => $connector_setting_description_result->connector_setting_description_id ?? null, // ID
                    'language_name' => $language_name,
                    'language_code' => $language_code,
                    'tariff_table_name' => $connector_setting_description_result->tariff_table_name ?? null,
                );
            }
        }

        // 供应商
        $data['vendor_list'] = array(
            ChargePointVendor::EVPowerCS => ChargePointVendor::getDescription(ChargePointVendor::EVPowerCS),
            ChargePointVendor::SchneiderProAC16 => ChargePointVendor::getDescription(ChargePointVendor::SchneiderProAC16),
        );

        return view("pages.{$data['module_name']}.form", $data);
    }

    /**
     * 添加
     *
     * @param Request $request
     * @return Factory|Application|View|RedirectResponse
     */
    public function add(Request $request): Factory|Application|View|RedirectResponse
    {
        $data = array();

        $model = $this->model;

        // 如果有connector_setting_number的存在就是复制除了connector_setting_number和name以外的收费表数据输出到新增页面
        if (filled($connector_setting_number = $request->input('_connector_setting_number'))) {
            $model = $model->with(['description', 'trialChargeRule'])->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->where('connector_setting_number', $connector_setting_number)->firstOrFail();
            $model->connector_setting_id = $model->connector_setting_number = $model->name = null;
            foreach ($model->description as $description) {
                $description->connector_setting_number = $description->connector_setting_description_id = null;
            }
            foreach ($model->trialChargeRule as $trial_charge_rule) {
                $trial_charge_rule->connector_setting_number = $trial_charge_rule->connector_setting_trial_charge_rule_id = null;
            }
        }

        if ($request->isMethod('post')) {
            return $this->modelValidateAndSave($request, $model);
        }

        $data['model'] = $model;
        return $this->getForm($request, $data);
    }

    /**
     * 切换设置
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function editSetting(Request $request): JsonResponse
    {
        $tariff_table_type = $request->input('tariff_table_type');
        $connector_setting_number = $request->input('connector_setting_number');

        $connector_setting = ConnectorSetting::with('connector')
            ->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('connector_setting_number', $connector_setting_number);

        $request->validate(self::rulesTariffTable($tariff_table_type, $connector_setting->site_number, $connector_setting), [], self::attributes());

        // 获取收费表类型对应表数据
        $tariff_table_type_result_list = $this->getTariffTableTypeInfo($tariff_table_type, $request);
        $connector_setting->tariff_table_type = $tariff_table_type;
        $connector_setting->simple_tariff_table_number = $tariff_table_type_result_list['simple_tariff_table_number'];
        $connector_setting->time_tariff_table_number = $tariff_table_type_result_list['time_tariff_table_number'];
        $connector_setting->energy_tariff_table_number = $tariff_table_type_result_list['energy_tariff_table_number'];
        $connector_setting->idling_penalty_tariff_table_number = $tariff_table_type_result_list['idling_penalty_tariff_table_number'];
        $connector_setting->peak_time_table_number = $tariff_table_type_result_list['peak_time_table_number'];
        $connector_setting->idling_penalty_grace_period = $request->input('idling_penalty_grace_period');
        if (filled($connector_setting->idling_penalty_grace_period)) {
            $connector_setting->idling_penalty_grace_period *= 60;
        }
        $connector_setting->save();

        self::setConnectorTokenBySetting($connector_setting);
        self::sendInitPushByKioskNumberList();

        return $this->returnJson();
    }

    /**
     * 删除
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function delete(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        $connector_setting_number = $request->input('connector_setting_number');

        $connector_setting = ConnectorSetting::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('connector_setting_number', $connector_setting_number);
        if (filled($connector_setting_number) && filled($connector_setting)) {
            if (blank($connector_list = $connector_setting?->connector)) {
                // 并且删除关联的描述
                $connector_setting->description()->delete();
                $connector_setting->trialChargeRule()->delete();
                $connector_setting->delete();

                self::sendInitPushByKioskNumberList();
            } else {
                $connector_str = '';
                foreach ($connector_list as $connector) {
                    $connector_str .= '<li>' . $connector->name . '</li>';
                }
                $this->code = 201;
                $this->message = __('common.error_has_binding_unable_to_delete_connector_setting', [
                    'connector' => $connector_str
                ]);
            }
        } else {
            $this->code = 404;
            $this->message = __('common.text_not_found', ['field' => __("$module_name.connector_setting_number")]);
        }

        return $this->returnJson();
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @param ConnectorSetting $model
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    protected function modelValidateAndSave(Request $request, ConnectorSetting $model): RedirectResponse
    {
        $tariff_table_type = $request->input('tariff_table_type');

        $request->validate(self::rules($request, $model), [], self::attributes());
        // 判断是否是新增还是编辑
        if (blank($model->connector_setting_id)) {
            $model = $this->model;
            $model->connector_setting_number = $request->input('connector_setting_number');
            $site_number = $model->site_number;

            if (isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) {
                // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下就直接获取页面传入的场地编号
                $site_number = $request->input('site_number');
            } else if (auth()->user()->site_number_list->count() === 1) {
                // 如果是新增并且管理员场地只有一个就直接赋值
                $site_number = auth()->user()->site_number_list->first();
            }
            $model->site_number = $site_number;
            $model->merchant_number = Site::firstWhere('site_number', $model->site_number)?->merchant_number;
        }

        $model->name = $request->input('name');
        $model->is_record_charge_record_meter_value = $request->input('is_record_charge_record_meter_value', 0);
        $model->is_remote_stop_when_no_charge_record = $request->input('is_remote_stop_when_no_charge_record', 0);
        $model->is_display_menu_extra_information = $request->input('is_display_menu_extra_information', 0);
        $model->is_display_charging_extra_information = $request->input('is_display_charging_extra_information', 0);
        $model->is_enable_estimate_amount = $request->input('is_enable_estimate_amount', 0);
        $model->is_enable_verify_setting_token = $request->input('is_enable_verify_setting_token', 0);
        $model->is_enable_charge_arrears = $request->input('is_enable_charge_arrears', 0);
        $model->is_enable_verify_park_sensor = $request->input('is_enable_verify_park_sensor', 0);
        $model->park_sensor_allow_start_charge_timeout = $request->input('park_sensor_allow_start_charge_timeout', null);
        if (filled($model->park_sensor_allow_start_charge_timeout)) {
            $model->park_sensor_allow_start_charge_timeout *= 60;
        }
        $model->is_enable_push_notification = $request->input('is_enable_push_notification', 0);
        $model->is_allow_points_overdraft = $request->input('is_allow_points_overdraft', 0);
        $model->start_charge_minimum_points_limit = $request->input('start_charge_minimum_points_limit');
        if (filled($model->start_charge_minimum_points_limit)) {
            $model->start_charge_minimum_points_limit *= 100;
        }
        $model->start_operation_timeout = $request->input('start_operation_timeout');
        $model->trial_charge_timeout = $request->input('trial_charge_timeout');
        $model->charge_maximum_vehicle_soc_limit = $request->input('charge_maximum_vehicle_soc_limit');
        $model->tariff_table_type = $tariff_table_type;
        $model->is_enable_member_card_group_tariff_table = $request->input('is_enable_member_card_group_tariff_table', 0);
        $model->is_enable_user_group_tariff_table = $request->input('is_enable_user_group_tariff_table', 0);
        $model->stop_operation_timeout = $request->input('stop_operation_timeout');
        $model->merchant_handling_fee_rate = $request->input('merchant_handling_fee_rate');
        $model->remark = $request->input('remark');
        $model->sort_order = $request->input('sort_order', 0);
        // 获取试充规则数据
        $trial_charge_rule_list = $request->input('trial_charge_rule', array());
        // 是否更改试充规则
        $is_modified_trial_charge_rule = $request->input('is_modified_trial_charge_rule', 0);

        // 获取收费表类型对应表数据
        $tariff_table_type_result_list = $this->getTariffTableTypeInfo($tariff_table_type, $request);
        $model->simple_tariff_table_number = $tariff_table_type_result_list['simple_tariff_table_number'];
        $model->time_tariff_table_number = $tariff_table_type_result_list['time_tariff_table_number'];
        $model->energy_tariff_table_number = $tariff_table_type_result_list['energy_tariff_table_number'];
        $model->idling_penalty_tariff_table_number = $tariff_table_type_result_list['idling_penalty_tariff_table_number'];
        $model->peak_time_table_number = $tariff_table_type_result_list['peak_time_table_number'];
        $model->idling_penalty_grace_period = $request->input('idling_penalty_grace_period');
        if (filled($model->idling_penalty_grace_period)) {
            $model->idling_penalty_grace_period *= 60;
        }

        $model->save();

        // 获取充电枪详情数据
        $connector_setting_description_list = $request->input('item', array());

        $config_languages = config('languages');
        // 单独加入双语语言，只有kiosk需要显示双语，cms不需要双语
        $config_languages['en_US_zh_HK'] = __('common.en_US_zh_HK');

        // 保存Connector Setting后再保存并关联Connector Setting描述
        foreach ($connector_setting_description_list as $connector_setting_description) {
            if (
                !isset($connector_setting_description['connector_setting_description_id']) ||
                blank($connector_setting_description_model = ConnectorSettingDescription::find($connector_setting_description['connector_setting_description_id']))
            ) {
                $connector_setting_description_model = new ConnectorSettingDescription;
            }
            $connector_setting_description_model->connector_setting_number = $model->connector_setting_number;
            $connector_setting_description_model->language_code = $connector_setting_description['language_code'];
            $connector_setting_description_model->tariff_table_name = $connector_setting_description['tariff_table_name'] ?? null;

            $connector_setting_description_model->save();
        }

        if (filled($model->connector_setting_id) && $is_modified_trial_charge_rule == 1) {
            // 先清空试充规则
            $model->trialChargeRule()->delete();

            foreach ($trial_charge_rule_list as $trial_charge_rule) {
                $trial_charge_rule_model = new ConnectorSettingTrialChargeRule;
                $trial_charge_rule_model->connector_setting_number = $model->connector_setting_number;
                $trial_charge_rule_model->context = $trial_charge_rule['context'];
                $trial_charge_rule_model->measurand = $trial_charge_rule['measurand'];
                $trial_charge_rule_model->standard_value = $trial_charge_rule['standard_value'];
                $trial_charge_rule_model->save();
            }
        }

        self::setConnectorTokenBySetting($model);
        self::sendInitPushByKioskNumberList();

        return redirect()->action(
            [self::class, 'showPage'],
            self::getUrlParams($request)
        );
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function rules(?Request $request, ?Model $model): array
    {
        $module_name = self::$module_name;
        $tariff_table_type = $request->input('tariff_table_type');
        $site_number = $model->site_number;
        if ((isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) && blank($model?->connector_setting_id)) {
            // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下就直接获取页面传入的场地编号
            $site_number = $request->input('site_number');
        } else if (blank($model?->connector_setting_id) && auth()->user()->site_number_list->count() === 1) {
            // 如果是新增并且管理员场地只有一个就直接赋值
            $site_number = auth()->user()->site_number_list->first();
        }

        $rules = array(
            'name' => 'required|string|max:45',
            'is_record_charge_record_meter_value' => 'bool',
            'is_remote_stop_when_no_charge_record' => 'bool',
            'is_display_menu_extra_information' => 'bool',
            'is_display_charging_extra_information' => 'bool',
            'is_enable_estimate_amount' => 'bool',
            'is_enable_verify_setting_token' => 'bool',
            'is_enable_charge_arrears' => 'bool',
            'is_enable_verify_park_sensor' => 'bool',
            'park_sensor_allow_start_charge_timeout' => 'nullable|integer|min:0|max:999999',
            'is_enable_push_notification' => 'bool',
            'is_allow_points_overdraft' => 'bool',
            'start_charge_minimum_points_limit' => 'nullable|integer|min:-999999|max:999999',
            'start_operation_timeout' => 'nullable|integer|min:0|max:999999',
            'trial_charge_timeout' => 'nullable|integer|min:0|max:999999',
            'charge_maximum_vehicle_soc_limit' => 'nullable|integer|min:0|max:100',
            'is_enable_member_card_group_tariff_table' => 'bool',
            'is_enable_user_group_tariff_table' => 'bool',
            'idling_penalty_grace_period' => 'nullable|integer|min:0|max:999999',
            'remark' => 'nullable|string|max:1000',
            'stop_operation_timeout' => 'nullable|integer|min:0|max:999999',
            'merchant_handling_fee_rate' => 'nullable|numeric|min:0|max:100',
            'sort_order' => 'integer|min:0',
        );

        // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下才需要判断场地编号是否存在
        if (blank($model->connector_setting_id)) {
            $rules['connector_setting_number'] = [
                'required',
                'unique:App\Models\Modules\ConnectorSetting,connector_setting_number',
                'max:30',
                'regex:/^[a-zA-Z0-9]+$/', // 只能填写字母和数字
            ];
            if (isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) {
                $rules['site_number'] = [
                    'required',
                    'exists:App\Models\Modules\Site,site_number',
                    function ($attr, $value, $fail) use ($module_name, $model) {
                        // 判断当前用户是否有该场地权限
                        $site = Site::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('site_number', $value);
                        if (blank($site)) {
                            $fail(__('common.text_not_found', ['field' => __("$module_name.site")]));
                        }
                    },
                ];
            }
        }

        $rules = array_merge($rules, self::rulesTariffTable($tariff_table_type, $site_number, $model));

        $config_languages = config('languages');
        // 单独加入双语语言，只有kiosk需要显示双语，cms不需要双语
        $config_languages['en_US_zh_HK'] = __('common.en_US_zh_HK');
        $language_codes = array_keys($config_languages);
        $rules['item.*.language_code'] = Rule::in($language_codes);

        $rules['item.*.tariff_table_name'] = 'nullable|string|max:255';

        // 存在并且为数组才开始判断，否则不判断
        if ($request->has('trial_charge_rule') && is_array($request->input('trial_charge_rule'))) {
            // 判断存在的情况下的重复值，数据是否合法是由下方去单独判断，这个只判断整体不重复
            $rules['trial_charge_rule'] = function ($attr, $value, $fail) use ($module_name) {
                $trial_charge_rule_list = array();
                foreach ($value as $trial_charge_rule) {
                    if (filled($trial_charge_rule['context']) && filled($trial_charge_rule['measurand'])) {
                        // 如果不在数组里面就添加进入数组，如果在数组里面就加入错误信息
                        $context_join_measurand = $trial_charge_rule['context'] . '_' . $trial_charge_rule['measurand'];
                        if (!in_array($context_join_measurand, $trial_charge_rule_list)) {
                            $trial_charge_rule_list[] = $context_join_measurand;
                        } else {
                            $fail(__("$module_name.duplicated_trial_charge_rule_combination"));
                        }
                    }
                }
            };
            $rules['trial_charge_rule.*.context'] = 'required|enum_value:' . ReadingContextEnum::class;
            $rules['trial_charge_rule.*.measurand'] = 'required|enum_value:' . MeasurandEnum::class;
            $rules['trial_charge_rule.*.standard_value'] = 'required|string|max:45';
        }
        return $rules;
    }

    /**
     * 获取应用于收费表修改请求的验证规则。
     *
     * @param $tariff_table_type
     * @param $site_number
     * @return array
     */
    protected static function rulesTariffTable($tariff_table_type, $site_number, $model): array
    {
        $module_name = self::$module_name;

        $rules['tariff_table_type'] = 'required|string|enum_value:' . TariffTableType::class;

        match ($tariff_table_type) {
            TariffTableType::SimpleTariffTable => $rules['simple_tariff_table_number'] = [
                'required',
                'string',
                'max:45',
                function ($attr, $value, $fail) use ($module_name, $site_number) {
                    if (blank(SimpleTariffTable::where('site_number', $site_number)->firstWhere('simple_tariff_table_number', $value))) {
                        $fail(__("$module_name.select_under_this_site"));
                    }
                },
            ], // 简单收费表
            TariffTableType::ComplexTimeTariffTable => $rules['time_tariff_table_number'] = [
                'required',
                'string',
                'max:45',
                function ($attr, $value, $fail) use ($module_name, $site_number) {
                    if (blank(TimeTariffTable::where('site_number', $site_number)->firstWhere('time_tariff_table_number', $value))) {
                        $fail(__("$module_name.select_under_this_site"));
                    }
                },
            ], // 复杂时间收费表
            TariffTableType::ComplexEnergyTariffTable => $rules['energy_tariff_table_number'] = [
                'required',
                'string',
                'max:45',
                function ($attr, $value, $fail) use ($module_name, $site_number) {
                    if (blank(EnergyTariffTable::where('site_number', $site_number)->firstWhere('energy_tariff_table_number', $value))) {
                        $fail(__("$module_name.select_under_this_site"));
                    }
                },
            ], // 复杂电量收费表
            default => '',
        };

        $rules['idling_penalty_tariff_table_number_SIMPLE_TARIFF_TABLE'] =
        $rules['idling_penalty_tariff_table_number_COMPLEX_TIME_TARIFF_TABLE'] =
        $rules['idling_penalty_tariff_table_number_COMPLEX_ENERGY_TARIFF_TABLE'] = [
            'nullable',
            'string',
            'max:45',
            function ($attr, $value, $fail) use ($module_name, $site_number) {
                if (blank(IdlingPenaltyTariffTable::where('site_number', $site_number)->firstWhere('idling_penalty_tariff_table_number', $value))) {
                    $fail(__("$module_name.select_under_this_site"));
                }
            },
        ];
        $rules['peak_time_table_number_SIMPLE_TARIFF_TABLE'] =
        $rules['peak_time_table_number_COMPLEX_TIME_TARIFF_TABLE'] =
        $rules['peak_time_table_number_COMPLEX_ENERGY_TARIFF_TABLE'] = [
            'nullable',
            'string',
            'max:45',
            function ($attr, $value, $fail) use ($module_name, $site_number) {
                if (blank(PeakTimeTable::where('site_number', $site_number)->firstWhere('peak_time_table_number', $value))) {
                    $fail(__("$module_name.select_under_this_site"));
                }
            },
        ];

        $rules['idling_penalty_grace_period'] = [
            'nullable',
            'integer',
            'min:0',
        ];

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'site_number' => __("$module_name.site"),
            'connector_setting_number' => __("$module_name.connector_setting_number"),
            'name' => __("$module_name.name"),
            'is_record_charge_record_meter_value' => __("$module_name.is_record_charge_record_meter_value"),
            'is_remote_stop_when_no_charge_record' => __("$module_name.is_remote_stop_when_no_charge_record"),
            'is_display_menu_extra_information' => __("$module_name.is_display_menu_extra_information"),
            'is_display_charging_extra_information' => __("$module_name.is_display_charging_extra_information"),
            'is_enable_estimate_amount' => __("$module_name.is_enable_estimate_amount"),
            'is_enable_verify_setting_token' => __("$module_name.is_enable_verify_setting_token"),
            'is_enable_charge_arrears' => __("$module_name.is_enable_charge_arrears"),
            'is_enable_verify_park_sensor' => __("$module_name.is_enable_verify_park_sensor"),
            'park_sensor_allow_start_charge_timeout' => __("$module_name.park_sensor_allow_start_charge_timeout"),
            'is_enable_push_notification' => __("$module_name.is_enable_push_notification"),
            'is_allow_points_overdraft' => __("$module_name.is_allow_points_overdraft"),
            'start_charge_minimum_points_limit' => __("$module_name.start_charge_minimum_points_limit"),
            'start_operation_timeout' => __("$module_name.start_operation_timeout"),
            'trial_charge_timeout' => __("$module_name.trial_charge_timeout"),
            'charge_maximum_vehicle_soc_limit' => __("$module_name.charge_maximum_vehicle_soc_limit"),
            'tariff_table_type' => __("$module_name.tariff_table_type"),
            'is_enable_member_card_group_tariff_table' => __("$module_name.is_enable_member_card_group_tariff_table"),
            'is_enable_user_group_tariff_table' => __("$module_name.is_enable_user_group_tariff_table"),
            // 收费表模式不同，返回的名称也要不同
            'simple_tariff_table_number_SIMPLE_TIME_TARIFF_TABLE' => __("$module_name.simple_tariff_table"),
            'simple_tariff_table_number_SIMPLE_ENERGY_TARIFF_TABLE' => __("$module_name.simple_tariff_table"),
            'simple_tariff_table_number' => __("$module_name.simple_tariff_table"),
            'time_tariff_table_number' => __("$module_name.time_tariff_table"),
            'energy_tariff_table_number' => __("$module_name.energy_tariff_table"),
            'idling_penalty_tariff_table_number_SIMPLE_TARIFF_TABLE' => __("$module_name.idling_penalty_tariff_table"),
            'peak_time_table_number_SIMPLE_TARIFF_TABLE' => __("$module_name.peak_time_table"),
            'idling_penalty_grace_period_SIMPLE_TARIFF_TABLE' => __("$module_name.idling_penalty_grace_period"),
            'idling_penalty_tariff_table_number_COMPLEX_TIME_TARIFF_TABLE' => __("$module_name.idling_penalty_tariff_table"),
            'peak_time_table_number_COMPLEX_TIME_TARIFF_TABLE' => __("$module_name.peak_time_table"),
            'idling_penalty_tariff_table_number_COMPLEX_ENERGY_TARIFF_TABLE' => __("$module_name.idling_penalty_tariff_table"),
            'idling_penalty_grace_period_COMPLEX_ENERGY_TARIFF_TABLE' => __("$module_name.idling_penalty_grace_period"),
            'peak_time_table_number_COMPLEX_ENERGY_TARIFF_TABLE' => __("$module_name.peak_time_table"),
            'stop_operation_timeout' => __("$module_name.stop_operation_timeout"),
            'merchant_handling_fee_rate' => __("$module_name.merchant_handling_fee_rate"),

            'remark' => __("$module_name.remark"),
            'sort_order' => __("$module_name.sort_order"),
            'item.*.tariff_table_name' => __("$module_name.tariff_table_name"),
            'item.*.language_code' => __("$module_name.language_code"),
            'trial_charge_rule.*.context' => __("$module_name.context"),
            'trial_charge_rule.*.measurand' => __("$module_name.measurand"),
            'trial_charge_rule.*.standard_value' => __("$module_name.standard_value"),
        ];
    }

    /**
     * 返回地址栏参数
     *
     * @param Request $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => 'true',
            'site_search' => $request->get('site_search'),
            'name_search' => $request->get('name_search'),
        );
    }

    /**
     * 获取收费表类型对应表数据
     */
    private function getTariffTableTypeInfo($tariff_table_type, $data): array
    {
        $tariff_table_type_result_list = array(
            'simple_tariff_table_number' => null,
            'time_tariff_table_number' => null,
            'energy_tariff_table_number' => null,
            'idling_penalty_tariff_table_number' => null,
            'peak_time_table_number' => null,
        );
        match ($tariff_table_type) {
            TariffTableType::SimpleTariffTable => [
                $tariff_table_type_result_list['simple_tariff_table_number'] = $data['simple_tariff_table_number'] ?? null,
                $tariff_table_type_result_list['idling_penalty_tariff_table_number'] = $data['idling_penalty_tariff_table_number_' . $tariff_table_type] ?? null,
                $tariff_table_type_result_list['peak_time_table_number'] = $data['peak_time_table_number_' . $tariff_table_type] ?? null,
            ],
            TariffTableType::ComplexTimeTariffTable => [
                $tariff_table_type_result_list['time_tariff_table_number'] = $data['time_tariff_table_number'] ?? null,
                $tariff_table_type_result_list['idling_penalty_tariff_table_number'] = $data['idling_penalty_tariff_table_number_' . $tariff_table_type] ?? null,
                $tariff_table_type_result_list['peak_time_table_number'] = $data['peak_time_table_number_' . $tariff_table_type] ?? null,
            ],
            TariffTableType::ComplexEnergyTariffTable => [
                $tariff_table_type_result_list['energy_tariff_table_number'] = $data['energy_tariff_table_number'] ?? null,
                $tariff_table_type_result_list['idling_penalty_tariff_table_number'] = $data['idling_penalty_tariff_table_number_' . $tariff_table_type] ?? null,
                $tariff_table_type_result_list['peak_time_table_number'] = $data['peak_time_table_number_' . $tariff_table_type] ?? null,
            ],
            default => '',
        };
        return $tariff_table_type_result_list;
    }
}

<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Validation\Rule;
use Illuminate\Http\{
    JsonResponse,
    RedirectResponse,
    Request,
};
use App\Http\Controllers\Admin\Traits\{
    Add,
    Edit,
};
use Illuminate\Foundation\Application;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use App\Models\Modules\{
    AdminOctopusCard,
    Site,
};
use PhpOffice\PhpSpreadsheet\Reader\Exception;

class AdminOctopusCardController extends CommonController
{
    use Add, Edit;

    protected static string $module_name = 'adminOctopusCard'; // 模块名称
    protected static bool $module_check_site = true; // 标记该模块校验场地

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new AdminOctopusCard;
    }

    /**
     * 初始页
     *
     * @param Request $request
     * @return View|Application|Factory
     */
    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'add_url' => action([self::class, 'add']),
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'octopus_card_number_search' => $request->input('octopus_card_number_search'),
            'site_search' => $request->get('site_search'),
            'site_list' => $this->getSiteOptionList(),
        );

        return view("pages.{$data['module_name']}.list", $data);
    }

    /**
     * 列表接口
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        $draw = (int)$request->input('draw', 1);
        $length = (int)$request->input('length', 10);
        $search = $this->_searchList($request);
        $data_list = $search->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $site_name = $this->getValueFromLanguageArray($data->site_name_json_init) ?? '—/—';
            $result[] = array(
                'admin_octopus_card_id' => $data->admin_octopus_card_id, // 管理员八达通卡ID
                'site_name' => $site_name, // 场地名称
                'octopus_card_number' => $data->octopus_card_number, // 卡号
                'is_enable' => $data->is_enable, // 是否启用
                'remark' => $data->remark ?? '—/—', // 备注
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );
        return response()->json($json);
    }

    /**
     * 表单页
     *
     * @param Request $request
     * @param array $data
     * @return View|Application|Factory
     */
    protected function getForm(Request $request, array $data): View|Application|Factory
    {
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'], self::getUrlParams($request)
        );
        $site_number = $data['model']->site_number;
        if ((isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) && blank($data['model']?->admin_octopus_card_id)) {
            // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下就直接获取页面传入的场地编号
            $site_number = $request->old('site_number', $data['model']->site_number);
        } else if (blank($data['model']?->admin_octopus_card_id) && auth()->user()->site_number_list->count() === 1) {
            // 如果是新增并且管理员场地只有一个就直接赋值
            $site_number = auth()->user()->site_number_list->first();
        }
        $site_name = $this->getValueFromLanguageArray(
            Site::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->firstWhere('site_number', $site_number)?->name_json
        );
        $data['model']->site_number = $site_number;
        $data['model']->site_name = $site_name;
        $data['model']->octopus_card_number = $request->old('octopus_card_number', $data['model']->octopus_card_number);
        $data['model']->is_enable = $request->old('is_enable', $data['model']->is_enable);
        $data['model']->remark = $request->old('remark', $data['model']->remark);

        return view("pages.{$data['module_name']}.form", $data);
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @param AdminOctopusCard $model
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    protected function modelValidateAndSave(Request $request, AdminOctopusCard $model): RedirectResponse
    {
        $request->validate(self::rules($request, $model), [], self::attributes());

        if ((isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) && blank($model?->admin_octopus_card_id)) {
            // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下就直接获取页面传入的场地编号
            $model->site_number = $request->input('site_number');
        } else if (blank($model?->admin_octopus_card_id) && auth()->user()->site_number_list->count() === 1) {
            // 如果是新增并且管理员场地只有一个就直接赋值
            $model->site_number = auth()->user()->site_number_list->first();
        }

        // 如果是新增并且场地有数据就把场地的商户编号赋值给管理员八达通卡商户编号
        if (filled($site = Site::firstWhere('site_number', $model?->site_number)) && blank($model?->admin_octopus_card_id)) {
            $model->merchant_number = $site->merchant_number;
        }
        $model->octopus_card_number = $request->input('octopus_card_number');
        $model->is_enable = $request->input('is_enable', 0);
        $model->remark = $request->input('remark');
        $model->save();

        self::setSiteAllConnectorToken($model->site_number);
        self::sendInitPushByKioskNumberList();

        return redirect()->action(
            [self::class, 'showPage'], self::getUrlParams($request)
        );
    }

    public function delete(Request $request): JsonResponse
    {
        $id = $request->post('id');

        if (!empty($id)) {
            $model = $this->model::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->find($id);
            if (filled($model)) {
                $site_number = $model->site_number;
                $model->delete();

                self::setSiteAllConnectorToken($site_number);
                self::sendInitPushByKioskNumberList();
            } else {
                $this->notFoundData('ID');
            }
        } else {
            $this->notFoundData('ID');
        }

        return $this->returnJson();
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function rules(?Request $request, ?Model $model): array
    {
        $module_name = self::$module_name;

        $site_number = $model->site_number;
        if ((isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) && blank($model?->admin_octopus_card_id)) {
            // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下就直接获取页面传入的场地编号
            $site_number = $request->input('site_number');
        } else if (blank($model?->admin_octopus_card_id) && auth()->user()->site_number_list->count() === 1) {
            // 如果是新增并且管理员场地只有一个就直接赋值
            $site_number = auth()->user()->site_number_list->first();
        }

        $rules = array(
            'octopus_card_number' => [
                'required',
                'numeric',
                'regex:/^[0-9]{0,9}$/',
                // 相同场地唯一
                Rule::unique('App\Models\Modules\AdminOctopusCard', 'octopus_card_number')
                    ->where(fn($query) => $query->where('site_number', $site_number))
                    ->where(fn($query) => $query->where('admin_octopus_card_id', '!=', $model?->admin_octopus_card_id)),
            ],
            'is_enable' => 'boolean',
            'remark' => 'nullable|max:1000',
        );

        // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下才需要判断场地编号是否存在
        if ((isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) && blank($model?->admin_octopus_card_id)) {
            $rules['site_number'] = [
                'required',
                'exists:App\Models\Modules\Site,site_number',
                function ($attr, $value, $fail) use ($module_name, $request) {
                    // 判断选择的site是否为当前管理员权限下的
                    $site = Site::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('site_number', $value);
                    if (blank($site)) $fail(__('common.text_not_found', ['field' => __("$module_name.site")]));
                },
            ];
        }

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'site_number' => __("$module_name.site"),
            'octopus_card_number' => __("$module_name.octopus_card_number"),
            'is_enable' => __("$module_name.is_enable"),
            'remark' => __("$module_name.remark"),
        ];
    }

    /**
     * 返回地址栏参数
     *
     * @param Request|null $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => 'true',
            'octopus_card_number_search' => $request->get('octopus_card_number_search'),
            'site_search' => $request->get('site_search'),
        );
    }

    /**
     * 获取搜索数据
     *
     * @param Request $request
     * @return mixed
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-11-08
     */
    protected function _searchList(Request $request): mixed
    {
        // dataTable字段
        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $octopus_card_number_search = $request->input('octopus_card_number_search');
        $site_search = $request->input('site_search');

        if ($order == 'site_name') $order = 'site.name_json';

        return AdminOctopusCard::query()
            ->select('admin_octopus_card.*', 'site.name_json as site_name_json_init')
            ->leftJoin('site', 'admin_octopus_card.site_number', 'site.site_number')
            ->when(filled($octopus_card_number_search), fn($query) => $query->where('admin_octopus_card.octopus_card_number', 'like', "%$octopus_card_number_search%"))
            ->when(filled($site_search), fn($query) => $query->where('admin_octopus_card.site_number', $site_search))
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('admin_octopus_card.site_number', auth()->user()->site_number_list))
            ->orderBy($order, $sort)
            ->latest('gmt_modified');
    }

    /**
     * 导入
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @throws Exception
     * <AUTHOR>
     * @date 2022-11-08
     * @example
     */
    public function import(Request $request): JsonResponse
    {
        // 获取文件数据
        $data = $this->_import($request);
        if ($data['code'] == 200) {
            // 解析数据
            $models = [];
            foreach ($data['data'] as $item) {
                $models[] = array(
                    'octopus_card_number' => $item[0],
                    'site_number' => $item[1],
                    'is_enable' => $item[2],
                    'remark' => $item[3],
                );
            }
            // 根据octopus_card_number创建或更新值
            AdminOctopusCard::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->upsert($models, ['octopus_card_number', 'site_number']);
        }
        return $this->returnJson();
    }

    /**
     * 导出
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     * <AUTHOR>
     * @date 2022-11-08
     * @example
     */
    public function export(Request $request): JsonResponse
    {
        // 获取数据
        $search = $this->_searchList($request);
        $data_list = $search->get();

        $result = array();
        // 添加表头
        $result[] = array(
            __(self::$module_name . '.site'),
            __(self::$module_name . '.octopus_card_number'),
            __(self::$module_name . '.is_enable'),
            __(self::$module_name . '.remark'),
        );
        foreach ($data_list as $data) {
            $site_name = $this->getValueFromLanguageArray($data->site_name_json_init) ?? '—/—';
            $result[] = array(
                'site' => $site_name,   // 场地名称
                'octopus_card_number' => $data->octopus_card_number, // 卡号
                'is_enable' => $data->is_enable ? '1' : '0', // 是否启用
                'remark' => $data->remark, // 备注
            );
        }
        // 获取文件数据
        $this->_export($result, __(self::$module_name . '.web_title'));

        return $this->returnJson();
    }
}

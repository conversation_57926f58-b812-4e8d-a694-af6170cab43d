<?php

namespace App\Enums;

use <PERSON><PERSON>amp<PERSON>\Enum\Enum;
use <PERSON>Samp<PERSON>\Enum\Contracts\LocalizedEnum;
use App\Enums\Traits\Tools;

/**
 * @method static CHARGE_POINT_CS_LMS_AND_SITE_LMS_NOT_MATCH
 *
 * @method static KIOSK_HTTP_REQUEST
 * @method static KIOSK_HTTP_RESPONSE
 * @method static KIOSK_HTTP_FAILURE
 * @method static KIOSK_HTTP_EXCEPTION
 *
 * @method static CMS_SEND_REMINDER_EMAIL_FAILURE
 * @method static CMS_SEND_REMINDER_SMS_FAILURE
 * @method static KIOSK_HARD_SHUTDOWN
 * @method static KIOSK_HARD_REBOOT
 * @method static KIOSK_SOFT_REBOOT
 * @method static KIOSK_SOFT_SHUTDOWN
 * @method static KIOSK_INITIALIZE_REQUEST_EXCEPTION
 * @method static KIOSK_INITIALIZE_FILE_EXCEPTION
 * @method static KIOSK_INITIALIZE_EXCEPTION
 * @method static KIOSK_RELOAD_INITIALIZE_DATA
 *
 * @method static COM_SERVER_WEBSOCKET_OPEN_EXCEPTION
 * @method static COM_SERVER_WEBSOCKET_CLOSE_EXCEPTION
 * @method static COM_SERVER_WEBSOCKET_RECONNECT
 * @method static COM_SERVER_WEBSOCKET_ON_ERROR
 * @method static COM_SERVER_WEBSOCKET_ON_OPEN
 * @method static COM_SERVER_WEBSOCKET_ON_CLOSE
 *
 * @method static CMS_WEBSOCKET_OPEN_EXCEPTION
 * @method static CMS_WEBSOCKET_CLOSE_EXCEPTION
 * @method static CMS_WEBSOCKET_RECONNECT
 * @method static CMS_WEBSOCKET_ON_ERROR
 * @method static CMS_WEBSOCKET_ON_OPEN
 * @method static CMS_WEBSOCKET_ON_CLOSE
 *
 * @method static REMOTE_START_CHARGE_TIMEOUT
 * @method static REMOTE_START_CHARGE_FAILURE
 * @method static REMOTE_START_CHARGE_EXCEPTION
 * @method static REMOTE_STOP_CHARGE_TIMEOUT
 * @method static REMOTE_STOP_CHARGE_FAILURE
 * @method static REMOTE_STOP_CHARGE_EXCEPTION
 * @method static UNLOCK_CONNECTOR_TIMEOUT
 * @method static UNLOCK_CONNECTOR_FAILURE
 * @method static UNLOCK_CONNECTOR_EXCEPTION
 *
 * @method static START_CHARGE_CHARGE_RECORD_STOP
 * @method static START_CHARGE_TRIAL_CHARGE_FAILURE
 * @method static START_CHARGE_INVALID_IDENTITY
 *
 * @method static NO_AVAILABLE_PAYMENT_METHOD
 * @method static NOT_SELECT_PAYMENT_METHOD
 *
 * @method static DEDUCT_CALCULATION_EXCEPTION
 * @method static DEDUCT_REQUEST_EXCEPTION
 *
 * @method static PRINT_RECEIPT_EXCEPTION
 * @method static RECEIPT_POS_RESPONSE_JSON_EXCEPTION
 *
 * @method static OCTOPUS_INITIALIZE_FAILURE
 * @method static OCTOPUS_INITIALIZE_EXCEPTION
 * @method static OCTOPUS_THREAD_EXCEPTION
 * @method static OCTOPUS_OPEN_EXCEPTION
 * @method static OCTOPUS_CLOSE_EXCEPTION
 * @method static OCTOPUS_INVALID_CARD
 * @method static OCTOPUS_GET_CARD_TIMEOUT
 * @method static OCTOPUS_DEDUCT_22
 * @method static OCTOPUS_NO_MONEY
 * @method static OCTOPUS_INVALID_PARAMETERS
 * @method static OCTOPUS_DEDUCT_TIMEOUT
 * @method static OCTOPUS_EXTRA_INFO_EXCEPTION
 *
 * @method static OCTOPUS_DATA_EXCHANGE_EXCEPTION
 * @method static OCTOPUS_UPLOAD_EXCEPTION
 * @method static OCTOPUS_DOWNLOAD_EXCEPTION
 *
 * @method static ADMIN_OCTOPUS_CARD_FREE_DEDUCT
 * @method static FREE_OCTOPUS_CARD_DEDUCT
 *
 * @method static POS_THREAD_EXCEPTION
 * @method static POS_PAYMENT_FAILURE
 * @method static POS_CHECK_HEALTH_EXCEPTION
 * @method static POS_BECOME_HEALTH
 * @method static POS_BECOME_UNHEALTH
 *
 * @method static TORCH_INITIALIZE_EXCEPTION
 * @method static PRINTER_PAGER_OUT
 * @method static PRINTER_ERROR
 *
 * @method static MAINTENANCE_ACTIVATION
 * @method static MAINTENANCE_INACTIVATION
 *
 * @method static POST_CHARGE_POINT_CS_BYPASS_SUCCESS
 * @method static POST_CHARGE_POINT_CS_BYPASS_FAILURE
 *
 * @method static POST_CHARGE_POINT_CS_LMS_SUCCESS
 * @method static POST_CHARGE_POINT_CS_LMS_FAILURE
 *
 * @method static CMS_CHANGE_LMS_STOP_CHARGE_FAILURE
 * @method static CHECK_CONNECTOR_ALIVE_FAILURE
 * @method static CHECK_CONNECTOR_ALIVE_OFFLINE
 * @method static CHECK_CONNECTOR_ALIVE_TIMEOUT
 * @method static CMS_ALIVE_TIMEOUT
 * @method static CMS_ALIVE_AVAILABLE
 */
final class EventTypeEnum extends Enum implements LocalizedEnum
{
    use Tools;

    /* 充电机CS */
    const ChargePointCsLmsAndSiteLmsNotMatch = 'CHARGE_POINT_CS_LMS_AND_SITE_LMS_NOT_MATCH';  // LMS与场地LMS不符
    /* HTTP */
    const KioskHTTPRequest = 'KIOSK_HTTP_REQUEST'; // KIOSK HTTP请求
    const KioskHTTPResponse = 'KIOSK_HTTP_RESPONSE'; // KIOSK HTTP响应
    const KioskHTTPFailure = 'KIOSK_HTTP_FAILURE'; // KIOSK HTTP失败
    const KioskHTTPException = 'KIOSK_HTTP_EXCEPTION'; // KIOSK HTTP异常

    const CMSSendReminderEmailFailure = 'CMS_SEND_REMINDER_EMAIL_FAILURE'; // CMS發送提醒郵件失敗
    const CMSSendReminderSMSFailure = 'CMS_SEND_REMINDER_SMS_FAILURE'; // CMS發送提醒短信失敗
    /* Kiosk */
    const KioskHardShutdown = 'KIOSK_HARD_SHUTDOWN'; // Kiosk硬件关机
    const KioskHardReboot = 'KIOSK_HARD_REBOOT'; // Kiosk硬件重启
    const KioskSoftReboot = 'KIOSK_SOFT_REBOOT'; // Kiosk软件重启
    const KioskSoftShutdown = 'KIOSK_SOFT_SHUTDOWN'; // Kiosk软件关机
    const KioskInitializeRequestException = 'KIOSK_INITIALIZE_REQUEST_EXCEPTION'; // Kiosk初始化请求异常
    const KioskInitializeFileException = 'KIOSK_INITIALIZE_FILE_EXCEPTION'; // Kiosk初始化文件异常
    const KioskInitializeException = 'KIOSK_INITIALIZE_EXCEPTION'; // Kiosk初始化异常
    const KioskReloadInitializeData = 'KIOSK_RELOAD_INITIALIZE_DATA'; // Kiosk重新加载初始化数据
    /* Com Server WebSocket */
    const ComServerWebsocketOpenException = 'COM_SERVER_WEBSOCKET_OPEN_EXCEPTION'; // Com Server WebSocket连接异常
    const ComServerWebsocketCloseException = 'COM_SERVER_WEBSOCKET_CLOSE_EXCEPTION'; // Com Server WebSocket关闭异常
    const ComServerWebsocketReconnect = 'COM_SERVER_WEBSOCKET_RECONNECT'; // Com Server WebSocket重新连接
    const ComServerWebsocketOnError = 'COM_SERVER_WEBSOCKET_ON_ERROR'; // Com Server WebSocket异常
    const ComServerWebsocketOnOpen = 'COM_SERVER_WEBSOCKET_ON_OPEN'; // Com Server WebSocket开启
    const ComServerWebsocketOnClose = 'COM_SERVER_WEBSOCKET_ON_CLOSE'; // Com Server WebSocket关闭
    /* CMS WebSocket */
    const CMSWebsocketOpenException = 'CMS_WEBSOCKET_OPEN_EXCEPTION'; // CMS WebSocket连接异常
    const CMSWebsocketCloseException = 'CMS_WEBSOCKET_CLOSE_EXCEPTION'; // CMS WebSocket关闭异常
    const CMSWebsocketReconnect = 'CMS_WEBSOCKET_RECONNECT'; // CMS WebSocket重新连接
    const CMSWebsocketOnError = 'CMS_WEBSOCKET_ON_ERROR'; // CMS WebSocket异常
    const CMSWebsocketOnOpen = 'CMS_WEBSOCKET_ON_OPEN'; // CMS WebSocket开启
    const CMSWebsocketOnClose = 'CMS_WEBSOCKET_ON_CLOSE'; // CMS WebSocket关闭
    /* 远程操作 */
    const RemoteStartChargeTimeout = 'REMOTE_START_CHARGE_TIMEOUT'; // 远程开始充电超时
    const RemoteStartChargeFailure = 'REMOTE_START_CHARGE_FAILURE'; // 远程开始充电失败
    const RemoteStartChargeException = 'REMOTE_START_CHARGE_EXCEPTION'; // 远程开始充电异常
    const RemoteStopChargeTimeout = 'REMOTE_STOP_CHARGE_TIMEOUT'; // 远程停止充电超时
    const RemoteStopChargeFailure = 'REMOTE_STOP_CHARGE_FAILURE'; // 远程停止充电失败
    const RemoteStopChargeException = 'REMOTE_STOP_CHARGE_EXCEPTION'; // 远程停止充电异常
    const UnlockConnectorTimeout = 'UNLOCK_CONNECTOR_TIMEOUT'; // 解锁充电枪超时
    const UnlockConnectorFailure = 'UNLOCK_CONNECTOR_FAILURE'; // 解锁充电枪失败
    const UnlockConnectorException = 'UNLOCK_CONNECTOR_EXCEPTION'; // 解锁充电枪异常
    /* 开始充电 */
    const StartChargeChargeRecordStop = 'START_CHARGE_CHARGE_RECORD_STOP'; // 开始充电充电记录停止
    const StartChargeTrialChargeFailure = 'START_CHARGE_TRIAL_CHARGE_FAILURE'; // 开始充电试充失败
    const StartChargeInvalidIdentity = 'START_CHARGE_INVALID_IDENTITY'; // 开始充电无效身份
    const StartChargeUpdatePreAuthorizationRecordFailure = 'START_CHARGE_UPDATE_PRE_AUTHORIZATION_RECORD_FAILURE'; // 开始充电更新预授权记录失败
    /* 付款方式 */
    const NoAvailablePaymentMethod = 'NO_AVAILABLE_PAYMENT_METHOD'; // 没有可用支付方式
    const NoSelectPaymentMethod = 'NOT_SELECT_PAYMENT_METHOD'; // 没有选择支付方式
    /* 扣款请求 */
    const DeductCalculationException = 'DEDUCT_CALCULATION_EXCEPTION'; // 扣款计算异常
    const DeductRequestException = 'DEDUCT_REQUEST_EXCEPTION'; // 扣款請求異常
    /* 收据 */
    const PrintReceiptException = 'PRINT_RECEIPT_EXCEPTION'; // 打印收据异常
    const ReceiptPOSResponseJsonException = 'RECEIPT_POS_RESPONSE_JSON_EXCEPTION'; // 收據POS回應Json異常
    /* 八达通 */
    const OctopusInitializeFailure = 'OCTOPUS_INITIALIZE_FAILURE'; // 八達通初始化失敗
    const OctopusInitializeException = 'OCTOPUS_INITIALIZE_EXCEPTION'; // 八達通初始化異常
    const OctopusThreadException = 'OCTOPUS_THREAD_EXCEPTION'; // 八達通線程異常
    const OctopusOpenException = 'OCTOPUS_OPEN_EXCEPTION'; // 八達通開啟異常
    const OctopusCloseException = 'OCTOPUS_CLOSE_EXCEPTION'; // 八達通關閉異常
    const OctopusInvalidCard = 'OCTOPUS_INVALID_CARD'; // 八達通非法卡
    const OctopusGetCardTimeout = 'OCTOPUS_GET_CARD_TIMEOUT'; // 八達通讀卡超時
    const OctopusDeduct22 = 'OCTOPUS_DEDUCT_22'; // 八達通扣款22
    const OctopusNoMoney = 'OCTOPUS_NO_MONEY'; // 八達通餘額不足
    const OctopusInvalidParameters = 'OCTOPUS_INVALID_PARAMETERS'; // 八達通非法參數
    const OctopusDeductTimeout = 'OCTOPUS_DEDUCT_TIMEOUT'; // 八達通扣款超時
    const OctopusExtraInfoException = 'OCTOPUS_EXTRA_INFO_EXCEPTION'; // 八達通額外資訊異常
    const OctopusCancel = 'OCTOPUS_CANCEL'; // 八達通取消
    const OctopusCancelTimeout = 'OCTOPUS_CANCEL_TIMEOUT'; // 八達通取消超時
    const OctopusOperationNotCompleted = 'OCTOPUS_OPERATION_NOT_COMPLETED'; // 八達通操作未完成
    /* 八达通数据交换异常 */
    const OctopusDataExchangeException = 'OCTOPUS_DATA_EXCHANGE_EXCEPTION'; // 八達通數據交換異常
    const OctopusUploadException = 'OCTOPUS_UPLOAD_EXCEPTION'; // 八達通上數異常
    const OctopusDownloadException = 'OCTOPUS_DOWNLOAD_EXCEPTION'; // 八達通下載黑名單異常
    /* 八达通3 */
    const Octopus3CmdExchangeException = 'OCTOPUS3_CMD_EXCHANGE_EXCEPTION'; // 八達通3命令交換異常
    const Octopus3ParseResponseException = 'OCTOPUS3_PARSE_RESPONSE_EXCEPTION'; // 八達通3解析響應異常
    const Octopus3TransactionRequestFailure = 'OCTOPUS3_TRANSACTION_REQUEST_FAILURE'; // 八達通3交易請求失敗
    const Octopus3TransactionPerformFailure = 'OCTOPUS3_TRANSACTION_PERFORM_FAILURE'; // 八達通3交易執行失敗
    const Octopus3TransactionPerformError = 'OCTOPUS3_TRANSACTION_PERFORM_ERROR'; // 八達通3交易執行錯誤
    const Octopus3TransactionCancelFailure = 'OCTOPUS3_TRANSACTION_CANCEL_FAILURE'; // 八達通3交易取消失敗
    /* 管理员八达通卡免费扣款 */
    const AdminOctopusCardFreeDeduct = 'ADMIN_OCTOPUS_CARD_FREE_DEDUCT'; // 管理員八達通卡免費扣款
    const FreeOctopusCardDeduct = 'FREE_OCTOPUS_CARD_DEDUCT'; // 免费八达通卡扣款
    /* POS */
    const POSVendorNotFound = 'POS_VENDOR_NOT_FOUND'; // 未找到POS供应商
    const YedpayPOSThreadException = 'YEDPAY_POS_THREAD_EXCEPTION'; // Yedpay POS线程异常
    const YedpayPOSPaymentFailure = 'YEDPAY_POS_PAYMENT_FAILURE'; // Yedpay POS支付失败
    const YedpayPOSPreAuthorizationFailure = 'YEDPAY_POS_PRE_AUTHORIZATION_FAILURE'; // Yedpay POS预授权失败
    const YedpayPOSPreAuthorizationCaptureFailure = 'YEDPAY_POS_PRE_AUTHORIZATION_CAPTURE_FAILURE'; // Yedpay POS预授权扣款失败
    const YedpayPOSCheckHealthException = 'YEDPAY_POS_CHECK_HEALTH_EXCEPTION'; // Yedpay POS检测健康异常
    const YedpayPOSBecomeHealth = 'YEDPAY_POS_BECOME_HEALTH'; // Yedpay POS转良好
    const YedpayPOSBecomeUnhealth = 'YEDPAY_POS_BECOME_UNHEALTH'; // Yedpay POS转不良
    const SoePayPOSThreadException = 'SOE_PAY_POS_THREAD_EXCEPTION'; // SoePay POS线程异常
    const SoePayPOSPaymentFailure = 'SOE_PAY_POS_PAYMENT_FAILURE'; // SoePay POS支付失败
    const SoePayPOSCheckHealthException = 'SOE_PAY_POS_CHECK_HEALTH_EXCEPTION'; // SoePay POS检测健康异常
    const SoePayPOSBecomeHealth = 'SOE_PAY_POS_BECOME_HEALTH'; // SoePay POS转良好
    const SoePayPOSBecomeUnhealth = 'SOE_PAY_POS_BECOME_UNHEALTH'; // SoePay POS转不良
    /* 充电预授权 */
    const ChargePreAuthorizationRecordUploadException = 'CHARGE_PRE_AUTHORIZATION_RECORD_UPLOAD_EXCEPTION'; // 充电预授权记录上传异常
    const UpdateChargeRecordChargePreAuthorizationRecordException = 'UPDATE_CHARGE_RECORD_CHARGE_PRE_AUTHORIZATION_RECORD_EXCEPTION'; // 更新充电记录充电预授权记录异常
    const DeductAmountGreaterThanPreAuthorizationAmount = 'DEDUCT_AMOUNT_GREATER_THAN_PRE_AUTHORIZATION_AMOUNT'; // 扣款金额大于预授权金额
    /* 闪光灯 */
    const TorchInitializeException = 'TORCH_INITIALIZE_EXCEPTION'; // 閃光燈初始化異常
    /* 打印机 */
    const PrinterPagerOut = 'PRINTER_PAGER_OUT'; // 印表機缺紙
    const PrinterError = 'PRINTER_ERROR'; // 打印機故障
    /* 维修 */
    const MaintenanceActivation = 'MAINTENANCE_ACTIVATION'; // 維修啟動
    const MaintenanceInactivation = 'MAINTENANCE_INACTIVATION'; // 維修失活

    const PostChargePointCSBypassSuccess = 'POST_CHARGE_POINT_CS_BYPASS_SUCCESS'; // 设置bypass成功
    const PostChargePointCSBypassFailure = 'POST_CHARGE_POINT_CS_BYPASS_FAILURE'; // 设置bypass失败

    const CMSChangeLMSStopChargeFailure = 'CMS_CHANGE_LMS_STOP_CHARGE_FAILURE'; // CMS改變LMS模式停止充電失敗

    const PostChargePointCSLMSSuccess = 'POST_CHARGE_POINT_CS_LMS_SUCCESS'; // 设置LMS成功
    const PostChargePointCSLMSFailure = 'POST_CHARGE_POINT_CS_LMS_FAILURE'; // 设置LMS失败

    // 检查充电枪活跃失败
    const CheckConnectorAliveFailure = 'CHECK_CONNECTOR_ALIVE_FAILURE';
    // 检查充电枪活跃离线
    const CheckConnectorAliveOffline = 'CHECK_CONNECTOR_ALIVE_OFFLINE';
    // 检查充电枪活跃超时
    const CheckConnectorAliveTimeout = 'CHECK_CONNECTOR_ALIVE_TIMEOUT';
    // CMS 活跃超时
    const CMSAliveTimeout = 'CMS_ALIVE_TIMEOUT';
    // CMS 活跃可用
    const CMSAliveAvailable = 'CMS_ALIVE_AVAILABLE';
    // 充电枪故障
    const ConnectorFaulted = 'CONNECTOR_FAULTED';
    // 充电枪故障恢复
    const ConnectorFaultRecovery = 'CONNECTOR_FAULT_RECOVERY';
}

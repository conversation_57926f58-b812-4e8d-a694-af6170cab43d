<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Http\{
    JsonResponse,
    Request,
};
use Illuminate\Foundation\Application;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use App\Models\Modules\{
    Vehicle,
};
use App\Enums\{
    EventTypeEnum,
};


class VehicleController extends CommonController
{

    protected static string $module_name = 'vehicle'; // 模块名称
    protected Vehicle $model;

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new Vehicle;
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'nickname_search' => $request->input('nickname_search'),
            'plate_number_search' => $request->input('plate_number_search')
        );

        $data['event_type_enum_list'] = array();
        foreach (EventTypeEnum::asSelectArray() as $value => $name) {
            $data['event_type_enum_list'][] = array(
                'name' => $name,
                'value' => $value,
            );
        }

        return view("pages.{$data['module_name']}.list", $data);
    }

    /**
     * 列表接口
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        $draw = (int)$request->input('draw', 1);
        $length = (int)$request->input('length', 10);
        $search = $this->_searchList($request);
        $data_list = $search->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $result[] = array(
                'vehicle_id' => $data->vehicle_id, // 主键ID
                'user_id' => $data->user_id, // 用户ID
                'nickname' => $data->nickname, // 用户昵称
                'avatar' => existsImage('avatar', $data->avatar_url) ?: existsImage('icon', 'not_select_image.png'), // 用户头像
                'plate_number' => $data->plate_number, // 车牌号
                'is_common' => $data->is_common, // 是否常用
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result
        );
        return response()->json($json);
    }

    /**
     * 返回地址栏参数
     *
     * @param Request $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(Request $request): array
    {
        return array(
            'is_page' => 'true',
            'nickname_search' => $request->get('nickname_search'),
            'plate_number_search' => $request->get('plate_number_search'),
        );
    }

    /**
     * 获取搜索数据
     *
     * @param Request $request
     * @return mixed
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-11-08
     */
    protected function _searchList(Request $request): mixed
    {
        // dataTable字段
        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $nickname_search = $request->input('nickname_search');
        $plate_number = $request->input('plate_number_search');

        $where = array();

        if (filled($nickname_search)) {
            $where[] = ['user.nickname', 'like', "%$nickname_search%"];
        }

        if (filled($plate_number)) {
            $where[] = ['plate_number', 'like', "%$plate_number%"];
        }

        return Vehicle::select('vehicle.*', 'user.nickname as nickname', 'user.avatar_url as avatar_url')
            ->leftJoin('user', 'vehicle.user_id', '=', 'user.user_id')
            ->where($where)
            ->orderBy($order, $sort)
            ->latest();
    }
}

<?php

namespace App\Models\Modules;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Model;
use App\Http\Controllers\App\TariffTableController;

class ChargeRecord extends Model
{

    protected $table = 'charge_record';
    protected $primaryKey = 'charge_record_id';

    const CREATED_AT = 'gmt_create'; //默认是小写的模型类名复数格式，此处为自定义表名
    const UPDATED_AT = 'gmt_modified'; //定义主键，默认为id

    protected $appends = ['tariff_table_url', 'charged_energy_summary_url'];

    // protected $fillable = [];
    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 类型转化器
     */
    protected $casts = [
        'is_record_charge_record_meter_value' => 'bool',
        'is_enable_charge_arrears' => 'bool',
        'is_enable_push_notification' => 'bool',
        'is_enable_member_card_group_tariff_table' => 'bool',
        'is_enable_user_group_tariff_table' => 'bool',
        'is_enable_top_up' => 'bool',
        'is_enable_admin_octopus_card_free_deduct' => 'bool',
        'is_enable_free_octopus_card' => 'bool',
        'is_top_up_need_confirm_identity' => 'bool',
        'is_enable_use_remain_charge_value' => 'bool',
        'is_enable_charge_value_adjust_selected_base_on_remain' => 'bool',
        'is_enable_round_up_tail_charge_value_calculation' => 'bool',
        'is_support_unlock_connector' => 'bool',
        'simple_tariff_table_json' => 'array',
        'time_tariff_table_item_json' => 'array',
        'energy_tariff_table_item_json' => 'array',
        'idling_penalty_tariff_table_item_json' => 'array',
        'peak_time_table_item_json' => 'array',
        'public_holiday_json' => 'array',
        'site_name' => 'array',
        'zone_name' => 'array',
        'merchant_name' => 'array',
        'member_card_group_name' => 'array',
        'is_need_generate_charge_payment_record' => 'bool',
        'is_allow_points_overdraft' => 'bool',
    ];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [
        'is_record_charge_record_meter_value' => false, // 是否记录充电记录仪表值
        'is_enable_charge_arrears' => false, // 是否启用充电欠款
        'is_enable_member_card_group_tariff_table' => false, // 是否启用会员卡组收费表
        'charge_value_interval' => 0, // 充电量间隔 - 选择/收费
        'charge_value_amount' => 0, // 充电量金额
        'idling_penalty_amount' => 0, // 闲置罚款金额
        'pre_paid_charge_value' => 0, // 预付充电量
        'pre_paid_purchase_charge_value' => 0, // 预付购买充电量
        'pre_paid_use_remain_charge_value' => 0, // 预付使用剩余充电量
        'pre_paid_charge_value_maximum_selection' => 0, // 预付充电量最大选择量
        'post_paid_purchase_charge_value' => 0, // 后付购买充电量
        'post_paid_maximum_charge_time' => 0, // 后付最大充电时间
        'is_enable_admin_octopus_card_free_deduct' => false, // 是否开启管理员八达通卡免费扣款
        'is_enable_free_octopus_card' => false, // 是否启用免费八达通卡
        'is_enable_top_up' => false, // 是否启用续充
        'is_top_up_need_confirm_identity' => false, // 是否续充需要确认身份
        'is_enable_use_remain_charge_value' => false, // 是否允许使用剩余充电量
        'is_enable_round_up_tail_charge_value_calculation' => false, // 是否启用向上取整尾部充电量计算
        'is_support_unlock_connector' => false, // 是否支持解锁充电枪
        'meter_start' => 0, // 开始充电仪表值
        'charged_energy' => 0, // 已充电量
        'off_peak_charged_energy' => 0, // 非高峰已充电量
        'on_peak_charged_energy' => 0, // 高峰已充电量
        'total_amount' => 0,    // 合共金额
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * 为 array / JSON 序列化准备日期格式
     *
     * @param DateTimeInterface $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    protected function asJson($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    public function getTariffTableUrlAttribute()
    {
        return action(
            [
                TariffTableController::class, 'chargeRecordTariffRule'
            ],
            [
                'charge_record_number' => $this->charge_record_number,
                'language_code' => app()->getLocale(),
            ]
        );
    }

    public function getChargedEnergySummaryUrlAttribute()
    {
        return action(
            [
                TariffTableController::class, 'chargeRecordChargedEnergy'
            ],
            [
                'charge_record_number' => $this->charge_record_number,
                'language_code' => app()->getLocale(),
            ]
        );
    }

    /**
     * 全部字段
     *
     * @var array
     */
    public array $columns = [
        'charge_record_id',
        'charge_record_number',
        'server_type',
        'com_server_number',
        'merchant_number',
        'merchant_name',
        'site_number',
        'site_name',
        'zone_number',
        'zone_name',
        'charge_point_number',
        'charge_point_name',
        'connector_number',
        'connector_name',
        'user_id',
        'member_card_id',
        'member_name',
        'member_card_group_id',
        'member_card_group_name',
        'lms_mode',
        'is_record_charge_record_meter_value',
        'is_enable_charge_arrears',
        'license_plate_number',
        'gmt_park_sensor_entry',
        'is_enable_push_notification',
        'is_allow_points_overdraft',
        'charge_tariff_scheme',
        'charge_value_type',
        'charge_value_interval',
        'charge_value_amount',
        'idling_penalty_amount',
        'total_amount',
        'actual_payment_amount',
        'use_points',
        'merchant_handling_fee_rate',
        'merchant_handling_fee',
        'merchant_receivable',
        'pre_paid_selected_charge_value',
        'pre_paid_charge_value',
        'pre_paid_purchase_charge_value',
        'pre_paid_use_remain_charge_value',
        'pre_paid_charge_value_maximum_selection',
        'post_paid_identity_type',
        'post_paid_purchase_charge_value',
        'post_paid_maximum_charge_time',
        'idling_penalty_effective_type',
        'trial_charge_timeout',
        'gmt_trial_charge_reached',
        'identity_confirmation_timeout',
        'user_confirmation_timeout',
        'charge_maximum_vehicle_soc_limit',
        'tariff_table_type',
        'is_enable_member_card_group_tariff_table',
        'is_enable_admin_octopus_card_free_deduct',
        'is_enable_free_octopus_card',
        'is_enable_top_up',
        'top_up_buffer_limit',
        'is_top_up_need_confirm_identity',
        'remain_charge_value_generation_trigger',
        'remain_charge_value_validity_period',
        'remain_charge_value_minimum_limit',
        'is_enable_use_remain_charge_value',
        'is_enable_charge_value_adjust_selected_base_on_remain',
        'is_enable_round_up_tail_charge_value_calculation',
        'gmt_start',
        'start_id_tag',
        'meter_start',
        'gmt_power_on',
        'reminder_email',
        'reminder_telephone',
        'charged_time',
        'off_peak_charged_time',
        'on_peak_charged_time',
        'charged_energy',
        'off_peak_charged_energy',
        'on_peak_charged_energy',
        'identity_type',
        'identity_number',
        'charge_pre_authorization_record_number',
        'meter_value_voltage',
        'meter_value_current',
        'meter_value_power',
        'meter_value_power_factor',
        'meter_value_power_offered',
        'meter_value_start_vehicle_soc',
        'meter_value_vehicle_soc',
        'meter_value_temperature',
        'meter_value_fan_speed',
        'remote_stop_operator_type',
        'remote_stop_operator_number',
        'remote_stop_reason',
        'remote_stop_detail',
        'gmt_remote_stop',
        'gmt_stop',
        'stop_id_tag',
        'meter_stop',
        'stop_reason',
        'is_support_unlock_connector',
        'remote_unlock_operator_type',
        'remote_unlock_operator_number',
        'gmt_remote_unlock',
        'gmt_unlocked',
        'idling_penalty_time',
        'off_peak_idling_penalty_time',
        'on_peak_idling_penalty_time',
        'gmt_idling_penalty_locked',
        'is_need_generate_charge_payment_record',
        'gmt_generated_charge_payment_record',
        'gmt_sync_to_cloud',
        'gmt_create',
        'gmt_modified',
    ];

    public function scopeExclude($query, $exclude = [])
    {
        return $query->select(array_diff($this->columns, (array)$exclude));
    }

    /**
     * 一对一关联充电桩
     */
    public function connector()
    {
        return $this->hasOne(Connector::class, 'connector_number', 'connector_number');
    }

    public function site()
    {
        return $this->hasOne(Site::class, 'site_number', 'site_number');
    }

    public function zone()
    {
        return $this->hasOne(Zone::class, 'zone_number', 'zone_number');
    }

    public function chargePoint()
    {
        return $this->hasOne(ChargePoint::class, 'charge_point_number', 'charge_point_number');
    }

    /**
     * 一对多关联EventLog
     */
    public function eventLog()
    {
        return $this->hasMany(EventLog::class, 'charge_record_number', 'charge_record_number');
    }

    /**
     * 一对多关联Email Send Record
     */
    public function emailSendRecord()
    {
        return $this->hasMany(EmailSendRecord::class, 'charge_record_number', 'charge_record_number');
    }

    /**
     * 一对多关联Sms Send Record
     */
    public function smsSendRecord()
    {
        return $this->hasMany(SmsSendRecord::class, 'charge_record_number', 'charge_record_number');
    }

    /**
     * 一对多关联充电支付纪录
     */
    public function chargePaymentRecord()
    {
        return $this->hasMany(ChargePaymentRecord::class, 'charge_record_number', 'charge_record_number');
    }

    /**
     * 一对一关联 试充规则
     */
    public function chargeRecordTrialCharge()
    {
        return $this->hasOne(ChargeRecordTrialCharge::class, 'charge_record_number', 'charge_record_number');
    }

    /**
     * 一对一关联 仪表值
     */
    public function chargeRecordMeterValue()
    {
        return $this->hasOne(ChargeRecordMeterValue::class, 'charge_record_number', 'charge_record_number');
    }

    /**
     * 一对一关联 已充电量
     */
    public function chargeRecordChargedEnergy()
    {
        return $this->hasOne(ChargeRecordChargedEnergy::class, 'charge_record_number', 'charge_record_number');
    }

    /**
     * 一对一关联 充电记录收费表规则
     */
    public function tariffTableRule()
    {
        return $this->hasOne(ChargeRecordTariffRule::class, 'charge_record_number', 'charge_record_number');
    }

    /**
     * 一对一关联用户表
     */
    public function user()
    {
        return $this->hasOne(AppUser::class, 'user_id', 'user_id');
    }

    /**
     * 一对一关联 充电预授权记录
     */
    public function chargePreAuthorizationRecord()
    {
        return $this->hasOne(ChargePreAuthorizationRecord::class, 'charge_pre_authorization_record_number', 'charge_pre_authorization_record_number');
    }

    /**
     * 一对一正向关联 充电欠款记录
     */
    public function chargeArrearsRecord()
    {
        return $this->hasOne(ChargeArrearsRecord::class, 'charge_record_number', 'charge_record_number');
    }
}

<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use App\Models\Modules\MaintenanceRecord;
use Illuminate\Contracts\View\{
    Factory,
    View
};
use Illuminate\Foundation\Application;
use Illuminate\Http\{
    JsonResponse,
    Request
};

// 數據問題後續解決，移除了表maintenance_event_log
class MaintenanceReportController extends CommonController
{
    protected static string $module_name = 'maintenanceReport'; // 模块名称

    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'time_frame_search' => $request->get('time_frame_search')
        );

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'year');
        $sort = $request->input('sort', 'asc');
        $time_frame_search = $request->input('time_frame_search');
        $result = array();
        // 获取数据
        $list_data = $this->listData($time_frame_search);
        // 数据长度
        $list_data_total = count($list_data);

        // 正序、倒序
        $sort_order = strtolower($sort) == 'desc' ? SORT_DESC : SORT_ASC;
        // 年月一起排序
        if (strtolower($order) == 'year' || strtolower($order) == 'month') {
            $year_sort_list = array_column($list_data, 'year');
            $month_sort_list = array_column($list_data, 'month');
            // 多字段排序
            array_multisort($year_sort_list, $sort_order, $month_sort_list, $sort_order, $list_data);
        } else {
            // 取出对应排序key的数据
            $order_sort_list = array_column($list_data, $order);
            array_multisort($order_sort_list, $sort_order, $list_data);
        }

        // 后统一格式化对应数据
        foreach ($list_data as $item) {
            $result[] = array(
                'year' => $item['year'],
                'month' => $item['month'],
                'total_time_in_maintenance_period' => $this->secTime($item['total_time_in_maintenance_period']),
                'total_time_in_bypass_mode' => $this->secTime($item['total_time_in_bypass_mode']),
                'bypass_mode_activation' => $item['bypass_mode_activation'],
                'payment_system_failure' => $item['payment_system_failure'],
                'lms_failure' => $item['lms_failure'],
                'payment_kiosk_failure' => $item['payment_kiosk_failure'],
                'lms_mode_change' => $item['lms_mode_change'],
                'charging_scheme_change' => $item['charging_scheme_change'],
                'sms_message_retransmission' => $item['sms_message_retransmission'],
                'email_message_retransmission' => $item['email_message_retransmission'],
                'sms_message_success' => $item['sms_message_success'],
                'sms_message_failure' => $item['sms_message_failure'],
                'email_message_success' => $item['email_message_success'],
                'email_message_failure' => $item['email_message_failure'],
                'number_of_invalid_telephone_number' => $item['number_of_invalid_telephone_number'],
                'number_of_invalid_email_address' => $item['number_of_invalid_email_address'],
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $list_data_total,
            'recordsFiltered' => $list_data_total,
            "data" => $result,
        );

        return response()->json($json);
    }

    // 导出功能
    public function excelExport(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        // dataTable字段
        $time_frame_search = $request->input('time_frame_search');
        $excel_data = array();

        // 获取数据
        $list_data = $this->listData($time_frame_search);

        // 先把语言赋值才可以在后面array出来表头，不然会输出未编译前的字符串
        $web_title = __('maintenanceReport.web_title');
        $excel_title = array(
            __("$module_name.in_hour"),
            __("$module_name.number_of_time_of"),
            __("$module_name.year"),
            __("$module_name.month"),
            __("$module_name.total_time_in_maintenance_period"),
            __("$module_name.total_time_in_bypass_mode"),
            __("$module_name.bypass_mode_activation"),
            __("$module_name.payment_system_failure"),
            __("$module_name.lms_failure"),
            __("$module_name.payment_kiosk_failure"),
            __("$module_name.lms_mode_change"),
            __("$module_name.charging_scheme_change"),
            __("$module_name.sms_message_retransmission"),
            __("$module_name.email_message_retransmission"),
            __("$module_name.sms_message_success"),
            __("$module_name.sms_message_failure"),
            __("$module_name.email_message_success"),
            __("$module_name.email_message_failure"),
            __("$module_name.number_of_invalid_telephone_number"),
            __("$module_name.number_of_invalid_email_address"),
        );

        // 后统一格式化对应数据
        foreach ($list_data as $item) {
            $excel_data[] = array(
                'year' => $item['year'],
                'month' => $item['month'],
                'total_time_in_maintenance_period' => bcdiv($item['total_time_in_maintenance_period'], 3600),
                'total_time_in_bypass_mode' => bcdiv($item['total_time_in_bypass_mode'], 3600),
                'bypass_mode_activation' => $item['bypass_mode_activation'],
                'payment_system_failure' => $item['payment_system_failure'],
                'lms_failure' => $item['lms_failure'],
                'payment_kiosk_failure' => $item['payment_kiosk_failure'],
                'lms_mode_change' => $item['lms_mode_change'],
                'charging_scheme_change' => $item['charging_scheme_change'],
                'sms_message_retransmission' => $item['sms_message_retransmission'],
                'email_message_retransmission' => $item['email_message_retransmission'],
                'sms_message_success' => $item['sms_message_success'],
                'sms_message_failure' => $item['sms_message_failure'],
                'email_message_success' => $item['email_message_success'],
                'email_message_failure' => $item['email_message_failure'],
                'number_of_invalid_telephone_number' => $item['number_of_invalid_telephone_number'],
                'number_of_invalid_email_address' => $item['number_of_invalid_email_address'],
            );
        }

        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet(); //实例化 Spreadsheet 对象

        $worksheet = $spreadsheet->getActiveSheet();

        // 工作簿名称为 "Usage Report"
        $worksheet->setTitle($web_title);

        // 设置多级表头
        $worksheet->setCellValueByColumnAndRow(1, 1, '');
        $worksheet->setCellValueByColumnAndRow(2, 1, '');
        $worksheet->setCellValueByColumnAndRow(3, 1, $excel_title[0]);
        $worksheet->setCellValueByColumnAndRow(5, 1, $excel_title[1]);
        $worksheet->setCellValueByColumnAndRow(1, 2, $excel_title[2]);
        $worksheet->setCellValueByColumnAndRow(2, 2, $excel_title[3]);
        $worksheet->setCellValueByColumnAndRow(3, 2, $excel_title[4]);
        $worksheet->setCellValueByColumnAndRow(4, 2, $excel_title[5]);
        $worksheet->setCellValueByColumnAndRow(5, 2, $excel_title[6]);
        $worksheet->setCellValueByColumnAndRow(6, 2, $excel_title[7]);
        $worksheet->setCellValueByColumnAndRow(7, 2, $excel_title[8]);
        $worksheet->setCellValueByColumnAndRow(8, 2, $excel_title[9]);
        $worksheet->setCellValueByColumnAndRow(9, 2, $excel_title[10]);
        $worksheet->setCellValueByColumnAndRow(10, 2, $excel_title[11]);
        $worksheet->setCellValueByColumnAndRow(11, 2, $excel_title[12]);
        $worksheet->setCellValueByColumnAndRow(12, 2, $excel_title[13]);
        $worksheet->setCellValueByColumnAndRow(13, 2, $excel_title[14]);
        $worksheet->setCellValueByColumnAndRow(14, 2, $excel_title[15]);
        $worksheet->setCellValueByColumnAndRow(15, 2, $excel_title[16]);
        $worksheet->setCellValueByColumnAndRow(16, 2, $excel_title[17]);
        $worksheet->setCellValueByColumnAndRow(17, 2, $excel_title[18]);
        $worksheet->setCellValueByColumnAndRow(18, 2, $excel_title[19]);
        $worksheet->mergeCells('C1:D1');
        $worksheet->mergeCells('E1:R1');

        // 计算数组长度用于动态补充表格样式
        $len = count($excel_data);
        // 因为前面两格表头样式占了2行，所以要加2用于设置单元格样式
        $total_rows = $len + 2;

        // 表头背景颜色
        $worksheet->getStyle('A1:R2')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('c0c0c0');

        //设置单元格样式
        $styleArray = [
            'font' => [
                'bold' => false
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['argb' => '666666'],
                ],
            ],
        ];
        $worksheet->getStyle("A1:R$total_rows")->applyFromArray($styleArray);
        // 设置单元格的宽度
        foreach (range('A', 'R') as $index => $value) {
            // 头两个是多级表头的表头
            $worksheet->getColumnDimension($value)->setWidth(strlen($excel_title[$index + 2]) + 2);
        }
        $index = 0;
        foreach ($excel_data as $excel_data_item) {
            $line = $index + 3; //从表格第3行开始
            $worksheet->setCellValueByColumnAndRow(1, $line, $excel_data_item['year']);
            $worksheet->setCellValueByColumnAndRow(2, $line, (int)$excel_data_item['month']);
            $worksheet->setCellValueByColumnAndRow(3, $line, $excel_data_item['total_time_in_maintenance_period']);
            $worksheet->setCellValueByColumnAndRow(4, $line, $excel_data_item['total_time_in_bypass_mode']);
            $worksheet->setCellValueByColumnAndRow(5, $line, $excel_data_item['bypass_mode_activation']);
            $worksheet->setCellValueByColumnAndRow(6, $line, $excel_data_item['payment_system_failure']);
            $worksheet->setCellValueByColumnAndRow(7, $line, $excel_data_item['lms_failure']);
            $worksheet->setCellValueByColumnAndRow(8, $line, $excel_data_item['payment_kiosk_failure']);
            $worksheet->setCellValueByColumnAndRow(9, $line, $excel_data_item['lms_mode_change']);
            $worksheet->setCellValueByColumnAndRow(10, $line, $excel_data_item['charging_scheme_change']);
            $worksheet->setCellValueByColumnAndRow(11, $line, $excel_data_item['sms_message_retransmission']);
            $worksheet->setCellValueByColumnAndRow(12, $line, $excel_data_item['email_message_retransmission']);
            $worksheet->setCellValueByColumnAndRow(13, $line, $excel_data_item['sms_message_success']);
            $worksheet->setCellValueByColumnAndRow(14, $line, $excel_data_item['sms_message_failure']);
            $worksheet->setCellValueByColumnAndRow(15, $line, $excel_data_item['email_message_success']);
            $worksheet->setCellValueByColumnAndRow(16, $line, $excel_data_item['email_message_failure']);
            $worksheet->setCellValueByColumnAndRow(17, $line, $excel_data_item['number_of_invalid_telephone_number']);
            $worksheet->setCellValueByColumnAndRow(18, $line, $excel_data_item['number_of_invalid_email_address']);
            $index++;
        }

        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $file_name = $web_title . ' ' . date('Y-m-d H:i:s') . '.xlsx';

        // 客户端文件下载
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $file_name . '"');
        header('Cache-Control: max-age=0');
        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
        exit;
    }

    /**
     * 查询数据
     * @param $time_frame_search
     * @return array
     */
    protected function listData($time_frame_search = null): array
    {
        // 结束日期即当前月份的最后一天
        $end_date = date('Y-m-t 23:59:59');
        // 起始日期即开始时间的第一天 - 当前时间往前推12月
        $start_date = date('Y-m-01 00:00:00', strtotime('-11 month'));
        // 实际结果集
        $real_result_list = array();
        // 默认填充数据
        $default_result_list = array(
            'total_time_in_maintenance_period' => 0,
            'total_time_in_bypass_mode' => 0,
            'bypass_mode_activation' => 0,
            'payment_system_failure' => 0,
            'lms_failure' => 0,
            'payment_kiosk_failure' => 0,
            'lms_mode_change' => 0,
            'charging_scheme_change' => 0,
            'sms_message_retransmission' => 0,
            'email_message_retransmission' => 0,
            'sms_message_success' => 0,
            'sms_message_failure' => 0,
            'email_message_success' => 0,
            'email_message_failure' => 0,
            'number_of_invalid_telephone_number' => 0,
            'number_of_invalid_email_address' => 0,
        );

        // 搜索时间不为空
        if (filled($time_frame_search)) {
            // 得到时间区间，分割成开始时间和结束时间
            $time_frame_range = explode(' - ', $time_frame_search);
            // 判断是否是时间类型且为开始时间结束时间类型
            if (count($time_frame_range) == 2 && strtotime($time_frame_range[0]) && strtotime($time_frame_range[1]) && (strtotime($time_frame_range[0]) <= strtotime($time_frame_range[1]))) {
                $start_date = date('Y-m-01 00:00:00', strtotime($time_frame_range[0]));
                $end_date = date('Y-m-t 23:59:59', strtotime($time_frame_range[1]));
            }
        }

        // 通过当前管理员的site_number_list拼接find_in_set语句
        $find_in_set_sql = '';
        foreach (auth()->user()->site_number_list as $index => $site_number) {
            if ($index == 0) {
                $find_in_set_sql .= "find_in_set('$site_number', site_number_list)";
                continue;
            }
            $find_in_set_sql .= " or find_in_set('$site_number', site_number_list)";
        }

        // 获取维修记录结果集
        $maintenance_record_result_list = MaintenanceRecord::selectRaw('DATE_FORMAT(gmt_start,"%Y-%m") AS month_year_key')
            ->selectRaw('SUM(UNIX_TIMESTAMP(gmt_end)-UNIX_TIMESTAMP(gmt_start)) AS total_time_in_maintenance_period')
            ->selectRaw('SUM(CASE WHEN is_bypass_mode = 1 THEN UNIX_TIMESTAMP(gmt_end)-UNIX_TIMESTAMP(gmt_start) ELSE 0 END) AS total_time_in_bypass_mode')
            ->when(!isSuperAdministrator(), fn($query) => $query->whereRaw('IF(site_number_list IS NOT NULL, ' . $find_in_set_sql . ', TRUE)'))
            ->whereBetween('gmt_start', [$start_date, $end_date])
            ->groupBy('month_year_key')
            ->get()
            ->toArray();

        // 将查询的结果集根据month_year_key拆分
        $new_result_list = array_column($maintenance_record_result_list, null, 'month_year_key');

        // 获取开始结束时间的时间戳
        $start_date_timestamp = strtotime($start_date);
        $end_date_timestamp = strtotime($end_date);
        // 根据开始结束日期区间的时间戳循环获取月份 - 例如2022-03 ~ 2023-02
        while ($start_date_timestamp <= $end_date_timestamp) {
            // 当前月份
            $current_month = date('Y-m', $start_date_timestamp);
            // 当前月份结果 - 当有搜索时间且数据为空时即填充0
            $default_result_list['year'] = date('Y', $start_date_timestamp);
            $default_result_list['month'] = date('m', $start_date_timestamp);
            // 将每月填充数据，默认为0
            $real_result_list[$current_month] = $default_result_list;

            if (!empty($new_result_list)) {
                // 如果查询数据存在该月，赋值数据
                if (array_key_exists($current_month, $new_result_list)) {
                    // 移除key
                    unset($new_result_list[$current_month]['month_year_key']);
                    // 合并覆盖默认数据
                    $new_result_list[$current_month] = array_merge($real_result_list[$current_month], $new_result_list[$current_month]);
                    $new_result_list[$current_month]['year'] = date('Y', $start_date_timestamp);
                    $new_result_list[$current_month]['month'] = date('m', $start_date_timestamp);
                    $real_result_list[$current_month] = $new_result_list[$current_month];
                }
            }

            // 每次+1月份作为循环出口
            $start_date_timestamp = strtotime("+1 month", $start_date_timestamp);
        }

        return $real_result_list;
    }

    //用于时间计算 计算天数小时
    protected function secTime($time): string
    {
        if (is_numeric($time)) {
            $days = floor($time / 86400);
            $hours = floor(($time - 86400 * $days) / 3600);
            return $days . __('common.unit_day') . " " . $hours . __('common.unit_h');
        }
        return 0 . __('common.unit_day') . " " . 0 . __('common.unit_h');
    }
}

<?php

namespace App\Http\Controllers\Admin\Traits;

use Illuminate\Contracts\View\{
    Factory,
    View
};
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Application;
use Illuminate\Http\{
    RedirectResponse,
    Request
};

trait Edit
{
    protected Model $model;

    abstract protected static function rules(?Request $request, ?Model $model); // 表单验证规则

    abstract protected static function attributes(); // 字段名称

    abstract protected static function getUrlParams(?Request $request); // 返回地址栏参数

    public function edit(Request $request, String $number): View|Application|Factory|RedirectResponse|null
    {
        $data = array();

        // 当前角色为非超级管理员时且当前模块需要校验商户时筛选商户Id
        $model = $this->model::when(
            !isSuperAdministrator() && (isset(self::$module_check_merchant) && self::$module_check_merchant === true),
            fn ($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list)
        )
            ->when(
                !isSuperAdministrator() && (isset(self::$module_check_site) && self::$module_check_site === true),
                fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list)
            )
            ->where(property_exists($this->model, 'table_number') ? $this->model::$table_number : $this->model->getKeyName(), $number)->firstOrFail();

        if ($request->isMethod('post')) {
            return $this->modelValidateAndSave($request, $model);
        }

        $data['model'] = $model;

        return $this->getForm($request, $data);
    }
}

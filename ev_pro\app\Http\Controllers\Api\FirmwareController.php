<?php
namespace App\Http\Controllers\Api;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Http\{
    JsonResponse,
    Request,
};

Class FirmwareController extends CommonController
{
    protected static function getLanguageCode(Request $request)
    {

        $language_code = match ($request->input('language_code', 'en_US')) {
            'zh_HK' => 'zh_HK',
            default => 'en_US',
        };
        // 设置多语言
        self::setLanguage($language_code);
        return $language_code;
    }

    public function checkChargePointFirmwareUpgrade(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $charge_point_number = $request->charge_point_number;
        $firmware_version = $request->firmware_version; // 版本号

        if (blank($charge_point_number)) {
            $this->missingField('charge_point_number');
            return $this->returnJson();
        }
        if (blank($firmware_version)) {
            $this->missingField('firmware_version');
            return $this->returnJson();
        }

        $this->data = [
            'has_new_version' => false, // 是否有新版本
            'version' => null, // 版本号
            'description' => null, // 描述
            'upgrade_url' => null, // 更新地址
        ];

        // 获取根目录json文件夹下的charge_point_firmware_version.json文件
        $charge_point_firmware_version_array = getArrayFromJsonFile('charge_point_firmware_version', false);

        // 无法解析或者为空
        if (blank($charge_point_firmware_version_array)) {
            return $this->returnJson();
        }
        // 平台不存在
        foreach ($charge_point_firmware_version_array as $key => $value) {
            // 判断key是否被包含在 $charge_point_number中，如果包含，将当前值赋给 $charge_point_version，并停止循环
            if (strpos($charge_point_number, $key) !== false) {
                $charge_point_version = $value;
                break;
            }
        }

        if (empty($charge_point_version)) {
            return $this->returnJson();
        }

        if ($charge_point_version['version'] !== $firmware_version) {
            $this->data['has_new_version'] = true;
        }
        $this->data['description'] = $charge_point_version['description'][$language_code] ?? ($charge_point_version['description']['en_US'] ?? (reset($charge_point_version['description']) ?? null));
        $this->data['upgrade_url'] = $charge_point_version['upgrade_url'] ?? null;
        $this->data['version'] = $charge_point_version['version'] ?? null;

        return $this->returnJson();
    }
}

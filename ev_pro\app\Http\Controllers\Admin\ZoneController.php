<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Application;
use Illuminate\Http\{
    JsonResponse,
    RedirectResponse,
};
use Illuminate\Http\Request;
use App\Http\Controllers\Admin\Traits\{
    Add,
    Edit,
};
use App\Models\Modules\{
    Zone,
    ZoneDescription,
    Site,
};

class ZoneController extends CommonController
{
    use Add, Edit;

    protected static string $module_name = 'zone'; // 模块名称

    protected static bool $module_check_site = true; // 标记该模块校验场地

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new Zone;
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'add_url' => action([self::class, 'add']),
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'name_search' => $request->get('name_search'),
            'site_search' => $request->get('site_search'),
        );

        // 场地下拉列表
        $data['site_list'] = $this->getSiteOptionList();

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'sort_order');
        $sort = $request->input('sort', 'asc');
        $length = (int)$request->input('length', 10);
        $name_search = $request->input('name_search');
        $site_search = $request->input('site_search');

        if ($order == 'name') $order = 'zone.name_json';
        if ($order == 'site_name') $order = 'site.name_json';

        $data_list = Zone::select('zone.*', 'site.name_json as site_name_json_init')
            ->leftJoin('site', 'zone.site_number', '=', 'site.site_number')
            ->with(['description'])
            ->when(filled($name_search), function ($query) use ($name_search) {
                return $query->whereHas('description', function ($query) use ($name_search) {
                    return $query->where('name', 'like', "%{$name_search}%");
                });
            })
            ->when(filled($site_search), fn($query) => $query->where('zone.site_number', $site_search))
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('zone.site_number', auth()->user()->site_number_list))
            ->orderBy($order, $sort)
            ->latest('gmt_modified')
            ->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $result[] = array(
                'zone_id' => $data->zone_id, // 区域ID
                'zone_number' => $data->zone_number, // 区域编号
                'name' => $this->getValueFromLanguageArray($data->name_json), // 名称
                'site_name' => $this->getValueFromLanguageArray($data->site_name_json_init) ?? '—/—', // 场地名称
                'remark' => $data->remark ?? '—/—',//备注
                'sort_order' => $data->sort_order, // 排序
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );

        return response()->json($json);
    }

    protected function getForm(Request $request, array $data): View|Application|Factory
    {
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'], self::getUrlParams($request)
        );

        // 新增时才回显区域编号和场地编号
        if (blank($data['model']->zone_id)) {
            $data['model']->zone_number = $request->old('zone_number', $data['model']->zone_number); // 区域编号
            // 超级管理员或者多场地能选场地，否则只取所属的单场地
            $site_number = isSuperAdministrator() || auth()->user()->site_number_list->count() > 1
                ? $request->old('site_number', $data['model']->site_number)
                : (auth()->user()->site_number_list->first() ?? null);
            $site_name = $this->getValueFromLanguageArray(Site::firstWhere('site_number', $site_number)?->name_json);
            $data['model']->site_number = $site_number; // 场地编号
            $data['model']->site_name = $site_name; // 场地名称
        }
        $data['model']->remark = $request->old('remark', $data['model']->remark); // 备注
        $data['model']->sort_order = $request->old('sort_order', $data['model']->sort_order); // 排序

        foreach (config('languages') as $language_code => $language_name) {
            // 有旧数据
            $zone_description_old_list = $request->old('item');
            if (filled($zone_description_old_list)) {
                $data['item'][$language_code] = array(
                    'language_name' => $language_name,
                    'language_code' => $language_code,
                    'zone_description_id' => $zone_description_old_list[$language_code]['zone_description_id'] ?? null, // ID
                    'name' => $zone_description_old_list[$language_code]['name'] ?? null,
                );
            } else {
                $zone_description_result = $data['model']->description->where('language_code', $language_code)->first();
                $data['item'][$language_code] = array(
                    'language_name' => $language_name,
                    'language_code' => $language_code,
                    'zone_description_id' => $zone_description_result->zone_description_id ?? null, // ID
                    'name' => $zone_description_result->name ?? null, // 名称
                );
            }
        }

        return view("pages.{$data['module_name']}.form", $data);
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @param Zone $model
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    protected function modelValidateAndSave(Request $request, Zone $model): RedirectResponse
    {
        $request->validate(self::rules($request, $model), [], self::attributes());
        // 新增时才保存区域、场地和商户编号
        if (blank($model->zone_id)) {
            $model->zone_number = $request->input('zone_number');
            $model->site_number = $request->input('site_number');
            if (filled($model->site_number) && filled($site = Site::firstWhere('site_number', $model->site_number))) {
                $model->merchant_number = $site->merchant_number;
            }
        }
        $model->remark = $request->input('remark');
        $model->sort_order = $request->input('sort_order', 0);
        // 获取请求场地描述数据
        $zone_description_list = $request->input('item', array());
        $name_json = array();
        foreach ($zone_description_list as $zone_description) {
            $name_json[$zone_description['language_code']] = $zone_description['name'];
        }
        $model->name_json = $name_json;
        $model->save();

        foreach ($zone_description_list as $zone_description) {
            if (isset($zone_description['zone_description_id'])) {
                $zone_description_model = ZoneDescription::findOr($zone_description['zone_description_id'], function () {
                    // 获取不到即新增
                    return new ZoneDescription;
                });
            } else {
                $zone_description_model = new ZoneDescription;
            }
            $zone_description_model->zone_number = $model->zone_number;
            $zone_description_model->language_code = $zone_description['language_code'];
            $zone_description_model->name = $zone_description['name'];

            $zone_description_model->save();
        }

        return redirect()->action(
            [self::class, 'showPage'], self::getUrlParams($request)
        );
    }

    /**
     * 删除
     *
     * @param Request $request
     * @return JsonResponse
     * @Description 删除对应数据并且删除对应头像
     * @example
     * <AUTHOR>
     * @date 2023-10-24
     */
    public function delete(Request $request): JsonResponse
    {
        $zone_number = $request->post('zone_number');
        $module_name = self::$module_name;

        if (filled($zone_number) &&
            filled($model = $this->model::with(['kiosk', 'connector'])
                ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->firstWhere('zone_number', $zone_number))
        ) {
            if ($model->kiosk->count() == 0 && $model->connector->count() == 0) {
                $model->description()->delete();
                $model->delete();
            } else {
                $this->code = 40001;
                $this->message = __("$module_name.error_zone_mismatching");
            }
        } else {
            $this->notFoundData(__("$module_name.web_title"));
        }

        return $this->returnJson();
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function rules(?Request $request, ?Model $model): array
    {
        $module_name = self::$module_name;
        $rules = array(
            'sort_order' => 'integer|min:0',
            'remark' => 'nullable|max:1000',
        );

        // 只有新增时才校验区域和场地编号
        if (blank($model->zone_id)) {
            $rules['zone_number'] = [
                'required',
                'max:30',
                'regex:/^[a-zA-Z0-9]+$/', // 只能填写字母和数字
                'unique:App\Models\Modules\Zone,zone_number',
            ];
            if (isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) {
                $rules['site_number'] = [
                    'required',
                    'exists:App\Models\Modules\Site,site_number',
                    function ($attr, $value, $fail) use ($request, $model, $module_name) {
                        // 新增时校验非超级管理员且未拥有该场地提示错误
                        if (!isSuperAdministrator() && !in_array($value, auth()->user()->site_number_list->toArray())) {
                            $fail(__('common.text_not_found', ['field' => __("$module_name.site")]));
                        }
                    },
                ];
            }
        }

        $rules['item.*.name'] = 'required|max:45';

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'zone_number' => __("$module_name.zone_number"),
            'site_number' => __("$module_name.site"),
            'name' => __("$module_name.name"),
            'remark' => __("$module_name.remark"),
            'sort_order' => __("$module_name.sort_order"),
            'item.*.name' => __("$module_name.description_name"),
        ];
    }

    /**
     * 返回地址栏参数
     *
     * @param Request|null $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => 'true',
            'name_search' => $request->get('name_search'),
            'site_search' => $request->get('site_search'),
        );
    }

}

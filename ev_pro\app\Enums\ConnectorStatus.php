<?php

namespace App\Enums;

use BenSampo\Enum\Enum;
use BenSampo\Enum\Contracts\LocalizedEnum;
use App\Enums\Traits\Tools;
use Lang;

/**
 * @method static Offline
 * @method static Available
 * @method static Preparing
 * @method static Charging
 * @method static Finishing
 * @method static Unavailable
 * @method static Queuing
 * @method static Faulted
 */
final class ConnectorStatus extends Enum implements LocalizedEnum
{
    use Tools;

    const Offline = 'OFFLINE'; // 离线
    const Available = 'AVAILABLE'; // 可用
    const Preparing = 'PREPARING'; // 准备中
    const Charging = 'CHARGING'; // 充电中
    const Finishing = 'FINISHING'; // 结束中
    const Unavailable = 'UNAVAILABLE'; // 不可用
    const Queuing = 'QUEUING'; // 队列中
    const Faulted = 'FAULTED'; // 故障

    /**
     * 通过语言值获取语言文件
     *
     * @throws InvalidEnumMemberException
     */
    public static function getLocalDescription($value, $local = null): ?string
    {
        if (parent::isLocalizable()) {
            $localizedStringKey = parent::getLocalizationKey() . '.' . $value;

            if (Lang::has($localizedStringKey)) {
                return __($localizedStringKey, [], $local);
            }
        }

        return parent::getFriendlyName(parent::getKey($value));
    }

    /**
     * 通过语言值获取该语言下的枚举数组
     *
     * @throws InvalidEnumMemberException
     */
    public static function asLocalSelectArray($local = null): array
    {
        $array = parent::asArray();
        $selectArray = [];

        foreach ($array as $value) {
            $selectArray[$value] = self::getLocalDescription($value, $local);
        }

        return $selectArray;
    }

    public static function getColors(): array
    {
        return array(
            self::Offline => '#B5B5BA',
            self::Available => '#4AB41C',
            self::Preparing => '#FDCA28',
            self::Charging => '#FF8518',
            self::Finishing => '#00B9FF',
            self::Unavailable => '#F61B1A',
            self::Queuing => '#025ca5',
            self::Faulted => '#CC0000',
        );
    }
}

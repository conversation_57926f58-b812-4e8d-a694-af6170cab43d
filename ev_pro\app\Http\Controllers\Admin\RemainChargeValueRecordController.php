<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use Illuminate\Foundation\Application;
use Illuminate\Http\{
    JsonResponse,
    Request,
};
use App\Enums\{
    IdentityType,
    ChargeValueType,
};
use App\Models\Modules\{
    RemainChargeValueRecord,
};

class RemainChargeValueRecordController extends CommonController
{
    protected static string $module_name = 'remainChargeValueRecord'; // 模块名称

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'charge_record_number_search' => $request->get('charge_record_number_search'),
            'identity_number_search' => $request->get('identity_number_search'),
            'used_charge_record_number_search' => $request->get('used_charge_record_number_search'),
        );

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $length = (int)$request->input('length', 10);
        $charge_record_number_search = $request->input('charge_record_number_search');
        $identity_number_search = $request->input('identity_number_search');
        $used_charge_record_number_search = $request->input('used_charge_record_number_search');

        $data_list = RemainChargeValueRecord::when(filled($charge_record_number_search), fn($query) => $query->where('charge_record_number', 'like', "%$charge_record_number_search%"))
            ->when(filled($identity_number_search), fn($query) => $query->where('identity_number', 'like', "%$identity_number_search%"))
            ->when(filled($used_charge_record_number_search), fn($query) => $query->where('used_charge_record_number', 'like', "%$used_charge_record_number_search%"))
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->orderBy($order, $sort)
            ->latest('gmt_modified')
            ->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $remain_charge_value = $data->remain_charge_value ?? 0;
            $remain_charge_value = match ($data->charge_value_type) {
                ChargeValueType::Time => round($remain_charge_value / 60) . __('common.unit_mins'),
                ChargeValueType::Energy => (double)bcdiv($remain_charge_value, 1000, 3) . __('common.unit_kwh'),
                default => $remain_charge_value
            };
            $result[] = array(
                'charge_record_number' => $data->charge_record_number, // 充电记录编号
                'identity_type' => IdentityType::getDescription($data->identity_type), // 身份类型
                'identity_number' => $data->identity_number, // 身份号码
                'charge_value_type' => ChargeValueType::getDescription($data->charge_value_type), // 充电量类型
                'remain_charge_value' => $remain_charge_value, // 剩余充电量
                'gmt_expired' => $data->gmt_expired, // 过期时间
                'used_charge_record_number' => $data->used_charge_record_number ?? '—/—', // 已用充电记录编号
                'gmt_used' => $data->gmt_used ?? '—/—', // 使用时间
                'gmt_invalid' => $data->gmt_invalid ?? '—/—', // 失效时间
                'remain_charge_value_record_number' => $data->remain_charge_value_record_number, // 剩余充电量记录编号
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );
        return response()->json($json);
    }

    /**
     * 返回地址栏参数
     *
     * @param Request $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(Request $request): array
    {
        return array(
            'is_page' => 'true',
            'charge_record_number_search' => $request->get('charge_record_number_search'),
            'identity_number_search' => $request->get('identity_number_search'),
            'used_charge_record_number_search' => $request->get('used_charge_record_number_search'),
        );
    }
}

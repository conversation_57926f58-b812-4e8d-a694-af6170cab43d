<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;
use App\Models\Traits\Emoji;
use Illuminate\Database\Eloquent\Casts\Attribute;

class NewsDescription extends Model
{
    use emoji;

    protected $table = 'news_description';
    protected $primaryKey = 'news_description_id';

    const CREATED_AT = 'gmt_create'; //默认是小写的模型类名复数格式，此处为自定义表名
    const UPDATED_AT = 'gmt_modified'; //定义主键，默认为id

    public $skipMutators = false; // 是否跳过修改器

    public $fillable = [
        'news_description_id',
        'language_code',
        'title',
        'content',
        'image_url'
    ];
    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 类型转化器
     */
    protected $casts = [

    ];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [

    ];

    /**
     * 获取图片路径
     */
    protected function imageUrl(): Attribute
    {
        return Attribute::make(
            get: fn (?string $value) => !$this->skipMutators ? existsImage('public', $value) : $value,
        );
    }

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * 新闻
     */
    public function news()
    {
        return $this->hasOne(News::class, 'news_id', 'news_id');
    }
}

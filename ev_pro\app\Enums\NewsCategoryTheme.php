<?php

namespace App\Enums;

use BenSampo\Enum\{
    Enum,
    Contracts\LocalizedEnum
};
use App\Enums\Traits\Tools;

/**
 * @method static ClassicList
 * @method static CardList
 * @method static Grid
 * @method static MasonryGrid
 * @method static Carousel
 */
final class NewsCategoryTheme extends Enum implements LocalizedEnum
{
    use Tools;

    const ClassicList = 'CLASSIC_LIST';
    const CardList = 'CARD_LIST';
    const Grid = 'GRID';
    const MasonryGrid = 'MASONRY_GRID';
    const Carousel = 'CAROUSEL';
}

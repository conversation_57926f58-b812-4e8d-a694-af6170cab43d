<?php

namespace App\Enums;

use BenSampo\Enum\Enum;
use BenSampo\Enum\Contracts\LocalizedEnum;
use App\Enums\Traits\Tools;

/**
 * @method static InterruptionBegin
 * @method static InterruptionEnd
 * @method static OTHER
 * @method static SampleClock
 * @method static SamplePeriodic
 * @method static TransactionBegin
 * @method static TransactionEnd
 * @method static TRIGGER
 */
final class ReadingContextEnum extends Enum implements LocalizedEnum
{
    use Tools;

    const InterruptionBegin = 'Interruption.Begin';
    const InterruptionEnd = 'Interruption.End';
    const Other = 'Other';
    const SampleClock = 'Sample.Clock';
    const SamplePeriodic = 'Sample.Periodic';
    const TransactionBegin = 'Transaction.Begin';
    const TransactionEnd = 'Transaction.End';
    const Trigger = 'Trigger';
}

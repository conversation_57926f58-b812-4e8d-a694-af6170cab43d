<?php

namespace App\Enums;

use BenSampo\Enum\Enum;
use BenSampo\Enum\Contracts\LocalizedEnum;
use App\Enums\Traits\Tools;

/**
 * @method static defaultIdleTimeout
 * @method static defaultMessageDisplayDuration
 * @method static defaultDialogDisplayDuration
 * @method static octopusGetCardNumberTimeout
 * @method static octopusMessageDisplayDuration
 * @method static octopusErrorDisplayDuration
 * @method static prePaidReceiptCompleteCountdown
 * @method static prePaidStopChargeSuccessCompleteCountdown
 * @method static postPaidReceiptCompleteCountdown
 * @method static postPaidStartChargeSuccessCompleteCountdown
 * @method static postPaidStartChargeCompletedCountdown
 * @method static enterReceiverSkipCountdown
 * @method static enterReceiverCompleteCountdown
 * @method static checkConnectorAliveTimeout
 * @method static pageFeeTariffIdleTimeout
 */
class VioskSettingScreenTimeKeyEnum extends Enum implements LocalizedEnum
{
    use Tools;

    // 默认闲置超时
    const defaultIdleTimeout = 'DEFAULT_IDLE_TIMEOUT';
    // 默认消息显示持续时间
    const defaultMessageDisplayDuration = 'DEFAULT_MESSAGE_DISPLAY_DURATION';
    // 默认弹窗显示持续时间
    const defaultDialogDisplayDuration = 'DEFAULT_DIALOG_DISPLAY_DURATION';
    // 八达通读卡超时
    const octopusGetCardNumberTimeout = "OCTOPUS_GET_CARD_NUMBER_TIMEOUT";
    // 八达通消息显示持续时间
    const octopusMessageDisplayDuration = "OCTOPUS_MESSAGE_DISPLAY_DURATION";
    // 八达通错误显示持续时间
    const octopusErrorDisplayDuration = "OCTOPUS_ERROR_DISPLAY_DURATION";
    // 八达通操作超时
    const octopusOperationTimeout = "OCTOPUS_OPERATION_TIMEOUT";
    // 预付收据完成倒计时
    const prePaidReceiptCompleteCountdown = 'PRE_PAID_RECEIPT_COMPLETE_COUNTDOWN';
    // 预付停止充电成功完成倒计时
    const prePaidStopChargeSuccessCompleteCountdown = 'PRE_PAID_STOP_CHARGE_SUCCESS_COMPLETE_COUNTDOWN';
    // 后付收据完成倒计时
    const postPaidReceiptCompleteCountdown = 'POST_PAID_RECEIPT_COMPLETE_COUNTDOWN';
    // 后付开始充电成功完成倒计时
    const postPaidStartChargeSuccessCompleteCountdown = 'POST_PAID_START_CHARGE_SUCCESS_COMPLETE_COUNTDOWN';
    // 后付开始充电已成功倒计时
    const postPaidStartChargeCompletedCountdown = 'POST_PAID_START_CHARGE_COMPLETED_COUNTDOWN';
    // 输入提示跳过倒计时
    const enterReceiverSkipCountdown = 'ENTER_RECEIVER_SKIP_COUNTDOWN';
    // 输入提示完成倒计时
    const enterReceiverCompleteCountdown = 'ENTER_RECEIVER_COMPLETE_COUNTDOWN';
    // 检查充电枪活跃超时
    const checkConnectorAliveTimeout = 'CHECK_CONNECTOR_ALIVE_TIMEOUT';
    // 帮助页面闲置超时
    const pageFeeTariffIdleTimeout = "PAGE_FEE_TARIFF_IDLE_TIMEOUT";
    // 查询用户超时
    const queryUserTimeout = "QUERY_USER_TIMEOUT";
    // 用户充电记录账单收据完成倒计时
    const userChargeRecordBillReceiptCompleteCountdown = "USER_CHARGE_RECORD_BILL_RECEIPT_COMPLETE_COUNTDOWN";
}

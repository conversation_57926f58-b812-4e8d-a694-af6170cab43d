<?php

namespace App\Models\Modules;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Model;

class PointsTransaction extends Model
{

    protected $table = 'points_transaction'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'points_transaction_id'; //定义主键，默认为id
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    // protected $fillable = [];

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
    ];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * 为 array / JSON 序列化准备日期格式
     *
     * @param DateTimeInterface $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    /**
     * Points Transaction 关联 AppUser
     */
    public function appUser()
    {
        return $this->hasOne(AppUser::class, 'user_id', 'user_id');
    }

    /**
     * 一对一关联积分钱包
     */
    public function pointsWallet()
    {
        return $this->hasOne(PointsWallet::class, 'points_wallet_id', 'points_wallet_id');
    }

    /**
     * 一对多关联Charge Payment Record
     */
    public function chargePaymentRecord()
    {
        return $this->hasMany(ChargePaymentRecord::class, 'points_transaction_id', 'points_transaction_id');
    }
}

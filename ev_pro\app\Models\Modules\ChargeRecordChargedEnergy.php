<?php

namespace App\Models\Modules;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Model;
class ChargeRecordChargedEnergy extends Model
{

    protected $table = 'charge_record_charged_energy'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'charge_record_charged_energy_id'; //定义主键，默认为id
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    // protected $fillable = [];


    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 类型转化器
     */
    protected $casts = [
        'charged_energy_item_json' => 'array',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    protected function asJson($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }


    /**
     * 一对一关联充电纪录
     */
    public function chargeRecord()
    {
        return $this->hasOne(ChargeRecord::class, 'charge_record_number', 'charge_record_number');
    }
}

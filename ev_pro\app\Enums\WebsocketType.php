<?php

namespace App\Enums;

use BenSampo\Enum\Enum;
use BenSampo\Enum\Contracts\LocalizedEnum;
use App\Enums\Traits\Tools;

/**
 * @method static HardRebootNotification
 * @method static SoftRebootNotification
 * @method static HardShutdownNotification
 * @method static SoftShutdownNotification
 * @method static MaintenanceNotification
 * @method static ReloadInitializationDataNotification
 * @method static OctopusDataExchangeUploadNotification
 * @method static OctopusDataExchangeDownloadNotification
 */
final class WebsocketType extends Enum implements LocalizedEnum
{
    use Tools;

    const HardRebootNotification = 'HardRebootNotification';
    const SoftRebootNotification = 'SoftRebootNotification';
    const HardShutdownNotification = 'HardShutdownNotification';
    const SoftShutdownNotification = 'SoftShutdownNotification';
    const MaintenanceNotification = 'MaintenanceNotification';
    const ReloadInitializationDataNotification = 'ReloadInitializationDataNotification';
    const NotifyUpdatedNotification = 'NotifyUpdatedNotification';
    const ChargeFeeCalculationNotification = 'ChargeFeeCalculationNotification';
    const OctopusDataExchangeUploadNotification = 'OctopusDataExchangeUploadNotification';
    const OctopusDataExchangeDownloadNotification = 'OctopusDataExchangeDownloadNotification';
}

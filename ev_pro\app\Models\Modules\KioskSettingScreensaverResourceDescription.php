<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;
use App\Models\Traits\Emoji;

class KioskSettingScreensaverResourceDescription extends Model
{
    use emoji;

    protected $table = 'kiosk_setting_screensaver_resource_description';
    protected $primaryKey = 'kiosk_setting_screensaver_resource_description_id';

    const CREATED_AT = 'gmt_create'; //默认是小写的模型类名复数格式，此处为自定义表名
    const UPDATED_AT = 'gmt_modified'; //定义主键，默认为id

    // protected $fillable = [];
    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 类型转化器
     */
    protected $casts = [
    ];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [
        'sort_order' => 0,
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];
}

<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Auth;
use Illuminate\Http\{
    JsonResponse,
    Request,
};
use Illuminate\Foundation\Application;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use App\Enums\{
    PaymentMethod,
    PaymentDeviceEnum,
    ChargeAccountingTransactionType,
    ChargeAccountingTransactionCategory,
};
use App\Models\Modules\{
    ChargeAccountingRecord,
};


class ChargeAccountingRecordController extends CommonController
{
    protected static string $module_name = 'chargeAccountingRecord'; // 模块名称

    /**
     * 初始页
     *
     * @param Request $request
     * @return View|Application|Factory
     */
    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'site_search' => $request->get('site_search'),
            'gmt_deduct_search' => $request->get('gmt_deduct_search'),
        );
        $data['payment_method_search'] = filled($request->get('payment_method_search')) ?
            explode(',', $request->get('payment_method_search'))
            : [];
        $data['transaction_type_search'] = filled($request->get('transaction_type_search')) ?
            explode(',', $request->get('transaction_type_search'))
            : [];

        // 商户下拉列表
        $data['site_list'] = self::getSiteOptionList();

        // 支付方式下拉列表
        $data['payment_method_list'] = array();
        foreach (PaymentMethod::asSelectArray() as $value => $name) {
            $data['payment_method_list'][] = array(
                'name' => $name,
                'value' => $value,
            );
        }

        // 交易类型下拉列表
        $data['transaction_type_list'] = array();
        foreach (ChargeAccountingTransactionType::asSelectArray() as $value => $name) {
            $data['transaction_type_list'][] = array(
                'name' => $name,
                'value' => $value,
            );
        }

        return view("pages.{$data['module_name']}.list", $data);
    }

    /**
     * 列表接口
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $length = (int)$request->input('length', 10);
        $search = $this->_searchList($request);
        $data_list = $search->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $site_name = $this->getValueFromLanguageArray($data->site?->name_json) ?? '—/—';

            $result[] = array(
                'charge_accounting_record_id' => $data->charge_accounting_record_id, // 充电会计记录ID
                'site_name' => $site_name, // 场地
                'payment_device' => PaymentDeviceEnum::getDescription($data->payment_device), // 支付设备
                'payment_method' => PaymentMethod::getDescription($data->payment_method), // 支付方式
                'transaction_type_enum' => $data->transaction_type, // 交易类型枚举
                'transaction_type' => ChargeAccountingTransactionType::getDescription($data->transaction_type), // 交易类型
                'transaction_category_enum' => $data->transaction_category, // 交易分类枚举
                'transaction_category' => ChargeAccountingTransactionCategory::getDescription($data->transaction_category), // 交易分类
                'source_transaction_number' => $data->source_transaction_number, // 来源交易编号
                'amount' => __('common.unit_hk') . (float)bcdiv($data->amount, 100, 1), // 金额
                'gmt_deduct' => $data->gmt_deduct, // 扣款时间
                'octopus_card_number' => $data->octopus_card_number, // 八达通卡号
                'pos_payment_method_name' => $data->pos_payment_method_name, // POS支付方式名称
                'pos_card_number' => $data->pos_card_number, // POS卡号
                'remark' => $data->remark, // 备注
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result
        );

        return response()->json($json);
    }

    /**
     * 导出
     *
     * @param Request $request
     * @return void
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function excelExport(Request $request): void
    {
        ini_set('memory_limit', '-1');
        // 获取数据
        $search = $this->_searchList($request);
        $data_list = $search->get();
        $module_name = self::$module_name;

        $result = array();
        foreach ($data_list as $data) {
            $site_name = $this->getValueFromLanguageArray($data->site?->name_json) ?? '—/—';
            $amount = bcdiv($data->amount, 100, 1);
            // 如果是支出，则金额为负数
            if ($data->transaction_type === ChargeAccountingTransactionType::Expense) {
                $amount = $amount * -1;
            }
            $result[] = array(
                'site_name' => $site_name, // 场地
                'payment_device' => PaymentDeviceEnum::getDescription($data->payment_device), // 支付设备
                'payment_method' => PaymentMethod::getDescription($data->payment_method), // 支付方式
                'transaction_type' => ChargeAccountingTransactionType::getDescription($data->transaction_type), // 交易类型
                'transaction_category' => ChargeAccountingTransactionCategory::getDescription($data->transaction_category), // 交易分类
                'amount' => $amount, // 金额
                'gmt_deduct' => $data->gmt_deduct, // 扣款时间
                'octopus_card_number' => $data->octopus_card_number, // 八达通卡号
                'pos_payment_method_name' => $data->pos_payment_method_name, // POS支付方式名称
                'pos_card_number' => $data->pos_card_number, // POS卡号
                'remark' => $data->remark, // 备注
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
            );
        }

        // 先把语言赋值才可以在后面array出来表头，不然会输出未编译前的字符串
        $site_name = __("$module_name.site");
        $payment_device = __("$module_name.payment_device");
        $payment_method = __("$module_name.payment_method");
        $transaction_type = __("$module_name.transaction_type");
        $transaction_category = __("$module_name.transaction_category");
        $amount = __("$module_name.amount") . ' ( ' . __('common.unit_hk_blank') . ' )';
        $gmt_deduct = __("$module_name.gmt_deduct");
        $octopus_card_number = __("$module_name.octopus_card_number");
        $pos_payment_method_name = __("$module_name.pos_payment_method_name");
        $pos_card_number = __("$module_name.pos_card_number");
        $remark = __("$module_name.remark");
        $gmt_create = __("$module_name.gmt_create");

        $web_title = __("$module_name.web_title");

        $remove_column_list = [];
        switch (env('CURRENT_PROJECT_NAME')) {
            default:
                // 需要移除的列
                $remove_column_list = [

                ];
                break;
        }

        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet(); //实例化 Spreadsheet 对象
        $worksheet = $spreadsheet->getActiveSheet();

        // 工作簿名称为 "Usage Report"
        $worksheet->setTitle($web_title);

        $styleArray = [
            'font' => [
                'bold' => false
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
        ];

        // 计算数组长度用于动态补充表格样式
        $len = count($result);
        // 因为前面两格表头样式占了一行，所以要加1用于设置单元格样式
        $total_rows = $len + 1;
        $styleArrayBody = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['argb' => '666666'],
                ],
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
        ];

        // 指定表头字段
        $excel_data = array(
            array(
                $site_name,
                $payment_device,
                $payment_method,
                $transaction_type,
                $transaction_category,
                $amount,
                $gmt_deduct,
                $octopus_card_number,
                $pos_payment_method_name,
                $pos_card_number,
                $remark,
                $gmt_create,
            )
        );

        // 移除指定列
        if (filled($remove_column_list)) $excel_data[0] = array_diff($excel_data[0], $remove_column_list);
        //设置单元格样式
        // 将数组长度转换为 Excel 列名
        $last_column = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex(count($excel_data[0]));
        $worksheet->getStyle("A1:{$last_column}$total_rows")->applyFromArray($styleArray)->getFont()->setName('Times New Roman')->setSize(11);
        $worksheet->getStyle("A1:{$last_column}1")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('c0c0c0');
        $worksheet->getStyle("A1:{$last_column}$total_rows")->applyFromArray($styleArrayBody);

        foreach ($result as $report) {
            $excel_row = array(
                $site_name => $report['site_name'],
                $payment_device => $report['payment_device'],
                $payment_method => $report['payment_method'],
                $transaction_type => $report['transaction_type'],
                $transaction_category => $report['transaction_category'],
                $amount => $report['amount'],
                $gmt_deduct => $report['gmt_deduct'],
                $octopus_card_number => $report['octopus_card_number'],
                $pos_payment_method_name => $report['pos_payment_method_name'],
                $pos_card_number => $report['pos_card_number'],
                $remark => $report['remark'],
                $gmt_create => $report['gmt_create'],
            );
            // 移除指定列
            if (filled($remove_column_list)) {
                foreach ($remove_column_list as $remove_column) {
                    unset($excel_row[$remove_column]);
                }
            }
            $excel_data[] = $excel_row;
        }

        $worksheet->fromArray($excel_data);
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $file_name = $web_title . ' ' . date('Y-m-d H:i:s') . '.xlsx';
        //设置单元格的宽度
        foreach ($spreadsheet->getActiveSheet()->getColumnIterator() as $column) {
            $spreadsheet->getActiveSheet()->getColumnDimension($column->getColumnIndex())->setAutoSize(true);
        }

        // 客户端文件下载
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $file_name . '"');
        header('Cache-Control: max-age=0');
        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
        exit;
    }

    /**
     * 搜索数据
     *
     * @param Request $request
     * @return mixed
     */
    protected function _searchList(Request $request): mixed
    {
        // dataTable字段
        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $site_number = $request->input('site_search');
        $payment_method = $request->input('payment_method_search');
        $transaction_type = $request->input('transaction_type_search');
        $gmt_deduct = $request->input('gmt_deduct_search');

        $gmt_deduct_search_list = self::getRangeDateTimeArray($gmt_deduct ?: '') ?: null;

        $charge_accounting_record = ChargeAccountingRecord::with('site')
            ->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->when(filled($site_number), fn ($query) => $query->where('site_number', $site_number))
            ->when(filled($payment_method), function ($query) use ($payment_method) {
                if (is_string($payment_method) && filled($payment_method_list = explode(',', $payment_method))) {
                    $payment_method = $payment_method_list;
                }
                $query->whereIn('payment_method', $payment_method);
            })
            ->when(filled($transaction_type), function ($query) use ($transaction_type) {
                if (is_string($transaction_type) && filled($transaction_type_list = explode(',', $transaction_type))) {
                    $transaction_type = $transaction_type_list;
                }
                $query->whereIn('transaction_type', $transaction_type);
            })
            ->when(filled($gmt_deduct_search_list), function ($query) use ($gmt_deduct_search_list) {
                $query->whereBetween('gmt_deduct', $gmt_deduct_search_list);
            })
            ->orderBy($order, $sort)
            ->latest('gmt_create');

        return $charge_accounting_record;
    }

    /**
     * 返回地址栏参数
     *
     * @param Request $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(Request $request): array
    {
        return array(
            'is_page' => 'true',
            'site_search' => $request->get('site_search'),
            'payment_method_search' => $request->get('payment_method_search'),
            'transaction_type_search' => $request->get('transaction_type_search'),
            'gmt_deduct_search' => $request->get('gmt_deduct_search'),
        );
    }
}

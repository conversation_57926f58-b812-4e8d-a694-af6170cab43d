<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;

class AppVersion extends Model
{

    protected $table = 'app_version'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'app_version_id'; //定义主键，默认为id
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    // protected $fillable = [];

    /**
     * 类型转化器
     */
    protected $casts = [
        'is_mandatory' => 'bool',
        'is_enable' => 'bool',
    ];

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'is_mandatory' => false,
        'is_enable' => false,
    ];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * 一对多关联AppVersionDescription
     */
    public function description()
    {
        return $this->hasMany(AppVersionDescription::class, 'app_version_id', 'app_version_id');
    }


    // deleting event
    public static function boot()
    {
        parent::boot();

        static::deleting(function ($model) {
            $model->description()->delete();
        });

        static::updating(function ($model) {
            $model->description()->delete();
        });
    }

    /**
     * 一对一关联Merchant
     */
    public function merchant()
    {
        return $this->hasOne(Merchant::class, 'merchant_number', 'merchant_number');
    }
}

<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Auth;
use Illuminate\Http\{
    JsonResponse,
    Request,
};
use Illuminate\Foundation\Application;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use App\Enums\{
    PaymentMethod,
    PaymentDeviceEnum,
    ChargePreAuthorizationStatus,
    ChargeValueType,
    PosVendorEnum,
    IdentityType,
};
use App\Models\Modules\{
    ChargePreAuthorizationRecord,
    ChargePaymentRecord,
    ChargePreAuthorizationRefundRecord,
};
use Illuminate\Support\Facades\Redis;


class ChargePreAuthorizationRecordController extends CommonController
{
    protected static string $module_name = 'chargePreAuthorizationRecord'; // 模块名称

    /**
     * 初始页
     *
     * @param Request $request
     * @return View|Application|Factory
     */
    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'charge_pre_authorization_record_number_search' => $request->get('charge_pre_authorization_record_number_search'),
            'gmt_create_search' => $request->get('gmt_create_search'),
        );
        $data['charge_pre_authorization_status_search'] = filled($request->get('charge_pre_authorization_status_search')) ?
            explode(',', $request->get('charge_pre_authorization_status_search'))
            : array();

        $data['charge_pre_authorization_status_list'] = array();
        foreach (ChargePreAuthorizationStatus::asSelectArray() as $value => $name) {
            $data['charge_pre_authorization_status_list'][] = array(
                'name' => $name,
                'value' => $value,
            );
        }

        return view("pages.{$data['module_name']}.list", $data);
    }

    /**
     * 列表接口
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $length = (int)$request->input('length', 10);
        $search = $this->_searchList($request);
        $data_list = $search->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {

            $result[] = array(
                'charge_pre_authorization_record_id' => $data->charge_pre_authorization_record_id, // 充电预授权记录ID
                'charge_pre_authorization_record_number' => $data->charge_pre_authorization_record_number, // 充电预授权记录编号
                'payment_device' => PaymentDeviceEnum::getDescription($data->payment_device), // 支付设备
                'charge_pre_authorization_status' => ChargePreAuthorizationStatus::getDescription($data->charge_pre_authorization_status), // 充电预授权状态
                'gmt_charge_pre_authorization_status' => $data->gmt_charge_pre_authorization_status, // 充电预授权状态时间
                'pre_authorization_amount' => __('common.unit_hk') . (float)bcdiv($data->pre_authorization_amount, 100, 1), // 预授权金额
                'deduct_amount' => __('common.unit_hk') . (float)bcdiv($data->deduct_amount, 100, 1), // 扣款金额
                'refund_amount' => filled($data->refund_amount) ? (__('common.unit_hk') . (float)bcdiv($data->refund_amount, 100, 1)) : '—/—', // 退款金额
                'actual_refund_amount' => filled($data->actual_refund_amount) ? (__('common.unit_hk') . (float)bcdiv($data->actual_refund_amount, 100, 1)) : '—/—', // 实际退款金额
                'identity_type' => IdentityType::getDescription($data->identity_type), // 身份类型
                'identity_number' => $data->identity_number, // 身份号码
                'connector_number' => $data->connector_number, // 充电枪编号
                'connector_name' => $data->connector_name, // 充电枪名称
                'kiosk_number' => $data->kiosk_number, // Kiosk编号
                'kiosk_name' => $data->kiosk_name, // Kiosk名称
                'octopus_transaction_id' => $data->octopus_transaction_id, // 八达通交易ID
                'octopus_receipt_number' => $data->octopus_receipt_number, // 八达通收据编号
                'gmt_octopus_deduct' => $data->gmt_octopus_deduct, // 八达通扣款时间
                'octopus_device_number' => $data->octopus_device_number, // 八达通设备编号
                'octopus_card_type' => $data->octopus_card_type, // 八达通卡类型
                'octopus_card_number' => $data->octopus_card_number, // 八达通卡号
                'octopus_raw_card_number' => $data->octopus_raw_card_number, // 八达通原始卡号
                'octopus_balance' => __('common.unit_hk') . (float)bcdiv($data->octopus_balance, 100, 1), // 八达通余额
                'octopus_last_added_value_type' => $data->octopus_last_added_value_type, // 八达通上次充值类型
                'octopus_last_added_value_date' => $data->octopus_last_added_value_date, // 八达通上次充值日期
                'is_admin_octopus_card' => $data->is_admin_octopus_card, // 是否为管理员八达通
                'is_free_octopus_card' => $data->is_free_octopus_card, // 是否为免费八达通
                'pos_vendor' => PosVendorEnum::getDescription($data->pos_vendor), // POS供应商
                'pos_company_id' => $data->pos_company_id, // POS公司ID
                'pos_pre_authorization_id' => $data->pos_pre_authorization_id, // POS预授权ID
                'pos_transaction_id' => $data->pos_transaction_id, // POS交易ID
                'pos_payment_method_name' => $data->pos_payment_method_name, // POS支付方式名称
                'pos_card_number' => $data->pos_card_number, // POS卡号
                'pos_trace_no' => $data->pos_trace_no, // POS追踪编号
                'pos_reference_id' => $data->pos_reference_id, // POS参考编号
                'gmt_pos_create' => $data->gmt_pos_create, // POS创建时间
                'gmt_pos_deduct' => $data->gmt_pos_deduct, // POS扣款时间
                'gmt_pos_cancel' => $data->gmt_pos_cancel, // POS取消时间
                'remark' => $data->remark, // 备注
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result
        );

        return response()->json($json);
    }

    /**
     * 视图页
     *
     * @param Request $request
     * @param $charge_pre_authorization_record_number
     * @return View|Application|Factory
     */
    public function view(Request $request, $charge_pre_authorization_record_number): View|Application|Factory
    {
        // dataTable字段
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'],
            self::getUrlParams($request)
        );

        // 获取充电预授权记录
        $charge_pre_authorization_record_result = ChargePreAuthorizationRecord::where('charge_pre_authorization_record_number', $charge_pre_authorization_record_number)
            ->firstOrFail();

        if (filled($charge_pre_authorization_record_result)) {
            // 拆分充电预授权记录编号
            $charge_pre_authorization_record_number_list = explode('-', $charge_pre_authorization_record_number);
            $charge_pre_authorization_record_number_end = end($charge_pre_authorization_record_number_list);

            // 获取支付方式
            switch ($charge_pre_authorization_record_result->payment_device) {
                case PaymentDeviceEnum::Pos:
                    $payment_method = $charge_pre_authorization_record_result->pos_payment_method_name;
                    $payment_method_image = existsImage('icon', 'payment_methods/' . strtolower($charge_pre_authorization_record_result->pos_payment_method_name) . '.png');
                    break;
                case PaymentDeviceEnum::Octopus:
                    $payment_method = $charge_pre_authorization_record_result->octopus_payment_method_name;
                    $payment_method_image = existsImage('icon', 'payment_methods/octopus.png');
                    break;
                default:
                    // 目前只有POS
                    $payment_method = null;
                    $payment_method_image = null;
                    break;
            }

            // 获取充电预授权记录
            $charge_pre_authorization_record_info = array(
                'charge_pre_authorization_record_id' => $charge_pre_authorization_record_result->charge_pre_authorization_record_id,
                'charge_pre_authorization_record_number' => $charge_pre_authorization_record_result->charge_pre_authorization_record_number,
                'payment_device' => PaymentDeviceEnum::getDescription($charge_pre_authorization_record_result->payment_device), // 支付设备
                'charge_pre_authorization_status' => ChargePreAuthorizationStatus::getDescription($charge_pre_authorization_record_result->charge_pre_authorization_status), // 充电预授权状态
                'charge_pre_authorization_status_enum' => $charge_pre_authorization_record_result->charge_pre_authorization_status, // 充电预授权状态枚举
                'gmt_charge_pre_authorization_status' => $charge_pre_authorization_record_result->gmt_charge_pre_authorization_status, // 充电预授权状态时间
                'pre_authorization_amount' => (float)bcdiv($charge_pre_authorization_record_result->pre_authorization_amount, 100, 1), // 预授权金额
                'deduct_amount' => (float)bcdiv($charge_pre_authorization_record_result->deduct_amount, 100, 1), // 扣款金额
                'refund_amount' => filled($charge_pre_authorization_record_result->refund_amount) ? ((float)bcdiv($charge_pre_authorization_record_result->refund_amount, 100, 1)) : '—/—', // 退款金额
                'actual_refund_amount' => filled($charge_pre_authorization_record_result->actual_refund_amount) ? ((float)bcdiv($charge_pre_authorization_record_result->actual_refund_amount, 100, 1)) : '—/—', // 实际退款金额
                'is_has_refund_amount' => filled($charge_pre_authorization_record_result->refund_amount) && $charge_pre_authorization_record_result->refund_amount > 0, // 是否有退款金额
                'is_has_actual_refund_amount' => filled($charge_pre_authorization_record_result->actual_refund_amount), // 是否有实际退款金额
                'is_refund_amount_equal_actual_refund_amount' => $charge_pre_authorization_record_result->refund_amount === $charge_pre_authorization_record_result->actual_refund_amount, // 退款金额是否等于实际退款金额
                'identity_type' => IdentityType::getDescription($charge_pre_authorization_record_result->identity_type), // 身份类型
                'identity_number' => $charge_pre_authorization_record_result->identity_number, // 身份号码
                'connector_number' => $charge_pre_authorization_record_result->connector_number, // 充电枪编号
                'connector_name' => $charge_pre_authorization_record_result->connector_name, // 充电枪名称
                'kiosk_number' => $charge_pre_authorization_record_result->kiosk_number, // Kiosk编号
                'kiosk_name' => $charge_pre_authorization_record_result->kiosk_name, // Kiosk名称
                'octopus_transaction_id' => $charge_pre_authorization_record_result->octopus_transaction_id, // 八达通交易ID
                'octopus_receipt_number' => $charge_pre_authorization_record_result->octopus_receipt_number, // 八达通收据编号
                'gmt_octopus_deduct' => $charge_pre_authorization_record_result->gmt_octopus_deduct, // 八达通扣款时间
                'octopus_device_number' => $charge_pre_authorization_record_result->octopus_device_number, // 八达通设备编号
                'octopus_card_type' => $charge_pre_authorization_record_result->octopus_card_type, // 八达通卡类型
                'octopus_card_number' => $charge_pre_authorization_record_result->octopus_card_number, // 八达通卡号
                'octopus_raw_card_number' => $charge_pre_authorization_record_result->octopus_raw_card_number, // 八达通原始卡号
                'octopus_balance' => __('common.unit_hk') . (float)bcdiv($charge_pre_authorization_record_result->octopus_balance, 100, 1), // 八达通余额
                'octopus_last_added_value_type' => $charge_pre_authorization_record_result->octopus_last_added_value_type, // 八达通上次充值类型
                'octopus_last_added_value_date' => $charge_pre_authorization_record_result->octopus_last_added_value_date, // 八达通上次充值日期
                'is_admin_octopus_card' => $charge_pre_authorization_record_result->is_admin_octopus_card, // 是否为管理员八达通
                'is_free_octopus_card' => $charge_pre_authorization_record_result->is_free_octopus_card, // 是否为免费八达通
                'pos_vendor' => PosVendorEnum::getDescription($charge_pre_authorization_record_result->pos_vendor), // POS供应商
                'pos_company_id' => $charge_pre_authorization_record_result->pos_company_id, // POS公司ID
                'pos_pre_authorization_id' => $charge_pre_authorization_record_result->pos_pre_authorization_id, // POS预授权ID
                'pos_transaction_id' => $charge_pre_authorization_record_result->pos_transaction_id, // POS交易ID
                'payment_method' => $payment_method, // 支付方式
                'payment_method_image' => ($payment_method_image ?: existsImage('icon', 'payment_methods/payment_method_placeholder.png')) ?: '', // 支付方式icon
                'pos_card_number' => $charge_pre_authorization_record_result->pos_card_number, // POS卡号
                'pos_trace_no' => $charge_pre_authorization_record_result->pos_trace_no, // POS追踪编号
                'pos_reference_id' => $charge_pre_authorization_record_result->pos_reference_id, // POS参考编号
                'gmt_pos_create' => $charge_pre_authorization_record_result->gmt_pos_create, // POS创建时间
                'gmt_pos_deduct' => $charge_pre_authorization_record_result->gmt_pos_deduct, // POS扣款时间
                'gmt_pos_cancel' => $charge_pre_authorization_record_result->gmt_pos_cancel, // POS取消时间
                'status_log' => $charge_pre_authorization_record_result->status_log, // 状态日志
                'remark' => $charge_pre_authorization_record_result->remark, // 备注
                'gmt_create' => $charge_pre_authorization_record_result->gmt_create->toDateTimeString(), // 创建时间
            );

            // 将status_log按照换行符或者回车符分割成数组
            $charge_pre_authorization_record_info['status_log_list'] = preg_split('/\r\n|\r|\n/', $charge_pre_authorization_record_result->status_log, -1, PREG_SPLIT_NO_EMPTY);

            // 卡片显示不同数据，PaymentMethod为POS就只显示POS相关的
            if ($charge_pre_authorization_record_result->payment_device === PaymentDeviceEnum::Pos) {
                $charge_pre_authorization_record_info['pos_data'] = array(
                    'pos_vendor' => PosVendorEnum::getDescription($charge_pre_authorization_record_result->pos_vendor), // POS供应商
                    'pos_company_id' => $charge_pre_authorization_record_result->pos_company_id ?? '—/—', // POS公司ID
                    'pos_pre_authorization_id' => $charge_pre_authorization_record_result->pos_pre_authorization_id ?? '—/—', // POS预授权ID
                    'pos_transaction_id' => $charge_pre_authorization_record_result->pos_transaction_id ?? '—/—', // POS交易ID
                    'pos_payment_method_name' => $charge_pre_authorization_record_result->pos_payment_method_name ?? '—/—', // POS支付方式名称
                    'pos_card_number' => $charge_pre_authorization_record_result->pos_card_number ?? '—/—', // POS卡号
                    'pos_trace_no' => $charge_pre_authorization_record_result->pos_trace_no ?? '—/—', // POS追踪编号
                    'pos_reference_id' => $charge_pre_authorization_record_result->pos_reference_id ?? '—/—', // POS参考编号
                    'gmt_pos_create' => $charge_pre_authorization_record_result->gmt_pos_create ?? '—/—', // POS创建时间
                    'gmt_pos_deduct' => $charge_pre_authorization_record_result->gmt_pos_deduct ?? '—/—', // POS扣款时间
                    'gmt_pos_cancel' => $charge_pre_authorization_record_result->gmt_pos_cancel ?? '—/—', // POS取消时间
                );
            } elseif ($charge_pre_authorization_record_result->payment_device === PaymentDeviceEnum::Octopus) {
                $charge_pre_authorization_record_info['octopus_data'] = array(
                    'octopus_transaction_id' => $charge_pre_authorization_record_result->octopus_transaction_id ?? '—/—', // 八达通交易ID
                    'octopus_receipt_number' => $charge_pre_authorization_record_result->octopus_receipt_number ?? '—/—', // 八达通收据编号
                    'gmt_octopus_deduct' => $charge_pre_authorization_record_result->gmt_octopus_deduct ?? '—/—', // 八达通扣款时间
                    'octopus_device_number' => $charge_pre_authorization_record_result->octopus_device_number ?? '—/—', // 八达通设备编号
                    'octopus_card_type' => $charge_pre_authorization_record_result->octopus_card_type ?? '—/—', // 八达通卡类型
                    'octopus_card_number' => $charge_pre_authorization_record_result->octopus_card_number ?? '—/—', // 八达通卡号
                    'octopus_raw_card_number' => $charge_pre_authorization_record_result->octopus_raw_card_number ?? '—/—', // 八达通原始卡号
                    'octopus_balance' => __('common.unit_hk') . (float)bcdiv($charge_pre_authorization_record_result->octopus_balance, 100, 1) ?? '—/—', // 八达通余额
                    'octopus_last_added_value_type' => $charge_pre_authorization_record_result->octopus_last_added_value_type ?? '—/—', // 八达通上次充值类型
                    'octopus_last_added_value_date' => $charge_pre_authorization_record_result->octopus_last_added_value_date ?? '—/—', // 八达通上次充值日期
                    'is_admin_octopus_card' => $charge_pre_authorization_record_result->is_admin_octopus_card ?? '—/—', // 是否为管理员八达通
                    'is_free_octopus_card' => $charge_pre_authorization_record_result->is_free_octopus_card ?? '—/—', // 是否为免费八达通
                );
            }

            // 查询关联的充电支付记录
            $charge_payment_record_result = ChargePaymentRecord::with('chargeRecord')
                ->where('charge_pre_authorization_record_number', $charge_pre_authorization_record_number)
                ->orderBy('gmt_create', 'desc')
                ->first();
            $charge_payment_record_info = null;
            if (filled($charge_payment_record_result)) {
                switch ($charge_payment_record_result->payment_device) {
                    case PaymentDeviceEnum::Pos:
                        $payment_method = $charge_payment_record_result->pos_payment_method_name ?: PaymentMethod::getDescription($charge_payment_record_result->payment_method);
                        $payment_method_image = existsImage('icon', 'payment_methods/' . strtolower($charge_payment_record_result->pos_payment_method_name ?: $charge_payment_record_result->payment_method) . '.png');
                        break;
                    case PaymentDeviceEnum::OnlinePayment:
                        $payment_method = PaymentDeviceEnum::getDescription($charge_payment_record_result->payment_device);
                        $payment_method_image = existsImage('icon', 'payment_methods/' . strtolower($charge_payment_record_result->payment_method) . '.png');
                        break;
                    default:
                        $payment_method = PaymentMethod::getDescription($charge_payment_record_result->payment_method);
                        $payment_method_image = existsImage('icon', 'payment_methods/' . strtolower($charge_payment_record_result->payment_method) . '.png');
                        break;
                }

                // 获取支付记录
                $charge_payment_record_info = array(
                    'charge_payment_record_id' => $charge_payment_record_result->charge_payment_record_id,
                    'charge_payment_record_number' => $charge_payment_record_result->charge_payment_record_number,
                    'charge_record_number' => $charge_payment_record_result->charge_record_number, // 充电记录编号
                    'payment_method' => $payment_method, // 支付方式
                    'payment_method_image' => ($payment_method_image ?: existsImage('icon', 'payment_methods/payment_method_placeholder.png')) ?: '', // 支付方式icon
                    'charge_value_amount' => (float)bcdiv($charge_payment_record_result->charge_value_amount, 100, 1), // 充电量金额
                    'idling_penalty_amount' => (float)bcdiv($charge_payment_record_result->idling_penalty_amount, 100, 1), // 闲置罚款金额
                    'total_amount' => (float)bcdiv($charge_payment_record_result->total_amount, 100, 1), // 总金额
                    'actual_payment_amount' => (float)bcdiv($charge_payment_record_result->actual_payment_amount, 100, 1), // 实际付款金额
                    'is_total_amount_equal_actual_payment_amount' => $charge_payment_record_result->total_amount === $charge_payment_record_result->actual_payment_amount, // 总金额是否等于实际付款金额
                    'octopus_device_number' => $charge_payment_record_result->octopus_device_number ?? '—/—', // 八达通设备编号
                    'octopus_card_number' => $charge_payment_record_result->octopus_card_number ?? '—/—', // 八达通卡号
                    'octopus_raw_card_number' => $charge_payment_record_result->octopus_raw_card_number ?? '—/—', // 八达通原始卡号
                    'octopus_card_type' => $charge_payment_record_result->octopus_card_type ?? '—/—', // 八达通卡类型
                    'octopus_balance' => __('common.unit_hk') . (float)bcdiv($charge_payment_record_result->octopus_balance, 100, 1) ?? '—/—', // 八达通余额
                    'octopus_last_added_value_type' => $charge_payment_record_result->octopus_last_added_value_type ?? '—/—', // 八达通上次充值类型
                    'octopus_last_added_value_date' => $charge_payment_record_result->octopus_last_added_value_date ?? '—/—', // 八达通上次充值日期
                    'pos_receipt_number' => $charge_payment_record_result->pos_receipt_number ?? '—/—', // POS收据编号
                    'gmt_pos_deduct' => $charge_payment_record_result->gmt_pos_deduct ?? '—/—', // POS扣款时间
                    'pos_transaction_id' => $charge_payment_record_result->pos_transaction_id ?? '—/—', // POS交易ID
                    'pos_payment_method_name' => $charge_payment_record_result->pos_payment_method_name ?? '—/—', // POS支付方式名称
                    'pos_card_number' => $charge_payment_record_result->pos_card_number ?? '—/—', // POS卡号
                    'pos_trace_no' => $charge_payment_record_result->pos_trace_no ?? '—/—', // POS追踪编号
                    'pos_reference_id' => $charge_payment_record_result->pos_reference_id ?? '—/—', // POS参考编号
                    'charge_pre_authorization_record_number' => $charge_payment_record_result->charge_pre_authorization_record_number ?? '—/—', // 充电预授权记录编号
                    'charge_value_type' => ChargeValueType::getDescription($charge_payment_record_result->chargeRecord?->charge_value_type), // 充电量类型描述
                    'charge_value_type_value' => $charge_payment_record_result->chargeRecord?->charge_value_type, // 充电量类型值
                    'tariff_table_type' => $charge_payment_record_result->chargeRecord?->tariff_table_type, // 收费表类型值
                    'charge_tariff_scheme' => $charge_payment_record_result->chargeRecord?->charge_tariff_scheme, // 充电收费方案
                    'is_idling_penalty_only' => $charge_payment_record_result->is_idling_penalty_only, // 是否仅闲置罚款
                    'is_charge_arrears' => $charge_payment_record_result->is_charge_arrears, // 是否充电欠款
                    'gmt_create' => $charge_payment_record_result->gmt_create->toDateTimeString(), // 创建时间
                    'charge_payment_record_view_url' => action(
                        [ChargePaymentRecordController::class, 'view'],
                        [
                            'id' => $charge_payment_record_result->charge_payment_record_number,
                        ]
                    ),
                );
                switch ($charge_payment_record_result->chargeRecord?->charge_value_type) {
                    case ChargeValueType::Time:
                        $charge_payment_record_info['charged_time'] = floor($charge_payment_record_result->charge_value / 60) . __('common.unit_mins'); // 已充时间
                        if ($charge_payment_record_result->use_remain_charge_value > 0) $charge_payment_record_info['use_remain_charge_value'] = floor($charge_payment_record_result->use_remain_charge_value / 60) . __('common.unit_mins'); // 使用剩余充电量
                        break;
                    case ChargeValueType::Energy:
                        $charge_payment_record_info['charged_energy'] = (float)bcdiv($charge_payment_record_result->charge_value, 1000, 3) . __('common.unit_kwh'); // 已充电量
                        if ($charge_payment_record_result->use_remain_charge_value > 0) $charge_payment_record_info['use_remain_charge_value'] = (float)bcdiv($charge_payment_record_result->use_remain_charge_value, 1000, 3) . __('common.unit_kwh'); // 使用剩余充电量
                        break;
                    default:
                        break;
                }

                // 卡片显示不同数据，PaymentMethod为OCTOPUS就只显示OCTOPUS相关的，如果是其他的就显示Pos Card
                if ($charge_payment_record_result->payment_device === PaymentDeviceEnum::Octopus) {
                    $charge_payment_record_info['octopus_data'] = array(
                        'octopus_device_number' => $charge_payment_record_result->octopus_device_number ?? '—/—', // 八达通设备编号
                        'octopus_card_number' => $charge_payment_record_result->octopus_card_number ?? '—/—', // 八达通卡号
                        'octopus_raw_card_number' => $charge_payment_record_result->octopus_raw_card_number ?? '—/—', // 八达通原始卡号
                        'octopus_card_type' => $charge_payment_record_result->octopus_card_type ?? '—/—', // 八达通卡类型
                        'octopus_balance' => filled($charge_payment_record_result->octopus_balance) ? __('common.unit_hk') . (float)bcdiv($charge_payment_record_result->octopus_balance, 100, 1) : '—/—', // 八达通余额
                        'is_admin_octopus_card' => $charge_payment_record_result->is_admin_octopus_card, // 是否为管理员八达通
                        'is_free_octopus_card' => $charge_payment_record_result->is_free_octopus_card, // 是否为免费八达通卡
                        'octopus_last_added_value_type' => $charge_payment_record_result->octopus_last_added_value_type ?? '—/—', // 八达通上次充值类型
                        'octopus_last_added_value_date' => filled($charge_payment_record_result->octopus_last_added_value_date) ? date('Y-m-d', strtotime($charge_payment_record_result->octopus_last_added_value_date)) : '—/—', // 八达通上次充值日期
                    );
                } else if ($charge_payment_record_result->payment_device === PaymentDeviceEnum::Pos) {
                    $charge_payment_record_info['pos_data'] = array(
                        'pos_receipt_number' => $charge_payment_record_result->pos_receipt_number ?? '—/—', // POS收据编号
                        'gmt_pos_deduct' => $charge_payment_record_result->gmt_pos_deduct ?? '—/—', // POS扣款时间
                        'pos_transaction_id' => $charge_payment_record_result->pos_transaction_id ?? '—/—', // POS交易ID
                        'pos_payment_method_name' => $charge_payment_record_result->pos_payment_method_name ?? '—/—', // POS支付方式名称
                        'pos_card_number' => $charge_payment_record_result->pos_card_number ?? '—/—', // POS卡号
                        'pos_trace_no' => $charge_payment_record_result->pos_trace_no ?? '—/—', // POS追踪编号
                        'pos_reference_id' => $charge_payment_record_result->pos_reference_id ?? '—/—', // POS参考编号
                        'charge_pre_authorization_record_number' => $charge_payment_record_result->charge_pre_authorization_record_number ?? '—/—', // 充电预授权记录编号
                    );
                } else if ($charge_payment_record_result->payment_device === PaymentDeviceEnum::OnlinePayment) {
                    $charge_payment_record_info['online_payment_data'] = array(
                        'actual_payment_amount' => __('common.unit_hk') . (filled($charge_payment_record_result->actual_payment_amount) ? (float)bcdiv($charge_payment_record_result->actual_payment_amount, 100, 1) : 0), // 实际付款金额
                        'use_points' => (filled($charge_payment_record_result->use_points) ? (float)bcdiv($charge_payment_record_result->use_points, 100, 1) : 0) . __('common.unit_points'), // 使用积分
                        'points_transaction_id' => $charge_payment_record_result->points_transaction_id ?? '—/—', // 积分交易ID
                    );
                }

            }

            // 查询关联的预授权退款记录
            $charge_pre_authorization_refund_record_result_list = ChargePreAuthorizationRefundRecord::where('charge_pre_authorization_record_number', $charge_pre_authorization_record_number)
                ->orderBy('gmt_create', 'desc')
                ->get();
            $charge_pre_authorization_refund_record_result_info_list = [];
            foreach($charge_pre_authorization_refund_record_result_list as $charge_pre_authorization_refund_record_result) {
                // 目前只有八达通
                switch ($charge_pre_authorization_refund_record_result->payment_device) {
                    case PaymentDeviceEnum::Octopus:
                    default:
                        $payment_device = PaymentDeviceEnum::getDescription($charge_pre_authorization_refund_record_result->payment_device);
                        $payment_device_image = existsImage('icon', 'payment_methods/' . strtolower($charge_pre_authorization_refund_record_result->payment_device) . '.png');
                        break;
                }

                // 获取支付记录
                $charge_pre_authorization_refund_record_result_info = array(
                    'charge_pre_authorization_refund_record_id' => $charge_pre_authorization_refund_record_result->charge_pre_authorization_refund_record_id,
                    'charge_pre_authorization_refund_record_number' => $charge_pre_authorization_refund_record_result->charge_pre_authorization_refund_record_number,
                    'charge_pre_authorization_record_number' => $charge_pre_authorization_refund_record_result->charge_pre_authorization_record_number, // 充电记录编号
                    'payment_device' => $payment_device, // 支付方式
                    'payment_device_image' => ($payment_device_image ?: existsImage('icon', 'payment_methods/payment_method_placeholder.png')) ?: '', // 支付方式icon
                    'refund_amount' => (float)bcdiv($charge_pre_authorization_refund_record_result->refund_amount, 100, 1), // 退款金额
                    'actual_refund_amount' => (float)bcdiv($charge_pre_authorization_refund_record_result->actual_refund_amount, 100, 1), // 实际退款金额
                    'is_refund_amount_equal_actual_refund_amount' => $charge_pre_authorization_refund_record_result->refund_amount === $charge_pre_authorization_refund_record_result->actual_refund_amount, // 退款金额是否等于实际退款金额
                    'identity_type' => IdentityType::getDescription($charge_pre_authorization_refund_record_result->identity_type), // 身份类型
                    'identity_number' => $charge_pre_authorization_refund_record_result->identity_number, // 身份号码
                    'kiosk_name' => $charge_pre_authorization_refund_record_result->kiosk_name, // Kiosk名称
                    'gmt_create' => $charge_pre_authorization_refund_record_result->gmt_create->toDateTimeString(), // 创建时间
                    'charge_pre_authorization_refund_record_view_url' => action(
                        [ChargePreAuthorizationRefundRecordController::class, 'view'],
                        [
                            'id' => $charge_pre_authorization_refund_record_result->charge_pre_authorization_refund_record_number,
                        ]
                    ),
                );

                // 卡片显示不同数据，PaymentMethod为OCTOPUS就只显示OCTOPUS相关的，目前只有八达通
                if ($charge_pre_authorization_refund_record_result->payment_device === PaymentDeviceEnum::Octopus) {
                    $charge_pre_authorization_refund_record_result_info['octopus_data'] = [
                        'octopus_transaction_id' => $charge_pre_authorization_refund_record_result->octopus_transaction_id, // 八达通交易ID
                        'octopus_receipt_number' => $charge_pre_authorization_refund_record_result->octopus_receipt_number, // 八达通收据编号
                        'gmt_octopus_refund' => $charge_pre_authorization_refund_record_result->gmt_octopus_refund, // 八达通退款时间
                        'octopus_fund_type' => $charge_pre_authorization_refund_record_result->octopus_fund_type ?? '—/—', // 八达通退款类型
                        'octopus_device_number' => $charge_pre_authorization_refund_record_result->octopus_device_number ?? '—/—', // 八达通设备编号
                        'octopus_card_type' => $charge_pre_authorization_refund_record_result->octopus_card_type ?? '—/—', // 八达通卡类型
                        'octopus_card_number' => $charge_pre_authorization_refund_record_result->octopus_card_number ?? '—/—', // 八达通卡号
                        'octopus_raw_card_number' => $charge_pre_authorization_refund_record_result->octopus_raw_card_number ?? '—/—', // 八达通原始卡号
                        'octopus_balance' => __('common.unit_hk') . (float)bcdiv($charge_pre_authorization_refund_record_result->octopus_balance, 100, 1) ?? '—/—', // 八达通余额
                        'octopus_last_added_value_type' => $charge_pre_authorization_refund_record_result->octopus_last_added_value_type ?? '—/—', // 八达通上次充值类型
                        'octopus_last_added_value_date' => $charge_pre_authorization_refund_record_result->octopus_last_added_value_date ?? '—/—', // 八达通上次充值日期
                    ];
                }

                $charge_pre_authorization_refund_record_result_info['charge_pre_authorization_record_number'] = $charge_pre_authorization_record_number_end;
                // 获取充电预授权退款记录编号
                $charge_pre_authorization_refund_record_number = $charge_pre_authorization_refund_record_result->charge_pre_authorization_refund_record_number;
                // 拆分充电预授权退款记录编号
                $charge_pre_authorization_refund_record_number_list = explode('-', $charge_pre_authorization_refund_record_number);
                $charge_pre_authorization_refund_record_number = end($charge_pre_authorization_refund_record_number_list);
                $charge_pre_authorization_refund_record_result_info['charge_pre_authorization_refund_record_number'] = $charge_pre_authorization_refund_record_number;

                $charge_pre_authorization_refund_record_result_info_list[] = $charge_pre_authorization_refund_record_result_info;

            }


            $charge_pre_authorization_record_info['charge_pre_authorization_record_number'] = $charge_pre_authorization_record_number_end;
            $charge_pre_authorization_record_info['charge_payment_record_info'] = $charge_payment_record_info;
            $charge_pre_authorization_record_info['charge_pre_authorization_refund_record_info_list'] = $charge_pre_authorization_refund_record_result_info_list;
            $data['data'] = $charge_pre_authorization_record_info;
        }

        return view("pages.{$data['module_name']}.view", $data);
    }

    /**
     * 导出
     *
     * @param Request $request
     * @return void
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function excelExport(Request $request): void
    {
        ini_set('memory_limit', '-1');
        // 获取数据
        $search = $this->_searchList($request);
        $data_list = $search->get();
        $module_name = self::$module_name;

        $result = array();
        foreach ($data_list as $data) {
            $result[] = array(
                'charge_pre_authorization_record_id' => $data->charge_pre_authorization_record_id, // 充电预授权记录ID
                'charge_pre_authorization_record_number' => $data->charge_pre_authorization_record_number, // 充电预授权记录编号
                'payment_device' => PaymentDeviceEnum::getDescription($data->payment_device), // 付款设备
                'charge_pre_authorization_status' => ChargePreAuthorizationStatus::getDescription($data->charge_pre_authorization_status), // 充电预授权状态
                'gmt_charge_pre_authorization_status' => $data->gmt_charge_pre_authorization_status, // 充电预授权状态时间
                'pre_authorization_amount' => bcdiv($data->pre_authorization_amount, 100, 1), // 预授权金额
                'deduct_amount' => bcdiv($data->deduct_amount, 100, 1), // 扣款金额
                'refund_amount' => filled($data->refund_amount) ? bcdiv($data->refund_amount, 100, 1) : '', // 退款金额
                'actual_refund_amount' => filled($data->actual_refund_amount) ? bcdiv($data->actual_refund_amount, 100, 1) : '', // 实际退款金额
                'identity_type' => IdentityType::getDescription($data->identity_type), // 身份类型
                'identity_number' => $data->identity_number, // 身份号码
                'connector_name' => $data->connector_name, // 充电枪名称
                'kiosk_name' => $data->kiosk_name, // Kiosk名称
                'octopus_transaction_id' => $data->octopus_transaction_id, // 八达通交易ID
                'octopus_receipt_number' => $data->octopus_receipt_number, // 八达通收据编号
                'gmt_octopus_deduct' => $data->gmt_octopus_deduct, // 八达通扣款时间
                'octopus_device_number' => $data->octopus_device_number, // 八达通设备编号
                'octopus_card_type' => (string)$data->octopus_card_type, // 八达通卡类型
                'octopus_card_number' => $data->octopus_card_number, // 八达通卡号
                'octopus_raw_card_number' => $data->octopus_raw_card_number, // 八达通原始卡号
                'octopus_balance' => bcdiv($data->octopus_balance, 100, 1), // 八达通余额
                'octopus_last_added_value_type' => (string)$data->octopus_last_added_value_type, // 八达通上次充值类型
                'octopus_last_added_value_date' => $data->octopus_last_added_value_date, // 八达通上次充值日期
                'is_admin_octopus_card' => $data->is_admin_octopus_card, // 是否为管理员八达通
                'is_free_octopus_card' => $data->is_free_octopus_card, // 是否为免费八达通
                'pos_vendor' => PosVendorEnum::getDescription($data->pos_vendor), // POS供应商
                'pos_company_id' => $data->pos_company_id, // POS公司ID
                'pos_pre_authorization_id' => $data->pos_pre_authorization_id, // POS预授权ID
                'pos_transaction_id' => $data->pos_transaction_id, // POS交易ID
                'pos_payment_method_name' => $data->pos_payment_method_name, // POS支付方式名称
                'pos_card_number' => $data->pos_card_number, // POS卡号
                'pos_trace_no' => $data->pos_trace_no, // POS追踪编号
                'pos_reference_id' => $data->pos_reference_id, // POS参考编号
                'gmt_pos_create' => $data->gmt_pos_create, // POS创建时间
                'gmt_pos_deduct' => $data->gmt_pos_deduct, // POS扣款时间
                'gmt_pos_cancel' => $data->gmt_pos_cancel, // POS取消时间
                'remark' => $data->remark, // 备注
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
            );
        }

        // 先把语言赋值才可以在后面array出来表头，不然会输出未编译前的字符串
        $charge_pre_authorization_record_number = __("$module_name.charge_pre_authorization_record_number");
        $payment_device = __("$module_name.payment_device");
        $charge_pre_authorization_status = __("$module_name.charge_pre_authorization_status");
        $gmt_charge_pre_authorization_status = __("$module_name.gmt_charge_pre_authorization_status");
        $pre_authorization_amount = __("$module_name.pre_authorization_amount") . ' ( ' . __('common.unit_hk_blank') . ' )';
        $deduct_amount = __("$module_name.deduct_amount") . ' ( ' . __('common.unit_hk_blank') . ' )';
        $refund_amount = __("$module_name.refund_amount") . ' ( ' . __('common.unit_hk_blank') . ' )';
        $actual_refund_amount = __("$module_name.actual_refund_amount") . ' ( ' . __('common.unit_hk_blank') . ' )';
        $identity_type = __("$module_name.identity_type");
        $identity_number = __("$module_name.identity_number");
        $connector_name = __("$module_name.connector_name");
        $kiosk_name = __("$module_name.kiosk_name");
        $octopus_transaction_id = __("$module_name.octopus_transaction_id");
        $octopus_receipt_number = __("$module_name.octopus_receipt_number");
        $gmt_octopus_deduct = __("$module_name.gmt_octopus_deduct");
        $octopus_device_number = __("$module_name.octopus_device_number");
        $octopus_card_type = __("$module_name.octopus_card_type");
        $octopus_card_number = __("$module_name.octopus_card_number");
        $octopus_raw_card_number = __("$module_name.octopus_raw_card_number");
        $octopus_balance = __("$module_name.octopus_balance"). ' ( ' . __('common.unit_hk_blank') . ' )';
        $octopus_last_added_value_type = __("$module_name.octopus_last_added_value_type");
        $octopus_last_added_value_date = __("$module_name.octopus_last_added_value_date");
        $is_admin_octopus_card = __("$module_name.is_admin_octopus_card");
        $is_free_octopus_card = __("$module_name.is_free_octopus_card");
        $pos_vendor = __("$module_name.pos_vendor");
        $pos_company_id = __("$module_name.pos_company_id");
        $pos_pre_authorization_id = __("$module_name.pos_pre_authorization_id");
        $pos_transaction_id = __("$module_name.pos_transaction_id");
        $pos_payment_method_name = __("$module_name.pos_payment_method_name");
        $pos_card_number = __("$module_name.pos_card_number");
        $pos_trace_no = __("$module_name.pos_trace_no");
        $pos_reference_id = __("$module_name.pos_reference_id");
        $gmt_pos_create = __("$module_name.gmt_pos_create");
        $gmt_pos_deduct = __("$module_name.gmt_pos_deduct");
        $gmt_pos_cancel = __("$module_name.gmt_pos_cancel");
        $remark = __("$module_name.remark");
        $gmt_create = __("$module_name.gmt_create");

        $web_title = __("$module_name.web_title");

        $remove_column_list = [];
        switch (env('CURRENT_PROJECT_NAME')) {
            default:
                // 需要移除的列
                $remove_column_list = [

                ];
                break;
        }

        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet(); //实例化 Spreadsheet 对象
        $worksheet = $spreadsheet->getActiveSheet();

        // 工作簿名称为 "Usage Report"
        $worksheet->setTitle($web_title);

        $styleArray = [
            'font' => [
                'bold' => false
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
        ];

        // 计算数组长度用于动态补充表格样式
        $len = count($result);
        // 因为前面两格表头样式占了一行，所以要加1用于设置单元格样式
        $total_rows = $len + 1;
        $styleArrayBody = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['argb' => '666666'],
                ],
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
        ];

        // 指定表头字段
        $excel_data = array(
            array(
                $charge_pre_authorization_record_number,
                $payment_device,
                $charge_pre_authorization_status,
                $gmt_charge_pre_authorization_status,
                $pre_authorization_amount,
                $deduct_amount,
                $refund_amount,
                $actual_refund_amount,
                $identity_type,
                $identity_number,
                $connector_name,
                $kiosk_name,
                $octopus_transaction_id,
                $octopus_receipt_number,
                $gmt_octopus_deduct,
                $octopus_device_number,
                $octopus_card_type,
                $octopus_card_number,
                $octopus_raw_card_number,
                $octopus_balance,
                $octopus_last_added_value_type,
                $octopus_last_added_value_date,
                $is_admin_octopus_card,
                $is_free_octopus_card,
                $pos_vendor,
                $pos_company_id,
                $pos_pre_authorization_id,
                $pos_transaction_id,
                $pos_payment_method_name,
                $pos_card_number,
                $pos_trace_no,
                $pos_reference_id,
                $gmt_pos_create,
                $gmt_pos_deduct,
                $gmt_pos_cancel,
                $remark,
                $gmt_create,
            )
        );

        // 移除指定列
        if (filled($remove_column_list)) $excel_data[0] = array_diff($excel_data[0], $remove_column_list);
        //设置单元格样式
        // 将数组长度转换为 Excel 列名
        $last_column = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex(count($excel_data[0]));
        $worksheet->getStyle("A1:{$last_column}$total_rows")->applyFromArray($styleArray)->getFont()->setName('Times New Roman')->setSize(11);
        $worksheet->getStyle("A1:{$last_column}1")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('c0c0c0');
        $worksheet->getStyle("A1:{$last_column}$total_rows")->applyFromArray($styleArrayBody);

        foreach ($result as $report) {
            $excel_row = array(
                $charge_pre_authorization_record_number => $report['charge_pre_authorization_record_number'],
                $payment_device => $report['payment_device'],
                $charge_pre_authorization_status => $report['charge_pre_authorization_status'],
                $gmt_charge_pre_authorization_status => $report['gmt_charge_pre_authorization_status'],
                $pre_authorization_amount => $report['pre_authorization_amount'],
                $deduct_amount => $report['deduct_amount'],
                $refund_amount => $report['refund_amount'],
                $actual_refund_amount => $report['actual_refund_amount'],
                $identity_type => $report['identity_type'],
                $identity_number => $report['identity_number'],
                $connector_name => $report['connector_name'],
                $kiosk_name => $report['kiosk_name'],
                $octopus_transaction_id => $report['octopus_transaction_id'],
                $octopus_receipt_number => $report['octopus_receipt_number'],
                $gmt_octopus_deduct => $report['gmt_octopus_deduct'],
                $octopus_device_number => $report['octopus_device_number'],
                $octopus_card_type => $report['octopus_card_type'],
                $octopus_card_number => $report['octopus_card_number'],
                $octopus_raw_card_number => $report['octopus_raw_card_number'],
                $octopus_balance => $report['octopus_balance'],
                $octopus_last_added_value_type => $report['octopus_last_added_value_type'],
                $octopus_last_added_value_date => $report['octopus_last_added_value_date'],
                $is_admin_octopus_card => $report['is_admin_octopus_card'] ? '✔' : '✘',
                $is_free_octopus_card => $report['is_free_octopus_card'] ? '✔' : '✘',
                $pos_vendor => $report['pos_vendor'],
                $pos_company_id => $report['pos_company_id'],
                $pos_pre_authorization_id => $report['pos_pre_authorization_id'],
                $pos_transaction_id => $report['pos_transaction_id'],
                $pos_payment_method_name => $report['pos_payment_method_name'],
                $pos_card_number => $report['pos_card_number'],
                $pos_trace_no => $report['pos_trace_no'],
                $pos_reference_id => $report['pos_reference_id'],
                $gmt_pos_create => $report['gmt_pos_create'],
                $gmt_pos_deduct => $report['gmt_pos_deduct'],
                $gmt_pos_cancel => $report['gmt_pos_cancel'],
                $remark => $report['remark'],
                $gmt_create => $report['gmt_create'],
            );
            // 移除指定列
            if (filled($remove_column_list)) {
                foreach ($remove_column_list as $remove_column) {
                    unset($excel_row[$remove_column]);
                }
            }
            $excel_data[] = $excel_row;
        }
        // dd($excel_data);

        $worksheet->fromArray($excel_data);
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $file_name = $web_title . ' ' . date('Y-m-d H:i:s') . '.xlsx';
        //设置单元格的宽度
        foreach ($spreadsheet->getActiveSheet()->getColumnIterator() as $column) {
            $spreadsheet->getActiveSheet()->getColumnDimension($column->getColumnIndex())->setAutoSize(true);
        }

        // 客户端文件下载
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $file_name . '"');
        header('Cache-Control: max-age=0');
        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
        exit;
    }

    /**
     * 搜索数据
     *
     * @param Request $request
     * @return mixed
     */
    protected function _searchList(Request $request): mixed
    {
        // dataTable字段
        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $charge_pre_authorization_record_number = $request->input('charge_pre_authorization_record_number_search');
        $charge_pre_authorization_status = $request->input('charge_pre_authorization_status_search');
        $gmt_create = $request->input('gmt_create_search');

        $gmt_create_search_list = self::getRangeDateTimeArray($gmt_create ?: '') ?: null;

        $charge_pre_authorization_record = ChargePreAuthorizationRecord::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
            ->when(filled($charge_pre_authorization_record_number), fn ($query) => $query->where('charge_pre_authorization_record_number', 'like', "%{$charge_pre_authorization_record_number}%"))
            ->when(filled($charge_pre_authorization_status), function ($query) use ($charge_pre_authorization_status) {
                if (is_string($charge_pre_authorization_status) && filled($charge_pre_authorization_status_list = explode(',', $charge_pre_authorization_status))) {
                    $charge_pre_authorization_status = $charge_pre_authorization_status_list;
                }
                $query->whereIn('charge_pre_authorization_status', $charge_pre_authorization_status);
            })
            ->when(filled($gmt_create_search_list), fn ($query) => $query->whereBetween('gmt_create', $gmt_create_search_list))
            ->orderBy($order, $sort)
            ->latest('gmt_create');

        return $charge_pre_authorization_record;
    }

    /**
     * 返回地址栏参数
     *
     * @param Request $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(Request $request): array
    {
        return array(
            'is_page' => 'true',
            'charge_pre_authorization_record_number_search' => $request->get('charge_pre_authorization_record_number_search'),
            'charge_pre_authorization_status_search' => $request->get('charge_pre_authorization_status_search'),
            'gmt_create_search' => $request->get('gmt_create_search'),
        );
    }

    public function modifyStatus(Request $request): JsonResponse
    {
        $charge_pre_authorization_record_number = $request->input('charge_pre_authorization_record_number');
        $reason = $request->input('reason');

        if (blank($charge_pre_authorization_record_number)) {
            $this->missingField('charge_pre_authorization_record_number');
            return $this->returnJson();
        }

        $charge_pre_authorization_record = ChargePreAuthorizationRecord::firstWhere('charge_pre_authorization_record_number', $charge_pre_authorization_record_number);
        if (blank($charge_pre_authorization_record)) {
            $this->notFoundData('charge_pre_authorization_record');
            return $this->returnJson();
        }

        // 判断是否与viosk关联
        if (filled($charge_pre_authorization_record?->connector?->chargePoint?->viosk)) {
            $viosk_number = $charge_pre_authorization_record?->connector?->chargePoint?->viosk?->viosk_number;
            // 如果有关联，不修改状态，获取Redis中的值，追加到数组最后，去重，重新存Redis
            $redis_need_deduct_charge_pre_authorization_record_number_list = Redis::get('need_deduct_charge_pre_authorization_record_number_list:' . $viosk_number);
            $need_deduct_charge_pre_authorization_record_number_list = null;
            if (filled($redis_need_deduct_charge_pre_authorization_record_number_list)) {
                $need_deduct_charge_pre_authorization_record_number_list = json_decode($redis_need_deduct_charge_pre_authorization_record_number_list, true) ?: null;
            }
            // 追加到数组最后，去重，重新存Redis
            $need_deduct_charge_pre_authorization_record_number_list[] = $charge_pre_authorization_record_number;
            $need_deduct_charge_pre_authorization_record_number_list = array_unique($need_deduct_charge_pre_authorization_record_number_list);
            Redis::set('need_deduct_charge_pre_authorization_record_number_list:' . $viosk_number, json_encode($need_deduct_charge_pre_authorization_record_number_list));
            return $this->returnJson();
        }

        if (filled($charge_pre_authorization_record->status_log)) {
            $charge_pre_authorization_record->status_log .= PHP_EOL;
        }
        $current_date_time = now()->format('Y-m-d H:i:s');
        $charge_pre_authorization_record->status_log .= "$current_date_time - by: cms, status: [{$charge_pre_authorization_record->charge_pre_authorization_status}] -> [" . ChargePreAuthorizationStatus::Processing . "], reason: $reason";

        $charge_pre_authorization_record->charge_pre_authorization_status = ChargePreAuthorizationStatus::Processing;
        $charge_pre_authorization_record->gmt_charge_pre_authorization_status = now();
        $result = $charge_pre_authorization_record->save();

        if (!$result) {
            $this->modelSaveFail();
            return $this->returnJson();
        }

        return $this->returnJson();
    }
}

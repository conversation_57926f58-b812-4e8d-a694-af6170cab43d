<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Contracts\View\{
    Factory,
    View
};
use Illuminate\Foundation\Application;
use Illuminate\Http\{
    JsonResponse,
    Request
};
use App\Enums\{
    TransactionType,
    TransactionCategory,
};
use App\Models\Modules\{
    PointsTransaction,
};

class PointsTransactionController extends CommonController
{

    protected static string $module_name = 'pointsTransaction'; // 模块名称
    protected PointsTransaction $model;

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new PointsTransaction;
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'nickname_search' => $request->nickname_search,
            'transaction_number_search' => $request->transaction_number_search,
            'transaction_type_search' => $request->transaction_type_search,
            'transaction_category_search' => $request->transaction_category_search,
        );
        $data['transaction_type_list'] = $data['transaction_category_list'] = array();

        foreach (TransactionType::asSelectArray() as $value => $name) {
            $data['transaction_type_list'][] = array(
                'name' => $name,
                'value' => $value,
            );
        }

        foreach (TransactionCategory::asSelectArray() as $value => $name) {
            $data['transaction_category_list'][] = array(
                'name' => $name,
                'value' => $value,
            );
        }
        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        $draw = (int)$request->input('draw', 1);
        $length = (int)$request->input('length', 10);
        $search = $this->_searchList($request);
        $data_list = $search->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $result[] = array(
                'points_transaction_id' => $data->points_transaction_id, // 积分交易记录ID
                'user_id' => $data->user_id, // 用户ID
                'nickname' => $data->nickname ?? $data->user_id, // 用户昵称
                'avatar' => existsImage('avatar', $data->avatar_url) ?: existsImage('icon', 'not_select_image.png'), // 用户头像
                'transaction_number' => $data->transaction_number, // 交易编号
                'transaction_type' => TransactionType::getDescription($data->transaction_type), // 交易类型
                'transaction_category' => TransactionCategory::getDescription($data->transaction_category), // 交易类别
                'amount' => (float)bcdiv($data->amount, 100, 1), // 金额
                'points_balance' => (float)bcdiv($data->points_balance, 100, 1), // 积分余额
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result
        );
        return response()->json($json);
    }

    /**
     * 返回地址栏参数
     *
     * @param Request $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(Request $request): array
    {
        return array(
            'is_page' => 'true',
            'nickname_search' => $request->get('nickname_search'),
            'transaction_number_search' => $request->get('transaction_number_search'),
            'transaction_type_search' => $request->get('transaction_type_search'),
            'transaction_category_search' => $request->get('transaction_category_search'),
        );
    }

    /**
     * 获取搜索数据
     *
     * @param Request $request
     * @return mixed
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-11-08
     */
    protected function _searchList(Request $request): mixed
    {
        // dataTable字段

        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $nickname_search = $request->nickname_search;
        $transaction_number_search = $request->transaction_number_search;
        $transaction_type_search = $request->transaction_type_search;
        $transaction_category_search = $request->transaction_category_search;

        return PointsTransaction::select('points_transaction.*','user.nickname as nickname', 'user.avatar_url as avatar_url')
            ->leftJoin('user', 'points_transaction.user_id', '=', 'user.user_id')
            ->when(filled($nickname_search), function ($query) use ($nickname_search) {
                $query->where('user.nickname', 'like', "%$nickname_search%");
            })
            ->when(filled($transaction_number_search), function ($query) use ($transaction_number_search) {
                $query->where('transaction_number', 'like', "%$transaction_number_search%");
            })
            ->when(filled($transaction_type_search), function ($query) use ($transaction_type_search) {
                $query->where('transaction_type', $transaction_type_search);
            })
            ->when(filled($transaction_category_search), function ($query) use ($transaction_category_search) {
                $query->where('transaction_category', $transaction_category_search);
            })
            ->orderBy($order, $sort)
            ->latest();
    }
}

<?php

namespace App\Enums;

use BenSampo\Enum\Enum;
use BenSampo\Enum\Contracts\LocalizedEnum;
use App\Enums\Traits\Tools;

/**
 * @method static Pending
 * @method static Completed
 * @method static Cancelled
 */
final class PaymentStatusEnum extends Enum implements LocalizedEnum
{
    use Tools;

    const Pending = 'PENDING';
    const Completed = 'COMPLETED';
    const Cancelled = 'CANCELLED';
}

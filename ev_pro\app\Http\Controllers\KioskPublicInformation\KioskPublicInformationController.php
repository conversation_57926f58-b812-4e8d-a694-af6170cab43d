<?php

namespace App\Http\Controllers\KioskPublicInformation;

use App\Enums\PaymentMethod;
use App\Http\Controllers\Common\CommonController;
use Debugbar;
use App\Models\Modules\{
    Kiosk,
    MemberCardGroup,
    Setting,
    UserGroup,
};
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\{
    JsonResponse,
    Request,
};
use App\Enums\{
    TariffTableType,
    DayType,
    LmsMode,
    ChargeValueType,
    ChargeTariffScheme,
    IdentityType,
};
use Faker\Provider\ar_EG\Payment;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Str;

class KioskPublicInformationController extends CommonController
{
    protected static string $module_name = 'kioskPublicInformation'; // 模块名称

    public function __construct(Request $request)
    {
        parent::__construct($request);
        Debugbar::disable();
    }

    public function kioskPublicInformationSupport(Request $request): View|Factory|Application
    {
        $kiosk_number = $request->input('kiosk_number');
        $connector_number = $request->input('connector_number');
        $language_code = $request->input('language_code', 'en_US');
        $language_code = isset(config('languages')[$language_code]) ? $language_code : 'en_US';

        // 设置多语言
        $this->setLanguage($language_code);

        // kiosk_number未传或为空，返回404
        if (blank($kiosk_number)) {
            abort(404);
        }

        $data = array(
            'module_name' => self::$module_name,
            'language_code' => $language_code,
            'connector_number' => $connector_number,
            'support_information_url' => action(
                [self::class, 'getKioskPublicInformationSupportInformation'],
                [
                    'kiosk_number' => $kiosk_number,
                    'connector_number' => $connector_number,
                    'language_code' => $language_code,
                ]
            ),
        );

        return view("pages.{$data['module_name']}.support", $data);
    }

    public function getKioskPublicInformationSupportInformation(Request $request): JsonResponse
    {
        $kiosk_number = $request->input('kiosk_number');
        $connector_number = $request->input('connector_number');
        $language_code = $request->input('language_code');

        // kiosk_number未传或为空
        if (blank($kiosk_number)) {
            self::missingField('kiosk_number');
            return $this->returnJson();
        }

        $kiosk = Kiosk::with([
            'chargePoint' => [
                'connector' => [
                    'setting' => [
                        'simpleTariffTable',
                        'timeTariffTable',
                        'energyTariffTable',
                    ],
                ],
            ],
            'kioskSetting',
            // 预加载支付方式
            'kioskPaymentMethod' => function ($query) {
                $query->where('is_enable', 1)
                    ->whereIn('payment_method', [
                        PaymentMethod::Octopus,
                        PaymentMethod::Visa,
                        PaymentMethod::MasterCard,
                        PaymentMethod::Unionpay,
                        PaymentMethod::Fps,
                        PaymentMethod::Alipay,
                        PaymentMethod::WechatPay,
                        PaymentMethod::CreditCard,
                        PaymentMethod::QRCode,
                    ])
                    ->orderBy('sort_order');
            },
        ])
            ->where('kiosk_number', $kiosk_number)
            ->first();

        // 获取语言
        if (blank($language_code) || !isset(config('languages')[$language_code])) {
            $language_code = $kiosk->kioskSetting->default_language_code;
        } else {
            $language_code = (filled($language_code) && isset(config('languages')[$language_code])) ? $language_code : 'en_US';
        }
        // 设置多语言
        $this->setLanguage($language_code);

        // kiosk未找到
        if (blank($kiosk)) {
            self::notFoundData('kiosk');
            return $this->returnJson();
        }

        $data = [
            // 收费类型: pre_paid_time, post_paid_time, pre_paid_energy, post_paid_energy
            // 收费类型数据: connector_list, description
            'tariff_type_list' => [],
            'payment_method_list' => [],
        ];

        // 多语言描述TAB
        $tariff_type_description_list = [
            'pre_paid_time' => __('kioskPublicInformation.support_pre_paid_time'),
            'post_paid_time' => __('kioskPublicInformation.support_post_paid_time'),
            'pre_paid_energy' => __('kioskPublicInformation.support_pre_paid_energy'),
            'post_paid_energy' => __('kioskPublicInformation.support_post_paid_energy'),
        ];

        $kiosk_setting_number = $kiosk->kiosk_setting_number;
        // 优先拿env中KIOSK_SUPPORT_KIOSK_SETTING_SUPPORT_MODE绑定的模式
        $support_mode = env('KIOSK_SUPPORT_KIOSK_SETTING_SUPPORT_MODE', '');
        // 转数组
        $kiosk_setting_to_support_mode_arr = filled($support_mode) ? explode(',', $support_mode) : [];
        // 将:前后分别当做数组的KEY和VALUE
        $kiosk_setting_to_support_mode_list = [];
        foreach ($kiosk_setting_to_support_mode_arr as $kiosk_setting_to_support_mode) {
            $arr = explode(':', $kiosk_setting_to_support_mode);
            $kiosk_setting_to_support_mode_list[$arr[0]] = $arr[1];
        }
        $kiosk_support_mode = $kiosk_setting_to_support_mode_list[$kiosk_setting_number] ?? (env('KIOSK_SUPPORT_OCTOPUS_MODE', '') ?: null);
        $data['kiosk_support_mode'] = $kiosk_support_mode;
        foreach ($kiosk->chargePoint as $charge_point) {
            if (filled($connector_number)) {
                $connector_list = $charge_point->connector->where('connector_number', $connector_number);
            } else {
                $connector_list = $charge_point->connector;
            }
            foreach ($connector_list as $connector) {
                $post_paid_identity_type_list = null;
                // 根据收费表类型查询对应收费表信息
                switch ($connector->setting->tariff_table_type) {
                    case TariffTableType::SimpleTariffTable:
                        // 简单收费表
                        if (filled($connector?->setting?->simpleTariffTable)) {
                            $charge_tariff_scheme = $connector->setting->simpleTariffTable->charge_tariff_scheme; // 收费方案
                            $charge_value_type = $connector->setting->simpleTariffTable->charge_value_type; // 充电量类型
                            if ($charge_tariff_scheme === ChargeTariffScheme::PostPaid) {
                                $post_paid_identity_type_list = $connector->setting->simpleTariffTable->post_paid_identity_type_list;
                            }
                        }
                        break;
                    case TariffTableType::ComplexTimeTariffTable:
                        // 复杂时间收费表
                        if (filled($connector?->setting?->timeTariffTable)) {
                            $charge_tariff_scheme = $connector->setting->timeTariffTable->charge_tariff_scheme; // 收费方案
                            $charge_value_type = ChargeValueType::Time; // 充电量类型
                            if ($charge_tariff_scheme === ChargeTariffScheme::PostPaid) {
                                $post_paid_identity_type_list = $connector->setting->timeTariffTable->post_paid_identity_type_list;
                            }
                        }
                        break;
                    case TariffTableType::ComplexEnergyTariffTable:
                        // 复杂电量收费表
                        if (filled($connector?->setting?->energyTariffTable)) {
                            $charge_tariff_scheme = ChargeTariffScheme::PostPaid; // 收费方案
                            $charge_value_type = ChargeValueType::Energy; // 充电量类型
                            $post_paid_identity_type_list = $connector->setting->energyTariffTable->post_paid_identity_type_list;
                        }
                        break;
                    default:
                        break;
                }
                if (filled($post_paid_identity_type_list)) {
                    $post_paid_identity_type_list = explode(',', $post_paid_identity_type_list);
                }

                if (isset($charge_tariff_scheme, $charge_value_type)) {
                    $tariff_type = self::getTariffType($charge_tariff_scheme, $charge_value_type); // 收费类型
                    $tariff_type_description = $tariff_type ? ($tariff_type_description_list[$tariff_type] ?? $tariff_type) : null; // 收费类型描述

                    if (filled($tariff_type)) {
                        if (isset($data['tariff_type_list'][$tariff_type])) {
                            $data['tariff_type_list'][$tariff_type]['connector_list'][] = $connector->name;
                        } else {
                            $language_code = strtolower($language_code);
                            $payment_method_image_list = [];
                            $payment_method_list = [];
                            // 循环获取所有支付方式的图片
                            foreach ($kiosk->kioskPaymentMethod as $kiosk_payment_method) {
                                $this->getPaymentMethodList($data, $payment_method_list, $payment_method_image_list, $kiosk_payment_method->payment_method, $kiosk_support_mode, $tariff_type, $language_code);
                            }
                            if (filled($post_paid_identity_type_list) && in_array(IdentityType::PosPreAuthorization, $post_paid_identity_type_list)) {
                                $this->getPaymentMethodList($data, $payment_method_list, $payment_method_image_list, IdentityType::PosPreAuthorization, $kiosk_support_mode, $tariff_type, $language_code);
                            }

                            $data['tariff_type_list'][$tariff_type] = [
                                'connector_list' => [$connector->name],
                                'description' => $tariff_type_description,
                                'payment_method_list' => $payment_method_list,
                                'payment_method_image_list' => $payment_method_image_list
                            ];
                        }
                    }
                }
            }
        }

        $this->data = $data;

        return $this->returnJson();
    }

    /**
     * 根据收费方案和充电量类型获取收费类型
     *
     * @param string|null $charge_tariff_scheme
     * @param string|null $charge_value_type
     * @return string|null
     * @Description
     * @example
     * @date 2023-10-12
     */
    protected static function getTariffType(?string $charge_tariff_scheme, ?string $charge_value_type): ?string
    {
        $tariff_type = match ($charge_tariff_scheme) {
            ChargeTariffScheme::PrePaid => match ($charge_value_type) {
                ChargeValueType::Time => 'pre_paid_time', // 预付时间
                ChargeValueType::Energy => 'pre_paid_energy', // 预付电量
                default => null,
            },
            ChargeTariffScheme::PostPaid => match ($charge_value_type) {
                ChargeValueType::Time => 'post_paid_time', // 后付时间
                ChargeValueType::Energy => 'post_paid_energy', // 后付电量
                default => null,
            },
            default => null,
        };

        return $tariff_type;
    }

    protected function getPaymentMethodList(&$data, &$payment_method_list, &$payment_method_image_list, $kiosk_payment_method, $kiosk_support_mode, $tariff_type, $language_code)
    {
        $payment_method_list[] = $kiosk_payment_method;
        // 预授权等支付方式需要额外追加到后面
        $display_payment_method_method = null; // 显示的支付方式
        $payment_method_icon_name = null; // 支付方式图标名称null; // 支付方式图标名称
        $other_payment_method_folder = null; // 其他支付方式的文件夹
        // 后缀
        $suffix = '';

        switch ($kiosk_payment_method) {
            case IdentityType::PosPreAuthorization:
                $display_payment_method_method = PaymentMethod::CreditCard;
                $payment_method_icon_name = 'pos_pre_auth';
                $other_payment_method_folder = 'pos_pre_auth';
                $suffix = 'pos_pre_auth';
                break;
            case PaymentMethod::Octopus:
                // 判断当前kioskSetting是否在$kiosk_setting_to_support_mode_list中
                if (!is_null($kiosk_support_mode)) {
                    $suffix = $kiosk_support_mode;
                }
                break;
            default:
                break;
        }


        // 八达通图片模式
        /* if ($kiosk_payment_method === PaymentMethod::Octopus) {
            // 如果suffix是pos_pre_auth，则设置suffix为拿env中KIOSK_SUPPORT_OCTOPUS_MODE绑定的模式，因为pos_pre_auth目前只支持信用卡
            if ($suffix === 'pos_pre_auth') {
                $suffix = env('KIOSK_SUPPORT_OCTOPUS_MODE', '');
            }
        }
        // 信用卡预授权
        if ($kiosk_payment_method === PaymentMethod::CreditCard) {
            // 如果suffix是pos_pre_auth，则重定向文件夹到pos_pre_auth
            if (($tariff_type === 'post_paid_time' || $tariff_type === 'post_paid_energy') && $suffix === 'pos_pre_auth') {
                $pos_pre_auth_payment_method_folder = 'pos_pre_auth';
            } else {
                // 如果suffix不是pos_pre_auth，则设置suffix为空
                $suffix = '';
            }
        } */
        $suffix = filled($suffix) ? "_$suffix" : '';
        $payment_method = strtolower($kiosk_payment_method);
        $payment_method_folder = $other_payment_method_folder ?? $payment_method;
        // 判断图片是否存在
        /* if (
            !is_file(public_path("images/support/$payment_method_folder/support_{$tariff_type}_start_charge{$suffix}_{$language_code}.png")) ||
            !is_file(public_path("images/support/$payment_method_folder/support_{$tariff_type}_stop_charge{$suffix}_{$language_code}.png"))
        ) continue; */

        $payment_method_icon = asset('images/support/payment_method_icon/' . ($payment_method_icon_name ?: $payment_method) . '.png');
        $payment_method_name = PaymentMethod::getDescription($display_payment_method_method ?: $kiosk_payment_method);
        if (!isset($data['payment_method_list'][$payment_method])) {
            $data['payment_method_list'][$kiosk_payment_method] = [
                'icon' => $payment_method_icon,
                'name' => $payment_method_name,
            ];
        }
        $payment_method_image_list[$kiosk_payment_method] = [
            'start_charging_image' => asset("images/support/$payment_method_folder/support_{$tariff_type}_start_charge{$suffix}_{$language_code}.png"),
            'stop_charging_image' => asset("images/support/$payment_method_folder/support_{$tariff_type}_stop_charge{$suffix}_{$language_code}.png"),
        ];
    }

    /**
     * 获取指定kiosk充电收费html
     *
     * @param Request $request
     * @return Application|Factory|View
     */
    public function kioskPublicInformationAutomatically(Request $request): View|Factory|Application
    {
        $kiosk_number = $request->input('kiosk_number');
        $connector_number = $request->input('connector_number');
        $member_card_group_id = $request->input('member_card_group_id');
        $user_group_id = $request->input('user_group_id');
        $language_code = $request->input('language_code', 'en_US_zh_HK');
        $language_code = isset(config('languages')[$language_code]) ? $language_code : 'en_US_zh_HK';

        // kiosk_number未传或为空，返回404
        if (blank($kiosk_number)) {
            abort(404);
        }

        $data = array(
            'module_name' => self::$module_name,
            'language_code' => $language_code,
            'get_kiosk_public_information_url' => action(
                [self::class, 'getKioskPublicInformation'],
                [
                    'kiosk_number' => $kiosk_number,
                    'connector_number' => $connector_number,
                    'member_card_group_id' => $member_card_group_id,
                    'user_group_id' => $user_group_id,
                    'language_code' => $language_code,
                ]
            ),
        );

        $template_name = 'kioskPublicInformation';
        // 雙語模板
        if ($language_code === 'en_US_zh_HK') {
            $template_name .= '-en_US_zh_HK';
        }

        return view("pages.{$data['module_name']}.$template_name", $data);
    }

    public function getKioskPublicInformation(Request $request): JsonResponse
    {
        global $language_code, $member_card_group_id, $user_group_id, $lms_mode, $day_type_list;
        $kiosk_number = $request->input('kiosk_number');
        $connector_number = $request->input('connector_number');
        $member_card_group_id = $request->input('member_card_group_id');
        $user_group_id = $request->input('user_group_id');
        $language_code = $request->input('language_code', 'en_US_zh_HK');
        $language_code = isset(config('languages')[$language_code]) ? $language_code : 'en_US_zh_HK';

        // 如果env配置了不显示会员组收费，直接设置$member_card_group_id 为空
        if (!env('IS_KIOSK_TARIFF_TABLE_PAGE_SHOW_MEMBER_GROUP', true)) {
            $member_card_group_id = null;
        }
        // 如果env配置了不显示用户组收费，直接设置$user_group_id 为空
        if (!env('IS_KIOSK_TARIFF_TABLE_PAGE_SHOW_USER_GROUP', true)) {
            $user_group_id = null;
        }

        $module_name = self::$module_name;

        // kiosk_number未传或为空，返回404
        if (blank($kiosk_number)) {
            self::missingField('kiosk_number');
            return $this->returnJson();
        }

        $kiosk = Kiosk::with([
            'chargePoint' => [
                'connector' => [
                    'setting' => function ($query) use ($language_code) {
                        $query->oldest('sort_order')
                            ->oldest('gmt_create')
                            ->oldest('connector_setting_number');
                    },
                    'setting.simpleTariffTable',
                    'setting.timeTariffTable',
                    'setting.energyTariffTable',
                    'setting.idlingPenaltyTariffTable',
                    'setting.peakTimeTable',
                    'setting.description' => function ($query) use ($language_code) {
                        $query->where('language_code', $language_code);
                    },
                ],
            ],
            'site',
            'kioskSetting' => [
                'description' => function ($query) use ($language_code) {
                    $query->where('language_code', $language_code);
                },
            ],
        ])
            ->where('kiosk_number', $kiosk_number)
            ->first();

        // 沒找到對應KIOSK，返回404
        if (blank($kiosk)) {
            self::notFoundData('kiosk');
            return $this->returnJson();
        }

        $data = array(
            'connector_number' => $connector_number,
            'simple_tariff_table_content_html_text_list' => [],
            'time_tariff_table_content_html_text_list' => [],
            'energy_tariff_table_content_html_text_list' => [],
            'idling_tariff_table_content_html_text_list' => [],
            'peak_time_table_list' => [],
            'off_peak_time_table_list' => [],
            'terms_and_conditions_html_text' => '',
            'member_card_group_list' => [],
            "user_group_list" => [],
            'default_tariff_table_title' => $language_code === 'en_US_zh_HK'
                ? __("$module_name.text_default", [], 'en_US') . '<br>' . __("$module_name.text_default", [], 'zh_HK')
                : __("$module_name.text_default", [], $language_code),
        );

        // 條款及細則
        if (filled($kiosk->kioskSetting) && filled($kiosk_description = $kiosk->kioskSetting->description->first())) {
            $data['terms_and_conditions_html_text'] = $kiosk_description->disclaimer_html ?? ''; // 获取条款html
        }

        $lms_mode = $kiosk?->site?->lms_mode;
        foreach (DayType::getValues() as $day_type) {
            $day_type_list[$day_type]['en_US'] = DayType::getLocalDescription($day_type, 'en_US');
            $day_type_list[$day_type]['zh_HK'] = DayType::getLocalDescription($day_type, 'zh_HK');
        }
        $used_member_card_group_id_list = [];
        $used_user_group_id_list = [];
        // 获取所有的day_type枚举及双语的描述
        $data['day_type_list'] = $day_type_list;


        $tariff_table_data = self::getTariffTableByKiosk($kiosk, $connector_number);
        $results = $tariff_table_data['results'];
        $connector_setting_to_all_tariff_table_list = $tariff_table_data['connector_setting_to_all_tariff_table_list'];

        foreach ($connector_setting_to_all_tariff_table_list as $connector_setting_number => &$connector_setting) {
            // 会员卡组去重去空
            $connector_setting['member_card_group_id_list'] = array_values(array_filter(array_unique($connector_setting['member_card_group_id_list'])));
            $used_member_card_group_id_list = array_merge($used_member_card_group_id_list, $connector_setting['member_card_group_id_list']);
            $is_enable_member_card_group_tariff_table_key = $connector_setting['is_enable_member_card_group_tariff_table'] ? "ENABLE" : "DISABLE";
            // 用户组去重去空
            $connector_setting['user_group_id_list'] = array_values(array_filter(array_unique($connector_setting['user_group_id_list'])));
            $used_user_group_id_list = array_merge($used_user_group_id_list, $connector_setting['user_group_id_list']);
            $is_enable_user_group_tariff_table_key = $connector_setting['is_enable_user_group_tariff_table'] ? "ENABLE" : "DISABLE";
            // 根据收费表类型设置对应数据
            // !!! 使用array_merge()时，会重置键名，导致收费表number重置为索引数组，所以使用+号合并数组 !!!
            switch ($connector_setting['tariff_table_type']) {
                case TariffTableType::SimpleTariffTable:
                    if (!isset(
                        $connector_setting['simple_tariff_table_number'],
                        $results[$connector_setting_number]['simple_tariff_table'][$connector_setting['simple_tariff_table_number']]
                    )) {
                        continue 2;
                    }
                    $simple_tariff_table_list = self::setSimpleTariffList(
                        $results[$connector_setting_number]['simple_tariff_table'][$connector_setting['simple_tariff_table_number']] ?? [],
                        $connector_setting['member_card_group_id_list'],
                        $connector_setting['is_enable_member_card_group_tariff_table'],
                        $is_enable_member_card_group_tariff_table_key,
                        $connector_setting['user_group_id_list'],
                        $connector_setting['is_enable_user_group_tariff_table'],
                        $is_enable_user_group_tariff_table_key
                    );
                    $data['simple_tariff_table_content_html_text_list'] += $simple_tariff_table_list['simple_tariff_table_content_html_text_list'];
                    // 闲置罚款表
                    if (isset($connector_setting['idling_penalty_tariff_table_number']) && filled($connector_setting['idling_penalty_tariff_table_number'])) {
                        $idling_tariff_table_list = self::setIdlingPenaltyList(
                            $results[$connector_setting_number]['idling_tariff_table'][$connector_setting['idling_penalty_tariff_table_number']] ?? [],
                            $connector_setting['member_card_group_id_list'],
                            $connector_setting['is_enable_member_card_group_tariff_table'],
                            $is_enable_member_card_group_tariff_table_key,
                            $connector_setting['user_group_id_list'],
                            $connector_setting['is_enable_user_group_tariff_table'],
                            $is_enable_user_group_tariff_table_key
                        );
                        $data['idling_tariff_table_content_html_text_list'] += $idling_tariff_table_list['idling_tariff_table_content_html_text_list'];
                    }
                    // 高峰时间表
                    if (isset($connector_setting['peak_time_table_number']) && filled($connector_setting['peak_time_table_number'])) {
                        $data['peak_time_table_list'] += self::setPeakTime($results['peak_tariff_table'][$connector_setting['peak_time_table_number']] ?? []);
                    }
                    break;
                case TariffTableType::ComplexTimeTariffTable:
                    if (!isset(
                        $connector_setting['time_tariff_table_number'],
                        $results[$connector_setting_number]['time_tariff_table'][$connector_setting['time_tariff_table_number']]
                    )) {
                        continue 2;
                    }
                    $time_tariff_table_list = self::setTimeTariffList(
                        $results[$connector_setting_number]['time_tariff_table'][$connector_setting['time_tariff_table_number']] ?? [],
                        $connector_setting['member_card_group_id_list'],
                        $connector_setting['is_enable_member_card_group_tariff_table'],
                        $is_enable_member_card_group_tariff_table_key,
                        $connector_setting['user_group_id_list'],
                        $connector_setting['is_enable_user_group_tariff_table'],
                        $is_enable_user_group_tariff_table_key
                    );
                    $data['time_tariff_table_content_html_text_list'] += $time_tariff_table_list['time_tariff_table_content_html_text_list'];
                    // 闲置罚款表
                    if (isset($connector_setting['idling_penalty_tariff_table_number']) && filled($connector_setting['idling_penalty_tariff_table_number'])) {
                        $idling_tariff_table_list = self::setIdlingPenaltyList(
                            $results[$connector_setting_number]['idling_tariff_table'][$connector_setting['idling_penalty_tariff_table_number']] ?? [],
                            $connector_setting['member_card_group_id_list'],
                            $connector_setting['is_enable_member_card_group_tariff_table'],
                            $is_enable_member_card_group_tariff_table_key,
                            $connector_setting['user_group_id_list'],
                            $connector_setting['is_enable_user_group_tariff_table'],
                            $is_enable_user_group_tariff_table_key
                        );
                        $data['idling_tariff_table_content_html_text_list'] += $idling_tariff_table_list['idling_tariff_table_content_html_text_list'];
                    }
                    // 高峰时间表
                    if (isset($connector_setting['peak_time_table_number']) && filled($connector_setting['peak_time_table_number'])) {
                        $data['peak_time_table_list'] += self::setPeakTime($results['peak_tariff_table'][$connector_setting['peak_time_table_number']] ?? []);
                    }
                    break;
                case TariffTableType::ComplexEnergyTariffTable:
                    if (!isset(
                        $connector_setting['energy_tariff_table_number'],
                        $results[$connector_setting_number]['energy_tariff_table'][$connector_setting['energy_tariff_table_number']]
                    )) {
                        continue 2;
                    }
                    $energy_tariff_table_list = self::setEnergyTariffList(
                        $results[$connector_setting_number]['energy_tariff_table'][$connector_setting['energy_tariff_table_number']] ?? [],
                        $connector_setting['member_card_group_id_list'],
                        $connector_setting['is_enable_member_card_group_tariff_table'],
                        $is_enable_member_card_group_tariff_table_key,
                        $connector_setting['user_group_id_list'],
                        $connector_setting['is_enable_user_group_tariff_table'],
                        $is_enable_user_group_tariff_table_key
                    );
                    $data['energy_tariff_table_content_html_text_list'] += $energy_tariff_table_list['energy_tariff_table_content_html_text_list'];
                    // 闲置罚款表
                    if (isset($connector_setting['idling_penalty_tariff_table_number']) && filled($connector_setting['idling_penalty_tariff_table_number'])) {
                        $idling_tariff_table_list = self::setIdlingPenaltyList(
                            $results[$connector_setting_number]['idling_tariff_table'][$connector_setting['idling_penalty_tariff_table_number']] ?? [],
                            $connector_setting['member_card_group_id_list'],
                            $connector_setting['is_enable_member_card_group_tariff_table'],
                            $is_enable_member_card_group_tariff_table_key,
                            $connector_setting['user_group_id_list'],
                            $connector_setting['is_enable_user_group_tariff_table'],
                            $is_enable_user_group_tariff_table_key
                        );
                        $data['idling_tariff_table_content_html_text_list'] += $idling_tariff_table_list['idling_tariff_table_content_html_text_list'];
                    }
                    // 高峰时间表
                    if (isset($connector_setting['peak_time_table_number']) && filled($connector_setting['peak_time_table_number'])) {
                        $data['peak_time_table_list'] += self::setPeakTime($results['peak_tariff_table'][$connector_setting['peak_time_table_number']] ?? []);
                    }
                    break;
                default:
                    break;
            };
        }

        // 处理非高峰时间表
        foreach ($data['peak_time_table_list'] as $peak_time_table_number => $peak_time_table) {
            // 非高峰时间表
            $data['off_peak_time_table_list'][$peak_time_table_number] = self::setOffPeakTime($peak_time_table);
        }

        $data['connector_setting_to_all_tariff_table_list'] = $connector_setting_to_all_tariff_table_list;

        // 会员卡组去重去空
        $used_member_card_group_id_list = array_filter(array_unique($used_member_card_group_id_list));
        $member_card_group_result = MemberCardGroup::select('member_card_group_id', 'name_json')
            ->whereIn('member_card_group_id', $used_member_card_group_id_list)
            ->get();
        foreach ($member_card_group_result as $member_card_group) {
            $data['member_card_group_list'][$member_card_group->member_card_group_id] = $language_code === 'en_US_zh_HK'
                ? (self::getValueFromLanguageArray($member_card_group->name_json, 'en_US') . '<br>' . self::getValueFromLanguageArray($member_card_group->name_json, 'zh_HK'))
                : self::getValueFromLanguageArray($member_card_group->name_json, $language_code);
        }
        // 用户组去重去空
        $used_user_group_id_list = array_filter(array_unique($used_user_group_id_list));
        $user_group_result = UserGroup::select('user_group_id', 'name_json')
            ->whereIn('user_group_id', $used_user_group_id_list)
            ->get();
        foreach ($user_group_result as $user_group) {
            $data['user_group_list'][$user_group->user_group_id] = $language_code === 'en_US_zh_HK'
                ? (self::getValueFromLanguageArray($user_group->name_json, 'en_US') . '<br>' . self::getValueFromLanguageArray($user_group->name_json, 'zh_HK'))
                : self::getValueFromLanguageArray($user_group->name_json, $language_code);
        }

        $this->data = $data;

        return $this->returnJson();
    }

    public function kioskPublicInformationKioskBindApp(Request $request): View|Factory|Application
    {
        $kiosk_number = $request->input('kiosk_number');
        $language_code = $request->input('language_code', 'en_US');
        $language_code = isset(config('languages')[$language_code]) ? $language_code : 'en_US';

        // 设置多语言
        $this->setLanguage($language_code);

        // kiosk_number未传或为空，返回404
        if (blank($kiosk_number)) {
            abort(404);
        }

        $kiosk_binding_app_request_interval = 3; // 展示二维码之后的check请求间隔时间，单位秒，默认3秒
        $setting = Setting::firstWhere('key', 'kiosk_binding_app_request_interval');
        if (filled($setting)) $kiosk_binding_app_request_interval = $setting->value;

        $data = array(
            'module_name' => self::$module_name,
            'language_code' => $language_code,
            'kiosk_binding_app_request_interval' => $kiosk_binding_app_request_interval * 1000,
            'get_kiosk_bind_app_info_url' => action(
                [self::class, 'getKioskBindAppInfo'],
                [
                    'kiosk_number' => $kiosk_number,
                    'language_code' => $language_code,
                ]
            ),
            'check_kiosk_bind_app_status_url' => action(
                [self::class, 'checkKioskBindAppStatus'],
                [
                    'kiosk_number' => $kiosk_number,
                    'language_code' => $language_code,
                ]
            ),
        );

        return view("pages.{$data['module_name']}.kioskBindApp", $data);
    }

    public function getKioskBindAppInfo(Request $request): JsonResponse
    {
        $kiosk_number = $request->input('kiosk_number');
        $language_code = $request->input('language_code', 'en_US');
        $language_code = isset(config('languages')[$language_code]) ? $language_code : 'en_US';

        // 设置多语言
        $this->setLanguage($language_code);

        // kiosk_number未传或为空，返回404
        if (blank($kiosk_number)) {
            self::missingField('kiosk_number');
            return $this->returnJson();
        }

        $kiosk = Kiosk::firstWhere('kiosk_number', $kiosk_number);
        // 沒找到對應KIOSK，返回404
        if (blank($kiosk)) {
            self::notFoundData('kiosk');
            return $this->returnJson();
        }

        // 二维码过期时间
        $qr_code_expired_time = env('KIOSK_BIND_APP_QR_CODE_SECOND', 600);
        // 生成二维码UUID
        if (filled($uuid = Redis::get('KioskBindAppKioskNumberToUUID:' . $kiosk->kiosk_number))) {
            // 首先判断是否已存在uuid，存在则重置时间为10分钟
            Redis::expire('KioskBindAppKioskNumberToUUID:' . $kiosk->kiosk_number, $qr_code_expired_time);
            Redis::expire('KioskBindAppUUIDToKioskNumber:' . $uuid, $qr_code_expired_time);
            Redis::expire('KioskBindAppKioskNumberToUser:' . $kiosk->kiosk_number, $qr_code_expired_time);
        } else {
            // 不存在重新生成uuid
            $uuid = retry(5, function () {
                $uuid = (string)Str::uuid();
                // 如果uuid已存在，重新生成
                if (filled(Redis::get('KioskBindAppUUIDToKioskNumber:' . $uuid))) {
                    throw new \Exception('Authorize Code Generate Error, Please Retry Again.');
                }
                return $uuid;
            }, 100);
            // 绑定kiosk_number和UUID，10分钟有效期
            Redis::setex('KioskBindAppKioskNumberToUUID:' . $kiosk->kiosk_number, $qr_code_expired_time, $uuid);
            Redis::setex('KioskBindAppUUIDToKioskNumber:' . $uuid, $qr_code_expired_time, $kiosk->kiosk_number);
            Redis::setex('KioskBindAppKioskNumberToUser:' . $kiosk->kiosk_number, $qr_code_expired_time, '');
        }

        $this->data = array(
            'qrcode' => 'evprokac://' . $uuid,
        );

        return $this->returnJson();
    }

    public function checkKioskBindAppStatus(Request $request): JsonResponse
    {
        $kiosk_number = $request->input('kiosk_number');
        $language_code = $request->input('language_code', 'en_US');
        $language_code = isset(config('languages')[$language_code]) ? $language_code : 'en_US';

        // 设置多语言
        $this->setLanguage($language_code);
        // 查询kiosk_number是否绑定了用户
        $user_id = Redis::get('KioskBindAppKioskNumberToUser:' . $kiosk_number);
        $uuid = Redis::get('KioskBindAppKioskNumberToUUID:' . $kiosk_number);
        if (filled($user_id)) {
            $this->data = [
                'status' => true,
                'user_id' => (int)$user_id,
            ];
            // 清除redis的key
            Redis::del('KioskBindAppKioskNumberToUUID:' . $kiosk_number);
            Redis::del('KioskBindAppUUIDToKioskNumber:' . $uuid);
            Redis::del('KioskBindAppKioskNumberToUser:' . $kiosk_number);
            return $this->returnJson();
        }
        // 如果还没有绑定用户，查询kiosk_number是否生成了uuid，如果没有生成uuid，二维码过期
        if (blank($uuid)) {
            $this->code = 40003;
            $this->message = __('kioskPublicInformation.error_qrcode_expired');
            return $this->returnJson();
        }

        $this->data = [
            'status' => false,
            'user_id' => null,
        ];

        return $this->returnJson();
    }

    /**
     * 返回kiosk下的所有connector_setting对应的收费表信息
     *
     * @param [type] $kiosk
     * @param [type] $connector_id
     * @return array
     * @Description
     * @example
     * @date 2023-10-16
     */
    public static function getTariffTableByKiosk($kiosk, $connector_number = null): array
    {
        global $member_card_group_id, $user_group_id;
        $results = array();
        // 取出connector setting並去重
        $connector_setting_list = [];
        $connector_setting_to_connector_number_list = [];
        // 存储connector_setting_number对应的所有收费表的关联
        $connector_setting_to_all_tariff_table_list = [];

        foreach ($kiosk->chargePoint as $charge_point) {
            $connector_list = $charge_point->connector->when(filled($connector_number), fn($query) => $query->where('connector_number', $connector_number));

            foreach ($connector_list as $connector) {
                $connector_setting_list[$connector->connector_setting_number] = $connector->setting;
                $connector_setting_to_connector_number_list[$connector->connector_setting_number][] = $connector->connector_number;
                $connector_setting_to_all_tariff_table_list[$connector->connector_setting_number]['sort_order'] = $connector->setting->sort_order;
                // 存储connector_setting_description的tariff_table_name
                $connector_setting_to_all_tariff_table_list[$connector->connector_setting_number]['tariff_table_name'] =
                    $connector->setting->description->first()?->tariff_table_name ?? '';
                // 存储connector_setting_number对应的connector名称
                $connector_setting_to_all_tariff_table_list[$connector->connector_setting_number]['connector_list'][] = $connector->name;

                // 根据收费表类型查询对应收费表信息
                $connector_setting_to_all_tariff_table_list[$connector->connector_setting_number]['tariff_table_type'] = $connector->setting->tariff_table_type;
                // 所有收费表用到的会员卡组number
                $used_member_card_group_id_list = [];
                // 所有收费表用到的用户组number
                $used_user_group_id_list = [];
                // 是否启用会员卡组收费表
                $is_enable_member_card_group_tariff_table = $connector?->setting?->is_enable_member_card_group_tariff_table;
                // 是否启用用户组收费表
                $is_enable_user_group_tariff_table = $connector?->setting?->is_enable_user_group_tariff_table;
                switch ($connector?->setting?->tariff_table_type) {
                    case TariffTableType::SimpleTariffTable:
                        $simple_tariff_table = $connector?->setting?->simpleTariffTable;
                        $idling_penalty_tariff_table = $connector?->setting?->idlingPenaltyTariffTable;
                        $peak_time_table = $connector?->setting?->peakTimeTable;
                        // 简单收费表
                        if (filled($simple_tariff_table)) {
                            // 预加载关联
                            $simple_tariff_table->load([
                                'item' => function ($query) use ($is_enable_member_card_group_tariff_table, $member_card_group_id, $is_enable_user_group_tariff_table, $user_group_id) {
                                    $query->when($is_enable_member_card_group_tariff_table || $is_enable_user_group_tariff_table, function ($query) use ($member_card_group_id, $user_group_id) {
                                        $query->when(filled($member_card_group_id), function ($query) use ($member_card_group_id) {
                                            // 查询对应会员卡组的收费数据
                                            $query->where('member_card_group_id', $member_card_group_id);
                                        });
                                        $query->when(filled($user_group_id), function ($query) use ($user_group_id) {
                                            // 查询对应用户组的收费数据
                                            $query->where('user_group_id', $user_group_id);
                                        });
                                    }, fn($query) => $query->whereRaw('1=0'));
                                }
                            ]);
                            // 存储简单收费表信息
                            $results[$connector->connector_setting_number]['simple_tariff_table'][$simple_tariff_table->simple_tariff_table_number] = clone $simple_tariff_table;
                            $used_member_card_group_id_list = array_merge($used_member_card_group_id_list, $simple_tariff_table->item?->pluck('member_card_group_id')?->toArray() ?? []);
                            $used_user_group_id_list = array_merge($used_user_group_id_list, $simple_tariff_table->item?->pluck('user_group_id')?->toArray() ?? []);
                            // 存储connector_setting_number对应的简单收费表number
                            $connector_setting_to_all_tariff_table_list[$connector->connector_setting_number]['simple_tariff_table_number'] = $simple_tariff_table->simple_tariff_table_number;
                            // 存储connector_setting_number对应的简单收费表（仅后付）的最大充电时间
                            if ($simple_tariff_table->charge_tariff_scheme === ChargeTariffScheme::PostPaid) {
                                $connector_setting_to_all_tariff_table_list[$connector->connector_setting_number]['maximum_charge_time'] = $simple_tariff_table->post_paid_maximum_charge_time / 60;
                            }
                        }
                        // 闲时罚款表
                        if (filled($idling_penalty_tariff_table)) {
                            // 预加载关联
                            $idling_penalty_tariff_table->load([
                                'item' => function ($query) use ($is_enable_member_card_group_tariff_table, $member_card_group_id, $is_enable_user_group_tariff_table, $user_group_id) {
                                    $query->when($is_enable_member_card_group_tariff_table, function ($query) use ($member_card_group_id) {
                                        $query->when(filled($member_card_group_id), function ($query) use ($member_card_group_id) {
                                            // 查询对应会员卡组的收费数据和默认的数据
                                            $query->where(function ($query) use ($member_card_group_id) {
                                                $query->where('member_card_group_id', $member_card_group_id)
                                                    ->orWhereNull('member_card_group_id');
                                            });
                                        });
                                    }, fn($query) => $query->whereNull('member_card_group_id'))
                                    ->when($is_enable_user_group_tariff_table, function ($query) use ($user_group_id) {
                                        $query->when(filled($user_group_id), function ($query) use ($user_group_id) {
                                            // 查询对应用户组的收费数据和默认的数据
                                            $query->where(function ($query) use ($user_group_id) {
                                                $query->where('user_group_id', $user_group_id)
                                                    ->orWhereNull('user_group_id');
                                            });
                                        });
                                    }, fn($query) => $query->whereNull('user_group_id'));
                                }
                            ]);
                            // 存储闲时罚款表信息
                            $results[$connector->connector_setting_number]['idling_tariff_table'][$idling_penalty_tariff_table->idling_penalty_tariff_table_number] = clone $idling_penalty_tariff_table;
                            $used_member_card_group_id_list = array_merge($used_member_card_group_id_list, $idling_penalty_tariff_table->item?->pluck('member_card_group_id')?->toArray() ?? []);
                            $used_user_group_id_list = array_merge($used_user_group_id_list, $idling_penalty_tariff_table->item?->pluck('user_group_id')?->toArray() ?? []);
                            // 存储connector_setting_number对应的闲时罚款表number
                            $connector_setting_to_all_tariff_table_list[$connector->connector_setting_number]['idling_penalty_tariff_table_number'] = $idling_penalty_tariff_table->idling_penalty_tariff_table_number;
                        }
                        // 高峰时间表
                        if (filled($peak_time_table)) {
                            // 预加载关联
                            $peak_time_table->load([
                                'item',
                            ]);
                            // 存储高峰时间表信息
                            $results['peak_tariff_table'][$connector->setting->peakTimeTable->peak_time_table_number] = $peak_time_table;
                            // 存储connector_setting_number对应的高峰时间表number
                            $connector_setting_to_all_tariff_table_list[$connector->connector_setting_number]['peak_time_table_number'] = $peak_time_table->peak_time_table_number;
                        }
                        break;
                    case TariffTableType::ComplexTimeTariffTable:
                        $time_tariff_table = $connector?->setting?->timeTariffTable;
                        $idling_penalty_tariff_table = $connector?->setting?->idlingPenaltyTariffTable;
                        $peak_time_table = $connector?->setting?->peakTimeTable;

                        // 复杂时间收费表
                        if (filled($time_tariff_table)) {
                            // 预加载关联
                            $time_tariff_table->load([
                                'item' => function ($query) use ($is_enable_member_card_group_tariff_table, $member_card_group_id, $is_enable_user_group_tariff_table, $user_group_id) {
                                    $query->when($is_enable_member_card_group_tariff_table, function ($query) use ($member_card_group_id) {
                                        $query->when(filled($member_card_group_id), function ($query) use ($member_card_group_id) {
                                            // 查询对应会员卡组的收费数据和默认的数据
                                            $query->where(function ($query) use ($member_card_group_id) {
                                                $query->where('member_card_group_id', $member_card_group_id)
                                                    ->orWhereNull('member_card_group_id');
                                            });
                                        });
                                    }, fn($query) => $query->whereNull('member_card_group_id'))
                                    ->when($is_enable_user_group_tariff_table, function ($query) use ($user_group_id) {
                                        $query->when(filled($user_group_id), function ($query) use ($user_group_id) {
                                            // 查询对应用户组的收费数据和默认的数据
                                            $query->where(function ($query) use ($user_group_id) {
                                                $query->where('user_group_id', $user_group_id)
                                                    ->orWhereNull('user_group_id');
                                            });
                                        });
                                    }, fn($query) => $query->whereNull('user_group_id'));
                                },
                                'item.itemSpecial' => function ($query) {
                                    $query->oldest('start_time');
                                }
                            ]);
                            // 存储复杂时间收费表信息
                            $results[$connector->connector_setting_number]['time_tariff_table'][$time_tariff_table->time_tariff_table_number] = clone $time_tariff_table;
                            $used_member_card_group_id_list = array_merge($used_member_card_group_id_list, $time_tariff_table->item?->pluck('member_card_group_id')?->toArray() ?? []);
                            $used_user_group_id_list = array_merge($used_user_group_id_list, $time_tariff_table->item?->pluck('user_group_id')?->toArray() ?? []);
                            // 存储connector_setting_number对应的复杂时间收费表number
                            $connector_setting_to_all_tariff_table_list[$connector->connector_setting_number]['time_tariff_table_number'] = $time_tariff_table->time_tariff_table_number;
                            // 存储connector_setting_number对应的复杂时间收费表（仅后付）的最大充电时间
                            if ($time_tariff_table->charge_tariff_scheme === ChargeTariffScheme::PostPaid) {
                                $connector_setting_to_all_tariff_table_list[$connector->connector_setting_number]['maximum_charge_time'] = $time_tariff_table->post_paid_maximum_charge_time / 60;
                            }
                        }
                        // 闲时罚款表
                        if (filled($idling_penalty_tariff_table)) {
                            // 预加载关联
                            $idling_penalty_tariff_table->load([
                                'item' => function ($query) use ($is_enable_member_card_group_tariff_table, $member_card_group_id, $is_enable_user_group_tariff_table, $user_group_id) {
                                    $query->when($is_enable_member_card_group_tariff_table, function ($query) use ($member_card_group_id) {
                                        $query->when(filled($member_card_group_id), function ($query) use ($member_card_group_id) {
                                            // 查询对应会员卡组的收费数据和默认的数据
                                            $query->where(function ($query) use ($member_card_group_id) {
                                                $query->where('member_card_group_id', $member_card_group_id)
                                                    ->orWhereNull('member_card_group_id');
                                            });
                                        });
                                    }, fn($query) => $query->whereNull('member_card_group_id'))
                                    ->when($is_enable_user_group_tariff_table, function ($query) use ($user_group_id) {
                                        $query->when(filled($user_group_id), function ($query) use ($user_group_id) {
                                            // 查询对应用户组的收费数据和默认的数据
                                            $query->where(function ($query) use ($user_group_id) {
                                                $query->where('user_group_id', $user_group_id)
                                                    ->orWhereNull('user_group_id');
                                            });
                                        });
                                    }, fn($query) => $query->whereNull('user_group_id'));
                                }
                            ]);
                            // 存储闲时罚款表信息
                            $results[$connector->connector_setting_number]['idling_tariff_table'][$idling_penalty_tariff_table->idling_penalty_tariff_table_number] = clone $idling_penalty_tariff_table;
                            $used_member_card_group_id_list = array_merge($used_member_card_group_id_list, $idling_penalty_tariff_table->item?->pluck('member_card_group_id')?->toArray() ?? []);
                            $used_user_group_id_list = array_merge($used_user_group_id_list, $idling_penalty_tariff_table->item?->pluck('user_group_id')?->toArray() ?? []);
                            // 存储connector_setting_number对应的闲时罚款表number
                            $connector_setting_to_all_tariff_table_list[$connector->connector_setting_number]['idling_penalty_tariff_table_number'] = $idling_penalty_tariff_table->idling_penalty_tariff_table_number;
                        }
                        // 高峰时间表
                        if (filled($peak_time_table)) {
                            // 预加载关联
                            $peak_time_table->load([
                                'item',
                            ]);
                            // 存储高峰时间表信息
                            $results['peak_tariff_table'][$connector->setting->peakTimeTable->peak_time_table_number] = $peak_time_table;
                            // 存储connector_setting_number对应的高峰时间表number
                            $connector_setting_to_all_tariff_table_list[$connector->connector_setting_number]['peak_time_table_number'] = $peak_time_table->peak_time_table_number;
                        }
                        break;
                    case TariffTableType::ComplexEnergyTariffTable:
                        $energy_tariff_table = $connector?->setting?->energyTariffTable;
                        $idling_penalty_tariff_table = $connector?->setting?->idlingPenaltyTariffTable;
                        $peak_time_table = $connector?->setting?->peakTimeTable;

                        // 复杂电量收费表
                        if (filled($energy_tariff_table)) {
                            // 预加载关联
                            $energy_tariff_table->load([
                                'item' => function ($query) use ($is_enable_member_card_group_tariff_table, $member_card_group_id, $is_enable_user_group_tariff_table, $user_group_id) {
                                    $query->when($is_enable_member_card_group_tariff_table, function ($query) use ($member_card_group_id) {
                                        $query->when(filled($member_card_group_id), function ($query) use ($member_card_group_id) {
                                            // 查询对应会员卡组的收费数据和默认的数据
                                            $query->where(function ($query) use ($member_card_group_id) {
                                                $query->where('member_card_group_id', $member_card_group_id)
                                                    ->orWhereNull('member_card_group_id');
                                            });
                                        });
                                    }, fn($query) => $query->whereNull('member_card_group_id'))
                                    ->when($is_enable_user_group_tariff_table, function ($query) use ($user_group_id) {
                                        $query->when(filled($user_group_id), function ($query) use ($user_group_id) {
                                            // 查询对应用户组的收费数据和默认的数据
                                            $query->where(function ($query) use ($user_group_id) {
                                                $query->where('user_group_id', $user_group_id)
                                                    ->orWhereNull('user_group_id');
                                            });
                                        });
                                    }, fn($query) => $query->whereNull('user_group_id'));
                                }
                            ]);
                            // 存储复杂电量收费表信息
                            $results[$connector->connector_setting_number]['energy_tariff_table'][$energy_tariff_table->energy_tariff_table_number] = clone $energy_tariff_table;
                            $used_member_card_group_id_list = array_merge($used_member_card_group_id_list, $energy_tariff_table->item?->pluck('member_card_group_id')?->toArray() ?? []);
                            $used_user_group_id_list = array_merge($used_user_group_id_list, $energy_tariff_table->item?->pluck('user_group_id')?->toArray() ?? []);
                            // 存储connector_setting_number对应的复杂电量收费表number
                            $connector_setting_to_all_tariff_table_list[$connector->connector_setting_number]['energy_tariff_table_number'] = $energy_tariff_table->energy_tariff_table_number;
                            // 存储connector_setting_number对应的复杂电量收费表（仅后付）的最大充电时间
                            $connector_setting_to_all_tariff_table_list[$connector->connector_setting_number]['maximum_charge_time'] = $energy_tariff_table->post_paid_maximum_charge_time / 60;
                        }
                        // 闲时罚款表
                        if (filled($idling_penalty_tariff_table)) {
                            // 预加载关联
                            $idling_penalty_tariff_table->load([
                                'item' => function ($query) use ($is_enable_member_card_group_tariff_table, $member_card_group_id, $is_enable_user_group_tariff_table, $user_group_id) {
                                    $query->when($is_enable_member_card_group_tariff_table, function ($query) use ($member_card_group_id) {
                                        $query->when(filled($member_card_group_id), function ($query) use ($member_card_group_id) {
                                            // 查询对应会员卡组的收费数据和默认的数据
                                            $query->where(function ($query) use ($member_card_group_id) {
                                                $query->where('member_card_group_id', $member_card_group_id)
                                                    ->orWhereNull('member_card_group_id');
                                            });
                                        });
                                    }, fn($query) => $query->whereNull('member_card_group_id'))
                                    ->when($is_enable_user_group_tariff_table, function ($query) use ($user_group_id) {
                                        $query->when(filled($user_group_id), function ($query) use ($user_group_id) {
                                            // 查询对应用户组的收费数据和默认的数据
                                            $query->where(function ($query) use ($user_group_id) {
                                                $query->where('user_group_id', $user_group_id)
                                                    ->orWhereNull('user_group_id');
                                            });
                                        });
                                    }, fn($query) => $query->whereNull('user_group_id'));
                                }
                            ]);
                            // 存储闲时罚款表信息
                            $results[$connector->connector_setting_number]['idling_tariff_table'][$idling_penalty_tariff_table->idling_penalty_tariff_table_number] = clone $idling_penalty_tariff_table;
                            $used_member_card_group_id_list = array_merge($used_member_card_group_id_list, $idling_penalty_tariff_table->item?->pluck('member_card_group_id')?->toArray() ?? []);
                            $used_user_group_id_list = array_merge($used_user_group_id_list, $idling_penalty_tariff_table->item?->pluck('user_group_id')?->toArray() ?? []);
                            // 存储connector_setting_number对应的闲时罚款表number
                            $connector_setting_to_all_tariff_table_list[$connector->connector_setting_number]['idling_penalty_tariff_table_number'] = $idling_penalty_tariff_table->idling_penalty_tariff_table_number;
                        }
                        // 高峰时间表
                        if (filled($peak_time_table)) {
                            // 预加载关联
                            $peak_time_table->load([
                                'item'
                            ]);
                            // 存储高峰时间表信息
                            $results['peak_tariff_table'][$peak_time_table->peak_time_table_number] = $peak_time_table;
                            // 存储connector_setting_number对应的高峰时间表number
                            $connector_setting_to_all_tariff_table_list[$connector->connector_setting_number]['peak_time_table_number'] = $peak_time_table->peak_time_table_number;
                        }
                        break;
                    default:
                        break;
                }

                if (!env('IS_KIOSK_TARIFF_TABLE_PAGE_SHOW_MEMBER_GROUP', true)) {
                    // 不显示会员卡组收费
                    $used_member_card_group_id_list = [];
                }
                if (!env('IS_KIOSK_TARIFF_TABLE_PAGE_SHOW_USER_GROUP', true)) {
                    // 不显示用户组收费
                    $used_user_group_id_list = [];
                }

                // 存储connector_setting_number对应的所有收费表的会员卡组number
                $connector_setting_to_all_tariff_table_list[$connector->connector_setting_number]['member_card_group_id_list'] = $used_member_card_group_id_list;
                // 存储connector_setting_number对应的所有收费表的用户组number
                $connector_setting_to_all_tariff_table_list[$connector->connector_setting_number]['user_group_id_list'] = $used_user_group_id_list;
                // 存储connector_setting_number对应是否启用会员卡组收费表
                $connector_setting_to_all_tariff_table_list[$connector->connector_setting_number]['is_enable_member_card_group_tariff_table'] = $is_enable_member_card_group_tariff_table;
                // 存储connector_setting_number对应是否启用用户组收费表
                $connector_setting_to_all_tariff_table_list[$connector->connector_setting_number]['is_enable_user_group_tariff_table'] = $is_enable_user_group_tariff_table;
            }
        }

        // connector_setting_to_all_tariff_table_list 按照sort_order排序
        $connector_setting_to_all_tariff_table_list = collect($connector_setting_to_all_tariff_table_list)->sortBy('sort_order')->toArray();
        return [
            'results' => $results,
            'connector_setting_list' => $connector_setting_list,
            'connector_setting_to_connector_number_list' => $connector_setting_to_connector_number_list,
            'connector_setting_to_all_tariff_table_list' => $connector_setting_to_all_tariff_table_list,
        ];
    }

    public static function setSimpleTariffList(
        $simple_tariff_table,
        $member_card_group_id_list = [],
        $is_enable_member_card_group_tariff_table = false,
        $is_enable_member_card_group_tariff_table_key = "DISABLE",
        $user_group_id_list = [],
        $is_enable_user_group_tariff_table = false,
        $is_enable_user_group_tariff_table_key = "DISABLE"
    ): array
    {
        global $language_code;
        if (blank($simple_tariff_table)) {
            return [
                'simple_tariff_table_content_html_text_list' => [],
            ];
        }

        $module_name = self::$module_name;
        $template_language_name = '';
        if ($language_code === 'en_US_zh_HK') {
            $template_language_name = 'en_US_zh_HK.';
        }
        $simple_tariff_table_number = $simple_tariff_table->simple_tariff_table_number;
        // 收费表html
        $tariff_table_content_html_text_list = [];
        // 收费表item
        $simple_tariff_table_item_list = [];

        // 生成收费表item函数
        $generate_simple_tariff_table_item_list = function (
            &$simple_tariff_table_item_list,
            $rate,
            $charge_value_type,
            $charge_value_interval,
            $member_card_group_id = 0,
            $user_group_id = 0,
        ) use ($language_code, $simple_tariff_table_number) {
            $rate = bcdiv($rate, 100, 1);
            $rate_unit = '';
            $rate_en_US_zh_HK = '';
            switch ($charge_value_type) {
                case ChargeValueType::Time:
                    $charge_value_interval /= 60;
                    $rate_unit = __('kioskPublicInformation.rate_interval_min', ['interval' => $charge_value_interval], $language_code);
                    if ($language_code === 'en_US_zh_HK') {
                        $rate_en_US_zh_HK = __('kioskSetting.text_rate_unit', [], 'en_US') . ' ' . $rate . ' ' .
                            __('kioskPublicInformation.rate_interval_min', ['interval' => $charge_value_interval], 'en_US') .
                            '<br>' . __('kioskSetting.text_rate_unit', [], 'zh_HK') . ' ' . $rate . ' ' .
                            __('kioskPublicInformation.rate_interval_min', ['interval' => $charge_value_interval], 'zh_HK');
                    }
                    break;
                case ChargeValueType::Energy:
                    $charge_value_interval /= 1000;
                    $rate_unit = __('kioskPublicInformation.energy_rate_kwh', ['interval' => $charge_value_interval], $language_code);
                    if ($language_code === 'en_US_zh_HK') {
                        $rate_en_US_zh_HK = __('kioskSetting.text_rate_unit', [], 'en_US') . ' ' . $rate . ' ' .
                            __('kioskPublicInformation.energy_rate_kwh', ['interval' => $charge_value_interval], 'en_US') .
                            '<br>' . __('kioskSetting.text_rate_unit', [], 'zh_HK') . ' ' . $rate . ' ' .
                            __('kioskPublicInformation.energy_rate_kwh', ['interval' => $charge_value_interval], 'zh_HK');
                    }
                    break;
                default:
                    break;
            }
            $simple_tariff_table_item_list[] = [
                'simple_tariff_table_number' => $simple_tariff_table_number,
                'rate' => $rate,
                'rate_unit' => $rate_unit,
                'rate_en_US_zh_HK' => $rate_en_US_zh_HK,
                'member_card_group_id' => $member_card_group_id,
                'user_group_id' => $user_group_id,
            ];
        };

        // 先添加一条默认收费数据
        $generate_simple_tariff_table_item_list(
            $simple_tariff_table_item_list,
            $simple_tariff_table->rate,
            $simple_tariff_table->charge_value_type,
            $simple_tariff_table->charge_value_interval
        );

        /* 是否显示会员卡组收费表 */
        // 如果开启会员卡组收费表且查询收费表item存在时
        $is_show_member_card_group_tariff_table = $is_enable_member_card_group_tariff_table && env('IS_KIOSK_TARIFF_TABLE_PAGE_SHOW_MEMBER_GROUP', true) && filled($simple_tariff_table->item);
        if ($is_show_member_card_group_tariff_table) {
            foreach ($member_card_group_id_list as $member_card_group_id) {
                $simple_tariff_table_item = $simple_tariff_table->item->firstWhere('member_card_group_id', $member_card_group_id);
                if (blank($simple_tariff_table_item)) {
                    continue;
                }

                $generate_simple_tariff_table_item_list(
                    $simple_tariff_table_item_list,
                    $simple_tariff_table_item->rate,
                    $simple_tariff_table->charge_value_type,
                    $simple_tariff_table->charge_value_interval,
                    $member_card_group_id
                );
            }
        }
        /* 是否显示用户组收费表 */
        // 如果开启用户组收费表且查询收费表item存在时
        $is_show_user_group_tariff_table = $is_enable_user_group_tariff_table && env('IS_KIOSK_TARIFF_TABLE_PAGE_SHOW_USER_GROUP', true) && filled($simple_tariff_table->item);
        if ($is_show_user_group_tariff_table) {
            foreach ($user_group_id_list as $user_group_id) {
                $simple_tariff_table_item = $simple_tariff_table->item->firstWhere('user_group_id', $user_group_id);
                if (blank($simple_tariff_table_item)) {
                    continue;
                }

                $generate_simple_tariff_table_item_list(
                    $simple_tariff_table_item_list,
                    $simple_tariff_table_item->rate,
                    $simple_tariff_table->charge_value_type,
                    $simple_tariff_table->charge_value_interval,
                    0,
                    $user_group_id
                );
            }
        }

        foreach ($simple_tariff_table_item_list as $simple_tariff_table_item) {
            $item_type = 'defaultFee';
            $redis_key_name = 0;
            if ($simple_tariff_table_item['member_card_group_id']) {
                $item_type = 'memberCardGroupFee';
                $redis_key_name = $simple_tariff_table_item['member_card_group_id'];
            } elseif ($simple_tariff_table_item['user_group_id']) {
                $item_type = 'userGroupFee';
                $redis_key_name = $simple_tariff_table_item['user_group_id'];
            }
            $redis_key = "simpleTariffTable:{$simple_tariff_table_item['simple_tariff_table_number']}:{$item_type}:{$language_code}";
            $simple_tariff_table_number_key = $simple_tariff_table_item['simple_tariff_table_number'] . '_' . $is_enable_member_card_group_tariff_table_key . '_' . $is_enable_user_group_tariff_table_key;
            $html = Redis::hget($redis_key, $redis_key_name);
            if ($html) {
                if (isset($tariff_table_content_html_text_list[$simple_tariff_table_number_key])) {
                    $tariff_table_content_html_text_list[$simple_tariff_table_number_key] .= $html;
                } else {
                    $tariff_table_content_html_text_list[$simple_tariff_table_number_key] = $html;
                }
                continue;
            }

            // 收費表内容html
            $tariff_table_content_html_text = view("pages.$module_name.template.{$template_language_name}simpleTariffTable", [
                'language_code' => $language_code,
                'simple_tariff_table_item' => $simple_tariff_table_item,
                'item_type' => $item_type,
            ])->render();
            if (isset($tariff_table_content_html_text_list[$simple_tariff_table_number_key])) {
                $tariff_table_content_html_text_list[$simple_tariff_table_number_key] .= $tariff_table_content_html_text;
            } else {
                $tariff_table_content_html_text_list[$simple_tariff_table_number_key] = $tariff_table_content_html_text;
            }
            Redis::hset($redis_key, $redis_key_name, $tariff_table_content_html_text);
        }

        return [
            'simple_tariff_table_content_html_text_list' => $tariff_table_content_html_text_list,
        ];
    }

    // 设置时间收费表数据
    public static function setTimeTariffList(
        $time_tariff_table,
        $member_card_group_id_list = [],
        $is_enable_member_card_group_tariff_table = false,
        $is_enable_member_card_group_tariff_table_key = "DISABLE",
        $user_group_id_list = [],
        $is_enable_user_group_tariff_table = false,
        $is_enable_user_group_tariff_table_key = "DISABLE"
    ): array
    {
        global $language_code, $lms_mode, $day_type_list;
        if (blank($time_tariff_table)) {
            return [
                'time_tariff_table_content_html_text_list' => [],
            ];
        }

        $module_name = self::$module_name;
        $template_language_name = '';
        if ($language_code === 'en_US_zh_HK') {
            $template_language_name = 'en_US_zh_HK.';
        }
        $is_show_concessionary_rate = $lms_mode === LmsMode::EvenDistribution;
        $time_tariff_table_number = $time_tariff_table->time_tariff_table_number;
        $charge_value_interval = $time_tariff_table->charge_value_interval / 60;
        // 收费表html
        $tariff_table_content_html_text_list = [];

        // 生成收费表内容html函数
        $generate_tariff_table_content_html_text_list = function (
            &$tariff_table_content_html_text_list,
            $item,
            $member_card_group_id = 0,
            $user_group_id = 0,
        ) use (
            $language_code,
            $module_name,
            $template_language_name,
            $is_show_concessionary_rate,
            $time_tariff_table_number,
            $is_enable_member_card_group_tariff_table_key,
            $is_enable_user_group_tariff_table_key,
            $day_type_list,
            $charge_value_interval,
        ) {
            $item_type = 'defaultFee';
            $redis_key_name = 0;
            if ($member_card_group_id) {
                $item_type = 'memberCardGroupFee';
                $redis_key_name = $member_card_group_id;
            } elseif ($user_group_id) {
                $item_type = 'userGroupFee';
                $redis_key_name = $user_group_id;
            }
            $redis_key = "timeTariffTable:{$time_tariff_table_number}:{$item_type}:{$language_code}";
            $time_tariff_table_number_key = "{$time_tariff_table_number}_{$is_enable_member_card_group_tariff_table_key}_{$is_enable_user_group_tariff_table_key}";
            $html = Redis::hget($redis_key, $redis_key_name);
            if ($html) {
                if (isset($tariff_table_content_html_text_list[$time_tariff_table_number_key])) {
                    $tariff_table_content_html_text_list[$time_tariff_table_number_key] .= $html;
                } else {
                    $tariff_table_content_html_text_list[$time_tariff_table_number_key] = $html;
                }

                return;
            }
            $time_tariff_table_array = self::formatTimeTariffList($item);

            if (blank($time_tariff_table_array)) {
                return;
            }
            // 收費表内容html
            $tariff_table_content_html_text = view("pages.{$module_name}.template.{$template_language_name}timeTariffTable", [
                'item_type' => $item_type,
                'member_card_group_id' => $member_card_group_id,
                'user_group_id' => $user_group_id,
                'time_tariff_table_item' => $time_tariff_table_array,
                'language_code' => $language_code,
                'is_show_concessionary_rate' => $is_show_concessionary_rate,
                'charge_value_interval' => $charge_value_interval,
                'day_type_list' => $day_type_list,
            ])->render();
            if (isset($tariff_table_content_html_text_list[$time_tariff_table_number_key])) {
                $tariff_table_content_html_text_list[$time_tariff_table_number_key] .= $tariff_table_content_html_text;
            } else {
                $tariff_table_content_html_text_list[$time_tariff_table_number_key] = $tariff_table_content_html_text;
            }
            Redis::hset($redis_key, $redis_key_name, $tariff_table_content_html_text);
        };

        // 生成默认收费表内容html
        $default_item = $time_tariff_table->item?->whereNull('member_card_group_id')
            ->whereNull('user_group_id');
        $generate_tariff_table_content_html_text_list($tariff_table_content_html_text_list, $default_item);

        // 是否显示会员卡组收费表
        if ($is_enable_member_card_group_tariff_table && env('IS_KIOSK_TARIFF_TABLE_PAGE_SHOW_MEMBER_GROUP', true)) {
            $time_tariff_table_item = $time_tariff_table->item?->whereNotNull('member_card_group_id')->groupBy('member_card_group_id');
            // 循环不同会员卡组的收费数据
            foreach ($member_card_group_id_list as $member_card_group_id) {
                $item = $time_tariff_table_item[$member_card_group_id] ?? [];
                if (blank($item)) {
                    continue;
                }
                $generate_tariff_table_content_html_text_list(
                    $tariff_table_content_html_text_list,
                    $item,
                    $member_card_group_id
                );
            }
        }
        // 是否显示用户组收费表
        if ($is_enable_user_group_tariff_table && env('IS_KIOSK_TARIFF_TABLE_PAGE_SHOW_USER_GROUP', true)) {
            $time_tariff_table_item = $time_tariff_table->item?->whereNotNull('user_group_id')->groupBy('user_group_id');
            // 循环不同用户组的收费数据
            foreach ($user_group_id_list as $user_group_id) {
                $item = $time_tariff_table_item[$user_group_id] ?? [];
                if (blank($item)) {
                    continue;
                }
                $generate_tariff_table_content_html_text_list(
                    $tariff_table_content_html_text_list,
                    $item,
                    0,
                    $user_group_id
                );
            }
        }

        return [
            'time_tariff_table_content_html_text_list' => $tariff_table_content_html_text_list,
        ];
    }

    // 设置电量收费表数据
    public static function setEnergyTariffList(
        $energy_tariff_table,
        $member_card_group_id_list = [],
        $is_enable_member_card_group_tariff_table = false,
        $is_enable_member_card_group_tariff_table_key = "DISABLE",
        $user_group_id_list = [],
        $is_enable_user_group_tariff_table = false,
        $is_enable_user_group_tariff_table_key = "DISABLE"
    ): array
    {
        global $language_code, $lms_mode;
        if (blank($energy_tariff_table)) {
            return [
                'energy_tariff_table_content_html_text_list' => [],
            ];
        }

        $module_name = self::$module_name;
        $template_language_name = '';
        if ($language_code === 'en_US_zh_HK') {
            $template_language_name = 'en_US_zh_HK.';
        }
        $is_show_concessionary_rate = $lms_mode === LmsMode::EvenDistribution;
        $energy_tariff_table_number = $energy_tariff_table->energy_tariff_table_number;
        // 收费表html
        $tariff_table_content_html_text_list = [];

        // 生成收费表内容html函数
        $generate_tariff_table_content_html_text_list = function (
            &$tariff_table_content_html_text_list,
            $item,
            $member_card_group_id = 0,
            $user_group_id = 0,
        ) use (
            $language_code,
            $module_name,
            $template_language_name,
            $is_show_concessionary_rate,
            $energy_tariff_table_number,
            $is_enable_member_card_group_tariff_table_key,
            $is_enable_user_group_tariff_table_key,
        ) {
            $item_type = 'defaultFee';
            $redis_key_name = 0;
            if ($member_card_group_id) {
                $item_type = 'memberCardGroupFee';
                $redis_key_name = $member_card_group_id;
            } elseif ($user_group_id) {
                $item_type = 'userGroupFee';
                $redis_key_name = $user_group_id;
            }
            $redis_key = "energyTariffTable:{$energy_tariff_table_number}:{$item_type}:{$language_code}";
            $energy_tariff_table_number_key = "{$energy_tariff_table_number}_{$is_enable_member_card_group_tariff_table_key}_{$is_enable_user_group_tariff_table_key}";
            $html = Redis::hget($redis_key, $redis_key_name);
            if ($html) {
                if (isset($tariff_table_content_html_text_list[$energy_tariff_table_number_key])) {
                    $tariff_table_content_html_text_list[$energy_tariff_table_number_key] .= $html;
                } else {
                    $tariff_table_content_html_text_list[$energy_tariff_table_number_key] = $html;
                }

                return;
            }

            // 合并后数组对象
            $energy_array = self::formatEnergyTariffList($item);
            // 合并电量数组
            $merge_energy_tariff_list = $energy_array['merge_energy_tariff_list'];

            if (blank($merge_energy_tariff_list)) {
                return;
            }
            // 判断每一行的高峰时间的费率和非高峰时间的费率是否都相等
            $is_same_on_peak_rate_and_off_peak_rate = true;
            foreach ($merge_energy_tariff_list as $merge_energy_tariff) {
                if ($is_show_concessionary_rate) {
                    if ($merge_energy_tariff['on_peak_concessionary_rate'] !== $merge_energy_tariff['off_peak_concessionary_rate']) {
                        $is_same_on_peak_rate_and_off_peak_rate = false;
                        break;
                    }
                } else {
                    if ($merge_energy_tariff['on_peak_normal_rate'] !== $merge_energy_tariff['off_peak_normal_rate']) {
                    $is_same_on_peak_rate_and_off_peak_rate = false;
                        break;
                    }
                }
            }
            // 收費表内容html
            $tariff_table_content_html_text = view("pages.{$module_name}.template.{$template_language_name}energyTariffTable", [
                'item_type' => $item_type,
                'member_card_group_id' => $member_card_group_id,
                'user_group_id' => $user_group_id,
                'merge_energy_tariff_list' => $merge_energy_tariff_list,
                'language_code' => $language_code,
                'is_show_concessionary_rate' => $is_show_concessionary_rate,
                'is_same_on_peak_rate_and_off_peak_rate' => $is_same_on_peak_rate_and_off_peak_rate,
            ])->render();
            if (isset($tariff_table_content_html_text_list[$energy_tariff_table_number_key])) {
                $tariff_table_content_html_text_list[$energy_tariff_table_number_key] .= $tariff_table_content_html_text;
            } else {
                $tariff_table_content_html_text_list[$energy_tariff_table_number_key] = $tariff_table_content_html_text;
            }
            Redis::hset($redis_key, $redis_key_name, $tariff_table_content_html_text);
        };

        // 生成默认收费表内容html
        $default_item = $energy_tariff_table->item?->whereNull('member_card_group_id')
            ->whereNull('user_group_id');
        $generate_tariff_table_content_html_text_list($tariff_table_content_html_text_list, $default_item);

        // 是否显示会员卡组收费表
        if ($is_enable_member_card_group_tariff_table && env('IS_KIOSK_TARIFF_TABLE_PAGE_SHOW_MEMBER_GROUP', true)) {
            $energy_tariff_table_item = $energy_tariff_table->item?->whereNotNull('member_card_group_id')->groupBy('member_card_group_id');
            // 循环不同会员卡组的收费数据
            foreach ($member_card_group_id_list as $member_card_group_id) {
                $item = $energy_tariff_table_item[$member_card_group_id] ?? [];
                if (blank($item)) {
                    continue;
                }
                $generate_tariff_table_content_html_text_list(
                    $tariff_table_content_html_text_list,
                    $item,
                    $member_card_group_id
                );
            }
        }
        // 是否显示用户组收费表
        if ($is_enable_user_group_tariff_table && env('IS_KIOSK_TARIFF_TABLE_PAGE_SHOW_USER_GROUP', true)) {
            $energy_tariff_table_item = $energy_tariff_table->item?->whereNotNull('user_group_id')->groupBy('user_group_id');
            // 循环不同用户组的收费数据
            foreach ($user_group_id_list as $user_group_id) {
                $item = $energy_tariff_table_item[$user_group_id] ?? [];
                if (blank($item)) {
                    continue;
                }
                $generate_tariff_table_content_html_text_list(
                    $tariff_table_content_html_text_list,
                    $item,
                    0,
                    $user_group_id
                );
            }
        }

        return [
            'energy_tariff_table_content_html_text_list' => $tariff_table_content_html_text_list,
        ];
    }

    // 设置闲时收费表数据
    public static function setIdlingPenaltyList(
        $time_idling_tariff_table,
        $member_card_group_id_list = [],
        $is_enable_member_card_group_tariff_table = false,
        $is_enable_member_card_group_tariff_table_key = "DISABLE",
        $user_group_id_list = [],
        $is_enable_user_group_tariff_table = false,
        $is_enable_user_group_tariff_table_key = "DISABLE"
    ): array
    {
        global $language_code;
        if (blank($time_idling_tariff_table)) {
            return [
                'idling_tariff_table_content_html_text_list' => [],
            ];
        }
        $module_name = self::$module_name;
        $template_language_name = '';
        if ($language_code === 'en_US_zh_HK') {
            $template_language_name = 'en_US_zh_HK.';
        }
        $idling_penalty_tariff_table_number = $time_idling_tariff_table->idling_penalty_tariff_table_number;
        // 收费表html
        $idling_tariff_table_content_html_text_list = [];

        // 生成收费表内容html函数
        $generate_tariff_table_content_html_text_list = function (
            &$idling_tariff_table_content_html_text_list,
            $item,
            $member_card_group_id = 0,
            $user_group_id = 0,
        ) use (
            $language_code,
            $module_name,
            $template_language_name,
            $idling_penalty_tariff_table_number,
            $is_enable_member_card_group_tariff_table_key,
            $is_enable_user_group_tariff_table_key,
        ) {
            $item_type = 'defaultFee';
            $redis_key_name = 0;
            if ($member_card_group_id) {
                $item_type = 'memberCardGroupFee';
                $redis_key_name = $member_card_group_id;
            } elseif ($user_group_id) {
                $item_type = 'userGroupFee';
                $redis_key_name = $user_group_id;
            }
            $redis_key = "idlingPenaltyTariffTable:{$idling_penalty_tariff_table_number}:{$item_type}:{$language_code}";
            $idling_penalty_tariff_table_number_key = "{$idling_penalty_tariff_table_number}_{$is_enable_member_card_group_tariff_table_key}_{$is_enable_user_group_tariff_table_key}";
            $html = Redis::hget($redis_key, $redis_key_name);
            if ($html) {
                if (isset($idling_tariff_table_content_html_text_list[$idling_penalty_tariff_table_number_key])) {
                    $idling_tariff_table_content_html_text_list[$idling_penalty_tariff_table_number_key] .= $html;
                } else {
                    $idling_tariff_table_content_html_text_list[$idling_penalty_tariff_table_number_key] = $html;
                }

                return;
            }

            // 合并后数组对象
            $idling_penalty_array = self::formatIdlingPenaltyTariffList($item);
            // 合并时间数组
            $merge_idling_tariff_list = $idling_penalty_array['merge_idling_tariff_list'];

            if (blank($merge_idling_tariff_list)) {
                return;
            }
            // 判断每一行的高峰时间的费率和非高峰时间的费率是否都相等
            $is_same_on_peak_rate_and_off_peak_rate = true;
            foreach ($merge_idling_tariff_list as $merge_idling_tariff) {
                if ($merge_idling_tariff['on_peak_rate'] !== $merge_idling_tariff['off_peak_rate']) {
                    $is_same_on_peak_rate_and_off_peak_rate = false;
                    break;
                }
            }
            // 收費表内容html
            $idling_tariff_table_content_html_text = view("pages.{$module_name}.template.{$template_language_name}idlingTariffTable", [
                'item_type' => $item_type,
                'member_card_group_id' => $member_card_group_id,
                'user_group_id' => $user_group_id,
                'merge_idling_tariff_list' => $merge_idling_tariff_list,
                'language_code' => $language_code,
                'is_same_on_peak_rate_and_off_peak_rate' => $is_same_on_peak_rate_and_off_peak_rate,
            ])->render();
            if (isset($idling_tariff_table_content_html_text_list[$idling_penalty_tariff_table_number_key])) {
                $idling_tariff_table_content_html_text_list[$idling_penalty_tariff_table_number_key] .= $idling_tariff_table_content_html_text;
            } else {
                $idling_tariff_table_content_html_text_list[$idling_penalty_tariff_table_number_key] = $idling_tariff_table_content_html_text;
            }
            Redis::hset($redis_key, $redis_key_name, $idling_tariff_table_content_html_text);
        };

        // 生成默认收费表内容html
        $default_item = $time_idling_tariff_table->item?->whereNull('member_card_group_id')
            ->whereNull('user_group_id');
        $generate_tariff_table_content_html_text_list($idling_tariff_table_content_html_text_list, $default_item);

        // 是否显示会员卡组收费表
        if ($is_enable_member_card_group_tariff_table && env('IS_KIOSK_TARIFF_TABLE_PAGE_SHOW_MEMBER_GROUP', true)) {
            $time_idling_tariff_table_item = $time_idling_tariff_table->item?->whereNotNull('member_card_group_id')->groupBy('member_card_group_id');
            // 循环不同会员卡组的收费数据
            foreach ($member_card_group_id_list ?? [] as $member_card_group_id) {
                $item = $time_idling_tariff_table_item[$member_card_group_id] ?? [];
                if (blank($item)) {
                    continue;
                }
                $generate_tariff_table_content_html_text_list(
                    $idling_tariff_table_content_html_text_list,
                    $item,
                    $member_card_group_id
                );
            }
        }
        // 是否显示用户组收费表
        if ($is_enable_user_group_tariff_table && env('IS_KIOSK_TARIFF_TABLE_PAGE_SHOW_USER_GROUP', true)) {
            $time_idling_tariff_table_item = $time_idling_tariff_table->item?->whereNotNull('user_group_id')->groupBy('user_group_id');
            // 循环不同用户组的收费数据
            foreach ($user_group_id_list ?? [] as $user_group_id) {
                $item = $time_idling_tariff_table_item[$user_group_id] ?? [];
                if (blank($item)) {
                    continue;
                }
                $generate_tariff_table_content_html_text_list(
                    $idling_tariff_table_content_html_text_list,
                    $item,
                    0,
                    $user_group_id
                );
            }
        }

        return [
            'idling_tariff_table_content_html_text_list' => $idling_tariff_table_content_html_text_list,
        ];
    }

    // 获取时间收费表解析数据
    public static function formatTimeTariffList($time_tariff_table_item_list): array
    {
        $time_tariff_table_array = [];
        foreach ($time_tariff_table_item_list as $time_tariff_table_item) {
            $item = self::formatTimeTariffItem($time_tariff_table_item);
            $item['item_special'] = self::formatTimeTariffItemSpecial($time_tariff_table_item);
            $time_tariff_table_array[] = $item;
        }

        return $time_tariff_table_array;
    }

    // 获取电量收费表解析数据
    public static function formatEnergyTariffList($energy_tariff_item_list): array
    {
        // 根据时间及费率相等合并后的数组
        $merge_energy_list = [];
        // 实际收费数组
        $all_energy_list = [];
        // 实际合并数组
        $merge_energy_tariff_list = [];

        // 合并同类项
        foreach ($energy_tariff_item_list as $index => $item) {
            // 格式化时间item
            $format_item = self::formatEnergyTariffItem($item);
            // 并且添加到格式化
            $all_energy_list[] = $format_item;

            // 如果是第一位
            if ($index == 0) {
                $merge_energy_list[] = $item;
                $merge_energy_tariff_list[] = $format_item;
                continue;
            }

            // 合并时的最后一项同类不添加
            $merge_energy_list_last = end($merge_energy_list);

            if (
                $merge_energy_list_last && $merge_energy_list_last['end_range'] == $item['start_range'] &&
                $merge_energy_list_last['off_peak_normal_rate'] == $item['off_peak_normal_rate'] &&
                $merge_energy_list_last['off_peak_concessionary_rate'] == $item['off_peak_concessionary_rate'] &&
                $merge_energy_list_last['on_peak_normal_rate'] == $item['on_peak_normal_rate'] &&
                $merge_energy_list_last['on_peak_concessionary_rate'] == $item['on_peak_concessionary_rate']
            ) {
                $new_energy_last_item = $item;
                $new_energy_last_item['start_range'] = $merge_energy_list_last['start_range'];

                // 否则与上一项相同，并变更最后一项
                array_splice($merge_energy_list, -1, 1, [$new_energy_last_item]);
                // 并且添加到格式化
                array_splice($merge_energy_tariff_list, -1, 1, [self::formatEnergyTariffItem($new_energy_last_item)]);
                continue;
            }
            $merge_energy_list[] = $item;
            // 并且添加到格式化
            $merge_energy_tariff_list[] = $format_item;
        }

        return [
            'all_energy_list' => $all_energy_list,
            'merge_energy_tariff_list' => $merge_energy_tariff_list,
        ];
    }

    // 获取闲置收费表解析数据
    public static function formatIdlingPenaltyTariffList($idling_penalty_item_list): array
    {
        // 根据时间及费率相等合并后的数组
        $merge_idling_list = [];
        // 实际收费数组
        $all_idling_tariff_list = [];
        // 实际合并数组
        $merge_idling_tariff_list = [];

        // 合并同类项
        foreach ($idling_penalty_item_list as $index => $item) {
            // 格式化时间item
            $format_item = self::formatIdlingPenaltyTariffItem($item);
            // 并且添加到格式化
            $all_idling_tariff_list[] = $format_item;

            if ($index == 0) {
                $merge_idling_list[] = $item;
                $merge_idling_tariff_list[] = $format_item;
                continue;
            }

            // 合并时的最后一项同类不添加
            $merge_idling_list_last = end($merge_idling_list);

            if (
                $merge_idling_list_last && $merge_idling_list_last['end_range'] == $item['start_range'] &&
                $merge_idling_list_last['on_peak_rate'] == $item['on_peak_rate'] &&
                $merge_idling_list_last['off_peak_rate'] == $item['off_peak_rate']
            ) {
                $new_idling_list_last_item = $item;
                $new_idling_list_last_item['start_range'] = $merge_idling_list_last['start_range'];

                // 否则与上一项相同，并变更最后一项
                array_splice($merge_idling_list, -1, 1, [$new_idling_list_last_item]);
                // 并且添加到格式化
                array_splice($merge_idling_tariff_list, -1, 1, [self::formatIdlingPenaltyTariffItem($new_idling_list_last_item)]);
                continue;
            }
            $merge_idling_list[] = $item;
            // 并且添加到格式化
            $merge_idling_tariff_list[] = $format_item;
        }

        return [
            'all_idling_tariff_list' => $all_idling_tariff_list,
            'merge_idling_tariff_list' => $merge_idling_tariff_list,
        ];
    }

    public static function setPeakTime($peak_time_table): array {
        $peak_time_table_json_list = [];

        if (blank($peak_time_table)) {
            return $peak_time_table_json_list;
        }

        $peak_time_table_item = $peak_time_table->item;

        if (blank($peak_time_table_item)) {
            return $peak_time_table_json_list;
        }

        $redis_key = "peakTimeTable:{$peak_time_table->peak_time_table_number}";
        // 高峰时间表已经移除member_card_group_id字段
        $redis_key_name = 0; // 默认数据

        $peak_time_table_json = Redis::hget($redis_key, $redis_key_name);
        if ($peak_time_table_json) {
            $peak_time_table_json_list[$peak_time_table->peak_time_table_number] = json_decode($peak_time_table_json, true);
            return $peak_time_table_json_list;
        }

        // 根据day_type分组
        $day_type_item_list = $peak_time_table_item->groupBy('day_type')->all();
        $common_item = collect($day_type_item_list[''] ?? []);
        // 移除common_item的day_type
        $common_item = $common_item->sortBy('start_time')->toArray();
        foreach ($common_item as &$common) {
            unset($common['day_type']);
        }
        // 判断数据是否完全一致
        $is_same = false;
        // 如果有多个day type或者common_item的数据多于三条，改为table显示
        if ((count($day_type_item_list) > 1) || (count($common_item) > 3)) {
            foreach ($day_type_item_list as $day_type_item) {
                $day_type_item = $day_type_item->sortBy('start_time')->toArray();
                foreach ($day_type_item as &$day_type_item_item) {
                    unset($day_type_item_item['day_type']);
                }
            }
        } else {
            $is_same = true;
        }
        // 如果只有default数据且数据不多于三条，输出common_item
        if ($is_same) {
            $peak_time_table_json_list[$peak_time_table->peak_time_table_number] = ['common' => $common_item];
        } else {
            $peak_time_table_json_list[$peak_time_table->peak_time_table_number] = $day_type_item_list;
        }
        Redis::hset($redis_key, $redis_key_name, json_encode($peak_time_table_json_list[$peak_time_table->peak_time_table_number]));

        return $peak_time_table_json_list;
    }

    /**
     * 通过高峰时间表拿到对应的非高峰时间表
     *
     * @param [type] $peak_time_table
     * @return array
     * @Description
     * @example
     * @date 2023-10-11
     */
    public static function setOffPeakTime($peak_time_table): array
    {
        $off_peak_time_table = [];
        // 定义一天的总秒数
        $total_time_slots = 24 * 60 * 60;
        if (isset($peak_time_table['common'])) {
            // 如果存在common，则只计算一次common对应的off_peak_time_table
            $peak_time_item = $peak_time_table['common'];
            $off_peak_time_table['common'] = self::getDayOffPeakTime($peak_time_item, $total_time_slots);
            return $off_peak_time_table;
        }
        // 遍历每一天的繁忙时段区间
        foreach ($peak_time_table as $day_type => $peak_time_item) {
            $off_peak_time_table[$day_type] = self::getDayOffPeakTime($peak_time_item, $total_time_slots);
        }

        return $off_peak_time_table;
    }

    /**
     * 通过一天的高峰时间表拿到对应的非高峰时间表
     *
     * @param array $peak_time_item
     * @param int $total_time_slots
     * @return array
     * @Description
     * @example
     * @date 2023-10-11
     */
    public static function getDayOffPeakTime($peak_time_item, $total_time_slots): array
    {
        $off_peak_time_table_item = [];
        $start_time = 0;
        foreach ($peak_time_item as $item) {
            $end_time = $item['start_time'];
            if ($end_time > $start_time) {
                $off_peak_time_table_item[] = [
                    'start_time' => $start_time,
                    'end_time' => $end_time,
                ];
            }
            $start_time = $item['end_time'];
        }
        // 如果最后一个高峰时间段的结束时间小于一天的总秒数，则添加一个非高峰时间段，但是如果是23:59，则不添加
        if ($start_time < $total_time_slots && $start_time !== ($total_time_slots - 60)) {
            $off_peak_time_table_item[] = [
                'start_time' => $start_time,
                'end_time' => $total_time_slots,
            ];
        }

        return $off_peak_time_table_item;
    }

    public static function formatTimeTariffItem($item): array
    {
        return [
            'day_type' => $item['day_type'],
            'default_normal_rate' => bcdiv($item['default_normal_rate'], 100, 1),
            'default_concessionary_rate' => bcdiv($item['default_concessionary_rate'], 100, 1),
            'item_special' => [],
        ];
    }

    public static function formatTimeTariffItemSpecial($time_tariff_table_item): array
    {
        $item_special_list = [];
        foreach ($time_tariff_table_item->itemSpecial as $index => $item_special) {
            $start_time = '00:00';
            $end_time = '00:00';

            if (filled($item_special->start_time)) {
                $start_time = self::convertSecondsToTime($item_special->start_time);
            }
            if (filled($item_special->end_time)) {
                $end_time = self::convertSecondsToTime($item_special->end_time);
            }

            // 如果开始时间不为0,且开始时间和上一条数据的结束时间不相等，则添加一条数据
            $last_item_special_end_time = end($item_special_list)['end_time'] ?? '00:00';
            if ($start_time !== '00:00' && $start_time !== $last_item_special_end_time) {
                $item_special_list[] = [
                    'start_time' => $last_item_special_end_time, // 上一条的结束时间即是本条的开始时间
                    'end_time' => $start_time, // 当前循环的这条的开始时间即是本条的结束时间
                    'normal_rate' => bcdiv($time_tariff_table_item->default_normal_rate, 100, 1),
                    'concessionary_rate' => bcdiv($time_tariff_table_item->default_concessionary_rate, 100, 1),
                ];
            }

            $item_special_list[] = [
                'start_time' => $start_time,
                'end_time' => $end_time,
                'normal_rate' => bcdiv($item_special['normal_rate'], 100, 1),
                'concessionary_rate' => bcdiv($item_special['concessionary_rate'], 100, 1),
            ];
        }
        // 补足最后一条
        $last_item_special_end_time = $item_special_list[count($item_special_list) - 1]['end_time'] ?? '00:00';
        if ($last_item_special_end_time !== '24:00') {
            $item_special_list[] = [
                'start_time' => $last_item_special_end_time, // 上一条的结束时间即是本条的开始时间
                'end_time' => '24:00', // 当前循环的这条的开始时间即是本条的结束时间
                'normal_rate' => bcdiv($time_tariff_table_item->default_normal_rate, 100, 1),
                'concessionary_rate' => bcdiv($time_tariff_table_item->default_concessionary_rate, 100, 1),
            ];
        }

        return $item_special_list;
    }

    // 格式化电量收费表项
    public static function formatEnergyTariffItem($item): array
    {
        return [
            'start_range' => (float)bcdiv($item['start_range'], 1000, 3),
            'end_range' => (float)bcdiv($item['end_range'], 1000, 3),
            'off_peak_normal_rate' => bcdiv($item['off_peak_normal_rate'], 100, 1),
            'off_peak_concessionary_rate' => bcdiv($item['off_peak_concessionary_rate'], 100, 1),
            'on_peak_normal_rate' => bcdiv($item['on_peak_normal_rate'], 100, 1),
            'on_peak_concessionary_rate' => bcdiv($item['on_peak_concessionary_rate'], 100, 1),
        ];
    }

    // 格式化闲置收费表项
    public static function formatIdlingPenaltyTariffItem($item): array
    {
        return [
            'start_range' => $item['start_range'] / 60,
            'end_range' => $item['end_range'] / 60,
            'on_peak_rate' => bcdiv($item['on_peak_rate'], 100, 1),
            'off_peak_rate' => bcdiv($item['off_peak_rate'], 100, 1),
        ];
    }

    public static function convertSecondsToTime($seconds)
    {
        if ($seconds >= 86400) { // 86400秒等于24小时
            return '24:00';
        }
        return gmdate("H:i", $seconds);
    }
}

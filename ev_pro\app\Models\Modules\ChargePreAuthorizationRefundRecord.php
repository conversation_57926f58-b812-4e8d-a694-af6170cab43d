<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;

class ChargePreAuthorizationRefundRecord extends Model
{

    protected $table = 'charge_pre_authorization_refund_record';
    protected $primaryKey = 'charge_pre_authorization_refund_record_id';

    const CREATED_AT = 'gmt_create'; //默认是小写的模型类名复数格式，此处为自定义表名
    const UPDATED_AT = 'gmt_modified'; //定义主键，默认为id


    // protected $fillable = [];
    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 类型转化器
     */
    protected $casts = [

    ];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [

    ];

    /**
     * 全部字段
     *
     * @var array
     */
    public array $columns = [
        'charge_pre_authorization_refund_record_id',
        'charge_pre_authorization_refund_record_number',
        'charge_pre_authorization_record_number',
        'merchant_number',
        'site_number',
        'payment_device',
        'refund_amount',
        'actual_refund_amount',
        'identity_type',
        'identity_number',
        'kiosk_number',
        'kiosk_name',
        'octopus_transaction_id',
        'octopus_receipt_number',
        'gmt_octopus_refund',
        'octopus_fund_type',
        'octopus_device_number',
        'octopus_card_type',
        'octopus_card_number',
        'octopus_raw_card_number',
        'octopus_balance',
        'octopus_last_added_value_type',
        'octopus_last_added_value_date',
        'octopus_response_json',
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    // 一对一关联预授权记录
    public function chargePreAuthorizationRecord()
    {
        return $this->hasOne(ChargePreAuthorizationRecord::class, 'charge_pre_authorization_record_number', 'charge_pre_authorization_record_number');
    }

}

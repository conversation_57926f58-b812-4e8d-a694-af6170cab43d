<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Common\CommonController;
use Debugbar;
use Illuminate\Http\{
    JsonResponse,
    Request,
};
use App\Models\Modules\{
    Viosk,
    PublicHoliday,
    AdminOctopusCard,
    Site,
    Zone,
    ConnectorSetting,
    ChargeRecord,
    ChargeRecordChargedEnergy,
    ChargeRecordMeterValue,
    ChargeRecordTariffRule,
    ChargeRecordTrialCharge,
    ChargePaymentRecord,
    ChargePaymentRecordCalculation,
    Merchant,
    ChargePreAuthorizationRecord,
    Connector,
    EventLog,
};
use App\Enums\{
    ChargeValueType,
    ChargeTariffScheme,
};
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Redis;

class VioskController extends CommonController
{
    public function __construct(Request $request)
    {
        parent::__construct($request);
        Debugbar::disable();
    }

    /**
     * 初始化
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function initialization(Request $request): JsonResponse
    {
        $viosk_number = $request->viosk_number;
        $current_build_version = $request->current_build_version;

        if (blank($viosk_number)) {
            $this->missingField('viosk_number');
            return $this->returnJson();
        }

        $viosk = Viosk::where('viosk_number', $viosk_number)->first();
        if (blank($viosk)) {
            $this->notFoundData('viosk');
            return $this->returnJson();
        }

        /* 初始化一些数据为null */
        $this->data['admin_octopus_card'] = null;
        $this->data['charge_point'] = null;
        $this->data['connector'] = null;
        $this->data['connector_setting'] = null;
        $this->data['viosk_payment_method'] = null;
        $this->data['time_tariff_table'] = null;
        $this->data['simple_tariff_table'] = null;
        $this->data['energy_tariff_table'] = null;
        $this->data['idling_penalty_tariff_table'] = null;
        $this->data['connector_setting_trial_charge_rule'] = null;
        $this->data['peak_time_table'] = null;
        $this->data['merchant'] = null;
        $this->data['site'] = null;
        $this->data['zone'] = null;
        $this->data['public_holiday'] = null;

        $this->data['viosk_setting'] = null;
        $this->data['merchant_number'] = null;
        $this->data['site_number'] = null;
        $this->data['zone_number'] = null;

        /* 系统当前时间 */
        $this->data['system_date_time'] = now()->format('Y-m-d H:i:s');
        /* CMS WebSocket URL */
        $this->data['cms_websocket_url'] = 'ws://' . config('app.websocket_host') . ':' . env('WEBSOCKET_KIOSK_PORT');

        /* 更新当前版本 */
        if (filled($current_build_version)) {
            $viosk->current_build_version = $current_build_version;
            $viosk->save();
        }

        // 支援图片
        $language_code_list = config('languages');
        $support_image_type_list = ['START_CHARGING', 'STOP_CHARGING'];
        $support_payment_method_list = ['OCTOPUS', 'CREDIT_CARD'];
        $support_image_list = [];
        foreach ($language_code_list as $language_code => $language_description) {
            foreach ($support_payment_method_list as $support_payment_method) {
                foreach (ChargeTariffScheme::asSelectArray() as $value => $name) {
                    foreach ($support_image_type_list as $support_image_type) {
                        $support_payment_method_to_lower = strtolower($support_payment_method);
                        $charge_tariff_scheme_to_lower = strtolower($value);
                        $support_image_type_to_lower = strtolower($support_image_type);
                        $image_path = "images/viosk_support/{$support_payment_method_to_lower}/support_{$charge_tariff_scheme_to_lower}_{$support_image_type_to_lower}_{$language_code}.png";
                        if (!is_file(public_path($image_path))) {
                            #TODO: 需要处理图片不存在的情况
                            // continue;
                        }
                        $support_image_list[] = [
                            'language_code' => $language_code,
                            'charge_tariff_scheme' => $value,
                            'type' => $support_image_type,
                            'viosk_payment_method' => $support_payment_method,
                            'image_url' => asset($image_path),
                        ];
                    }
                }
            }
        }
        $this->data['support_image_list'] = $support_image_list;

        /* 返回Viosk数据 */
        $this->data['viosk_id'] = $viosk->viosk_id;
        $this->data['viosk_number'] = $viosk->viosk_number;
        $this->data['license_code'] = $viosk->license_code;
        $this->data['site_number'] = $viosk->site_number;
        $this->data['zone_number'] = $viosk->zone_number;
        $this->data['merchant_number'] = $viosk->merchant_number;
        $this->data['name'] = $viosk->name;
        $this->data['viosk_setting_number'] = $viosk->viosk_setting_number;
        $this->data['charge_point_number'] = $viosk->charge_point_number;
        $this->data['ip_address'] = $viosk->ip_address;
        $this->data['current_build_version'] = $viosk->current_build_version;
        $this->data['charge_point_com_port'] = $viosk->charge_point_com_port;
        $this->data['charge_point_baud_rate'] = $viosk->charge_point_baud_rate;
        $this->data['octopus_com_port'] = $viosk->octopus_com_port;
        $this->data['octopus_baud_rate'] = $viosk->octopus_baud_rate;
        $this->data['gmt_octopus_last_upload'] = $viosk->gmt_octopus_last_upload;
        $this->data['gmt_octopus_last_download'] = $viosk->gmt_octopus_last_download;
        $this->data['pos_vendor'] = $viosk->pos_vendor;
        $this->data['yedpay_pos_api_url'] = $viosk->yedpay_pos_api_url;
        $this->data['yedpay_pos_api_key'] = $viosk->yedpay_pos_api_key;
        $this->data['yedpay_pos_pre_authorization_amount'] = $viosk->yedpay_pos_pre_authorization_amount;
        $this->data['soe_pay_pos_com_port'] = $viosk->soe_pay_pos_com_port;
        $this->data['soe_pay_pos_baud_rate'] = $viosk->soe_pay_pos_baud_rate;
        $this->data['soe_pay_pos_api_key'] = $viosk->soe_pay_pos_api_key;
        $this->data['soe_pay_pos_api_token'] = $viosk->soe_pay_pos_api_token;
        $this->data['soe_pay_pos_pre_authorization_amount'] = $viosk->soe_pay_pos_pre_authorization_amount;


        $merchant_number = $viosk->merchant_number; // 商户编号
        $site_number = $viosk->site_number; // 场地编号
        /* Public Holiday */
        $this->data['public_holiday'] = $this->getArray(PublicHoliday::where('site_number', $site_number)->oldest('start_date')->get()->toArray());

        /* 管理员八达通卡 */
        $this->data['admin_octopus_card'] = $this->getArray(AdminOctopusCard::where('site_number', $site_number)->where('is_enable', 1)->pluck('octopus_card_number'));

        /* 场地 */
        $this->data['site'] = Site::where('site_number', $site_number)->first();
        if (filled($this->data['site'])) {
            $this->data['site'] = $this->data['site']->toArray();
            $this->data['site']['main_image_url'] = existsImage('public', $this->data['site']['main_image_url']);
            $this->data['site']['site_name'] = self::getValueFromLanguageArray($this->data['site']['name_json']);
            $this->data['site']['name_json'] = json_encode($this->data['site']['name_json']) ?: null;
        }


        /* 区域 */
        $this->data['zone'] = Zone::where('site_number', $site_number)->with('description')->first();
        if (filled($this->data['zone'])) {
            $this->data['zone'] = $this->data['zone']->toArray();
            $this->data['zone']['description_list'] = $this->data['zone']['description'];
            $this->data['zone']['zone_name'] = self::getValueFromLanguageArray($this->data['zone']['name_json']);
            $this->data['zone']['name_json'] = json_encode($this->data['zone']['name_json']) ?: null;
            unset($this->data['zone']['description']);
        }

        /* 商戶 */
        $this->data['merchant'] = Merchant::where('merchant_number', $merchant_number)->first();
        if (filled($this->data['merchant'])) {
            $this->data['merchant'] = $this->data['merchant']->toArray();
            $this->data['merchant']['merchant_name'] = self::getValueFromLanguageArray($this->data['merchant']['name_json']);
            $this->data['merchant']['logo_image_url'] = existsImage('public', $this->data['merchant']['logo_image_url']);
            $this->data['merchant']['member_card_background_image_url'] = existsImage('public', $this->data['merchant']['member_card_background_image_url']);
            $this->data['merchant']['name_json'] = json_encode($this->data['merchant']['name_json']) ?: null;
        }

        /* Viosk Payment Method */
        $this->data['viosk_payment_method'] = $this->getArray($viosk->vioskPaymentMethod()->where('is_enable', 1)->pluck('payment_method'));

        /* 获取充电桩和中央服务器和充电枪 */
        $connector_setting_number_list = []; // 充电枪用到的配置ID
        // 充电桩
        $charge_point = $viosk->chargePoint()->with(relations: ['connector.description', 'comServer'])->first();
        $this->data['charge_point'] = $charge_point->makeHidden(['connector', 'comServer']);
        $this->data['charge_point']->com_server = $charge_point->comServer;
        // 充电枪
        foreach ($charge_point->connector->sortBy('sort_order') as $connector) {
            if (!in_array($connector->connector_setting_number, $connector_setting_number_list)) {
                $connector_setting_number_list[] = $connector->connector_setting_number;
            }
            $connector->site_number = $charge_point->site_number;
            // 充电枪
            $connector->description_list = $connector->description;
            unset($connector->description);
            $this->data['connector'][] = $connector;
        }

        /* 充电枪配置 */
        if (filled($connector_setting_number_list)) {
            // 查询充电枪配置及其关联收费方案
            $connector_setting_list = ConnectorSetting::with('trialChargeRule')
                ->whereIn('connector_setting_number', $connector_setting_number_list)
                ->oldest('sort_order')
                ->get();
            // 试充规则
            $connector_setting_list->each(function ($item) {
                $item->connector_setting_trial_charge_rule = $item->trialChargeRule;
                unset($item->trialChargeRule);
            });
            // 预加载connector_setting关联的收费方案
            $connector_setting_list->load([
                'timeTariffTable' => [
                    'item' => [
                        // itemSpecial 按照时间排序
                        'itemSpecial' => function ($query) {
                            $query->oldest('start_time');
                        }
                    ]
                ],
                'simpleTariffTable' => [
                    'item' => function ($query) {
                        $query->oldest('gmt_create');
                    }
                ],
                'energyTariffTable' => [
                    'item' => function ($query) {
                        $query->oldest('start_range');
                    }
                ],
                'idlingPenaltyTariffTable' => [
                    'item' => function ($query) {
                        $query->oldest('start_range');
                    }
                ],
                'peakTimeTable' => [
                    'item' => function ($query) {
                        $query->oldest('start_time');
                    }
                ],
            ]);
            /* 处理收费方案 */
            foreach ($connector_setting_list as $connector_setting) {
                // 时间收费方案
                if (filled($connector_setting->timeTariffTable)) {
                    $time_tariff_table_item = null;
                    foreach ($connector_setting->timeTariffTable->item as $item) {
                        $item->time_tariff_table_item_special_list = $this->getArray($item->itemSpecial);
                        $time_tariff_table_item[] = $item;
                    }
                    $connector_setting->timeTariffTable->time_tariff_table_item = $time_tariff_table_item;
                    unset($connector_setting->timeTariffTable->item);
                    $this->data['time_tariff_table'][$connector_setting->timeTariffTable->time_tariff_table_id] = $connector_setting->timeTariffTable->makeHidden(['time_tariff_table_item_json', 'member_card_group_time_tariff_table_item_json']);
                }
                // 简单收费方案
                if (filled($connector_setting->simpleTariffTable)) {
                    $connector_setting->simpleTariffTable->simple_tariff_table_item = $this->getArray($connector_setting->simpleTariffTable->item);
                    unset($connector_setting->simpleTariffTable->item);
                    $this->data['simple_tariff_table'][$connector_setting->simpleTariffTable->simple_tariff_table_id] = $connector_setting->simpleTariffTable->makeHidden(['member_card_group_simple_tariff_table_item_json']);
                    // 电量收费方案描述
                    $connector_setting->tariff_table_description = null;
                    if ($connector_setting->simpleTariffTable->charge_value_type === ChargeValueType::Energy) {
                        $connector_setting->tariff_table_description = [
                            [
                                'title' => __('api.viosk_tariff_table_title', [], 'en_US'),
                                'name' => __('api.viosk_tariff_table_name', [], 'en_US'),
                                'fee' => __('common.unit_hk', [], 'en_US') . (float)bcdiv($connector_setting->simpleTariffTable->rate, 100, 1),
                                'unit' => ' / ' . (float)bcdiv($connector_setting->simpleTariffTable->charge_value_interval, 1000, 3) . __('common.unit_kwh', [], 'en_US'),
                                'language_code' => 'en_US',
                                'sort_order' => $connector_setting->simpleTariffTable->sort_order
                            ],
                            [
                                'title' => __('api.viosk_tariff_table_title', [], 'zh_HK'),
                                'name' => __('api.viosk_tariff_table_name', [], 'zh_HK'),
                                'fee' => __('common.unit_hk', [], 'zh_HK') . (float)bcdiv($connector_setting->simpleTariffTable->rate, 100, 1),
                                'unit' => ' / ' . (float)bcdiv($connector_setting->simpleTariffTable->charge_value_interval, 1000, 3) . __('common.unit_kwh', [], 'zh_HK'),
                                'language_code' => 'zh_HK',
                                'sort_order' => $connector_setting->simpleTariffTable->sort_order
                            ]
                        ];
                    }
                }
                // 电量收费方案
                if (filled($connector_setting->energyTariffTable)) {
                    $connector_setting->energyTariffTable->energy_tariff_table_item = $this->getArray($connector_setting->energyTariffTable->item);
                    unset($connector_setting->energyTariffTable->item);
                    $this->data['energy_tariff_table'][$connector_setting->energyTariffTable->energy_tariff_table_id] = $connector_setting->energyTariffTable->makeHidden(['energy_tariff_table_item_json', 'member_card_group_energy_tariff_table_item_json']);
                }
                // 閒置罰款
                if (filled($connector_setting->idlingPenaltyTariffTable)) {
                    $connector_setting->idlingPenaltyTariffTable->idling_penalty_tariff_table_item = $this->getArray($connector_setting->idlingPenaltyTariffTable->item);
                    unset($connector_setting->idlingPenaltyTariffTable->item);
                    $this->data['idling_penalty_tariff_table'][$connector_setting->idlingPenaltyTariffTable->idling_penalty_tariff_table_id] = $connector_setting->idlingPenaltyTariffTable->makeHidden(['idling_penalty_tariff_table_item_json', 'member_card_group_idling_penalty_tariff_table_item_json']);
                }
                // 高峰時間表
                if (filled($connector_setting->peakTimeTable)) {
                    $connector_setting->peakTimeTable->peak_time_table_item = $this->getArray($connector_setting->peakTimeTable->item);
                    unset($connector_setting->peakTimeTable->item);
                    $this->data['peak_time_table'][$connector_setting->peakTimeTable->peak_time_table_id] = $connector_setting->peakTimeTable->makeHidden(['peak_time_table_item_json']);
                }
                // 去除不必要的关联和字段
                unset($connector_setting->timeTariffTable);
                unset($connector_setting->simpleTariffTable);
                unset($connector_setting->energyTariffTable);
                unset($connector_setting->idlingPenaltyTariffTable);
                unset($connector_setting->peakTimeTable);
            }

            /* 重置键名 */
            $this->data['time_tariff_table'] = !is_null($this->data['time_tariff_table']) ? array_values($this->data['time_tariff_table']) : null;
            $this->data['simple_tariff_table'] = !is_null($this->data['simple_tariff_table']) ? array_values($this->data['simple_tariff_table']) : null;
            $this->data['energy_tariff_table'] = !is_null($this->data['energy_tariff_table']) ? array_values($this->data['energy_tariff_table']) : null;
            $this->data['idling_penalty_tariff_table'] = !is_null($this->data['idling_penalty_tariff_table']) ? array_values($this->data['idling_penalty_tariff_table']) : null;
            $this->data['peak_time_table'] = !is_null($this->data['peak_time_table']) ? array_values($this->data['peak_time_table']) : null;

            $this->data['connector_setting'] = $this->getArray($connector_setting_list->toArray());
        }

        /* Viosk Setting */
        if (filled($viosk->vioskSetting)) {
            // Viosk Setting Description
            $viosk->vioskSetting->description_list = array();
            $viosk_setting_description_result = $viosk->vioskSetting->description;
            if (filled($viosk_setting_description_result)) {
                // 预留处理函数，方便后续添加处理图片或HTML
                $func = function (array $data) use ($viosk): array {
                    $data['main_logo_image_url'] = existsImage('public', $data['main_logo_image_url']);
                    $data['home_page_image_url'] = existsImage('public', $data['home_page_image_url']);
                    return $data;
                };
                $viosk->vioskSetting->description_list = array_map($func, $viosk_setting_description_result->toArray());
            }
            unset($viosk->vioskSetting->description);
            $this->data['viosk_setting'] = $viosk->vioskSetting;
        }
        unset($viosk->vioskSetting->description);

        // Viosk Setting Screen Time
        $viosk->vioskSetting->screen_time_list = null;
        if (isset($viosk->vioskSetting->screenTime)) {
            $viosk->vioskSetting->screen_time_list = $this->getArray($viosk->vioskSetting->screenTime);
        }
        unset($viosk->vioskSetting->screenTime);

        /* Viosk Setting */
        $this->data['viosk_setting'] = $viosk->vioskSetting;

        return $this->returnJson();
    }

    /**
     * 检查viosk心跳
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function heartbeat(Request $request): JsonResponse
    {
        $data = $request->input('data');

        $data = json_decode($data ?? null, true);

        $viosk_number = $data['viosk_number'] ?? null;
        $connector_setting_token_map = $data['connector_setting_token_map'] ?? [];


        $current_time = now();
        $this->data['current_time'] = $current_time->format('Y-m-d H:i:s');

        if (blank($viosk_number)) {
            return $this->returnJson();
        }

        $viosk = Viosk::firstWhere('viosk_number', $viosk_number);
        if (blank($viosk)) {
            return $this->returnJson();
        }
        $viosk->gmt_last_alive = $current_time;
        $viosk->save();

        // 从Redis获取viosk_action_type_list
        $viosk_action_type_list = null;
        $redis_viosk_action_type_list = Redis::get('viosk_action_type_list:' . $viosk_number);
        if (filled($redis_viosk_action_type_list)) {
            $viosk_action_type_list = json_decode($redis_viosk_action_type_list, true) ?: null;
            // 清除Redis
            Redis::del('viosk_action_type_list:' . $viosk_number);
        }
        // 从Redis获取need_deduct_charge_pre_authorization_record_number_list
        $need_deduct_charge_pre_authorization_record_number_list = null;
        $redis_need_deduct_charge_pre_authorization_record_number_list = Redis::get('need_deduct_charge_pre_authorization_record_number_list:' . $viosk_number);
        if (filled($redis_need_deduct_charge_pre_authorization_record_number_list)) {
            $need_deduct_charge_pre_authorization_record_number_list = json_decode($redis_need_deduct_charge_pre_authorization_record_number_list, true) ?: null;
            // 清除Redis
            Redis::del('need_deduct_charge_pre_authorization_record_number_list:' . $viosk_number);
        }

        // 从Redis获取need_upload_log_file_request_list
        $need_upload_log_file_request_list = null;
        $redis_need_upload_log_file_request_list = Redis::get('need_upload_log_file_request_list:' . $viosk_number);
        if (filled($redis_need_upload_log_file_request_list)) {
            $need_upload_log_file_request_list = json_decode($redis_need_upload_log_file_request_list, true) ?: null;
            // 清除Redis
            Redis::del('need_upload_log_file_request_list:' . $viosk_number);
        }

        // 从Redis获取need_delete_app_months_ago_record
        $need_delete_app_months_ago_record = null;
        $redis_need_delete_app_months_ago_record = Redis::get('need_delete_app_months_ago_record:' . $viosk_number);
        if (filled($redis_need_delete_app_months_ago_record)) {
            $need_delete_app_months_ago_record = $redis_need_delete_app_months_ago_record ?? null;
            // 清除Redis
            Redis::del('need_delete_app_months_ago_record:' . $viosk_number);
        }

        // 校验viosk关联的charge_point关联的connector的setting_token是否一致
        foreach ($viosk->chargePoint->connector as $connector) {
            $setting_token = $connector->setting_token;
            if (!isset($connector_setting_token_map[$connector->connector_number]) || $connector_setting_token_map[$connector->connector_number] !== $setting_token) {
                $viosk_action_type_list[] = 'ReloadInitializationDataNotification';
                break;
            }
        }

        // 去重
        $this->data['viosk_action_type_list'] = $viosk_action_type_list ? array_unique($viosk_action_type_list) : null;
        $this->data['need_deduct_charge_pre_authorization_record_number_list'] = $need_deduct_charge_pre_authorization_record_number_list ? array_unique($need_deduct_charge_pre_authorization_record_number_list) : null;
        $this->data['need_upload_log_file_request_list'] = $need_upload_log_file_request_list ? array_unique($need_upload_log_file_request_list) : null;
        $this->data['need_delete_app_months_ago_record'] = !is_null($need_delete_app_months_ago_record) ? $need_delete_app_months_ago_record : null;

        return $this->returnJson();
    }

    public function syncChargeRecord(Request $request)
    {
        $data = $request->input('data');

        $data = json_decode($data ?? null, true);

        // 解析失败或者无数据
        if (is_null($data) || empty($data)) {
            $this->missingField('data');
            return $this->returnJson();
        }
        // charge_record_number必填
        if (!isset($data['charge_record_number']) || blank($data['charge_record_number'])) {
            $this->missingField('charge_record_number');
            return $this->returnJson();
        }
        // server_type必填
        if (!isset($data['server_type']) || blank($data['server_type'])) {
            $this->missingField('server_type');
            return $this->returnJson();
        }
        // com_server_number必填
        if (!isset($data['com_server_number']) || blank($data['com_server_number'])) {
            $this->missingField('com_server_number');
            return $this->returnJson();
        }
        // site_number必填
        if (!isset($data['site_number']) || blank($data['site_number'])) {
            $this->missingField('site_number');
            return $this->returnJson();
        }
        // charge_point_number必填
        if (!isset($data['charge_point_number']) || blank($data['charge_point_number'])) {
            $this->missingField('charge_point_number');
            return $this->returnJson();
        }
        // connector_number必填
        if (!isset($data['connector_number']) || blank($data['connector_number'])) {
            $this->missingField('connector_number');
            return $this->returnJson();
        }
        // lms_mode必填
        if (!isset($data['lms_mode']) || blank($data['lms_mode'])) {
            $this->missingField('lms_mode');
            return $this->returnJson();
        }
        // charge_tariff_scheme必填
        if (!isset($data['charge_tariff_scheme']) || blank($data['charge_tariff_scheme'])) {
            $this->missingField('charge_tariff_scheme');
            return $this->returnJson();
        }
        // charge_value_type必填
        if (!isset($data['charge_value_type']) || blank($data['charge_value_type'])) {
            $this->missingField('charge_value_type');
            return $this->returnJson();
        }
        // tariff_table_type必填
        if (!isset($data['tariff_table_type']) || blank($data['tariff_table_type'])) {
            $this->missingField('tariff_table_type');
            return $this->returnJson();
        }
        // gmt_start必填
        if (!isset($data['gmt_start']) || blank($data['gmt_start'])) {
            $this->missingField('gmt_start');
            return $this->returnJson();
        }

        unset($data['charge_record_id']);

        // json转为数组
        $data['merchant_name'] = json_decode($data['merchant_name'] ?? null, true) ?? null;
        $data['site_name'] = json_decode($data['site_name'] ?? null, true) ?? null;
        $data['zone_name'] = json_decode($data['zone_name'] ?? null, true) ?? null;
        $data['member_card_group_name'] = json_decode($data['member_card_group_name'] ?? null, true) ?? null;

        $charge_record = ChargeRecord::firstOrNew([
            'charge_record_number' => $data['charge_record_number'],
        ]);
        $data = Arr::only($data, $charge_record->columns);
        $charge_record->fill($data);
        $result = $charge_record->save();

        $this->data = $result;

        return $this->returnJson();
    }

    public function syncChargeRecordChargedEnergy(Request $request)
    {
        $data = $request->input('data');

        $data = json_decode($data ?? null, true);

        // 解析失败或者无数据
        if (is_null($data) || empty($data)) {
            $this->missingField('data');
            return $this->returnJson();
        }
        // charge_record_number必填
        if (!isset($data['charge_record_number']) || blank($data['charge_record_number'])) {
            $this->missingField('charge_record_number');
            return $this->returnJson();
        }

        $charge_record_charged_energy = ChargeRecordChargedEnergy::firstOrNew([
            'charge_record_number' => $data['charge_record_number'],
        ]);
        $charge_record_charged_energy->charged_energy_item_json = json_decode($data['charged_energy_item_json'] ?? null, true) ?? null; // 已充电量项JSON
        $charge_record_charged_energy->gmt_sync_to_cloud = $data['gmt_sync_to_cloud'] ?? null; // 同步至云端时间
        $result = $charge_record_charged_energy->save();

        $this->data = $result;

        return $this->returnJson();
    }

    public function syncChargeRecordMeterValue(Request $request)
    {
        $data = $request->input('data');

        $data = json_decode($data ?? null, true);

        // 解析失败或者无数据
        if (is_null($data) || empty($data)) {
            $this->missingField('data');
            return $this->returnJson();
        }
        // charge_record_number必填
        if (!isset($data['charge_record_number']) || blank($data['charge_record_number'])) {
            $this->missingField('charge_record_number');
            return $this->returnJson();
        }

        $charge_record_meter_value = ChargeRecordMeterValue::firstOrNew([
            'charge_record_number' => $data['charge_record_number'],
        ]);
        $charge_record_meter_value->meter_value_item_json = json_decode($data['meter_value_item_json'] ?? null, true) ?? null; // 仪表值项JSON
        $charge_record_meter_value->gmt_sync_to_cloud = $data['gmt_sync_to_cloud'] ?? null; // 同步至云端时间
        $result = $charge_record_meter_value->save();

        $this->data = $result;

        return $this->returnJson();
    }

    public function syncChargeRecordTariffRule(Request $request)
    {
        $data = $request->input('data');

        $data = json_decode($data ?? null, true);

        // 解析失败或者无数据
        if (is_null($data) || empty($data)) {
            $this->missingField('data');
            return $this->returnJson();
        }
        // charge_record_number必填
        if (!isset($data['charge_record_number']) || blank($data['charge_record_number'])) {
            $this->missingField('charge_record_number');
            return $this->returnJson();
        }

        $charge_record_tariff_rule = ChargeRecordTariffRule::firstOrNew([
            'charge_record_number' => $data['charge_record_number'],
        ]);
        $charge_record_tariff_rule->simple_tariff_table_json = json_decode($data['simple_tariff_table_json'] ?? null, true) ?? null; // 简单收费表JSON
        $charge_record_tariff_rule->member_card_group_simple_tariff_table_item_json = json_decode($data['member_card_group_simple_tariff_table_item_json'] ?? null, true) ?? null; // 会员卡组简单收费表项JSON
        $charge_record_tariff_rule->time_tariff_table_item_json = json_decode($data['time_tariff_table_item_json'] ?? null, true) ?? null; // 时间收费表项JSON
        $charge_record_tariff_rule->member_card_group_time_tariff_table_item_json = json_decode($data['member_card_group_time_tariff_table_item_json'] ?? null, true) ?? null; // 会员卡组时间收费表项JSON
        $charge_record_tariff_rule->energy_tariff_table_item_json = json_decode($data['energy_tariff_table_item_json'] ?? null, true) ?? null; // 电量收费表项JSON
        $charge_record_tariff_rule->member_card_group_energy_tariff_table_item_json = json_decode($data['member_card_group_energy_tariff_table_item_json'] ?? null, true) ?? null; // 会员卡组电量收费表项JSON
        $charge_record_tariff_rule->idling_penalty_tariff_table_item_json = json_decode($data['idling_penalty_tariff_table_item_json'] ?? null, true) ?? null; // 闲置罚款收费表项JSON
        $charge_record_tariff_rule->member_card_group_idling_penalty_tariff_table_item_json = json_decode($data['member_card_group_idling_penalty_tariff_table_item_json'] ?? null, true) ?? null; // 会员卡组闲置罚款收费表项JSON
        $charge_record_tariff_rule->peak_time_table_item_json = json_decode($data['peak_time_table_item_json'] ?? null, true) ?? null; // 高峰时间表项JSON
        $charge_record_tariff_rule->public_holiday_json = json_decode($data['public_holiday_json'] ?? null, true) ?? null; // 公共假期JSON
        $charge_record_tariff_rule->gmt_sync_to_cloud = $data['gmt_sync_to_cloud'] ?? null; // 同步至云端时间
        $result = $charge_record_tariff_rule->save();

        $this->data = $result;

        return $this->returnJson();
    }

    public function syncChargeRecordTrialCharge(Request $request)
    {
        $data = $request->input('data');

        $data = json_decode($data ?? null, true);

        // 解析失败或者无数据
        if (is_null($data) || empty($data)) {
            $this->missingField('data');
            return $this->returnJson();
        }
        // charge_record_number必填
        if (!isset($data['charge_record_number']) || blank($data['charge_record_number'])) {
            $this->missingField('charge_record_number');
            return $this->returnJson();
        }

        $charge_record_trial = ChargeRecordTrialCharge::firstOrNew([
            'charge_record_number' => $data['charge_record_number'],
        ]);
        $charge_record_trial->trial_charge_item_json = json_decode($data['trial_charge_item_json'] ?? null, true) ?? null; // 试充项JSON
        $charge_record_trial->gmt_sync_to_cloud = $data['gmt_sync_to_cloud'] ?? null; // 同步至云端时间
        $result = $charge_record_trial->save();

        $this->data = $result;

        return $this->returnJson();
    }

    public function syncChargePaymentRecord(Request $request)
    {
        $data = $request->input('data');

        $data = json_decode($data ?? null, true);

        // 解析失败或者无数据
        if (is_null($data) || empty($data)) {
            $this->missingField('data');
            return $this->returnJson();
        }
        // charge_record_number必填
        if (!isset($data['charge_record_number']) || blank($data['charge_record_number'])) {
            $this->missingField('charge_record_number');
            return $this->returnJson();
        }
        // charge_payment_record_number必填
        if (!isset($data['charge_payment_record_number']) || blank($data['charge_payment_record_number'])) {
            $this->missingField('charge_payment_record_number');
            return $this->returnJson();
        }
        // charge_tariff_scheme必填
        if (!isset($data['charge_tariff_scheme']) || blank($data['charge_tariff_scheme'])) {
            $this->missingField('charge_tariff_scheme');
            return $this->returnJson();
        }
        // charge_value_type必填
        if (!isset($data['charge_value_type']) || blank($data['charge_value_type'])) {
            $this->missingField('charge_value_type');
            return $this->returnJson();
        }

        unset($data['charge_payment_record_id']);

        $data['kiosk_number'] = $data['viosk_number'] ?? null;
        $data['kiosk_name'] = $data['viosk_name'] ?? null;

        $charge_payment_record = ChargePaymentRecord::firstOrNew([
            'charge_payment_record_number' => $data['charge_payment_record_number'],
        ]);
        $data = Arr::only($data, $charge_payment_record->columns);
        $charge_payment_record->fill($data);
        $result = $charge_payment_record->save();

        $this->data = $result;

        return $this->returnJson();
    }

    public function syncChargePaymentRecordCalculation(Request $request)
    {
        $data = $request->input('data');

        $data = json_decode($data ?? null, true);

        // 解析失败或者无数据
        if (is_null($data) || empty($data)) {
            $this->missingField('data');
            return $this->returnJson();
        }
        // charge_payment_record_number必填
        if (!isset($data['charge_payment_record_number']) || blank($data['charge_payment_record_number'])) {
            $this->missingField('charge_payment_record_number');
            return $this->returnJson();
        }

        $charge_payment_record_calculation = ChargePaymentRecordCalculation::firstOrNew([
            'charge_payment_record_number' => $data['charge_payment_record_number'],
        ]);
        $charge_payment_record_calculation->charge_value_amount_calculation_json = $data['charge_value_amount_calculation_json'] ?? null; // 充电量金额计算JSON
        $charge_payment_record_calculation->idling_penalty_amount_calculation_json = $data['idling_penalty_amount_calculation_json'] ?? null; // 闲置罚款金额计算JSON
        $charge_payment_record_calculation->gmt_sync_to_cloud = $data['gmt_sync_to_cloud'] ?? null; // 同步至云端时间
        $result = $charge_payment_record_calculation->save();

        $this->data = $result;

        return $this->returnJson();
    }

    public function syncChargePreAuthorizationRecord(Request $request)
    {
        $data = $request->input('data');

        $data = json_decode($data ?? null, true);

        // 解析失败或者无数据
        if (is_null($data) || empty($data)) {
            $this->missingField('data');
            return $this->returnJson();
        }

        // connector_number必填
        if (!isset($data['connector_number']) || blank($data['connector_number'])) {
            $this->missingField('connector_number');
            return $this->returnJson();
        }

        unset($data['charge_pre_authorization_record_id']);

        $data['kiosk_number'] = $data['viosk_number'] ?? null;
        $data['kiosk_name'] = $data['viosk_name'] ?? null;

        $charge_pre_authorization_record = ChargePreAuthorizationRecord::firstOrNew([
            'charge_pre_authorization_record_number' => $data['charge_pre_authorization_record_number'],
        ]);
        $data = Arr::only($data, $charge_pre_authorization_record->columns);
        $charge_pre_authorization_record->fill($data);
        $result = $charge_pre_authorization_record->save();

        $this->data = $result;

        return $this->returnJson();
    }

    public function getUpdateInformation(Request $request): JsonResponse
    {
        // $language_code = self::getLanguageCode($request);
        $version = $request->version; // 版本号

        if (blank($version)) {
            $this->missingField('version');
            return $this->returnJson();
        }

        $this->data = [
            'has_new_version' => false, // 是否有新版本
            'version' => null, // 版本号
            'description' => null, // 描述
            'is_constraint' => false, // 是否强制更新
            'update_url' => null, // 更新地址
        ];

        // 获取根目录json文件夹下的viosk_version.json文件
        $viosk_version_array = getArrayFromJsonFile('viosk_version', false);

        // 无法解析或者为空
        if (blank($viosk_version_array)) {
            return $this->returnJson();
        }

        $viosk_version = $viosk_version_array;
        if (version_compare($viosk_version['version'] ?? '', $version, '>')) {
            $this->data['has_new_version'] = true;
        }
        $this->data['description'] = $viosk_version['description'] ?? null;
        $this->data['is_constraint'] = $viosk_version['is_constraint'] ?? false;
        $this->data['update_url'] = $viosk_version['update_url'] ?? null;
        $this->data['version'] = $viosk_version['version'] ?? null;

        return $this->returnJson();
    }

    public function uploadLogFile(Request $request): JsonResponse
    {
        $viosk_number = $request->viosk_number;
        $log_file = $request->file('file');

        if (blank($viosk_number)) {
            $this->missingField('viosk_number');
            return $this->returnJson();
        }

        if (!$request->hasFile('file') || !$request->file('file')->isValid()) {
            $this->missingField('file');
            return $this->returnJson();
        }

        // 替换viosk_number非法文件名为_
        $viosk_number = preg_replace('/[^a-zA-Z0-9_-]/', '_', $viosk_number);
        $full_path = '/viosk_log_file/' . $viosk_number . '/';


        $file_name = $log_file->getClientOriginalName(); // 获取文件的原始名称
        $log_file->storeAs($full_path, $file_name);

        return $this->returnJson();
    }

    public function syncEventLog(Request $request): JsonResponse
    {
        $data = $request->input('data');
        $data = json_decode($data ?? null, true);

        // 解析失败或者无数据
        if (is_null($data) || empty($data)) {
            $this->missingField('data');
            return $this->returnJson();
        }

        $merchant_number = $data['merchant_number'] ?? null; // 商户编号
        $merchant_name = $data['merchant_name'] ?? null; // 商户名称
        $site_number = $data['site_number'] ?? null; // 场地编号
        $site_name = $data['site_name'] ?? null; // 场地名称
        $target = $data['target'] ?? null; // 目标
        $target_number = $data['target_number'] ?? null; // 目标编号
        $target_name = $data['target_name'] ?? null; // 目标名称
        $event_type = $data['event_type'] ?? null; // 事件类型
        $event_code = $data['event_code'] ?? null; // 事件编码
        $description = $data['description'] ?? null; // 描述
        $exception = $data['exception'] ?? null; //异常
        $charge_record_number = $data['charge_record_number'] ?? null; // 充电记录编号
        $remark = $data['remark'] ?? null; // 备注

        if (!empty($target) && !empty($target_number) && !empty($target_name) && !empty($event_type)) {
            $sub_query = EventLog::where('merchant_number', $merchant_number)
                ->where('site_number', $site_number)
                ->where('target', $target)
                ->where('target_number', $target_number)
                ->orderBy('event_log_id', 'desc')
                ->limit(env('KIOSK_API_EVENT_LOG_LIMIT', 10));
            $event_log_model = EventLog::fromSub($sub_query, 'sub_query')
                ->where('event_type', $event_type)
                ->where('description', $description)
                ->where('exception', $exception)
                ->where('charge_record_number', $charge_record_number)
                ->where('remark', $remark)
                ->orderBy('event_log_id', 'desc')
                ->first();
            if (filled($event_log_model)) {
                // 仅修改修改时间
                $event_log_model->{EventLog::UPDATED_AT} = now();
                $event_log_model->save();
            } else {
                $event_log_model = new EventLog;
                $event_log_model->merchant_number = $merchant_number; // 商户编号
                $event_log_model->merchant_name = $merchant_name; // 商户名称
                $event_log_model->site_number = $site_number; // 场地编号
                $event_log_model->site_name = $site_name; // 场地名称
                $event_log_model->target = $target; //
                $event_log_model->target_number = $target_number; //
                $event_log_model->target_name = $target_name;
                $event_log_model->event_type = $event_type; //
                $event_log_model->event_code = $event_code;
                $event_log_model->description = $description; //
                $event_log_model->exception = $exception; //
                $event_log_model->charge_record_number = $charge_record_number;
                $event_log_model->remark = $remark;

                // 保存日志，根据规则保存对应日志
                $this->saveEventLogOrRules($event_log_model, true);
            }


            $this->data = true;

            // 并发送websocket通知
            self::sendInitPushWeb();
        } else {
            $this->data = false;
            $this->missingField('target|target_number|target_name|event_type');
        }

        return $this->returnJson();
    }
}

<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;
use App\Models\Traits\Emoji;

class PublicHoliday extends Model
{
    use emoji;

    protected $table = 'public_holiday'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'public_holiday_id'; //定义主键，默认为id
    // 表的唯一number
    public static $table_number = 'public_holiday_number';
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    // protected $fillable = [];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    // creating event
    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->public_holiday_number = $model->site_number . '-' . $model->public_holiday_number;
        });
    }
}

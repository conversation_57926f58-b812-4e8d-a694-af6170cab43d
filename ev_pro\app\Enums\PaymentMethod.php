<?php

namespace App\Enums;

use BenSampo\Enum\Enum;
use BenSampo\Enum\Contracts\LocalizedEnum;
use App\Enums\Traits\Tools;

/**
 * @method static Octopus
 * @method static Visa
 * @method static MasterCard
 * @method static Unionpay
 * @method static Fps
 * @method static Alipay
 * @method static WechatPay
 * @method static Yedpay
 */
final class PaymentMethod extends Enum implements LocalizedEnum
{
    use Tools;

    const Octopus = 'OCTOPUS';
    const Visa = 'VISA';
    const MasterCard = 'MASTER_CARD';
    const Unionpay = 'UNIONPAY';
    const Fps = 'FPS';// 转数快
    const Alipay = 'ALIPAY';
    const WechatPay = 'WECHAT_PAY';
    const CreditCard = 'CREDIT_CARD';
    const QRCode = 'QR_CODE';
}

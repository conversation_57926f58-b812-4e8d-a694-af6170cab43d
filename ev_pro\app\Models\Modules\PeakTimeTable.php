<?php

namespace App\Models\Modules;

use App\Enums\TariffTableType;
use Illuminate\Database\Eloquent\Model;

class PeakTimeTable extends Model
{

    protected $table = 'peak_time_table'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'peak_time_table_id'; //定义主键，默认为id
    // 表的唯一number
    public static $table_number = 'peak_time_table_number';
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    // protected $fillable = [];

    /**
     * 类型转化器
     */
    protected $casts = [
        'peak_time_table_item_json' => 'array',
    ];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'sort_order' => 0, // 排序
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * 一对多关联Peak Time Table Item
     */
    public function item()
    {
        return $this->hasMany(PeakTimeTableItem::class, 'peak_time_table_number', 'peak_time_table_number');
    }


    // deleting event
    public static function boot()
    {
        parent::boot();

        static::deleting(function ($model) {
            $model->item()->delete();
        });

        /* static::updating(function ($model) {
            $model->item()->delete();
        }); */
    }

    /**
     * 查找多个connector setting
     */
    public function connectorSetting()
    {
        return $this->hasMany(ConnectorSetting::class, 'peak_time_table_number', 'peak_time_table_number')
            ->whereIn('tariff_table_type', [TariffTableType::ComplexEnergyTariffTable, TariffTableType::ComplexTimeTariffTable]);
    }
}

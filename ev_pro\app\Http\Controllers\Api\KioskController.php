<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Common\CommonController;
use App\Http\Controllers\KioskPublicInformation\KioskPublicInformationController;
use App\Jobs\RequestNotifyKioskChargeRecordJob;
use App\Jobs\SendEmailJob;
use App\Jobs\SendSMSJob;
use Exception;
use Illuminate\Http\{
    JsonResponse,
    Request,
};
use Illuminate\Database\Eloquent\Builder;
use App\Enums\{
    ChargeTariffScheme,
    EventTypeEnum,
    IdentityType,
    PaymentMethod,
    ChargeValueType,
    LmsMode,
    PaymentDeviceEnum,
    PaymentStatusEnum,
    OctopusDataExchangeMode,
    ChargePreAuthorizationStatus,
    ChargeArrearsStatus,
    TariffTableType,
    ChargeAccountingTransactionCategory,
    ChargeAccountingTransactionType,
    UserCredentialType,
};
use App\Models\Modules\{
    ChargePaymentRecordCalculation,
    ChargePoint,
    ChargePointCs,
    Connector,
    AdminOctopusCard,
    ConnectorSetting,
    Kiosk,
    ChargeRecord,
    ComServer,
    ChargePaymentRecord,
    MemberCard,
    MemberCardGroup,
    RemainChargeValueRecord,
    OctopusUploadFileRecord,
    OctopusDownloadFileRecord,
    KioskScreenOperationRecord,
    PublicHoliday,
    Site,
    Zone,
    EventLog,
    OctopusCardReplacementRecord,
    MaintenanceRecord,
    AppUser,
    FreeOctopusCard,
    ChargePreAuthorizationRecord,
    Merchant,
    ChargeArrearsRecord,
    ChargeRecordTariffRule,
    ChargeAccountingRecord,
    ChargePreAuthorizationRefundRecord,
    UserCredential,
    UserGroup,
};
use Faker\Provider\ar_EG\Payment;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class KioskController extends CommonController
{

    protected static $default_currency_code = 'HKD'; // 货币代码
    protected static $default_currency_symbol = 'HK$'; // 默认货币符号

    protected static function getLanguageCode(Request $request)
    {

        $language_code = match ($request->input('language_code', 'en_US')) {
            'zh_HK' => 'zh_HK',
            default => 'en_US',
        };
        // 设置多语言
        self::setLanguage($language_code);
        return $language_code;
    }

    /**
     * 验证身份号码是否可用
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function verifyIdentityNumberAvailable(Request $request): JsonResponse
    {
        $identity_type = $request->input('identity_type');
        $identity_number = $request->input('identity_number');

        $this->data = true;

        if (blank($identity_type)) {
            $this->missingField('identity_type');
            return $this->returnJson();
        }

        if (blank($identity_number)) {
            $this->missingField('identity_number');
            return $this->returnJson();
        }

        if (!IdentityType::hasValue($identity_type)) {
            $this->missingField('identity_type');
            return $this->returnJson();
        }

        // 如果是POS预授权，不用进行查询数据库，直接返回true
        if ($identity_type === IdentityType::PosPreAuthorization) {
            $this->data = true;
            return $this->returnJson();
        }

        $result = Connector::whereHas('chargeRecord', function (Builder $query) use ($identity_type, $identity_number) {
            $query->where('identity_type', $identity_type)
                // 如果identity_type是Octopus，需要拆分新旧卡号查询；如果是解锁码，直接判断
                ->when($identity_type === IdentityType::Octopus, function (Builder $query) use ($identity_number) {
                    $octopus_card_list = explode(',', $identity_number);
                    $query->whereIn('identity_number', $octopus_card_list);
                }, function (Builder $query) use ($identity_number) {
                    $query->where('identity_number', $identity_number);
                });
        })->exists();

        $this->data = !$result;

        return $this->returnJson();
    }

    /**
     * 初始化
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example 根据kioskId初始化获取所有信息
     * <AUTHOR>
     * @date 2022-05-13
     */
    public function initialization(Request $request): JsonResponse
    {
        $kiosk_number = $request->kiosk_number;
        $current_build_version = $request->current_build_version;

        // Kiosk Number未传
        if (blank($kiosk_number)) {
            $this->missingField('kiosk_number');
            return $this->returnJson();
        }

        // Kiosk
        $kiosk = Kiosk::where('kiosk_number', $kiosk_number)->first();
        // Kiosk不存在
        if (blank($kiosk)) {
            $this->notFoundData('kiosk');
            return $this->returnJson();
        }

        $this->data['admin_octopus_card'] = null;
        $this->data['charge_point'] = null;
        $this->data['connector'] = null;
        $this->data['connector_setting'] = null;
        $this->data['com_server'] = null;
        $this->data['kiosk_screen_time'] = null;
        $this->data['kiosk_payment_method'] = null;
        $this->data['time_tariff_table'] = null;
        $this->data['simple_tariff_table'] = null;
        $this->data['energy_tariff_table'] = null;
        $this->data['idling_penalty_tariff_table'] = null;
        $this->data['peak_time_table'] = null;
        $this->data['merchant'] = null;
        $this->data['site'] = null;
        $this->data['zone'] = null;
        $this->data['public_holiday'] = null;

        $this->data['kiosk_setting'] = null;
        $this->data['merchant_number'] = null;
        $this->data['site_number'] = null;
        $this->data['zone_number'] = null;
        $this->data['maintenance_record'] = null;

        $this->data['system_date_time'] = date('Y-m-d H:i:s'); // 系统当前时间
        $this->data['cms_websocket_url'] = 'ws://' . config('app.websocket_host') . ':' . env('WEBSOCKET_KIOSK_PORT');

        // 当前版本
        if (filled($current_build_version)) {
            $kiosk->current_build_version = $current_build_version;
            $kiosk->save();
        }
        $this->data['kiosk_id'] = $kiosk->kiosk_id;
        $this->data['kiosk_number'] = $kiosk->kiosk_number;
        $this->data['license_code'] = $kiosk->license_code;
        $this->data['merchant_number'] = $kiosk->merchant_number;
        $this->data['site_number'] = $kiosk->site_number;
        $this->data['name'] = $kiosk->name;
        $this->data['zone_number'] = $kiosk->zone_number;
        $this->data['current_build_version'] = $kiosk->current_build_version;
        $this->data['octopus_device_type'] = $kiosk->octopus_device_type;
        $this->data['octopus_com_port'] = $kiosk->octopus_com_port;
        $this->data['octopus_baud_rate'] = $kiosk->octopus_baud_rate;
        $this->data['octopus_data_exchange_program_local_path'] = $kiosk->octopus_data_exchange_program_local_path;
        $this->data['octopus_pre_authorization_amount'] = $kiosk->octopus_pre_authorization_amount;
        $this->data['gmt_octopus_last_upload'] = $kiosk->gmt_octopus_last_upload;
        $this->data['gmt_octopus_last_download'] = $kiosk->gmt_octopus_last_download;
        $this->data['pos_vendor'] = $kiosk->pos_vendor;
        $this->data['yedpay_pos_api_url'] = $kiosk->yedpay_pos_api_url;
        $this->data['yedpay_pos_api_key'] = $kiosk->yedpay_pos_api_key;
        $this->data['yedpay_pos_pre_authorization_amount'] = $kiosk->yedpay_pos_pre_authorization_amount;
        $this->data['soe_pay_pos_api_url'] = $kiosk->soe_pay_pos_api_url;
        $this->data['soe_pay_pos_api_key'] = $kiosk->soe_pay_pos_api_key;
        $this->data['soe_pay_pos_api_token'] = $kiosk->soe_pay_pos_api_token;
        $this->data['torch_port_name'] = $kiosk->torch_port_name;
        $this->data['torch_baud_rate'] = $kiosk->torch_baud_rate;
        $this->data['torch_data_bits'] = $kiosk->torch_data_bits;

        $merchant_number = $kiosk->merchant_number; // 商户编号
        $site_number = $kiosk->site_number; // 场地编号
        // Public Holiday
        $this->data['public_holiday'] = $this->getArray(PublicHoliday::where('site_number', $site_number)->oldest('start_date')->get()->toArray());

        // 管理员八达通卡
        $this->data['admin_octopus_card'] = $this->getArray(AdminOctopusCard::where('site_number', $site_number)->where('is_enable', 1)->pluck('octopus_card_number'));

        // 商户
        $this->data['merchant'] = Merchant::where('merchant_number', $merchant_number)->first();
        if (filled($this->data['merchant'])) {
            $this->data['merchant']['merchant_name'] = self::getValueFromLanguageArray($this->data['merchant']['name_json']);
            $this->data['merchant']['logo_image_url'] = existsImage('public', $this->data['merchant']['logo_image_url']);
            $this->data['merchant']['member_card_background_image_url'] = existsImage('public', $this->data['merchant']['member_card_background_image_url']);
            $this->data['merchant']['name_json'] = json($this->data['merchant']['name_json']);
        }

        // 场地
        foreach (Site::where('merchant_number', $merchant_number)->oldest('sort_order')->oldest('acronym')->get()->toArray() as &$value) {
            $value['main_image_url'] = existsImage('public', $value['main_image_url']);
            $value['site_name'] = self::getValueFromLanguageArray($value['name_json']);
            $value['name_json'] = json($value['name_json']);
            $this->data['site'][] = $value;
        }

        // 区域
        foreach (Zone::where('site_number', $site_number)->with('description')->oldest('sort_order')->get()->toArray() as &$value) {
            // 修改关联名description为description_list
            $value['description_list'] = $value['description'];
            $value['zone_name'] = self::getValueFromLanguageArray($value['name_json']);
            $value['name_json'] = json($value['name_json']);
            unset($value['description']);
            $this->data['zone'][] = $value;
        }

        // Kiosk Payment Method
        $this->data['kiosk_payment_method'] = $this->getArray($kiosk->kioskPaymentMethod()->where('is_enable', 1)->pluck('payment_method'));

        // Maintenance Record
        $maintenance_record = MaintenanceRecord::orWhere(function ($query) use ($kiosk) {
            $query->orWhere(function ($in_query) {
                $in_query->whereNull('kiosk_number_list')->whereNull('site_number_list');
            })
                ->orWhereRaw('find_in_set("' . $kiosk->kiosk_number . '", kiosk_number_list)')
                ->when(filled($kiosk->site_number), function ($in_query) use ($kiosk) {
                    $in_query->orWhereRaw('find_in_set("' . $kiosk->site_number . '", site_number_list)');
                });
        })
            ->where(function ($query) {
                $query->whereNull('gmt_end')
                    ->orWhere('gmt_end', '>', date('Y-m-d H:i:s'));
            })
            ->latest()
            ->first();
        $this->data['maintenance_record'] = $this->getArray($maintenance_record);

        $com_server_number_list = []; // 充电桩用到的中央服务器编号
        $connector_setting_number_list = []; // 充电枪用到的配置ID
        // 充电桩
        foreach ($kiosk->chargePoint()->with('connector.description')->oldest('sort_order')->get() as $charge_point) {
            // 判断kiosk_to_charge_point中间表的is_enable
            if (!$charge_point->pivot->is_enable) continue;
            if (!in_array($charge_point->com_server_number, $com_server_number_list)) {
                $com_server_number_list[] = $charge_point->com_server_number;
            }

            // 充电桩
            $this->data['charge_point'][] = $charge_point->makeHidden(['pivot', 'connector']);

            // 充电枪
            foreach ($charge_point->connector->sortBy('sort_order') as $connector) {
                if (!in_array($connector->connector_setting_number, $connector_setting_number_list)) {
                    $connector_setting_number_list[] = $connector->connector_setting_number;
                }
                $connector->site_number = $charge_point->site_number;
                // 充电枪
                $connector->description_list = $connector->description;
                unset($connector->description);
                $this->data['connector'][] = $connector;
            }
        }

        // 中央服务器
        if (!empty($com_server_number_list)) {
            $this->data['com_server'] = $this->getArray(ComServer::whereIn('com_server_number', $com_server_number_list)->get());
        }

        // 充电枪配置
        if (!empty($connector_setting_number_list)) {
            // 查询充电枪配置及其关联收费方案
            $connector_setting_list = ConnectorSetting::whereIn('connector_setting_number', $connector_setting_number_list)
                ->oldest('sort_order')
                ->get();
            $this->data['connector_setting'] = $this->getArray($connector_setting_list->toArray());
            // 预加载connector_setting关联的收费方案
            $connector_setting_list->load([
                'timeTariffTable' => [
                    'item' => [
                        // itemSpecial 按照时间排序
                        'itemSpecial' => function ($query) {
                            $query->oldest('start_time');
                        }
                    ],
                ],
                'simpleTariffTable' => [
                    'item' => function ($query) {
                        $query->oldest('gmt_create');
                    }
                ],
                'energyTariffTable' => [
                    'item' => function ($query) {
                        $query->oldest('start_range');
                    }
                ],
                'idlingPenaltyTariffTable' => [
                    'item' => function ($query) {
                        $query->oldest('start_range');
                    }
                ],
                'peakTimeTable' => [
                    'item' => function ($query) {
                        $query->oldest('start_time');
                    }
                ],
            ]);

            // 循环充电枪配置
            foreach ($connector_setting_list as $connector_setting) {
                // 時間收費方案
                if (filled($connector_setting->timeTariffTable)) {
                    // 時間收費方案
                    $time_tariff_table_item = null;
                    foreach ($connector_setting->timeTariffTable->item as $item) {
                        $item->time_tariff_table_item_special_list = $this->getArray($item->itemSpecial);
                        $time_tariff_table_item[] = $item;
                    }
                    // 将post_paid_identity_type_list转换为数组
                    $connector_setting->timeTariffTable->post_paid_identity_type_list = filled($connector_setting->timeTariffTable->post_paid_identity_type_list) ? explode(',', $connector_setting->timeTariffTable->post_paid_identity_type_list) : null;
                    $connector_setting->timeTariffTable->time_tariff_table_item = $time_tariff_table_item;
                    unset($connector_setting->timeTariffTable->item);
                    $this->data['time_tariff_table'][$connector_setting->timeTariffTable->time_tariff_table_id] = $connector_setting->timeTariffTable->makeHidden(['time_tariff_table_item_json', 'member_card_group_time_tariff_table_item_json', 'user_group_time_tariff_table_item_json']);
                }

                // 簡單收費方案
                if (filled($connector_setting->simpleTariffTable)) {
                    // 将post_paid_identity_type_list转换为数组
                    $connector_setting->simpleTariffTable->post_paid_identity_type_list = filled($connector_setting->simpleTariffTable->post_paid_identity_type_list) ? explode(',', $connector_setting->simpleTariffTable->post_paid_identity_type_list) : null;
                    $connector_setting->simpleTariffTable->simple_tariff_table_item = $this->getArray($connector_setting->simpleTariffTable->item);
                    unset($connector_setting->simpleTariffTable->item);
                    $this->data['simple_tariff_table'][$connector_setting->simpleTariffTable->simple_tariff_table_id] = $connector_setting->simpleTariffTable->makeHidden(['member_card_group_simple_tariff_table_item_json', 'user_group_time_tariff_table_item_json']);
                }

                // 电量收費方案
                if (filled($connector_setting->energyTariffTable)) {
                    // 电量收費方案
                    // 将post_paid_identity_type_list转换为数组
                    $connector_setting->energyTariffTable->post_paid_identity_type_list = filled($connector_setting->energyTariffTable->post_paid_identity_type_list) ? explode(',', $connector_setting->energyTariffTable->post_paid_identity_type_list) : null;
                    $connector_setting->energyTariffTable->energy_tariff_table_item = $this->getArray($connector_setting->energyTariffTable->item);
                    unset($connector_setting->energyTariffTable->item);
                    $this->data['energy_tariff_table'][$connector_setting->energyTariffTable->energy_tariff_table_id] = $connector_setting->energyTariffTable->makeHidden(['energy_tariff_table_item_json', 'member_card_group_energy_tariff_table_item_json', 'user_group_energy_tariff_table_item_json']);
                }

                // 閒置罰款
                if (filled($connector_setting->idlingPenaltyTariffTable)) {
                    $connector_setting->idlingPenaltyTariffTable->idling_penalty_tariff_table_item = $this->getArray($connector_setting->idlingPenaltyTariffTable->item);
                    unset($connector_setting->idlingPenaltyTariffTable->item);
                    $this->data['idling_penalty_tariff_table'][$connector_setting->idlingPenaltyTariffTable->idling_penalty_tariff_table_id] = $connector_setting->idlingPenaltyTariffTable->makeHidden(['idling_penalty_tariff_table_item_json', 'member_card_group_idling_penalty_tariff_table_item_json', 'user_group_idling_penalty_tariff_table_item_json']);
                }
                // 高峰時間表
                if (filled($connector_setting->peakTimeTable)) {
                    $connector_setting->peakTimeTable->peak_time_table_item = $this->getArray($connector_setting->peakTimeTable->item);
                    unset($connector_setting->peakTimeTable->item);
                    $this->data['peak_time_table'][$connector_setting->peakTimeTable->peak_time_table_id] = $connector_setting->peakTimeTable->makeHidden(['peak_time_table_item_json']);
                }
            }

            // 重置鍵名
            $this->data['time_tariff_table'] = !is_null($this->data['time_tariff_table']) ? array_values($this->data['time_tariff_table']) : null;
            $this->data['simple_tariff_table'] = !is_null($this->data['simple_tariff_table']) ? array_values($this->data['simple_tariff_table']) : null;
            $this->data['energy_tariff_table'] = !is_null($this->data['energy_tariff_table']) ? array_values($this->data['energy_tariff_table']) : null;
            $this->data['idling_penalty_tariff_table'] = !is_null($this->data['idling_penalty_tariff_table']) ? array_values($this->data['idling_penalty_tariff_table']) : null;
            $this->data['peak_time_table'] = !is_null($this->data['peak_time_table']) ? array_values($this->data['peak_time_table']) : null;
        }

        // Kiosk Settings
        if (filled($kiosk->kioskSetting)) {
            // 将print_history_receipt_identity_type_list转换为数组
            $kiosk->kioskSetting->print_history_receipt_identity_type_list = filled($kiosk->kioskSetting->print_history_receipt_identity_type_list) ? explode(',', $kiosk->kioskSetting->print_history_receipt_identity_type_list) : null;
            // Kiosk Setting Description
            $kiosk->kioskSetting->description_list = [];
            // 不查询收费表html text
            $kiosk_setting_description_result = $kiosk->kioskSetting->description;
            if (filled($kiosk_setting_description_result)) {
                // 处理图片，生成图片完整地址
                $func = function (array $data) use ($kiosk): array {
                    $kiosk_public_information_params = array(
                        'kiosk_number' => $kiosk->kiosk_number,
                        'language_code' => $data['language_code'] ?? 'en_US_zh_HK',
                    );
                    $data['main_logo_image_url'] = existsImage('public', $data['main_logo_image_url']);
                    $data['home_page_image_url'] = existsImage('public', $data['home_page_image_url']);
                    $data['authorize_user_qr_code_url'] = action([KioskPublicInformationController::class, 'kioskPublicInformationKioskBindApp'], $kiosk_public_information_params);

                    if ($kiosk->kioskSetting->is_display_fee_page) {
                        $data['fee_url'] = action([KioskPublicInformationController::class, 'kioskPublicInformationAutomatically'], $kiosk_public_information_params);
                    }
                    if ($kiosk->kioskSetting->is_display_support_page) {
                        $data['support_url'] = action([KioskPublicInformationController::class, 'kioskPublicInformationSupport'], $kiosk_public_information_params);
                    }
                    unset($data['disclaimer_html']);
                    return $data;
                };
                $kiosk->kioskSetting->description_list = array_map($func, $kiosk_setting_description_result->toArray());
            }
            unset($kiosk->kioskSetting->description);

            // Kiosk Setting Screensaver Resource Description
            $kiosk->kioskSetting->screensaver_resource_description_list = null;
            if (filled($kiosk->kioskSetting->screensaver)) {
                // 处理图片，生成图片完整地址
                $func = function (array $data): array {
                    $data['resource_url'] = existsImage('public', $data['resource_url']);
                    return $data;
                };
                $kiosk->kioskSetting->screensaver_resource_description_list = array_map($func, $kiosk->kioskSetting->screensaver()->oldest('sort_order')->get()->toArray());

                $kiosk->kioskSetting->screensaver_resource_description_list = Arr::where($kiosk->kioskSetting->screensaver_resource_description_list, function ($value, $key) {
                    return filled($value['resource_url']);
                });
                // 重置數組的key
                $kiosk->kioskSetting->screensaver_resource_description_list = array_values($kiosk->kioskSetting->screensaver_resource_description_list);
            }
            unset($kiosk->kioskSetting->screensaver);

            // Kiosk Setting Screen Time
            $kiosk->kioskSetting->screen_time_list = null;
            if (isset($kiosk->kioskSetting->screenTime)) {
                $kiosk->kioskSetting->screen_time_list = $this->getArray($kiosk->kioskSetting->screenTime);
            }
            unset($kiosk->kioskSetting->screenTime);

            // Kiosk Setting
            $this->data['kiosk_setting'] = $kiosk->kioskSetting;
        }

        return $this->returnJson();
    }

    /**
     * 更新充电记录提醒
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    public function updateChargeRecordReminder(Request $request): JsonResponse
    {
        $charge_record_number = $request->input('charge_record_number');
        $reminder_telephone = $request->input('reminder_telephone'); // 提醒电话
        $reminder_email = $request->input('reminder_email'); // 提醒电邮
        $language_code = self::getLanguageCode($request);

        $this->data = null;
        if (!empty($charge_record_number) && filled($charge_record = ChargeRecord::firstWhere('charge_record_number', $charge_record_number))) {
            $charge_record->reminder_telephone = $reminder_telephone;
            $charge_record->reminder_email = $reminder_email;
            $charge_record->gmt_sync_to_cloud = null;
            $charge_record->save();

            try {
                // 通知Kiosk充电记录
                $this->requestNotifyKioskChargeRecord($charge_record);

                // 发送邮件和短信
                $current_charge_record_number = $charge_record->connector->current_charge_record_number ?? null;
                if (blank($current_charge_record_number) || $current_charge_record_number !== $charge_record->charge_record_number) {
                    $this->sendMailAndSMS($charge_record);
                }
            } catch (Exception $e) {
                logger('updateChargeRecordReminder', ['message' => $e->getMessage()]);
            }

            $this->data = $this->chargeRecordArrayToJson($charge_record);
        } else {
            $this->missingField('charge_record_number');
        }

        return $this->returnJson();
    }

    /**
     * 更新充电记录身份
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    public function updateChargeRecordIdentity(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $charge_record_number = $request->input('charge_record_number');
        $identity_type = $request->input('identity_type'); // 身份类型
        $identity_number = $request->input('identity_number'); // 身份号码
        $member_card_group_id = $request->input('member_card_group_id'); // 会员卡组ID
        $octopus_raw_card_number = $request->input('octopus_raw_card_number'); // 八达通原始卡号

        $this->data = null;
        if (filled($charge_record_number) && filled($identity_type) && filled($identity_number) && IdentityType::hasValue($identity_type)) {
            $charge_record = ChargeRecord::firstWhere('charge_record_number', $charge_record_number);
            if (filled($charge_record)) {
                // 八达通 回填member_card_id和member_name
                if (filled($identity_type) && IdentityType::hasValue($identity_type) && $identity_type === IdentityType::Octopus) {
                    $member_card = self::getMemberCardByOctopusCardNumber($identity_number, $charge_record->site_number, $octopus_raw_card_number);
                    if (filled($member_card)) {
                        $charge_record->member_card_id = $member_card->member_card_id;
                        $charge_record->member_name = $member_card->member_name;
                        $charge_record->user_id = $member_card->user_id;
                        $member_card_group_id = $member_card->member_card_group_id;
                        if (filled($member_card_group_id)) {
                            $member_card_group = MemberCardGroup::find($member_card_group_id);
                            if (filled($member_card_group)) {
                                $charge_record->member_card_group_id = $member_card_group->member_card_group_id;
                                $charge_record->member_card_group_name = $member_card_group->name_json;
                            }
                        }
                    }
                }

                $charge_record->identity_type = $identity_type;
                $charge_record->identity_number = $identity_number;
                $charge_record->gmt_sync_to_cloud = null;
                $charge_record->save();
                $this->data = $this->chargeRecordArrayToJson($charge_record);
            } else {
                $this->missingField('charge_record_number');
            }
        } else {
            $this->missingField('charge_record_number|identity_type|identity_number');
        }

        return $this->returnJson();
    }

    /**
     * 更新充电记录闲置罚款已锁定时间
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     */
    public function updateChargeRecordIdlingPenaltyLocked(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $charge_record_number = $request->input('charge_record_number');

        $this->data = false;
        if (blank($charge_record_number)) {
            $this->missingField('charge_record_number');
            return $this->returnJson();
        }
        $charge_record = ChargeRecord::firstWhere('charge_record_number', $charge_record_number);
        if (blank($charge_record)) {
            $this->notFoundData('charge_record');
            return $this->returnJson();
        }
        $charge_record->gmt_idling_penalty_locked = now();
        $result = $charge_record->save();
        $this->data = $result;

        return $this->returnJson();
    }

    /**
     * Octopus先付扣款
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-16
     */
    public function octopusPrePaidDeduct(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $charge_record_number = $request->input('charge_record_number'); // 充电记录编号
        $payment_device = PaymentDeviceEnum::Octopus; // 支付设备
        $payment_method = $request->input('payment_method'); // 支付方式
        $payment_status = $request->input('payment_status', PaymentStatusEnum::Completed); // 支付状态
        $charge_value_type = $request->input('charge_value_type'); // 充电量类型
        $is_top_up = $request->boolean('is_top_up'); // 是否为续充
        $charge_value = $request->input('charge_value'); // 充电量
        $is_idling_penalty_only = $request->boolean('is_idling_penalty_only', false); // 是否仅闲置罚款
        $is_charge_arrears = $request->boolean('is_charge_arrears', false); // 是否充电欠款
        $charge_value_amount = $request->input('charge_value_amount'); // 充电量金额
        $charge_value_amount_calculation_json = $request->input('charge_value_amount_calculation_json'); // 充电量金额计算JSON
        $idling_penalty_time = $request->input('idling_penalty_time'); // 闲置罚款时间
        $idling_penalty_amount = $request->input('idling_penalty_amount'); // 闲置罚款金额
        $idling_penalty_amount_calculation_json = $request->input('idling_penalty_amount_calculation_json'); // 闲置罚款金额计算JSON
        $gmt_idling_penalty_start = $request->input('gmt_idling_penalty_start'); // 闲置罚款开始时间
        $gmt_idling_penalty_stop = $request->input('gmt_idling_penalty_stop'); // 闲置罚款结束时间
        $octopus_transaction_id = $request->input('octopus_transaction_id'); // 八达通交易ID
        $gmt_receipt_start = $request->input('gmt_receipt_start'); // 收据开始时间
        $gmt_receipt_stop = $request->input('gmt_receipt_stop'); // 收据结束时间
        $octopus_receipt_number = $request->input('octopus_receipt_number'); // 八达通收据编号
        $gmt_octopus_deduct = $request->input('gmt_octopus_deduct'); // 八达通扣款时间
        $octopus_device_number = $request->input('octopus_device_number'); // 八达通设备编号
        $octopus_card_type = $request->input('octopus_card_type'); // 八达通卡类型
        $octopus_card_number = $request->input('octopus_card_number'); // 八达通卡号
        $octopus_raw_card_number = $request->input('octopus_raw_card_number'); // 八达通原始卡号
        $octopus_balance = $request->input('octopus_balance'); // 八达通余额
        $is_admin_octopus_card = $request->boolean('is_admin_octopus_card'); // 是否为管理员八达通
        $is_free_octopus_card = $request->boolean('is_free_octopus_card'); // 是否为免费八达通
        $octopus_last_added_value_type = $request->input('octopus_last_added_value_type'); // 八达通最后增值类型
        $octopus_last_added_value_date = $request->input('octopus_last_added_value_date'); // 八达通最后增值日期
        $octopus_response_json = $request->input('octopus_response_json'); // 八达通响应JSON
        $remain_charge_value_record_number = $request->input('remain_charge_value_record_number'); // 剩余充电量记录编号
        $gmt_payment_status = $gmt_octopus_deduct; // 支付状态时间
        $kiosk_number = $request->input('kiosk_number'); // Kiosk编号
        $kiosk_name = $request->input('kiosk_name'); // Kiosk名称
        $total_amount = $request->input('total_amount'); // 合共金额

        $this->data = null;
        if (blank($charge_record_number) || blank($payment_method) || !PaymentMethod::hasValue($payment_method)) {
            $this->missingField('charge_record_number|payment_method');
            return $this->returnJson();
        }
        $charge_record = ChargeRecord::firstWhere('charge_record_number', $charge_record_number);
        if (blank($charge_record)) {
            $this->missingField('charge_record_number');
            return $this->returnJson();
        }

        $charge_payment_record = ChargePaymentRecord::where('charge_record_number', $charge_record_number)
            ->where('octopus_receipt_number', $octopus_receipt_number)
            ->where('gmt_octopus_deduct', $gmt_octopus_deduct)
            ->first();

        if (blank($charge_payment_record)) {
            $remain_charge_value_record = null;
            if (filled($remain_charge_value_record_number)) {
                $remain_charge_value_record = RemainChargeValueRecord::firstWhere('remain_charge_value_record_number', $remain_charge_value_record_number);
                if (blank($remain_charge_value_record)) {
                    $this->missingField('remain_charge_value_record_number');
                    return $this->returnJson();
                }
            }

            if ($payment_method === PaymentMethod::Octopus) {
                $identity_number = $octopus_card_number;
                $identity_type = IdentityType::Octopus;
            } else {
                $identity_number = substr($charge_record_number, -4);
                $identity_type = IdentityType::UnplugCode;
            }

            $charge_payment_record = new ChargePaymentRecord;
            $charge_payment_record->merchant_number = $charge_record->merchant_number; // 商户编号
            $charge_payment_record->site_number = $charge_record->site_number; // 站点编号
            $charge_payment_record->currency_code = $charge_record->currency_code; // 货币代码
            $charge_payment_record->currency_symbol = $charge_record->currency_symbol; // 货币符号
            $charge_payment_record->charge_record_number = $charge_record_number; // 充电记录编号
            $charge_payment_record->charge_payment_record_number = $charge_record_number . env('DELIMITER') . generateHashCode($charge_record_number . time()); // 充电支付记录编号
            $charge_payment_record->payment_device = $payment_device; // 支付设备
            $charge_payment_record->payment_method = $payment_method; // 支付方式
            $charge_payment_record->payment_status = $payment_status; // 支付状态
            $charge_payment_record->gmt_payment_status = $gmt_payment_status; // 支付状态时间
            $charge_payment_record->charge_tariff_scheme = ChargeTariffScheme::PrePaid; // 充电收费方案
            $charge_payment_record->charge_value_type = $charge_value_type; // 充电量类型
            $charge_payment_record->is_top_up = (int)$is_top_up; // 是否为续充
            $charge_payment_record->charge_value = $charge_value; // 充电量
            $charge_payment_record->is_idling_penalty_only = $is_idling_penalty_only; // 是否仅闲置罚款
            $charge_payment_record->is_charge_arrears = $is_charge_arrears; // 是否充电欠款
            $charge_payment_record->charge_value_amount = $charge_value_amount; // 充电量金额
            $charge_payment_record->idling_penalty_time = $idling_penalty_time; // 闲置罚款时间
            $charge_payment_record->idling_penalty_amount = $idling_penalty_amount; // 闲置罚款金额
            $charge_payment_record->gmt_idling_penalty_start = $gmt_idling_penalty_start; // 闲置罚款开始时间
            $charge_payment_record->gmt_idling_penalty_stop = $gmt_idling_penalty_stop; // 闲置罚款结束时间
            $charge_payment_record->total_amount = filled($total_amount) ? $total_amount : $charge_value_amount; // 金额
            $charge_payment_record->actual_payment_amount = $charge_payment_record->total_amount; // 实际付款金额
            $charge_payment_record->octopus_transaction_id = $octopus_transaction_id; // 八达通交易ID
            $charge_payment_record->gmt_receipt_start = $gmt_receipt_start; // 收据开始时间
            $charge_payment_record->gmt_receipt_stop = $gmt_receipt_stop; // 收据结束时间
            $charge_payment_record->octopus_receipt_number = $octopus_receipt_number; // 八达通收据编号
            $charge_payment_record->gmt_octopus_deduct = $gmt_octopus_deduct; // 八达通扣款时间
            $charge_payment_record->octopus_device_number = $octopus_device_number; // 八达通设备编号
            $charge_payment_record->octopus_card_type = $octopus_card_type; // 八达通卡类型
            $charge_payment_record->octopus_card_number = $octopus_card_number; // 八达通卡号
            $charge_payment_record->octopus_raw_card_number = $octopus_raw_card_number; // 八达通原始卡号
            $charge_payment_record->octopus_balance = $octopus_balance; // 八达通余额
            $charge_payment_record->is_admin_octopus_card = (int)$is_admin_octopus_card; // 是否为管理员八达通
            $charge_payment_record->is_free_octopus_card = (int)$is_free_octopus_card; // 是否为免费八达通
            $charge_payment_record->octopus_last_added_value_type = $octopus_last_added_value_type; // 八达通最后增值类型
            $charge_payment_record->octopus_last_added_value_date = $octopus_last_added_value_date; // 八达通最后增值日期
            $charge_payment_record->octopus_response_json = $octopus_response_json; // 八达通响应JSON
            $charge_payment_record->kiosk_number = $kiosk_number; // Kiosk编号
            $charge_payment_record->kiosk_name = $kiosk_name; // Kiosk名称

            // 计算商户手续费
            $charge_payment_record->merchant_handling_fee = 0;
            $charge_payment_record->merchant_receivable = $charge_payment_record->actual_payment_amount;
            if (filled($charge_record->merchant_handling_fee_rate) && $charge_record->merchant_handling_fee_rate > 0) {
                $merchant_handling_fee_rate = $charge_record->merchant_handling_fee_rate;
                // 因为是百分比，所以需要除以100
                $merchant_handling_fee_rate /= 100;
                // 计算商户手续费金额，向下取整保留两位小数
                $merchant_handling_fee = (float)bcmul($charge_payment_record->actual_payment_amount, $merchant_handling_fee_rate, 0);
                $charge_payment_record->merchant_handling_fee = $merchant_handling_fee;
                // 商户应收 = 实付金额 - 商户手续费
                $charge_payment_record->merchant_receivable = $charge_payment_record->actual_payment_amount - $charge_payment_record->merchant_handling_fee;
            }

            DB::transaction(function () use (
                &$charge_payment_record,
                &$charge_record,
                &$remain_charge_value_record,
                $identity_type,
                $identity_number,
                $charge_value_amount_calculation_json,
                $idling_penalty_amount_calculation_json
            ) {
                try {
                    if (filled($remain_charge_value_record)) {
                        // 更新remain_charge_value_record表
                        $remain_charge_value_record->used_charge_record_number = $charge_record->charge_record_number;
                        $remain_charge_value_record->gmt_used = date('Y-m-d H:i:s');
                        $remain_charge_value_record->saveOrFail();

                        // 根据chargeRecord的connector的connector_setting_number，筛选出connector_setting_number和传入的connector_number的connector_setting_number相等的数据
                        $connector_setting_number = $charge_record?->connector?->connector_setting_number ?? null;
                        // 失效其他identity_type和identity_number相等的数据
                        RemainChargeValueRecord::whereHas('chargeRecord.connector', function ($query) use ($connector_setting_number) {
                            $query->where('connector_setting_number', $connector_setting_number);
                        })
                            ->where('identity_type', $remain_charge_value_record->identity_type)
                            ->where('identity_number', $remain_charge_value_record->identity_number)
                            ->where('remain_charge_value_record_number', '<>', $remain_charge_value_record->remain_charge_value_record_number)
                            ->whereNull('gmt_invalid')
                            ->update(['gmt_invalid' => date('Y-m-d H:i:s')]);

                        $charge_record->pre_paid_use_remain_charge_value = $remain_charge_value_record->remain_charge_value; // 预付使用剩余充电量
                        $charge_payment_record->use_remain_charge_value = $remain_charge_value_record->remain_charge_value;
                    }

                    $charge_payment_record->saveOrFail();

                    // 保存支付流水
                    $charge_accounting_record = new ChargeAccountingRecord;
                    $charge_accounting_record->merchant_number = $charge_payment_record->merchant_number;
                    $charge_accounting_record->site_number = $charge_payment_record->site_number;
                    $charge_accounting_record->currency_code = $charge_payment_record->currency_code;
                    $charge_accounting_record->currency_symbol = $charge_payment_record->currency_symbol;
                    $charge_accounting_record->payment_device = $charge_payment_record->payment_device;
                    $charge_accounting_record->payment_method = $charge_payment_record->payment_method;
                    $charge_accounting_record->transaction_type = ChargeAccountingTransactionType::Income;
                    $charge_accounting_record->transaction_category = ChargeAccountingTransactionCategory::KioskPrePaidOctopusDeduct;
                    $charge_accounting_record->source_transaction_number = $charge_payment_record->charge_payment_record_number;
                    $charge_accounting_record->amount = $charge_payment_record->actual_payment_amount;
                    $charge_accounting_record->gmt_deduct = $charge_payment_record->gmt_octopus_deduct;
                    $charge_accounting_record->octopus_card_number = $charge_payment_record->octopus_card_number;
                    $charge_accounting_record->saveOrFail();

                    // 充电欠款
                    if ($charge_payment_record->is_charge_arrears) {
                        $charge_arrears_record = ChargeArrearsRecord::firstWhere('charge_record_number', $charge_record->charge_record_number);
                        if (blank($charge_arrears_record)) {
                            logger('API: /api/kiosk/octopusPrePaidDeduct; charge_arrears_record not found', [
                                'charge_record_number' => $charge_record->charge_record_number,
                            ]);
                        } else {
                            $current_date_time = now()->format('Y-m-d H:i:s');
                            // status_log
                            if (filled($charge_arrears_record->status_log)) {
                                $charge_arrears_record->status_log .= PHP_EOL;
                            }
                            $charge_arrears_record->status_log .= "$current_date_time - by: api, status: [{$charge_arrears_record->charge_arrears_status}] -> [" . ChargeArrearsStatus::Completed . "], reason: octopus pre paid deduct";
                            $charge_arrears_record->charge_arrears_status = ChargeArrearsStatus::Completed;
                            $charge_arrears_record->gmt_charge_arrears_status = now();
                            $charge_arrears_record->saveOrFail();
                        }
                    }

                    // 保存计算json
                    if (blank($charge_payment_record_calculation_model = $charge_payment_record->chargePaymentRecordCalculation)) {
                        $charge_payment_record_calculation_model = new ChargePaymentRecordCalculation;
                        $charge_payment_record_calculation_model->charge_payment_record_number = $charge_payment_record->charge_payment_record_number;
                    }
                    $charge_payment_record_calculation_model->currency_code = $charge_payment_record->currency_code;
                    $charge_payment_record_calculation_model->currency_symbol = $charge_payment_record->currency_symbol;
                    $charge_payment_record_calculation_model->charge_value_amount_calculation_json = $charge_value_amount_calculation_json; // 充电量金额计算JSON
                    $charge_payment_record_calculation_model->idling_penalty_amount_calculation_json = $idling_penalty_amount_calculation_json; // 充电量金额计算JSON
                    $charge_payment_record_calculation_model->saveOrFail();

                    $charge_record->identity_type = blank($charge_record->identity_type) ? $identity_type : $charge_record->identity_type;
                    $charge_record->identity_number = blank($charge_record->identity_number) ? $identity_number : $charge_record->identity_number;
                    $charge_record->charge_value_amount += $charge_payment_record->charge_value_amount; // 充电量金额

                    if ($charge_payment_record->is_top_up !== true) {
                        $charge_record->pre_paid_selected_charge_value = $charge_payment_record->charge_value;
                    }
                    $charge_record->pre_paid_purchase_charge_value += $charge_payment_record->charge_value; // 预付购买充电量
                    $charge_record->pre_paid_charge_value = $charge_record->pre_paid_purchase_charge_value + $charge_record->pre_paid_use_remain_charge_value; // 预付充电量
                    if ($charge_payment_record->is_idling_penalty_only === true) {
                        $charge_record->gmt_idling_penalty_locked = $charge_payment_record->gmt_octopus_deduct; // 闲置罚款已锁定时间
                        $charge_record->idling_penalty_amount += $charge_payment_record->idling_penalty_amount; // 闲置罚款金额
                    }

                    $charge_record->total_amount += $charge_payment_record->total_amount; // 合共金额
                    $charge_record->actual_payment_amount = $charge_record->total_amount; // 实际支付金额
                    $charge_record->merchant_handling_fee += $charge_payment_record->merchant_handling_fee; // 商户手续费
                    $charge_record->merchant_receivable += $charge_payment_record->merchant_receivable; // 商户应收款
                    $charge_record->gmt_sync_to_cloud = null; // 同步至云端时间

                    $charge_record->saveOrFail();
                } catch (Exception $e) {
                    logger($e->getMessage());
                    logger('API: /api/kiosk/octopusPrePaidDeduct; remain_charge_value_record/charge_payment_record/charge_record/charge_value_amount_calculation_json/idling_penalty_amount_calculation_json save failed', [
                        'remain_charge_value_record' => $remain_charge_value_record,
                        'charge_record' => $charge_record,
                        'charge_payment_record' => $charge_payment_record,
                        'charge_value_amount_calculation_json' => $charge_value_amount_calculation_json,
                        'idling_penalty_amount_calculation_json' => $idling_penalty_amount_calculation_json,
                    ]);
                    throw new \Exception('Internal Server Error', 500);
                }

            });

        }

        $this->data = array(
            'charge_record' => $this->chargeRecordArrayToJson($charge_record),
            'charge_payment_record' => isset($charge_payment_record) && filled($charge_payment_record) ? $this->chargePaymentRecordArrayToJson($charge_payment_record) : null,
        );

        try {
            // 通知Kiosk充电记录
            $this->requestNotifyKioskChargeRecord($charge_record);
        } catch (Exception $e) {
        }

        return $this->returnJson();
    }

    /**
     * POS先付扣款
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-16
     */
    public function posPrePaidDeduct(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $charge_record_number = $request->input('charge_record_number'); // 充电记录编号
        $payment_device = PaymentDeviceEnum::Pos; // 付款设备
        $payment_method = $request->input('payment_method'); // 支付方式
        $payment_status = $request->input('payment_status', PaymentStatusEnum::Completed); // 支付状态
        $charge_value_type = $request->input('charge_value_type'); // 充电量类型
        $is_top_up = $request->boolean('is_top_up'); // 是否为续充
        $charge_value = $request->input('charge_value'); // 充电量
        $is_idling_penalty_only = $request->boolean('is_idling_penalty_only', false); // 是否仅闲置罚款
        $is_charge_arrears = $request->boolean('is_charge_arrears', false); // 是否充电欠款
        $charge_value_amount = $request->input('charge_value_amount'); // 充电量金额
        $charge_value_amount_calculation_json = $request->input('charge_value_amount_calculation_json'); // 充电量金额计算JSON
        $idling_penalty_time = $request->input('idling_penalty_time'); // 闲置罚款时间
        $idling_penalty_amount = $request->input('idling_penalty_amount'); // 闲置罚款金额
        $idling_penalty_amount_calculation_json = $request->input('idling_penalty_amount_calculation_json'); // 闲置罚款金额计算JSON
        $gmt_idling_penalty_start = $request->input('gmt_idling_penalty_start'); // 闲置罚款开始时间
        $gmt_idling_penalty_stop = $request->input('gmt_idling_penalty_stop'); // 闲置罚款结束时间
        $gmt_receipt_start = $request->input('gmt_receipt_start'); // 收据开始时间
        $gmt_receipt_stop = $request->input('gmt_receipt_stop'); // 收据结束时间
        $pos_vendor = $request->input('pos_vendor'); // POS供应商
        $pos_receipt_number = $request->input('pos_receipt_number'); // POS收据编号
        $gmt_pos_deduct = $request->input('gmt_pos_deduct'); // POS扣款时间
        $pos_transaction_id = $request->input('pos_transaction_id'); // POS交易ID
        $pos_payment_method_name = $request->input('pos_payment_method_name'); // POS支付方式名称
        $pos_card_number = $request->input('pos_card_number'); // POS卡号
        $pos_trace_no = $request->input('pos_trace_no'); // POS追踪编号
        $pos_reference_id = $request->input('pos_reference_id'); // POS参考编号
        $pos_response_json = $request->input('pos_response_json'); // POS响应JSON
        $gmt_payment_status = $gmt_pos_deduct; // 支付状态时间
        $kiosk_number = $request->input('kiosk_number'); // Kiosk编号
        $kiosk_name = $request->input('kiosk_name'); // Kiosk名称
        $is_idling_penalty_only = $request->boolean('is_idling_penalty_only', false); // 是否仅闲置罚款
        $is_charge_arrears = $request->boolean('is_charge_arrears', false); // 是否充电欠款
        $total_amount = $request->input('total_amount'); // 合共金额

        $this->data = null;
        if (blank($charge_record_number) || blank($payment_method) || !PaymentMethod::hasValue($payment_method)) {
            $this->missingField('charge_record_number|payment_method');
            return $this->returnJson();
        }
        $charge_record = ChargeRecord::firstWhere('charge_record_number', $charge_record_number);
        if (blank($charge_record)) {
            $this->missingField('charge_record_number');
            return $this->returnJson();
        }

        $charge_payment_record = ChargePaymentRecord::where('charge_record_number', $charge_record_number)
            ->where('pos_receipt_number', $pos_receipt_number)
            ->where('gmt_pos_deduct', $gmt_pos_deduct)
            ->first();

        if (blank($charge_payment_record)) {
            if ($payment_method === PaymentMethod::Octopus) {
                $identity_number = $pos_card_number;
                $identity_type = IdentityType::Octopus;
            } else {
                $identity_number = substr($charge_record_number, -4);
                $identity_type = IdentityType::UnplugCode;
            }

            $charge_payment_record = new ChargePaymentRecord;
            $charge_payment_record->merchant_number = $charge_record->merchant_number; // 商户编号
            $charge_payment_record->site_number = $charge_record->site_number; // 站点编号
            $charge_payment_record->currency_code = $charge_record->currency_code; // 货币代码
            $charge_payment_record->currency_symbol = $charge_record->currency_symbol; // 货币符号
            $charge_payment_record->charge_record_number = $charge_record_number; // 充电记录编号
            $charge_payment_record->charge_payment_record_number = $charge_record_number . env('DELIMITER') . generateHashCode($charge_record_number . time()); // 充电支付记录编号
            $charge_payment_record->payment_device = $payment_device; // 付款设备
            $charge_payment_record->payment_method = $payment_method; // 支付方式
            $charge_payment_record->payment_status = $payment_status; // 支付状态
            $charge_payment_record->gmt_payment_status = $gmt_payment_status; // 支付状态时间
            $charge_payment_record->charge_tariff_scheme = ChargeTariffScheme::PrePaid; // 充电收费方案
            $charge_payment_record->charge_value_type = $charge_value_type; // 充电量类型
            $charge_payment_record->is_top_up = (int)$is_top_up; // 是否为续充
            $charge_payment_record->charge_value = $charge_value; // 充电量
            $charge_payment_record->is_idling_penalty_only = $is_idling_penalty_only; // 是否仅闲置罚款
            $charge_payment_record->is_charge_arrears = $is_charge_arrears; // 是否充电欠款
            $charge_payment_record->charge_value_amount = $charge_value_amount; // 充电量金额
            $charge_payment_record->idling_penalty_time = $idling_penalty_time; // 闲置罚款时间
            $charge_payment_record->idling_penalty_amount = $idling_penalty_amount; // 闲置罚款金额
            $charge_payment_record->gmt_idling_penalty_start = $gmt_idling_penalty_start; // 闲置罚款开始时间
            $charge_payment_record->gmt_idling_penalty_stop = $gmt_idling_penalty_stop; // 闲置罚款结束时间
            $charge_payment_record->total_amount = filled($total_amount) ? $total_amount : $charge_value_amount; // 金额
            $charge_payment_record->actual_payment_amount = $charge_payment_record->total_amount; // 实际付款金额
            $charge_payment_record->gmt_receipt_start = $gmt_receipt_start; // 收据开始时间
            $charge_payment_record->gmt_receipt_stop = $gmt_receipt_stop; // 收据结束时间
            $charge_payment_record->pos_vendor = $pos_vendor; // POS供应商
            $charge_payment_record->pos_receipt_number = $pos_receipt_number; // POS收据编号
            $charge_payment_record->gmt_pos_deduct = $gmt_pos_deduct; // POS扣款时间
            $charge_payment_record->pos_transaction_id = $pos_transaction_id; // POS交易ID
            $charge_payment_record->pos_payment_method_name = $pos_payment_method_name; // POS支付方式名称
            $charge_payment_record->pos_card_number = $pos_card_number; // POS卡号
            $charge_payment_record->pos_trace_no = $pos_trace_no; // POS追踪编号
            $charge_payment_record->pos_reference_id = $pos_reference_id; // POS参考编号
            $charge_payment_record->pos_response_json = $pos_response_json; // POS响应JSON
            $charge_payment_record->kiosk_number = $kiosk_number; // Kiosk编号
            $charge_payment_record->kiosk_name = $kiosk_name; // Kiosk名称

            // 计算商户手续费
            $charge_payment_record->merchant_handling_fee = 0;
            $charge_payment_record->merchant_receivable = $charge_payment_record->actual_payment_amount;
            if (filled($charge_record->merchant_handling_fee_rate) && $charge_record->merchant_handling_fee_rate > 0) {
                $merchant_handling_fee_rate = $charge_record->merchant_handling_fee_rate;
                // 因为是百分比，所以需要除以100
                $merchant_handling_fee_rate /= 100;
                // 计算商户手续费金额，向下取整保留两位小数
                $merchant_handling_fee = (float)bcmul($charge_payment_record->actual_payment_amount, $merchant_handling_fee_rate, 0);
                $charge_payment_record->merchant_handling_fee = $merchant_handling_fee;
                // 商户应收 = 实付金额 - 商户手续费
                $charge_payment_record->merchant_receivable = $charge_payment_record->actual_payment_amount - $charge_payment_record->merchant_handling_fee;
            }

            DB::transaction(function () use (
                &$charge_payment_record,
                &$charge_record,
                $identity_type,
                $identity_number,
                $charge_value_amount_calculation_json,
                $idling_penalty_amount_calculation_json
            ) {
                try {
                    $charge_payment_record->saveOrFail();

                    // 保存支付流水
                    $charge_accounting_record = new ChargeAccountingRecord;
                    $charge_accounting_record->merchant_number = $charge_payment_record->merchant_number;
                    $charge_accounting_record->site_number = $charge_payment_record->site_number;
                    $charge_accounting_record->currency_code = $charge_payment_record->currency_code;
                    $charge_accounting_record->currency_symbol = $charge_payment_record->currency_symbol;
                    $charge_accounting_record->payment_device = $charge_payment_record->payment_device;
                    $charge_accounting_record->payment_method = $charge_payment_record->payment_method;
                    $charge_accounting_record->transaction_type = ChargeAccountingTransactionType::Income;
                    $charge_accounting_record->transaction_category = ChargeAccountingTransactionCategory::KioskPrePaidPosDeduct;
                    $charge_accounting_record->source_transaction_number = $charge_payment_record->charge_payment_record_number;
                    $charge_accounting_record->amount = $charge_payment_record->actual_payment_amount;
                    $charge_accounting_record->gmt_deduct = $charge_payment_record->gmt_pos_deduct;
                    $charge_accounting_record->pos_payment_method_name = $charge_payment_record->pos_payment_method_name;
                    $charge_accounting_record->pos_card_number = $charge_payment_record->pos_card_number;
                    $charge_accounting_record->saveOrFail();

                    // 充电欠款
                    if ($charge_payment_record->is_charge_arrears) {
                        $charge_arrears_record = ChargeArrearsRecord::firstWhere('charge_record_number', $charge_record->charge_record_number);
                        if (blank($charge_arrears_record)) {
                            logger('API: /api/kiosk/posPrePaidDeduct; charge_arrears_record not found', [
                                'charge_record_number' => $charge_record->charge_record_number,
                            ]);
                        } else {
                            $current_date_time = now()->format('Y-m-d H:i:s');
                            // status_log
                            if (filled($charge_arrears_record->status_log)) {
                                $charge_arrears_record->status_log .= PHP_EOL;
                            }
                            $charge_arrears_record->status_log .= "$current_date_time - by: api, status: [{$charge_arrears_record->charge_arrears_status}] -> [" . ChargeArrearsStatus::Completed . "], reason: pos pre paid deduct";
                            $charge_arrears_record->charge_arrears_status = ChargeArrearsStatus::Completed;
                            $charge_arrears_record->gmt_charge_arrears_status = now();
                            $charge_arrears_record->saveOrFail();
                        }
                    }

                    // 保存计算json
                    if (blank($charge_payment_record_calculation_model = $charge_payment_record->chargePaymentRecordCalculation)) {
                        $charge_payment_record_calculation_model = new ChargePaymentRecordCalculation;
                        $charge_payment_record_calculation_model->charge_payment_record_number = $charge_payment_record->charge_payment_record_number;
                    }
                    $charge_payment_record_calculation_model->currency_code = $charge_payment_record->currency_code;
                    $charge_payment_record_calculation_model->currency_symbol = $charge_payment_record->currency_symbol;
                    $charge_payment_record_calculation_model->charge_value_amount_calculation_json = $charge_value_amount_calculation_json; // 充电量金额计算JSON
                    $charge_payment_record_calculation_model->idling_penalty_amount_calculation_json = $idling_penalty_amount_calculation_json; // 充电量金额计算JSON
                    $charge_payment_record_calculation_model->saveOrFail();

                    $charge_record->identity_type = blank($charge_record->identity_type) ? $identity_type : $charge_record->identity_type;
                    $charge_record->identity_number = blank($charge_record->identity_number) ? $identity_number : $charge_record->identity_number;
                    $charge_record->charge_value_amount += $charge_payment_record->charge_value_amount; // 充电量金额

                    if ($charge_payment_record->is_top_up !== true) {
                        $charge_record->pre_paid_selected_charge_value = $charge_payment_record->charge_value;
                    }
                    $charge_record->pre_paid_purchase_charge_value += $charge_payment_record->charge_value; // 预付购买充电量
                    $charge_record->pre_paid_charge_value = $charge_record->pre_paid_purchase_charge_value + $charge_record->pre_paid_use_remain_charge_value; // 预付充电量
                    if ($charge_payment_record->is_idling_penalty_only === true) {
                        $charge_record->gmt_idling_penalty_locked = $charge_payment_record->gmt_pos_deduct; // 闲置罚款已锁定时间
                        $charge_record->idling_penalty_amount += $charge_payment_record->idling_penalty_amount; // 闲置罚款金额
                    }

                    $charge_record->total_amount += $charge_payment_record->total_amount; // 合共金额
                    $charge_record->actual_payment_amount = $charge_record->total_amount; // 实际支付金额
                    $charge_record->merchant_handling_fee += $charge_payment_record->merchant_handling_fee; // 商户手续费
                    $charge_record->merchant_receivable += $charge_payment_record->merchant_receivable; // 商户应收款
                    $charge_record->gmt_sync_to_cloud = null; // 同步至云端时间

                    $charge_record->saveOrFail();
                } catch (Exception $e) {
                    logger($e->getMessage());
                    logger('API: /api/kiosk/posPrePaidDeduct; charge_payment_record/charge_record/charge_value_amount_calculation_json save failed/$idling_penalty_amount_calculation_json', [
                        'charge_record' => $charge_record,
                        'charge_payment_record' => $charge_payment_record,
                        'charge_value_amount_calculation_json' => $charge_value_amount_calculation_json,
                        'idling_penalty_amount_calculation_json' => $idling_penalty_amount_calculation_json,
                    ]);
                    throw new \Exception('Internal Server Error', 500);
                }

            });

        }

        $this->data = array(
            'charge_record' => $this->chargeRecordArrayToJson($charge_record),
            'charge_payment_record' => isset($charge_payment_record) && filled($charge_payment_record) ? $this->chargePaymentRecordArrayToJson($charge_payment_record) : null,
        );

        try {
            // 通知Kiosk充电记录
            $this->requestNotifyKioskChargeRecord($charge_record);
        } catch (Exception $e) {
        }

        return $this->returnJson();
    }

    /**
     * 后付扣款
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-16
     */
    public function octopusPostPaidDeduct(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $charge_record_number = $request->input('charge_record_number'); // 充电记录编号
        $payment_device = PaymentDeviceEnum::Octopus; // 支付设备
        $payment_method = $request->input('payment_method'); // 支付方式
        $payment_status = $request->input('payment_status', PaymentStatusEnum::Completed); // 支付状态
        $charge_value_type = $request->input('charge_value_type'); // 充电量类型
        $is_idling_penalty_only = $request->boolean('is_idling_penalty_only', false); // 是否仅闲置罚款
        $is_charge_arrears = $request->boolean('is_charge_arrears', false); // 是否充电欠款
        $charge_value = $request->input('charge_value'); // 充电量
        $charge_value_amount = $request->input('charge_value_amount'); // 充电量金额
        $charge_value_amount_calculation_json = $request->input('charge_value_amount_calculation_json'); // 充电量金额计算JSON
        $idling_penalty_time = $request->input('idling_penalty_time'); // 闲置罚款时间
        $idling_penalty_amount = $request->input('idling_penalty_amount'); // 闲置罚款金额
        $idling_penalty_amount_calculation_json = $request->input('idling_penalty_amount_calculation_json'); // 闲置罚款金额计算JSON
        $gmt_idling_penalty_start = $request->input('gmt_idling_penalty_start'); // 闲置罚款开始时间
        $gmt_idling_penalty_stop = $request->input('gmt_idling_penalty_stop'); // 闲置罚款结束时间
        $total_amount = $request->input('total_amount'); // 合共金额
        $octopus_transaction_id = $request->input('octopus_transaction_id'); // 八达通交易ID
        $gmt_receipt_start = $request->input('gmt_receipt_start'); // 收据开始时间
        $gmt_receipt_stop = $request->input('gmt_receipt_stop'); // 收据结束时间
        $octopus_receipt_number = $request->input('octopus_receipt_number'); // 八达通收据编号
        $gmt_octopus_deduct = $request->input('gmt_octopus_deduct'); // 八达通扣款时间
        $octopus_device_number = $request->input('octopus_device_number'); // 八达通设备编号
        $octopus_card_type = $request->input('octopus_card_type'); // 八达通卡类型
        $octopus_card_number = $request->input('octopus_card_number'); // 八达通卡号
        $octopus_raw_card_number = $request->input('octopus_raw_card_number'); // 八达通原始卡号
        $octopus_balance = $request->input('octopus_balance'); // 八达通余额
        $is_admin_octopus_card = $request->boolean('is_admin_octopus_card'); // 是否为管理员八达通
        $is_free_octopus_card = $request->boolean('is_free_octopus_card'); // 是否为免费八达通
        $octopus_last_added_value_type = $request->input('octopus_last_added_value_type'); // 八达通最后增值类型
        $octopus_last_added_value_date = $request->input('octopus_last_added_value_date'); // 八达通最后增值日期
        $octopus_response_json = $request->input('octopus_response_json'); // 八达通响应JSON
        $gmt_payment_status = $gmt_octopus_deduct; // 支付状态时间
        $kiosk_number = $request->input('kiosk_number'); // Kiosk编号
        $kiosk_name = $request->input('kiosk_name'); // Kiosk名称

        $this->data = null;
        if (blank($charge_record_number) || blank($payment_method) || !PaymentMethod::hasValue($payment_method)) {
            $this->missingField('charge_record_number|payment_method');
            return $this->returnJson();
        }
        $charge_record = ChargeRecord::firstWhere('charge_record_number', $charge_record_number);
        if (blank($charge_record)) {
            $this->missingField('charge_record_number');
            return $this->returnJson();
        }

        // 查询八达通扣款记录
        $charge_payment_record = ChargePaymentRecord::where('charge_record_number', $charge_record_number)
            ->where('octopus_receipt_number', $octopus_receipt_number)
            ->where('gmt_octopus_deduct', $gmt_octopus_deduct)
            ->first();
        // 没有就新增
        if (blank($charge_payment_record)) {
            $charge_payment_record = new ChargePaymentRecord;
            $charge_payment_record->merchant_number = $charge_record->merchant_number; // 商户编号
            $charge_payment_record->site_number = $charge_record->site_number; // 站点编号
            $charge_payment_record->currency_code = $charge_record->currency_code; // 货币代码
            $charge_payment_record->currency_symbol = $charge_record->currency_symbol; // 货币符号
            $charge_payment_record->charge_record_number = $charge_record_number; // 充电记录编号
            $charge_payment_record->charge_payment_record_number = $charge_record_number . env('DELIMITER') . generateHashCode($charge_record_number . time()); // 充电支付记录编号
            $charge_payment_record->payment_device = $payment_device; // 支付设备
            $charge_payment_record->payment_method = $payment_method; // 支付方式
            $charge_payment_record->payment_status = $payment_status; // 支付状态
            $charge_payment_record->gmt_payment_status = $gmt_payment_status; // 支付状态时间
            $charge_payment_record->charge_tariff_scheme = ChargeTariffScheme::PostPaid; // 充电收费方案
            $charge_payment_record->charge_value_type = $charge_value_type; // 充电量类型
            $charge_payment_record->charge_value = $charge_value; // 充电量
            $charge_payment_record->is_idling_penalty_only = $is_idling_penalty_only; // 是否仅闲置罚款
            $charge_payment_record->is_charge_arrears = $is_charge_arrears; // 是否充电欠款
            $charge_payment_record->charge_value_amount = $charge_value_amount; // 充电量金额
            $charge_payment_record->idling_penalty_time = $idling_penalty_time; // 闲置罚款时间
            $charge_payment_record->idling_penalty_amount = $idling_penalty_amount; // 闲置罚款金额
            $charge_payment_record->gmt_idling_penalty_start = $gmt_idling_penalty_start; // 闲置罚款开始时间
            $charge_payment_record->gmt_idling_penalty_stop = $gmt_idling_penalty_stop; // 闲置罚款结束时间
            $charge_payment_record->total_amount = $total_amount; // 合共金额
            $charge_payment_record->actual_payment_amount = $charge_payment_record->total_amount; // 实际付款金额
            $charge_payment_record->octopus_transaction_id = $octopus_transaction_id; // 八达通交易ID
            $charge_payment_record->gmt_receipt_start = $gmt_receipt_start; // 收据开始时间
            $charge_payment_record->gmt_receipt_stop = $gmt_receipt_stop; // 收据结束时间
            $charge_payment_record->octopus_receipt_number = $octopus_receipt_number; // 八达通收据编号
            $charge_payment_record->gmt_octopus_deduct = $gmt_octopus_deduct; // 八达通扣款时间
            $charge_payment_record->octopus_device_number = $octopus_device_number; // 八达通设备编号
            $charge_payment_record->octopus_card_type = $octopus_card_type; // 八达通卡类型
            $charge_payment_record->octopus_card_number = $octopus_card_number; // 八达通卡号
            $charge_payment_record->octopus_raw_card_number = $octopus_raw_card_number; // 八达通原始卡号
            $charge_payment_record->octopus_balance = $octopus_balance; // 八达通余额
            $charge_payment_record->is_admin_octopus_card = (int)$is_admin_octopus_card; // 是否为管理员八达通
            $charge_payment_record->is_free_octopus_card = (int)$is_free_octopus_card; // 是否为免费八达通
            $charge_payment_record->octopus_last_added_value_type = $octopus_last_added_value_type; // 八达通最后增值类型
            $charge_payment_record->octopus_last_added_value_date = $octopus_last_added_value_date; // 八达通最后增值日期
            $charge_payment_record->octopus_response_json = $octopus_response_json; // 八达通响应JSON
            $charge_payment_record->kiosk_number = $kiosk_number; // Kiosk编号
            $charge_payment_record->kiosk_name = $kiosk_name; // Kiosk名称

            // 计算商户手续费
            $charge_payment_record->merchant_handling_fee = 0;
            $charge_payment_record->merchant_receivable = $charge_payment_record->actual_payment_amount;
            if (filled($charge_record->merchant_handling_fee_rate) && $charge_record->merchant_handling_fee_rate > 0) {
                $merchant_handling_fee_rate = $charge_record->merchant_handling_fee_rate;
                // 因为是百分比，所以需要除以100
                $merchant_handling_fee_rate /= 100;
                // 计算商户手续费金额，向下取整保留两位小数
                $merchant_handling_fee = (float)bcmul($charge_payment_record->actual_payment_amount, $merchant_handling_fee_rate, 0);
                $charge_payment_record->merchant_handling_fee = $merchant_handling_fee;
                // 商户应收 = 实付金额 - 商户手续费
                $charge_payment_record->merchant_receivable = $charge_payment_record->actual_payment_amount - $charge_payment_record->merchant_handling_fee;
            }
            DB::transaction(function () use (
                &$charge_payment_record,
                &$charge_record,
                $charge_value_amount_calculation_json,
                $idling_penalty_amount_calculation_json
            ) {
                try {
                    $charge_payment_record->saveOrFail();

                    // 保存支付流水
                    $charge_accounting_record = new ChargeAccountingRecord;
                    $charge_accounting_record->merchant_number = $charge_payment_record->merchant_number;
                    $charge_accounting_record->site_number = $charge_payment_record->site_number;
                    $charge_accounting_record->currency_code = $charge_payment_record->currency_code;
                    $charge_accounting_record->currency_symbol = $charge_payment_record->currency_symbol;
                    $charge_accounting_record->payment_device = $charge_payment_record->payment_device;
                    $charge_accounting_record->payment_method = $charge_payment_record->payment_method;
                    $charge_accounting_record->transaction_type = ChargeAccountingTransactionType::Income;
                    $charge_accounting_record->transaction_category = ChargeAccountingTransactionCategory::KioskPostPaidOctopusDeduct;
                    $charge_accounting_record->source_transaction_number = $charge_payment_record->charge_payment_record_number;
                    $charge_accounting_record->amount = $charge_payment_record->actual_payment_amount;
                    $charge_accounting_record->gmt_deduct = $charge_payment_record->gmt_octopus_deduct;
                    $charge_accounting_record->octopus_card_number = $charge_payment_record->octopus_card_number;
                    $charge_accounting_record->saveOrFail();

                    // 保存计算json
                    if (blank($charge_payment_record_calculation_model = $charge_payment_record->chargePaymentRecordCalculation)) {
                        $charge_payment_record_calculation_model = new ChargePaymentRecordCalculation;
                        $charge_payment_record_calculation_model->charge_payment_record_number = $charge_payment_record->charge_payment_record_number;
                    }
                    $charge_payment_record_calculation_model->currency_code = $charge_payment_record->currency_code;
                    $charge_payment_record_calculation_model->currency_symbol = $charge_payment_record->currency_symbol;
                    $charge_payment_record_calculation_model->charge_value_amount_calculation_json = $charge_value_amount_calculation_json; // 充电量金额计算JSON
                    $charge_payment_record_calculation_model->idling_penalty_amount_calculation_json = $idling_penalty_amount_calculation_json; // 充电量金额计算JSON
                    $charge_payment_record_calculation_model->saveOrFail();

                    // 充电欠款
                    if ($charge_payment_record->is_charge_arrears) {
                        $charge_arrears_record = ChargeArrearsRecord::firstWhere('charge_record_number', $charge_record->charge_record_number);
                        if (blank($charge_arrears_record)) {
                            logger('API: /api/kiosk/octopusPostPaidDeduct; charge_arrears_record not found', [
                                'charge_record_number' => $charge_record->charge_record_number,
                            ]);
                        } else {
                            $current_date_time = now()->format('Y-m-d H:i:s');
                            // status_log
                            if (filled($charge_arrears_record->status_log)) {
                                $charge_arrears_record->status_log .= PHP_EOL;
                            }
                            $charge_arrears_record->status_log .= "$current_date_time - by: api, status: [{$charge_arrears_record->charge_arrears_status}] -> [" . ChargeArrearsStatus::Completed . "], reason: octopus post paid deduct";
                            $charge_arrears_record->charge_arrears_status = ChargeArrearsStatus::Completed;
                            $charge_arrears_record->gmt_charge_arrears_status = now();
                            $charge_arrears_record->saveOrFail();
                        }
                    }

                    $charge_record->post_paid_purchase_charge_value = $charge_payment_record->charge_value;
                    $charge_record->charge_value_amount += $charge_payment_record->charge_value_amount; // 充电量金额
                    $charge_record->idling_penalty_amount += $charge_payment_record->idling_penalty_amount; // 闲置罚款金额
                    $charge_record->gmt_idling_penalty_locked = $charge_payment_record->gmt_octopus_deduct; // 闲置罚款已锁定时间
                    $charge_record->total_amount += $charge_payment_record->total_amount; // 合共金额
                    $charge_record->actual_payment_amount = $charge_record->total_amount; // 实际支付金额
                    $charge_record->merchant_handling_fee += $charge_payment_record->merchant_handling_fee; // 商户手续费
                    $charge_record->merchant_receivable += $charge_payment_record->merchant_receivable; // 商户应收款
                    $charge_record->gmt_sync_to_cloud = null; // 同步至云端时间

                    $charge_record->saveOrFail();
                } catch (Exception $e) {
                    logger($e->getMessage());
                    logger('API: /api/kiosk/octopusPostPaidDeduct; charge_payment_record/charge_record/charge_value_amount_calculation_json/idling_penalty_amount_calculation_json save failed', [
                        'charge_record' => $charge_record,
                        'charge_payment_record' => $charge_payment_record,
                        'charge_value_amount_calculation_json' => $charge_value_amount_calculation_json,
                        'idling_penalty_amount_calculation_json' => $idling_penalty_amount_calculation_json,
                    ]);
                    throw new \Exception('Internal Server Error', 500);
                }
            });
        }

        $this->data = [
            'charge_record' => $this->chargeRecordArrayToJson($charge_record),
            'charge_payment_record' => isset($charge_payment_record) && filled($charge_payment_record) ? $this->chargePaymentRecordArrayToJson($charge_payment_record) : null,
        ];

        try {
            // 通知Kiosk充电记录
            $this->requestNotifyKioskChargeRecord($charge_record);
        } catch (Exception $e) {
            logger('API: /api/kiosk/octopusPostPaidDeduct; requestNotifyKioskChargeRecord error', ['error' => $e->getMessage()]);
        }

        return $this->returnJson();
    }

    /**
     * 后付扣款
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-16
     */
    public function posPostPaidDeduct(Request $request): JsonResponse
    {
        $language_code = self::getLanguageCode($request);
        $charge_record_number = $request->input('charge_record_number'); // 充电记录编号
        $payment_device = PaymentDeviceEnum::Pos; // 支付设备
        $payment_method = $request->input('payment_method'); // 支付方式
        $payment_status = $request->input('payment_status', PaymentStatusEnum::Completed); // 支付状态
        $charge_value_type = $request->input('charge_value_type'); // 充电量类型
        $charge_value = $request->input('charge_value'); // 充电量
        $is_idling_penalty_only = $request->boolean('is_idling_penalty_only', false); // 是否仅闲置罚款
        $is_charge_arrears = $request->boolean('is_charge_arrears', false); // 是否充电欠款
        $charge_value_amount = $request->input('charge_value_amount'); // 充电量金额
        $charge_value_amount_calculation_json = $request->input('charge_value_amount_calculation_json'); // 充电量金额计算JSON
        $idling_penalty_time = $request->input('idling_penalty_time'); // 闲置罚款时间
        $idling_penalty_amount = $request->input('idling_penalty_amount'); // 闲置罚款金额
        $idling_penalty_amount_calculation_json = $request->input('idling_penalty_amount_calculation_json'); // 闲置罚款金额计算JSON
        $gmt_idling_penalty_start = $request->input('gmt_idling_penalty_start'); // 闲置罚款开始时间
        $gmt_idling_penalty_stop = $request->input('gmt_idling_penalty_stop'); // 闲置罚款结束时间
        $total_amount = $request->input('total_amount'); // 合共金额
        $actual_payment_amount = $request->input('actual_payment_amount'); // 实际付款金额
        $gmt_receipt_start = $request->input('gmt_receipt_start'); // 收据开始时间
        $gmt_receipt_stop = $request->input('gmt_receipt_stop'); // 收据结束时间
        $pos_vendor = $request->input('pos_vendor'); // POS供应商
        $pos_receipt_number = $request->input('pos_receipt_number'); // POS收据编号
        $gmt_pos_deduct = $request->input('gmt_pos_deduct'); // POS扣款时间
        $pos_transaction_id = $request->input('pos_transaction_id'); // POS交易ID
        $pos_payment_method_name = $request->input('pos_payment_method_name'); // POS支付方式名称
        $pos_card_number = $request->input('pos_card_number'); // POS卡号
        $pos_trace_no = $request->input('pos_trace_no'); // POS追踪编号
        $pos_reference_id = $request->input('pos_reference_id'); // POS参考编号
        $pos_response_json = $request->input('pos_response_json'); // POS响应JSON
        $kiosk_number = $request->input('kiosk_number'); // Kiosk编号
        $kiosk_name = $request->input('kiosk_name'); // Kiosk名称
        $charge_pre_authorization_record_number = $request->input('charge_pre_authorization_record_number'); // 充电预授权记录编号

        $gmt_payment_status = $gmt_pos_deduct; // 支付状态时间

        $this->data = null;
        if (blank($charge_record_number) || blank($payment_method) || !PaymentMethod::hasValue($payment_method)) {
            $this->missingField('charge_record_number|payment_method');
            return $this->returnJson();
        }
        $charge_record = ChargeRecord::firstWhere('charge_record_number', $charge_record_number);
        if (blank($charge_record)) {
            $this->missingField('charge_record_number');
            return $this->returnJson();
        }
        $charge_payment_record = ChargePaymentRecord::where('charge_record_number', $charge_record_number)
            ->where('pos_receipt_number', $pos_receipt_number)
            ->where('gmt_pos_deduct', $gmt_pos_deduct)
            ->first();
        if (blank($charge_payment_record)) {
            $charge_payment_record = new ChargePaymentRecord;
            $charge_payment_record->merchant_number = $charge_record->merchant_number; // 商户编号
            $charge_payment_record->currency_code = $charge_record->currency_code; // 货币代码
            $charge_payment_record->currency_symbol = $charge_record->currency_symbol; // 货币符号
            $charge_payment_record->site_number = $charge_record->site_number; // 站点编号
            $charge_payment_record->charge_record_number = $charge_record_number; // 充电记录编号
            $charge_payment_record->charge_payment_record_number = $charge_record_number . env('DELIMITER') . generateHashCode($charge_record_number . time()); // 充电支付记录编号
            $charge_payment_record->payment_device = $payment_device; // 支付设备
            $charge_payment_record->payment_method = $payment_method; // 支付方式
            $charge_payment_record->payment_status = $payment_status; // 支付状态
            $charge_payment_record->gmt_payment_status = $gmt_payment_status; // 支付状态时间
            $charge_payment_record->charge_tariff_scheme = ChargeTariffScheme::PostPaid; // 充电收费方案
            $charge_payment_record->charge_value_type = $charge_value_type; // 充电量类型
            $charge_payment_record->is_idling_penalty_only = $is_idling_penalty_only; // 是否仅闲置罚款
            $charge_payment_record->is_charge_arrears = $is_charge_arrears; // 是否充电欠款
            $charge_payment_record->charge_value = $charge_value; // 充电量
            $charge_payment_record->charge_value_amount = $charge_value_amount; // 充电量金额
            $charge_payment_record->idling_penalty_time = $idling_penalty_time; // 闲置罚款时间
            $charge_payment_record->idling_penalty_amount = $idling_penalty_amount; // 闲置罚款金额
            $charge_payment_record->gmt_idling_penalty_start = $gmt_idling_penalty_start; // 闲置罚款开始时间
            $charge_payment_record->gmt_idling_penalty_stop = $gmt_idling_penalty_stop; // 闲置罚款结束时间
            $charge_payment_record->total_amount = $total_amount; // 合共金额
            $charge_payment_record->actual_payment_amount = filled($actual_payment_amount) ? $actual_payment_amount : $charge_payment_record->total_amount; // 实际付款金额
            $charge_payment_record->gmt_receipt_start = $gmt_receipt_start; // 收据开始时间
            $charge_payment_record->gmt_receipt_stop = $gmt_receipt_stop; // 收据结束时间
            $charge_payment_record->pos_vendor = $pos_vendor; // POS供应商
            $charge_payment_record->pos_receipt_number = $pos_receipt_number; // POS收据编号
            $charge_payment_record->gmt_pos_deduct = $gmt_pos_deduct; // POS扣款时间
            $charge_payment_record->pos_transaction_id = $pos_transaction_id; // POS交易ID
            $charge_payment_record->pos_payment_method_name = $pos_payment_method_name; // POS支付方式名称
            $charge_payment_record->pos_card_number = $pos_card_number; // POS卡号
            $charge_payment_record->pos_trace_no = $pos_trace_no; // POS追踪编号
            $charge_payment_record->pos_reference_id = $pos_reference_id; // POS参考编号
            $charge_payment_record->pos_response_json = $pos_response_json; // POS响应JSON
            $charge_payment_record->kiosk_number = $kiosk_number; // Kiosk编号
            $charge_payment_record->kiosk_name = $kiosk_name; // Kiosk名称
            $charge_payment_record->charge_pre_authorization_record_number = $charge_pre_authorization_record_number; // 充电预授权记录编号

            // 计算商户手续费
            $charge_payment_record->merchant_handling_fee = 0;
            $charge_payment_record->merchant_receivable = $charge_payment_record->actual_payment_amount;
            if (filled($charge_record->merchant_handling_fee_rate) && $charge_record->merchant_handling_fee_rate > 0) {
                $merchant_handling_fee_rate = $charge_record->merchant_handling_fee_rate;
                // 因为是百分比，所以需要除以100
                $merchant_handling_fee_rate /= 100;
                // 计算商户手续费金额，向下取整保留两位小数
                $merchant_handling_fee = (float)bcmul($charge_payment_record->actual_payment_amount, $merchant_handling_fee_rate, 0);
                $charge_payment_record->merchant_handling_fee = $merchant_handling_fee;
                // 商户应收 = 实付金额 - 商户手续费
                $charge_payment_record->merchant_receivable = $charge_payment_record->actual_payment_amount - $charge_payment_record->merchant_handling_fee;
            }

            // 使用事务同时处理所有SQL操作
            $result = DB::transaction(function () use (
                &$charge_payment_record,
                &$charge_record,
                $charge_value_amount_calculation_json,
                $idling_penalty_amount_calculation_json,
                $charge_pre_authorization_record_number
            ) {
                try {
                    $charge_payment_record->saveOrFail();

                    // 保存支付流水
                    $charge_accounting_record = new ChargeAccountingRecord;
                    $charge_accounting_record->merchant_number = $charge_payment_record->merchant_number;
                    $charge_accounting_record->site_number = $charge_payment_record->site_number;
                    $charge_accounting_record->currency_code = $charge_payment_record->currency_code;
                    $charge_accounting_record->currency_symbol = $charge_payment_record->currency_symbol;
                    $charge_accounting_record->payment_device = $charge_payment_record->payment_device;
                    $charge_accounting_record->payment_method = $charge_payment_record->payment_method;
                    $charge_accounting_record->transaction_type = ChargeAccountingTransactionType::Income;
                    $charge_accounting_record->transaction_category = ChargeAccountingTransactionCategory::KioskPostPaidPosDeduct;
                    $charge_accounting_record->source_transaction_number = $charge_payment_record->charge_payment_record_number;
                    $charge_accounting_record->amount = $charge_payment_record->actual_payment_amount;
                    $charge_accounting_record->gmt_deduct = $charge_payment_record->gmt_pos_deduct;
                    $charge_accounting_record->pos_payment_method_name = $charge_payment_record->pos_payment_method_name;
                    $charge_accounting_record->pos_card_number = $charge_payment_record->pos_card_number;
                    $charge_accounting_record->saveOrFail();

                    // 更新预授权记录状态
                    if (filled($charge_pre_authorization_record_number)) {
                        $charge_pre_authorization_record = ChargePreAuthorizationRecord::firstWhere('charge_pre_authorization_record_number', $charge_pre_authorization_record_number);
                        if (filled($charge_pre_authorization_record)) {
                            $current_date_time = now()->format('Y-m-d H:i:s');
                            $charge_pre_authorization_record->status_log .= PHP_EOL . "$current_date_time - by: api, status: [{$charge_pre_authorization_record->charge_pre_authorization_status}] -> [" . ChargePreAuthorizationStatus::Completed . "], reason: pos post paid deduct";
                            $charge_pre_authorization_record->charge_pre_authorization_status = ChargePreAuthorizationStatus::Completed;
                            $charge_pre_authorization_record->gmt_charge_pre_authorization_status = now();
                            $charge_pre_authorization_record->pos_deduct_response_json = $charge_payment_record->pos_response_json;
                            $charge_pre_authorization_record->gmt_pos_deduct = $charge_payment_record->gmt_pos_deduct;
                            $charge_pre_authorization_record->deduct_amount = $charge_payment_record->actual_payment_amount;
                            $charge_pre_authorization_record->pos_transaction_id = $charge_payment_record->pos_transaction_id;
                            $charge_pre_authorization_record->saveOrFail();
                        } else {
                            logger('API: /api/kiosk/posPostPaidDeduct; charge_pre_authorization_record not found', ['charge_pre_authorization_record_number' => $charge_pre_authorization_record_number]);
                        }
                    }

                    // 充电欠款
                    if ($charge_payment_record->is_charge_arrears) {
                        $charge_arrears_record = ChargeArrearsRecord::firstWhere('charge_record_number', $charge_record->charge_record_number);
                        if (blank($charge_arrears_record)) {
                            logger('API: /api/kiosk/posPostPaidDeduct; charge_arrears_record not found', [
                                'charge_record_number' => $charge_record->charge_record_number,
                            ]);
                        } else {
                            $current_date_time = now()->format('Y-m-d H:i:s');
                            // status_log
                            if (filled($charge_arrears_record->status_log)) {
                                $charge_arrears_record->status_log .= PHP_EOL;
                            }
                            $charge_arrears_record->status_log .= "$current_date_time - by: api, status: [{$charge_arrears_record->charge_arrears_status}] -> [" . ChargeArrearsStatus::Completed . "], reason: pos post paid deduct";
                            $charge_arrears_record->charge_arrears_status = ChargeArrearsStatus::Completed;
                            $charge_arrears_record->gmt_charge_arrears_status = now();
                            $charge_arrears_record->saveOrFail();
                        }
                    }

                    // 保存计算json
                    if (blank($charge_payment_record_calculation_model = $charge_payment_record->chargePaymentRecordCalculation)) {
                        $charge_payment_record_calculation_model = new ChargePaymentRecordCalculation;
                        $charge_payment_record_calculation_model->charge_payment_record_number = $charge_payment_record->charge_payment_record_number;
                    }
                    $charge_payment_record_calculation_model->currency_code = $charge_payment_record->currency_code;
                    $charge_payment_record_calculation_model->currency_symbol = $charge_payment_record->currency_symbol;
                    $charge_payment_record_calculation_model->charge_value_amount_calculation_json = $charge_value_amount_calculation_json; // 充电量金额计算JSON
                    $charge_payment_record_calculation_model->idling_penalty_amount_calculation_json = $idling_penalty_amount_calculation_json; // 充电量金额计算JSON
                    $charge_payment_record_calculation_model->saveOrFail();

                    $charge_record->post_paid_purchase_charge_value = $charge_payment_record->charge_value;
                    $charge_record->charge_value_amount += $charge_payment_record->charge_value_amount; // 充电量金额
                    $charge_record->idling_penalty_amount += $charge_payment_record->idling_penalty_amount; // 闲置罚款金额
                    $charge_record->gmt_idling_penalty_locked = $charge_payment_record->gmt_pos_deduct; // 闲置罚款已锁定时间
                    $charge_record->total_amount += $charge_payment_record->total_amount; // 合共金额
                    $charge_record->actual_payment_amount += $charge_payment_record->actual_payment_amount; // 实际支付金额
                    $charge_record->merchant_handling_fee += $charge_payment_record->merchant_handling_fee; // 商户手续费
                    $charge_record->merchant_receivable += $charge_payment_record->merchant_receivable; // 商户应收款
                    $charge_record->gmt_sync_to_cloud = null; // 同步至云端时间

                    $charge_record->saveOrFail();
                } catch (Exception $e) {
                    logger($e->getMessage());
                    logger('API: /api/kiosk/posPostPaidDeduct; charge_payment_record/charge_pre_authorization_record/charge_record/charge_value_amount_calculation_json/idling_penalty_amount_calculation_json save failed', [
                        'charge_record' => $charge_record,
                        'charge_payment_record' => $charge_payment_record,
                        'charge_pre_authorization_record_number' => $charge_pre_authorization_record_number,
                        'charge_value_amount_calculation_json' => $charge_value_amount_calculation_json,
                        'idling_penalty_amount_calculation_json' => $idling_penalty_amount_calculation_json,
                    ]);
                    throw new \Exception('Internal Server Error', 500);
                }
            });

        }

        $this->data = array(
            'charge_record' => $this->chargeRecordArrayToJson($charge_record),
            'charge_payment_record' => isset($charge_payment_record) && filled($charge_payment_record) ? $this->chargePaymentRecordArrayToJson($charge_payment_record) : null,
        );

        try {
            // 通知Kiosk充电记录
            $this->requestNotifyKioskChargeRecord($charge_record);
        } catch (Exception $e) {
            logger($e->getMessage());
            logger('API: /api/kiosk/posPostPaidDeduct; requestNotifyKioskChargeRecord error', ['error' => $e->getMessage()]);
        }

        return $this->returnJson();
    }

    /**
     * 后付扣款APP
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-16
     */
    public function octopusUserChargeRecordBillDeduct(Request $request): JsonResponse
    {
        $total_amount = $request->total_amount;
        $language_code = self::getLanguageCode($request);
        $user_charge_record_bill_item_number_list = $request->user_charge_record_bill_item_number_list; // 充电记录编号
        $user_charge_record_bill_item_number_list = json_decode($user_charge_record_bill_item_number_list, true) ?: [];
        $payment_device = PaymentDeviceEnum::Octopus; // 支付设备
        $payment_method = $request->input('payment_method'); // 支付方式
        $payment_status = $request->input('payment_status', PaymentStatusEnum::Completed); // 支付状态
        $octopus_receipt_number = $request->input('octopus_receipt_number'); // 八达通收据编号
        $gmt_octopus_deduct = $request->input('gmt_octopus_deduct'); // 八达通扣款时间
        $octopus_device_number = $request->input('octopus_device_number'); // 八达通设备编号
        $octopus_card_type = $request->input('octopus_card_type'); // 八达通卡类型
        $octopus_card_number = $request->input('octopus_card_number'); // 八达通卡号
        $octopus_balance = $request->input('octopus_balance'); // 八达通余额
        $octopus_last_added_value_type = $request->input('octopus_last_added_value_type'); // 八达通最后增值类型
        $octopus_last_added_value_date = $request->input('octopus_last_added_value_date'); // 八达通最后增值日期
        $gmt_payment_status = $gmt_octopus_deduct; // 支付状态时间

        $this->data = null;
        $user_charge_record_bill_item_list = [];
        if (blank($payment_method) || !PaymentMethod::hasValue($payment_method)) {
            $this->missingField('payment_method');
            return $this->returnJson();
        }
        if (blank($user_charge_record_bill_item_number_list)) {
            $this->missingField('user_charge_record_bill_item_number_list');
            return $this->returnJson();
        }
        $bill_condition = function ($query) use ($user_charge_record_bill_item_number_list) {
            $query->whereIn('charge_payment_record_number', $user_charge_record_bill_item_number_list);
        };
        // 查询同商户下所有未付款的充电记录
        $charge_record_list = ChargeRecord::with(['chargePaymentRecord' => $bill_condition])
            ->whereHas('chargePaymentRecord', $bill_condition)
            ->get();
        foreach ($charge_record_list as $charge_record) {
            foreach ($charge_record->chargePaymentRecord as $charge_payment_record) {
                $charge_record_number_array = explode('-', $charge_record->charge_record_number);
                $user_charge_record_bill_item_list[] = [
                    'user_charge_record_bill_item_number' => $charge_payment_record->charge_payment_record_number,
                    'bill_number' => end($charge_record_number_array) ?? null,
                    'gmt_start' => $charge_record->gmt_power_on ?? $charge_record->gmt_start,
                    'amount' => $charge_payment_record->total_amount,
                ];
                if ($charge_payment_record->payment_status !== PaymentStatusEnum::Pending) continue;
                $result = DB::transaction(function () use (
                    &$charge_record,
                    $charge_payment_record,
                    $payment_device,
                    $payment_method,
                    $payment_status,
                    $gmt_payment_status,
                    $octopus_receipt_number,
                    $gmt_octopus_deduct,
                    $octopus_device_number,
                    $octopus_card_type,
                    $octopus_card_number,
                    $octopus_balance,
                    $octopus_last_added_value_type,
                    $octopus_last_added_value_date,
                    &$total_amount
                ) {
                    $charge_payment_record->payment_device = $payment_device; // 支付设备
                    $charge_payment_record->payment_method = $payment_method; // 支付方式
                    $charge_payment_record->payment_status = $payment_status; // 支付状态
                    $charge_payment_record->gmt_payment_status = $gmt_payment_status; // 支付状态时间
                    $charge_payment_record->octopus_receipt_number = $octopus_receipt_number; // POS收据编号
                    $charge_payment_record->gmt_octopus_deduct = $gmt_octopus_deduct; // 八达通扣款时间
                    $charge_payment_record->octopus_device_number = $octopus_device_number; // 八达通设备编号
                    $charge_payment_record->octopus_card_type = $octopus_card_type; // 八达通卡类型
                    $charge_payment_record->octopus_card_number = $octopus_card_number; // 八达通卡号
                    $charge_payment_record->octopus_balance = $octopus_balance; // 八达通余额
                    $charge_payment_record->octopus_last_added_value_type = $octopus_last_added_value_type; // 八达通最后增值类型
                    $charge_payment_record->octopus_last_added_value_date = $octopus_last_added_value_date; // 八达通最后增值日期
                    // 计算付款金额
                    $charge_payment_record->use_points = 0; // 使用的积分
                    $charge_payment_record->actual_payment_amount = 0; // 实际支付金额
                    if ($total_amount > 0) {
                        // 实际支付金额。总金额如果大于实际支付金额，抵扣所有实际支付金额；否则抵扣总金额等量的实际支付金额
                        // $charge_payment_record->actual_payment_amount = $total_amount > $total_amount ? $total_amount : $total_amount; // 实际支付金额
                        $charge_payment_record->actual_payment_amount = $total_amount > $charge_payment_record->actual_payment_amount ? $charge_payment_record->total_amount : $total_amount; // 实际支付金额
                        // 扣除对应实际支付金额
                        $total_amount = bcsub($total_amount, $charge_payment_record->actual_payment_amount, 0);
                    }
                    $charge_record->actual_payment_amount += $charge_payment_record->actual_payment_amount; // 实际支付金额
                    $charge_payment_record->save();

                    return true;
                });
            }
            $charge_record->save();

            try {
                // 通知Kiosk充电记录
                $charge_record = $charge_record->first(); // Retrieve the actual ChargeRecord object
                $this->requestNotifyKioskChargeRecord($charge_record);
            } catch (Exception $e) {
            }
        }

        $this->data = [
            'user_charge_record_bill_item_number_list' => $user_charge_record_bill_item_number_list,
            'user_charge_record_bill_item_list' => $user_charge_record_bill_item_list,
            'payment_device' => $payment_device,
            'payment_method' => $payment_method,
            'payment_status' => $payment_status,
            'total_amount' => (int)$request->total_amount,
            'octopus_receipt_number' => $octopus_receipt_number,
            'gmt_octopus_deduct' => $gmt_octopus_deduct,
            'octopus_device_number' => $octopus_device_number,
            'octopus_card_type' => $octopus_card_type,
            'octopus_card_number' => $octopus_card_number,
            'octopus_balance' => $octopus_balance,
            'octopus_last_added_value_type' => $octopus_last_added_value_type,
            'octopus_last_added_value_date' => $octopus_last_added_value_date,
        ];

        return $this->returnJson();
    }

    /**
     * 创建充电POS预授权记录
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function createPOSChargePreAuthorizationRecord(Request $request): JsonResponse
    {
        $pre_authorization_amount = $request->pre_authorization_amount; // 预授权金额
        $identity_type = $request->identity_type; // 身份类型
        $identity_number = $request->identity_number; // 身份号码
        $connector_number = $request->connector_number; // 充电枪编号
        $connector_name = $request->connector_name; // 充电枪名称
        $kiosk_number = $request->kiosk_number; // Kiosk编号
        $kiosk_name = $request->kiosk_name; // Kiosk名称
        $pos_vendor = $request->pos_vendor; // POS供应商
        $pos_pre_authorization_id = $request->pos_pre_authorization_id; // POS预授权ID
        $pos_company_id = $request->pos_company_id; // POS公司ID
        $pos_transaction_id = $request->pos_transaction_id; // POS交易ID
        $pos_payment_method_name = $request->pos_payment_method_name; // POS支付方式名称
        $pos_card_number = $request->pos_card_number; // POS卡号
        $pos_trace_no = $request->pos_trace_no; // POS追踪编号
        $pos_reference_id = $request->pos_reference_id; // POS参考编号
        $gmt_pos_create = $request->gmt_pos_create; // POS创建时间
        $pos_create_response_json = $request->pos_create_response_json; // POS创建响应JSON


        // 如果connector_number为空，则返回错误
        if (blank($connector_number)) {
            $this->missingField('connector_number');
            return $this->returnJson();
        }

        // 如果pos_pre_authorization_id和pos_company_id为空，则返回错误
        if (blank($pos_pre_authorization_id) && blank($pos_company_id)) {
            $this->missingField('pos_pre_authorization_id|pos_company_id');
            return $this->returnJson();
        }

        $connector = Connector::firstWhere('connector_number', $connector_number);
        if (blank($connector)) {
            $this->notFoundData('connector_number');
            return $this->returnJson();
        }

        $payment_device = PaymentDeviceEnum::Pos; // 支付设备

        $charge_pre_authorization_record = new ChargePreAuthorizationRecord();

        // 生成charge_pre_authorization_record_number，规则为connector_number+pos_pre_authorization_id+10位纯数字，需要保证数据库中唯一，使用retry尝试生成五次
        $charge_pre_authorization_record_number = retry(5, function () use ($connector_number, $pos_pre_authorization_id) {
            $charge_pre_authorization_record_number = $connector_number . '-' . $pos_pre_authorization_id . '-' . str_pad(rand(0, 9999999999), 10, '0', STR_PAD_LEFT);
            $charge_pre_authorization_record_exists = ChargePreAuthorizationRecord::where('charge_pre_authorization_record_number', $charge_pre_authorization_record_number)->exists();
            if ($charge_pre_authorization_record_exists) {
                throw new Exception('charge_pre_authorization_record_number already exists');
            }
            return $charge_pre_authorization_record_number;
        }, 100);
        if (blank($charge_pre_authorization_record_number)) {
            logger('API: /api/kiosk/createPOSChargePreAuthorizationRecord; charge_pre_authorization_record_number generate failed', [
                'connector_number' => $connector_number,
                'pos_pre_authorization_id' => $pos_pre_authorization_id,
            ]);
            $this->missingField('charge_pre_authorization_record_number');
            $this->message = 'charge_pre_authorization_record_number generate failed';
            return $this->returnJson();
        }
        $currency_code = self::$default_currency_code;
        $currency_symbol = self::$default_currency_symbol;
        $kiosk = Kiosk::firstWhere('kiosk_number', $kiosk_number);
        if (filled($kiosk?->site)) {
            $currency_code = $kiosk?->site?->currency_code ?? $currency_code;
            $currency_symbol = $kiosk?->site?->currency_symbol ?? $currency_symbol;
        }
        $charge_pre_authorization_record->charge_pre_authorization_record_number = $charge_pre_authorization_record_number;
        $charge_pre_authorization_record->charge_pre_authorization_status = ChargePreAuthorizationStatus::Pending;
        $charge_pre_authorization_record->merchant_number = $connector->merchant_number;
        $charge_pre_authorization_record->site_number = $connector->site_number;
        $charge_pre_authorization_record->currency_code = $currency_code;
        $charge_pre_authorization_record->currency_symbol = $currency_symbol;
        $charge_pre_authorization_record->gmt_charge_pre_authorization_status = now();
        $charge_pre_authorization_record->payment_device = $payment_device;
        $charge_pre_authorization_record->pre_authorization_amount = $pre_authorization_amount;
        $charge_pre_authorization_record->identity_type = $identity_type;
        $charge_pre_authorization_record->identity_number = $identity_number;
        $charge_pre_authorization_record->connector_number = $connector_number;
        $charge_pre_authorization_record->connector_name = $connector_name;
        $charge_pre_authorization_record->kiosk_number = $kiosk_number;
        $charge_pre_authorization_record->kiosk_name = $kiosk_name;
        $charge_pre_authorization_record->pos_vendor = $pos_vendor;
        $charge_pre_authorization_record->pos_pre_authorization_id = $pos_pre_authorization_id;
        $charge_pre_authorization_record->pos_company_id = $pos_company_id;
        $charge_pre_authorization_record->pos_transaction_id = $pos_transaction_id;
        $charge_pre_authorization_record->pos_payment_method_name = $pos_payment_method_name;
        $charge_pre_authorization_record->pos_card_number = $pos_card_number;
        $charge_pre_authorization_record->pos_trace_no = $pos_trace_no;
        $charge_pre_authorization_record->pos_reference_id = $pos_reference_id;
        $charge_pre_authorization_record->gmt_pos_create = $gmt_pos_create;
        $charge_pre_authorization_record->pos_create_response_json = $pos_create_response_json;
        $charge_pre_authorization_record->save();

        $this->data = $charge_pre_authorization_record;
        return $this->returnJson();
    }

    /**
     * 更新充电POS预授权记录
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function updatePOSChargePreAuthorizationRecord(Request $request): JsonResponse
    {
        $charge_pre_authorization_record_number = $request->charge_pre_authorization_record_number; // 充电预授权记录编号
        $charge_pre_authorization_status = $request->charge_pre_authorization_status; // 充电预授权状态
        $gmt_pos_deduct = $request->gmt_pos_deduct; // POS扣款时间
        $pos_deduct_response_json = $request->pos_deduct_response_json; // POS扣款响应JSON
        $gmt_pos_cancel = $request->gmt_pos_cancel; // POS取消时间
        $pos_cancel_response_json = $request->pos_cancel_response_json; // POS取消响应JSON
        $remark = $request->remark; // 备注

        if (blank($charge_pre_authorization_record_number)) {
            $this->missingField('charge_pre_authorization_record_number');
            return $this->returnJson();
        }

        $charge_pre_authorization_record = ChargePreAuthorizationRecord::firstWhere('charge_pre_authorization_record_number', $charge_pre_authorization_record_number);
        if (blank($charge_pre_authorization_record)) {
            $this->missingField('charge_pre_authorization_record_number');
            return $this->returnJson();
        }

        $current_date_time = now()->format('Y-m-d H:i:s');
        if (filled($charge_pre_authorization_status)) {
            // 如果充电预授权记录状态为【已完成】，则返回true
            if ($charge_pre_authorization_record->charge_pre_authorization_status === ChargePreAuthorizationStatus::Completed) {
                $this->data = true;
                return $this->returnJson();
            }
            // status_log
            if (filled($charge_pre_authorization_record->status_log)) {
                $charge_pre_authorization_record->status_log .= PHP_EOL;
            }
            $charge_pre_authorization_record->status_log .= "$current_date_time - by: api, status: [{$charge_pre_authorization_record->charge_pre_authorization_status}] -> [$charge_pre_authorization_status], reason: update charge pre authorization record status";
            $charge_pre_authorization_record->charge_pre_authorization_status = $charge_pre_authorization_status;
            $charge_pre_authorization_record->gmt_charge_pre_authorization_status = now();
        }
        if (filled($gmt_pos_deduct)) {
            $charge_pre_authorization_record->gmt_pos_deduct = $gmt_pos_deduct;
        }
        if (filled($pos_deduct_response_json)) {
            $charge_pre_authorization_record->pos_deduct_response_json = $pos_deduct_response_json;
        }
        if (filled($gmt_pos_cancel)) {
            $charge_pre_authorization_record->gmt_pos_cancel = $gmt_pos_cancel;
        }
        if (filled($pos_cancel_response_json)) {
            $charge_pre_authorization_record->pos_cancel_response_json = $pos_cancel_response_json;
        }
        if (filled($remark)) {
            if (filled($charge_pre_authorization_record->remark)) {
                $charge_pre_authorization_record->remark .= PHP_EOL;
            }
            $charge_pre_authorization_record->remark .= "$current_date_time $remark";
        }


        $this->data = $charge_pre_authorization_record->save();
        return $this->returnJson();
    }

    /**
     * 更新充电记录充电预授权记录编号
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function updateChargeRecordChargePreAuthorizationRecordNumber(Request $request): JsonResponse
    {
        $charge_record_number = $request->charge_record_number; // 充电记录编号
        $charge_pre_authorization_record_number = $request->charge_pre_authorization_record_number; // 充电预授权记录编号

        $charge_record = ChargeRecord::firstWhere('charge_record_number', $charge_record_number);
        if (blank($charge_record)) {
            $this->missingField('charge_record_number');
            return $this->returnJson();
        }
        $charge_pre_authorization_record = ChargePreAuthorizationRecord::firstWhere('charge_pre_authorization_record_number', $charge_pre_authorization_record_number);
        if (blank($charge_pre_authorization_record)) {
            $this->missingField('charge_pre_authorization_record_number');
            return $this->returnJson();
        }

        // 使用事务更新充电记录充电预授权记录编号和预授权记录状态为【已确认】
        $result = DB::transaction(function () use ($charge_record_number, $charge_pre_authorization_record, $charge_record, $charge_pre_authorization_record_number) {
            try {
                if (filled($charge_pre_authorization_record->status_log)) {
                    $charge_pre_authorization_record->status_log .= PHP_EOL;
                }
                $current_date_time = now()->format('Y-m-d H:i:s');
                $charge_pre_authorization_record->status_log .= "$current_date_time - by: api, status: [{$charge_pre_authorization_record->charge_pre_authorization_status}] -> [" . ChargePreAuthorizationStatus::Confirmed . "], reason: update charge record charge pre authorization record number";
                $charge_pre_authorization_record->charge_pre_authorization_status = ChargePreAuthorizationStatus::Confirmed;
                $charge_pre_authorization_record->gmt_charge_pre_authorization_status = now();
                $charge_pre_authorization_record->saveOrFail();
                $charge_record->charge_pre_authorization_record_number = $charge_pre_authorization_record_number;
                return $charge_record->saveOrFail();
            } catch (Exception $e) {
                logger($e->getMessage());
                logger('API: /api/kiosk/updateChargeRecordChargePreAuthorizationRecordNumber; charge_pre_authorization_record/charge_record save failed', [
                    'charge_pre_authorization_record_number' => $charge_pre_authorization_record_number,
                    'charge_record_number' => $charge_record_number,
                ]);
                throw new \Exception('Internal Server Error', 500);
            }
        });

        // 执行成功，返回true；执行失败，返回false
        $this->data = $result;

        return $this->returnJson();
    }

    /**
     * 创建充电欠款记录
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function createChargeArrearsRecord(Request $request): JsonResponse
    {
        $charge_record_number = $request->charge_record_number; // 充电记录编号
        $connector_number = $request->connector_number; // 充电枪编号
        $connector_name = $request->connector_name; // 充电枪名称
        $kiosk_number = $request->kiosk_number; // Kiosk编号
        $kiosk_name = $request->kiosk_name; // Kiosk名称
        $identity_type = $request->identity_type; // 身份类型
        $identity_number = $request->identity_number; // 身份号码

        // 如果connector_number为空，则返回错误
        if (blank($connector_number)) {
            $this->missingField('connector_number');
            return $this->returnJson();
        }

        // 如果charge_record_number为空，则返回错误
        if (blank($charge_record_number)) {
            $this->missingField('charge_record_number');
            return $this->returnJson();
        }


        $connector = Connector::firstWhere('connector_number', $connector_number);
        if (blank($connector)) {
            $this->notFoundData('connector_number');
            return $this->returnJson();
        }
        $charge_record_exists = ChargeRecord::where('charge_record_number', $charge_record_number)->exists();
        if (blank($charge_record_exists)) {
            $this->notFoundData('charge_record_number');
            return $this->returnJson();
        }

        $charge_arrears_record = new ChargeArrearsRecord();
        // 生成charge_arrears_record_number，规则为charge_record_number+10位纯数字，需要保证数据库中唯一，使用retry尝试生成五次
        $charge_arrears_record_number = retry(5, function () use ($charge_record_number) {
            $charge_arrears_record_number = $charge_record_number . '-' . str_pad(rand(0, 9999999999), 10, '0', STR_PAD_LEFT);
            $charge_arrears_record_exists = ChargeArrearsRecord::where('charge_arrears_record_number', $charge_arrears_record_number)->exists();
            if ($charge_arrears_record_exists) {
                throw new Exception('charge_arrears_record_number already exists');
            }
            return $charge_arrears_record_number;
        }, 100);
        if (blank($charge_arrears_record_number)) {
            logger('API: /api/kiosk/createChargeArrearsRecord; charge_arrears_record_number generate failed', [
                'charge_record_number' => $charge_record_number,
            ]);
            $this->missingField('charge_arrears_record_number');
            $this->message = 'charge_arrears_record_number generate failed';
            return $this->returnJson();
        }
        $currency_code = self::$default_currency_code;
        $currency_symbol = self::$default_currency_symbol;
        $kiosk = Kiosk::firstWhere('kiosk_number', $kiosk_number);
        if (filled($kiosk?->site)) {
            $currency_code = $kiosk?->site?->currency_code ?? $currency_code;
            $currency_symbol = $kiosk?->site?->currency_symbol ?? $currency_symbol;
        }
        $charge_arrears_record->charge_arrears_record_number = $charge_arrears_record_number;
        $charge_arrears_record->merchant_number = $connector->merchant_number;
        $charge_arrears_record->site_number = $connector->site_number;
        $charge_arrears_record->currency_code = $currency_code;
        $charge_arrears_record->currency_symbol = $currency_symbol;
        $charge_arrears_record->charge_record_number = $charge_record_number;
        $charge_arrears_record->charge_arrears_status = ChargeArrearsStatus::Pending;
        $charge_arrears_record->gmt_charge_arrears_status = now();
        $charge_arrears_record->connector_number = $connector_number;
        $charge_arrears_record->connector_name = $connector_name;
        $charge_arrears_record->kiosk_number = $kiosk_number;
        $charge_arrears_record->kiosk_name = $kiosk_name;
        $charge_arrears_record->identity_type = $identity_type;
        $charge_arrears_record->identity_number = $identity_number;
        $charge_arrears_record->save();

        $this->data = $charge_arrears_record;
        return $this->returnJson();
    }

    /**
     * 更新充电欠款记录
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function updateChargeArrearsRecord(Request $request): JsonResponse
    {
        $charge_arrears_record_number = $request->charge_arrears_record_number; // 充电欠款记录编号
        $charge_arrears_status = $request->charge_arrears_status; // 充电欠款状态
        $estimate_arrears_charge_value_amount = $request->estimate_arrears_charge_value_amount; // 预计欠款充电量金额
        $estimate_arrears_idling_penalty_amount = $request->estimate_arrears_idling_penalty_amount; // 预计欠款罚款金额
        $remark = $request->remark; // 备注

        if (blank($charge_arrears_record_number)) {
            $this->missingField('charge_arrears_record_number');
            return $this->returnJson();
        }

        $charge_arrears_record = ChargeArrearsRecord::firstWhere('charge_arrears_record_number', $charge_arrears_record_number);
        if (blank($charge_arrears_record)) {
            $this->missingField('charge_arrears_record_number');
            return $this->returnJson();
        }

        $current_date_time = now()->format('Y-m-d H:i:s');
        if (filled($charge_arrears_status)) {
            // 如果充电欠款记录状态为【已完成】，则返回true
            if ($charge_arrears_record->charge_arrears_status === ChargeArrearsStatus::Completed) {
                $this->data = true;
                return $this->returnJson();
            }
            // status_log
            if (filled($charge_arrears_record->status_log)) {
                $charge_arrears_record->status_log .= PHP_EOL;
            }
            $charge_arrears_record->status_log .= "$current_date_time - by: api, status: [{$charge_arrears_record->charge_arrears_status}] -> [$charge_arrears_status], reason: update charge arrears record status";
            $charge_arrears_record->charge_arrears_status = $charge_arrears_status;
            $charge_arrears_record->gmt_charge_arrears_status = now();
        }
        if (filled($estimate_arrears_charge_value_amount)) {
            $charge_arrears_record->estimate_arrears_charge_value_amount = $estimate_arrears_charge_value_amount;
        }
        if (filled($estimate_arrears_idling_penalty_amount)) {
            $charge_arrears_record->estimate_arrears_idling_penalty_amount = $estimate_arrears_idling_penalty_amount;
        }
        if (filled($remark)) {
            if (filled($charge_arrears_record->remark)) {
                $charge_arrears_record->remark .= PHP_EOL;
            }
            $charge_arrears_record->remark .= "$current_date_time $remark";
        }


        $this->data = $charge_arrears_record->save();
        return $this->returnJson();
    }

    /**
     * 获取充电欠款记录
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getChargeArrearsRecord(Request $request): JsonResponse
    {
        $identity_type = $request->identity_type; // 身份类型
        $identity_number = $request->identity_number; // 身份号码
        $kiosk_number = $request->kiosk_number; // Kiosk编号

        if (blank($identity_type)) {
            $this->missingField('identity_type');
            return $this->returnJson();
        }

        if (blank($identity_number)) {
            $this->missingField('identity_number');
            return $this->returnJson();
        }

        if (blank($kiosk_number)) {
            $this->missingField('kiosk_number');
            return $this->returnJson();
        }

        $kiosk = Kiosk::firstWhere('kiosk_number', $kiosk_number);
        if (blank($kiosk)) {
            $this->notFoundData('kiosk_number');
            return $this->returnJson();
        }

        $charge_arrears_record = ChargeArrearsRecord::with([
                'chargeRecord' => [
                    'tariffTableRule',
                    'chargePaymentRecord',
                    'chargeRecordChargedEnergy',
                ]
            ])
            ->where('identity_type', $identity_type)
            ->when($identity_type === IdentityType::Octopus, function ($query) use ($identity_number) {
                $query->whereIn('identity_number', explode(',', $identity_number));
            }, function ($query) use ($identity_number) {
                $query->where('identity_number', $identity_number);
            })
            ->where('site_number', $kiosk->site_number)
            ->where('charge_arrears_status', ChargeArrearsStatus::Unpaid)
            ->orderBy('gmt_create', 'desc')
            ->first();

        if (filled($charge_arrears_record)) {
            $this->data = $charge_arrears_record;
            $charge_record = $this->chargeRecordArrayToJson($charge_arrears_record->chargeRecord);
            $this->data['charge_record'] = $charge_record;
            $this->data['charge_record']['charge_record_tariff_rule'] = $this->chargeRecordTariffRuleArrayToJson($charge_record->tariffTableRule);
            $this->data['charge_record']['charge_payment_record_list'] = $charge_record->chargePaymentRecord ?? null;
            $this->data['charge_record']['charged_energy_item_list'] = $charge_record->chargeRecordChargedEnergy?->charged_energy_item_json ?? null;
            unset($this->data['chargeRecord']);
            unset($charge_record->tariffTableRule);
            unset($charge_record->chargePaymentRecord);
            unset($charge_record->chargeRecordChargedEnergy);
        }

        return $this->returnJson();
    }

    /**
     * 创建八达通充电预授权记录
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * @date 2025-05-22
     */
    public function createOctopusChargePreAuthorizationRecord(Request $request): JsonResponse
    {
        $pre_authorization_amount = $request->pre_authorization_amount; // 预授权金额
        $charge_record_number = $request->charge_record_number; // 充电记录编号
        $identity_type = $request->identity_type; // 身份类型
        $identity_number = $request->identity_number; // 身份号码
        $kiosk_number = $request->kiosk_number; // Kiosk编号
        $kiosk_name = $request->kiosk_name; // Kiosk名称
        $octopus_transaction_id = $request->octopus_transaction_id; // 八达通交易ID
        $octopus_receipt_number = $request->octopus_receipt_number; // 八达通收据编号
        $gmt_octopus_deduct = $request->gmt_octopus_deduct; // 八达通扣款时间
        $octopus_device_number = $request->octopus_device_number; // 八达通设备编号
        $octopus_card_type = $request->octopus_card_type; // 八达通卡类型
        $octopus_card_number = $request->octopus_card_number; // 八达通卡号
        $octopus_raw_card_number = $request->octopus_raw_card_number; // 八达通原始卡号
        $octopus_balance = $request->octopus_balance; // 八达通余额
        $octopus_last_added_value_type = $request->octopus_last_added_value_type; // 八达通最后添加值类型
        $octopus_last_added_value_date = $request->octopus_last_added_value_date; // 八达通最后添加值日期
        $octopus_response_json = $request->octopus_response_json; // 八达通响应JSON
        $is_admin_octopus_card = $request->boolean('is_admin_octopus_card', false); // 是否为八达通管理卡
        $is_free_octopus_card = $request->boolean('is_free_octopus_card', false); // 是否为八达通免费卡

        if (blank($charge_record_number)) {
            $this->missingField('charge_record_number');
            return $this->returnJson();
        }

        $charge_record = ChargeRecord::firstWhere('charge_record_number', $charge_record_number);
        if (blank($charge_record)) {
            $this->notFoundData('charge_record_number');
            return $this->returnJson();
        }

        $connector = Connector::firstWhere('connector_number', $charge_record->connector_number);
        if (blank($connector)) {
            $this->notFoundData('connector_number');
            return $this->returnJson();
        }

        $payment_device = PaymentDeviceEnum::Octopus; // 支付设备

        $charge_pre_authorization_record_number = $charge_record->charge_pre_authorization_record_number;
        $charge_pre_authorization_record = ChargePreAuthorizationRecord::firstWhere('charge_pre_authorization_record_number', $charge_pre_authorization_record_number);
        if (blank($charge_pre_authorization_record)) {
            // 生成charge_pre_authorization_record_number，规则为connector_number+octopus_receipt_number+10位纯数字，需要保证数据库中唯一，使用retry尝试生成五次
            $connector_number = $connector->connector_number;
            $charge_pre_authorization_record_number = retry(5, function () use ($connector_number, $octopus_receipt_number) {
                $charge_pre_authorization_record_number = $connector_number . '-' . $octopus_receipt_number . '-' . str_pad(rand(0, 9999999999), 10, '0', STR_PAD_LEFT);
                $charge_pre_authorization_record_exists = ChargePreAuthorizationRecord::where('charge_pre_authorization_record_number', $charge_pre_authorization_record_number)->exists();
                if ($charge_pre_authorization_record_exists) {
                    throw new Exception('charge_pre_authorization_record_number already exists');
                }
                return $charge_pre_authorization_record_number;
            }, 100);
            if (blank($charge_pre_authorization_record_number)) {
                logger('API: /api/kiosk/createOctopusChargePreAuthorization; charge_pre_authorization_record_number generate failed', [
                    'charge_record' => $charge_record,
                ]);
                $this->missingField('charge_pre_authorization_record_number');
                $this->message = 'charge_pre_authorization_record_number generate failed';
                return $this->returnJson();
            }
            $currency_code = self::$default_currency_code;
            $currency_symbol = self::$default_currency_symbol;
            $kiosk = Kiosk::firstWhere('kiosk_number', $kiosk_number);
            if (filled($kiosk?->site)) {
                $currency_code = $kiosk?->site?->currency_code ?? $currency_code;
                $currency_symbol = $kiosk?->site?->currency_symbol ?? $currency_symbol;
            }
            $charge_pre_authorization_record = new ChargePreAuthorizationRecord;
            $charge_pre_authorization_record->charge_pre_authorization_record_number = $charge_pre_authorization_record_number;
            $charge_pre_authorization_record->charge_pre_authorization_status = ChargePreAuthorizationStatus::Confirmed;
            $charge_pre_authorization_record->merchant_number = $charge_record->merchant_number;
            $charge_pre_authorization_record->site_number = $charge_record->site_number;
            $charge_pre_authorization_record->currency_code = $currency_code;
            $charge_pre_authorization_record->currency_symbol = $currency_symbol;
            $charge_pre_authorization_record->gmt_charge_pre_authorization_status = now();
            $charge_pre_authorization_record->payment_device = $payment_device;
            $charge_pre_authorization_record->pre_authorization_amount = $pre_authorization_amount;
            $charge_pre_authorization_record->identity_type = $identity_type;
            $charge_pre_authorization_record->identity_number = $identity_number;
            $charge_pre_authorization_record->connector_number = $connector_number;
            $charge_pre_authorization_record->connector_name = $charge_record->connector_name;
            $charge_pre_authorization_record->kiosk_number = $kiosk_number;
            $charge_pre_authorization_record->kiosk_name = $kiosk_name;
            $charge_pre_authorization_record->octopus_transaction_id = $octopus_transaction_id;
            $charge_pre_authorization_record->octopus_receipt_number = $octopus_receipt_number;
            $charge_pre_authorization_record->gmt_octopus_deduct = $gmt_octopus_deduct;
            $charge_pre_authorization_record->octopus_device_number = $octopus_device_number;
            $charge_pre_authorization_record->octopus_card_type = $octopus_card_type;
            $charge_pre_authorization_record->octopus_card_number = $octopus_card_number;
            $charge_pre_authorization_record->octopus_raw_card_number = $octopus_raw_card_number;
            $charge_pre_authorization_record->octopus_balance = $octopus_balance;
            $charge_pre_authorization_record->octopus_last_added_value_type = $octopus_last_added_value_type;
            $charge_pre_authorization_record->octopus_last_added_value_date = $octopus_last_added_value_date;
            $charge_pre_authorization_record->octopus_response_json = $octopus_response_json;
            $charge_pre_authorization_record->is_admin_octopus_card = $is_admin_octopus_card;
            $charge_pre_authorization_record->is_free_octopus_card = $is_free_octopus_card;
            // 如果充电记录关联的充电枪的当前充电记录编号不等于接口传入的充电记录编号，则直接将预授权状态变为[PROCESSING]
            $connector_current_charge_record_number = $connector->current_charge_record_number;
            if ($connector_current_charge_record_number !== $charge_record_number) {
                $charge_pre_authorization_record->charge_pre_authorization_status = ChargePreAuthorizationStatus::Processing;
                $charge_pre_authorization_record->remark = now()->format('Y-m-d H:i:s') . ' Charge Record Completed, Charge Pre Authorization status is [PROCESSING].';
            }

            // 保存预授权记录，然后回填charge_pre_authorization_record_number到charge_record
            DB::transaction(function () use (&$charge_pre_authorization_record, &$charge_record, $currency_code, $currency_symbol) {
                try {
                    $charge_pre_authorization_record->saveOrFail();
                    $charge_record->charge_pre_authorization_record_number = $charge_pre_authorization_record->charge_pre_authorization_record_number;
                    $charge_record->saveOrFail();
                    // 保存支付流水
                    $charge_accounting_record = new ChargeAccountingRecord;
                    $charge_accounting_record->merchant_number = $charge_record->merchant_number;
                    $charge_accounting_record->site_number = $charge_record->site_number;
                    $charge_accounting_record->currency_code = $currency_code;
                    $charge_accounting_record->currency_symbol = $currency_symbol;
                    $charge_accounting_record->payment_device = $charge_pre_authorization_record->payment_device;
                    $charge_accounting_record->payment_method = PaymentMethod::Octopus;
                    $charge_accounting_record->transaction_type = ChargeAccountingTransactionType::Income;
                    $charge_accounting_record->transaction_category = ChargeAccountingTransactionCategory::KioskOctopusPreAuthorizationDeduct;
                    $charge_accounting_record->source_transaction_number = $charge_pre_authorization_record->charge_pre_authorization_record_number;
                    $charge_accounting_record->amount = $charge_pre_authorization_record->pre_authorization_amount;
                    $charge_accounting_record->gmt_deduct = $charge_pre_authorization_record->gmt_octopus_deduct;
                    $charge_accounting_record->octopus_card_number = $charge_pre_authorization_record->octopus_card_number;
                    $charge_accounting_record->saveOrFail();

                } catch (Exception $e) {
                    logger()->error('createOctopusChargePreAuthorization error', ['error' => $e->getMessage()]);
                    logger('API: /api/kiosk/createOctopusChargePreAuthorization; charge_pre_authorization_record/charge_record save failed', [
                        'charge_pre_authorization_record' => $charge_pre_authorization_record,
                        'charge_record' => $charge_record,
                    ]);
                    throw new \Exception('Internal Server Error', 500);
                }
            });
        }

        $this->data = $charge_pre_authorization_record;

        return $this->returnJson();
    }

    /**
     * 八达通充电预授权结算
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * @date 2025-05-22
     */
    public function settleOctopusChargePreAuthorization(Request $request): JsonResponse
    {
        $charge_pre_authorization_record_number = $request->charge_pre_authorization_record_number; // 充电预授权记录编号
        $payment_method = $request->input('payment_method', PaymentMethod::Octopus); // 支付方式
        $charge_value = $request->charge_value; // 充电量
        $charge_value_amount = $request->charge_value_amount; // 充电量金额
        $charge_value_amount_calculation_json = $request->charge_value_amount_calculation_json; // 充电量金额计算JSON
        $idling_penalty_time = $request->idling_penalty_time; // 闲置罚款时间
        $idling_penalty_amount = $request->idling_penalty_amount; // 闲置罚款金额
        $idling_penalty_amount_calculation_json = $request->idling_penalty_amount_calculation_json; // 闲置罚款金额计算JSON
        $gmt_idling_penalty_start = $request->gmt_idling_penalty_start; // 闲置罚款开始时间
        $gmt_idling_penalty_stop = $request->gmt_idling_penalty_stop; // 闲置罚款结束时间
        $total_amount = $request->total_amount; // 合共金额
        $actual_payment_amount = $request->actual_payment_amount; // 实际支付金额
        $gmt_receipt_start = $request->gmt_receipt_start; // 收据开始时间
        $gmt_receipt_stop = $request->gmt_receipt_stop; // 收据结束时间

        if (blank($charge_pre_authorization_record_number)) {
            $this->missingField('charge_pre_authorization_record_number');
            return $this->returnJson();
        }

        $charge_pre_authorization_record = ChargePreAuthorizationRecord::firstWhere('charge_pre_authorization_record_number', $charge_pre_authorization_record_number);
        if (blank($charge_pre_authorization_record)) {
            $this->notFoundData('charge_pre_authorization_record_number');
            return $this->returnJson();
        }

        $charge_record = ChargeRecord::firstWhere('charge_pre_authorization_record_number', $charge_pre_authorization_record_number);
        if (blank($charge_record)) {
            $this->notFoundData('charge_pre_authorization_record_number');
            return $this->returnJson();
        }

        // 查询八达通扣款记录
        $charge_payment_record = ChargePaymentRecord::where('charge_record_number', $charge_record->charge_record_number)
            ->where('octopus_receipt_number', $charge_pre_authorization_record->octopus_receipt_number)
            ->where('gmt_octopus_deduct', $charge_pre_authorization_record->gmt_octopus_deduct)
            ->first();
        // 不存在则新增
        if (blank($charge_payment_record)) {
            $charge_payment_record = new ChargePaymentRecord;
            $charge_payment_record->merchant_number = $charge_record->merchant_number; // 商户编号
            $charge_payment_record->site_number = $charge_record->site_number; // 站点编号
            $charge_payment_record->currency_code = $charge_record->currency_code;
            $charge_payment_record->currency_symbol = $charge_record->currency_symbol;
            $charge_payment_record->charge_record_number = $charge_record->charge_record_number; // 充电记录编号
            $charge_payment_record->charge_payment_record_number = $charge_record->charge_record_number . env('DELIMITER') . generateHashCode($charge_record->charge_record_number . time()); // 充电支付记录编号
            $charge_payment_record->payment_device = $charge_pre_authorization_record->payment_device; // 支付设备
            $charge_payment_record->payment_method = $payment_method; // 支付方式
            $charge_payment_record->payment_status = PaymentStatusEnum::Completed; // 支付状态
            $charge_payment_record->gmt_payment_status = $charge_pre_authorization_record->gmt_octopus_deduct; // 支付状态时间
            $charge_payment_record->charge_tariff_scheme = $charge_record->charge_tariff_scheme; // 充电收费方案
            $charge_payment_record->charge_value_type = $charge_record->charge_value_type; // 充电量类型
            $charge_payment_record->charge_value = $charge_value; // 充电量
            $charge_payment_record->is_idling_penalty_only = false; // 是否仅闲置罚款
            $charge_payment_record->is_charge_arrears = false; // 是否充电欠款
            $charge_payment_record->charge_value_amount = $charge_value_amount; // 充电量金额
            $charge_payment_record->idling_penalty_time = $idling_penalty_time; // 闲置罚款时间
            $charge_payment_record->idling_penalty_amount = $idling_penalty_amount; // 闲置罚款金额
            $charge_payment_record->gmt_idling_penalty_start = $gmt_idling_penalty_start; // 闲置罚款开始时间
            $charge_payment_record->gmt_idling_penalty_stop = $gmt_idling_penalty_stop; // 闲置罚款结束时间
            $charge_payment_record->total_amount = $total_amount; // 合共金额
            $charge_payment_record->actual_payment_amount = $actual_payment_amount; // 实际付款金额
            $charge_payment_record->octopus_transaction_id = $charge_pre_authorization_record->octopus_transaction_id; // 八达通交易ID
            $charge_payment_record->gmt_receipt_start = $gmt_receipt_start; // 收据开始时间
            $charge_payment_record->gmt_receipt_stop = $gmt_receipt_stop; // 收据结束时间
            $charge_payment_record->octopus_receipt_number = $charge_pre_authorization_record->octopus_receipt_number; // 八达通收据编号
            $charge_payment_record->gmt_octopus_deduct = $charge_pre_authorization_record->gmt_octopus_deduct; // 八达通扣款时间
            $charge_payment_record->octopus_device_number = $charge_pre_authorization_record->octopus_device_number; // 八达通设备编号
            $charge_payment_record->octopus_card_type = $charge_pre_authorization_record->octopus_card_type; // 八达通卡类型
            $charge_payment_record->octopus_card_number = $charge_pre_authorization_record->octopus_card_number; // 八达通卡号
            $charge_payment_record->octopus_raw_card_number = $charge_pre_authorization_record->octopus_raw_card_number; // 八达通原始卡号
            $charge_payment_record->octopus_balance = $charge_pre_authorization_record->octopus_balance; // 八达通余额
            $charge_payment_record->is_admin_octopus_card = $charge_pre_authorization_record->is_admin_octopus_card; // 是否为管理员八达通
            $charge_payment_record->is_free_octopus_card = $charge_pre_authorization_record->is_free_octopus_card; // 是否为免费八达通
            $charge_payment_record->octopus_last_added_value_type = $charge_pre_authorization_record->octopus_last_added_value_type; // 八达通最后增值类型
            $charge_payment_record->octopus_last_added_value_date = $charge_pre_authorization_record->octopus_last_added_value_date; // 八达通最后增值日期
            $charge_payment_record->octopus_response_json = $charge_pre_authorization_record->octopus_response_json; // 八达通响应JSON
            $charge_payment_record->kiosk_number = $charge_pre_authorization_record->kiosk_number; // Kiosk编号
            $charge_payment_record->kiosk_name = $charge_pre_authorization_record->kiosk_name; // Kiosk名称
            $charge_payment_record->charge_pre_authorization_record_number = $charge_pre_authorization_record->charge_pre_authorization_record_number; // 充电预授权记录编号

            // 计算商户手续费
            $charge_payment_record->merchant_handling_fee = 0;
            $charge_payment_record->merchant_receivable = $charge_payment_record->actual_payment_amount;
            if (filled($charge_record->merchant_handling_fee_rate) && $charge_record->merchant_handling_fee_rate > 0) {
                $merchant_handling_fee_rate = $charge_record->merchant_handling_fee_rate;
                // 因为是百分比，所以需要除以100
                $merchant_handling_fee_rate /= 100;
                // 计算商户手续费金额，向下取整保留两位小数
                $merchant_handling_fee = (float)bcmul($charge_payment_record->actual_payment_amount, $merchant_handling_fee_rate, 0);
                $charge_payment_record->merchant_handling_fee = $merchant_handling_fee;
                // 商户应收 = 实付金额 - 商户手续费
                $charge_payment_record->merchant_receivable = $charge_payment_record->actual_payment_amount - $charge_payment_record->merchant_handling_fee;
            }
            DB::transaction(function () use (
                &$charge_payment_record,
                &$charge_record,
                &$charge_pre_authorization_record,
                $charge_value_amount_calculation_json,
                $idling_penalty_amount_calculation_json
            ) {
                try {
                    $charge_payment_record->saveOrFail();

                    // 保存计算json
                    if (blank($charge_payment_record_calculation_model = $charge_payment_record->chargePaymentRecordCalculation)) {
                        $charge_payment_record_calculation_model = new ChargePaymentRecordCalculation;
                        $charge_payment_record_calculation_model->charge_payment_record_number = $charge_payment_record->charge_payment_record_number;
                    }
                    $charge_payment_record_calculation_model->currency_code = $charge_payment_record->currency_code;
                    $charge_payment_record_calculation_model->currency_symbol = $charge_payment_record->currency_symbol;
                    $charge_payment_record_calculation_model->charge_value_amount_calculation_json = $charge_value_amount_calculation_json; // 充电量金额计算JSON
                    $charge_payment_record_calculation_model->idling_penalty_amount_calculation_json = $idling_penalty_amount_calculation_json; // 充电量金额计算JSON
                    $charge_payment_record_calculation_model->saveOrFail();

                    // 更新充电记录
                    $charge_record->post_paid_purchase_charge_value = $charge_payment_record->charge_value;
                    $charge_record->charge_value_amount += $charge_payment_record->charge_value_amount; // 充电量金额
                    $charge_record->idling_penalty_amount += $charge_payment_record->idling_penalty_amount; // 闲置罚款金额
                    $charge_record->gmt_idling_penalty_locked = $charge_payment_record->gmt_octopus_deduct; // 闲置罚款已锁定时间
                    $charge_record->total_amount += $charge_payment_record->total_amount; // 合共金额
                    $charge_record->actual_payment_amount = $charge_payment_record->actual_payment_amount; // 实际支付金额
                    $charge_record->merchant_handling_fee += $charge_payment_record->merchant_handling_fee; // 商户手续费
                    $charge_record->merchant_receivable += $charge_payment_record->merchant_receivable; // 商户应收款
                    $charge_record->gmt_sync_to_cloud = null; // 同步至云端时间
                    $charge_record->saveOrFail();

                    // 更新预授权状态
                    $current_date_time = now()->format('Y-m-d H:i:s');
                    $charge_pre_authorization_status = ChargePreAuthorizationStatus::Completed;
                    $remark = $current_date_time . ' No Refund';
                    $refund_amount = 0;
                    // 实际支付金额小于预授权金额，需要退款，预授权状态为待退款
                    if ($charge_payment_record->actual_payment_amount < $charge_pre_authorization_record->pre_authorization_amount) {
                        $charge_pre_authorization_status = ChargePreAuthorizationStatus::PendingRefund;
                        $refund_amount = bcsub($charge_pre_authorization_record->pre_authorization_amount, $charge_payment_record->actual_payment_amount, 0);
                        $charge_pre_authorization_record->refund_amount = $refund_amount;
                        $remark = '';
                    }
                    // 预授权状态不一致，则更新状态(预授权状态为已完成，则不更新)
                    if ($charge_pre_authorization_record->charge_pre_authorization_status !== $charge_pre_authorization_status) {
                        // status_log
                        if (filled($charge_pre_authorization_record->status_log)) {
                            $charge_pre_authorization_record->status_log .= PHP_EOL;
                        }
                        $charge_pre_authorization_record->status_log .= "$current_date_time - by: api, status: [{$charge_pre_authorization_record->charge_pre_authorization_status}] -> [$charge_pre_authorization_status], reason: deduct octopus charge pre authorization";
                        $charge_pre_authorization_record->charge_pre_authorization_status = $charge_pre_authorization_status;
                        $charge_pre_authorization_record->gmt_charge_pre_authorization_status = $current_date_time;
                        // remark
                        if (filled($charge_pre_authorization_record->remark)) {
                            $charge_pre_authorization_record->remark .= PHP_EOL;
                        }
                        $charge_pre_authorization_record->remark .= $remark;
                    }
                    $charge_pre_authorization_record->deduct_amount = $charge_payment_record->actual_payment_amount;
                    $charge_pre_authorization_record->saveOrFail();
                } catch (Exception $e) {
                    logger($e->getMessage());
                    logger('API: /api/kiosk/settleOctopusChargePreAuthorization; charge_payment_record/charge_pre_authorization_record/charge_record/charge_value_amount_calculation_json/idling_penalty_amount_calculation_json save failed', [
                        'charge_record' => $charge_record,
                        'charge_payment_record' => $charge_payment_record,
                        'charge_pre_authorization_record' => $charge_pre_authorization_record,
                        'charge_value_amount_calculation_json' => $charge_value_amount_calculation_json,
                        'idling_penalty_amount_calculation_json' => $idling_penalty_amount_calculation_json,
                    ]);
                    throw new \Exception('Internal Server Error', 500);
                }
            });
        }

        $this->data = [
            'charge_pre_authorization_record' => $charge_pre_authorization_record,
            'charge_payment_record' => $charge_payment_record,
        ];

        return $this->returnJson();
    }

    /**
     * 获取待退款预授权记录
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * @date 2025-05-23
     */
    public function getPendingRefundChargePreAuthorizationRecord(Request $request): JsonResponse
    {
        $identity_type = $request->identity_type; // 身份类型
        $identity_number = $request->identity_number; // 身份号码
        $kiosk_number = $request->kiosk_number; // Kiosk编号

        if (blank($identity_type) || blank($identity_number) || blank($kiosk_number)) {
            $this->missingField('identity_type|identity_number|kiosk_number');
            return $this->returnJson();
        }

        $kiosk = Kiosk::firstWhere('kiosk_number', $kiosk_number);
        if (blank($kiosk)) {
            $this->notFoundData('kiosk_number');
            return $this->returnJson();
        }

        $charge_pre_authorization_record = ChargePreAuthorizationRecord::with([
                'chargeRecord' => [
                    'chargePaymentRecord'
                ]
            ])
            // 查询确保chargeRecord和chargePaymentRecord必须存在
            ->whereHas('chargeRecord')
            ->whereHas('chargeRecord.chargePaymentRecord')
            ->where('identity_type', $identity_type);
        // 目前只支持八达通预授权退款
        switch ($identity_type) {
            case IdentityType::OctopusPreAuthorization:
                $charge_pre_authorization_record = $charge_pre_authorization_record->whereIn('identity_number', explode(',', $identity_number));
                break;
            default:
                return $this->returnJson();
        }
        // 查询同场地下最新一条未退款的退款金额大于0的预授权记录
        $charge_pre_authorization_record = $charge_pre_authorization_record->where('site_number', $kiosk->site_number)
            ->where('charge_pre_authorization_status', ChargePreAuthorizationStatus::PendingRefund)
            ->where('refund_amount', '>', 0)
            ->orderBy('gmt_create', 'desc')
            ->first();

        if (filled($charge_pre_authorization_record)) {
            $this->data = $charge_pre_authorization_record;
            $charge_record = $this->chargeRecordArrayToJson($charge_pre_authorization_record?->chargeRecord);
            $this->data['charge_record'] = $charge_record;
            $this->data['charge_record']['charge_payment_record_list'] = $charge_record?->chargePaymentRecord ?? null;
            unset($this->data['chargeRecord']);
            unset($charge_record->chargePaymentRecord);
        }

        return $this->returnJson();
    }

    /**
     * 八达通充电预授权退款
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * @date 2025-05-23
     */
    public function octopusChargePreAuthorizationRefund(Request $request): JsonResponse
    {
        $charge_pre_authorization_record_number = $request->charge_pre_authorization_record_number; // 预授权记录编号
        $refund_amount = $request->refund_amount; // 退款金额
        $actual_refund_amount = $refund_amount; // 实际退款金额
        $kiosk_number = $request->kiosk_number; // Kiosk编号
        $kiosk_name = $request->kiosk_name; // Kiosk名称
        $octopus_transaction_id = $request->octopus_transaction_id; // 八达通交易ID
        $octopus_receipt_number = $request->octopus_receipt_number; // 八达通收据编号
        $gmt_octopus_refund = $request->gmt_octopus_refund; // 八达通退款时间
        $octopus_fund_type = $request->octopus_fund_type; // 八达通退款类型
        $octopus_device_number = $request->octopus_device_number; // 八达通设备编号
        $octopus_card_type = $request->octopus_card_type; // 八达通卡类型
        $octopus_card_number = $request->octopus_card_number; // 八达通卡号
        $octopus_raw_card_number = $request->octopus_raw_card_number; // 八达通原始卡号
        $octopus_balance = $request->octopus_balance; // 八达通余额
        $octopus_last_added_value_type = $request->octopus_last_added_value_type; // 八达通最后增值类型
        $octopus_last_added_value_date = $request->octopus_last_added_value_date; // 八达通最后增值日期
        $octopus_response_json = $request->octopus_response_json; // 八达通响应JSON

        if (blank($charge_pre_authorization_record_number)) {
            $this->missingField('charge_pre_authorization_record_number');
            return $this->returnJson();
        }

        $charge_pre_authorization_record = ChargePreAuthorizationRecord::firstWhere('charge_pre_authorization_record_number', $charge_pre_authorization_record_number);
        if (blank($charge_pre_authorization_record)) {
            $this->notFoundData('charge_pre_authorization_record_number');
            return $this->returnJson();
        }

        $current_date_time = now()->format('Y-m-d H:i:s');

        // 查询退款记录
        $charge_pre_authorization_refund_record = ChargePreAuthorizationRefundRecord::where('charge_pre_authorization_record_number', $charge_pre_authorization_record_number)
            ->where('octopus_receipt_number', $octopus_receipt_number)
            ->first();

        if (blank($charge_pre_authorization_refund_record)) {
            // 生成charge_pre_authorization_refund_record_number，规则为charge_pre_authorization_record_number+10位纯数字，需要保证数据库中唯一，使用retry尝试生成五次
            $charge_pre_authorization_refund_record_number = retry(5, function () use ($charge_pre_authorization_record_number) {
                $charge_pre_authorization_refund_record_number = $charge_pre_authorization_record_number . '-' . str_pad(rand(0, 9999999999), 10, '0', STR_PAD_LEFT);
                $charge_pre_authorization_refund_record_exists = ChargePreAuthorizationRefundRecord::where('charge_pre_authorization_refund_record_number', $charge_pre_authorization_refund_record_number)->exists();
                if ($charge_pre_authorization_refund_record_exists) {
                    throw new Exception('charge_pre_authorization_refund_record_number already exists');
                }
                return $charge_pre_authorization_refund_record_number;
            }, 100);
            if (blank($charge_pre_authorization_refund_record_number)) {
                logger('API: /api/kiosk/octopusPreAuthorizationRefund; charge_pre_authorization_refund_record_number generate failed', [
                    'charge_pre_authorization_record' => $charge_pre_authorization_record,
                ]);
                $this->missingField('charge_pre_authorization_refund_record_number');
                $this->message = 'charge_pre_authorization_refund_record_number generate failed';
                return $this->returnJson();
            }
            $currency_code = self::$default_currency_code;
            $currency_symbol = self::$default_currency_symbol;
            $kiosk = Kiosk::firstWhere('kiosk_number', $kiosk_number);
            if (filled($kiosk?->site)) {
                $currency_code = $kiosk?->site?->currency_code ?? $currency_code;
                $currency_symbol = $kiosk?->site?->currency_symbol ?? $currency_symbol;
            }
            // 新建退款记录
            $charge_pre_authorization_refund_record = new ChargePreAuthorizationRefundRecord;
            $charge_pre_authorization_refund_record->charge_pre_authorization_refund_record_number = $charge_pre_authorization_refund_record_number;
            $charge_pre_authorization_refund_record->charge_pre_authorization_record_number = $charge_pre_authorization_record_number;
            $charge_pre_authorization_refund_record->merchant_number = $charge_pre_authorization_record->merchant_number;
            $charge_pre_authorization_refund_record->site_number = $charge_pre_authorization_record->site_number;
            $charge_pre_authorization_refund_record->currency_code = $currency_code;
            $charge_pre_authorization_refund_record->currency_symbol = $currency_symbol;
            $charge_pre_authorization_refund_record->payment_device = PaymentDeviceEnum::Octopus;
            $charge_pre_authorization_refund_record->refund_amount = $refund_amount;
            $charge_pre_authorization_refund_record->actual_refund_amount = $actual_refund_amount;
            $charge_pre_authorization_refund_record->identity_type = $charge_pre_authorization_record->identity_type;
            $charge_pre_authorization_refund_record->identity_number = $charge_pre_authorization_record->identity_number;
            $charge_pre_authorization_refund_record->kiosk_number = $kiosk_number;
            $charge_pre_authorization_refund_record->kiosk_name = $kiosk_name;
            $charge_pre_authorization_refund_record->octopus_transaction_id = $octopus_transaction_id;
            $charge_pre_authorization_refund_record->octopus_receipt_number = $octopus_receipt_number;
            $charge_pre_authorization_refund_record->gmt_octopus_refund = $gmt_octopus_refund;
            $charge_pre_authorization_refund_record->octopus_fund_type = $octopus_fund_type;
            $charge_pre_authorization_refund_record->octopus_device_number = $octopus_device_number;
            $charge_pre_authorization_refund_record->octopus_card_type = $octopus_card_type;
            $charge_pre_authorization_refund_record->octopus_card_number = $octopus_card_number;
            $charge_pre_authorization_refund_record->octopus_raw_card_number = $octopus_raw_card_number;
            $charge_pre_authorization_refund_record->octopus_balance = $octopus_balance;
            $charge_pre_authorization_refund_record->octopus_last_added_value_type = $octopus_last_added_value_type;
            $charge_pre_authorization_refund_record->octopus_last_added_value_date = $octopus_last_added_value_date;
            $charge_pre_authorization_refund_record->octopus_response_json = $octopus_response_json;

            // 更新预授权表
            $charge_pre_authorization_record->actual_refund_amount = $actual_refund_amount;
            $charge_pre_authorization_status = ChargePreAuthorizationStatus::Completed;
            // status_log
            if (filled($charge_pre_authorization_record->status_log)) {
                $charge_pre_authorization_record->status_log .= PHP_EOL;
            }
            $charge_pre_authorization_record->status_log .= "$current_date_time - by: api, status: [{$charge_pre_authorization_record->charge_pre_authorization_status}] -> [$charge_pre_authorization_status], reason: octopus charge pre authorization refund";
            $charge_pre_authorization_record->charge_pre_authorization_status = $charge_pre_authorization_status;
            $charge_pre_authorization_record->gmt_charge_pre_authorization_status = $current_date_time;

            // 保存
            DB::transaction(function () use (&$charge_pre_authorization_record, &$charge_pre_authorization_refund_record, $currency_code, $currency_symbol) {
                try {
                    $charge_pre_authorization_record->saveOrFail();
                    $charge_pre_authorization_refund_record->saveOrFail();
                    // 保存退款流水
                    $charge_accounting_record = new ChargeAccountingRecord;
                    $charge_accounting_record->merchant_number = $charge_pre_authorization_record->merchant_number;
                    $charge_accounting_record->site_number = $charge_pre_authorization_record->site_number;
                    $charge_accounting_record->currency_code = $currency_code;
                    $charge_accounting_record->currency_symbol = $currency_symbol;
                    $charge_accounting_record->payment_device = $charge_pre_authorization_record->payment_device;
                    $charge_accounting_record->payment_method = PaymentMethod::Octopus;
                    $charge_accounting_record->transaction_type = ChargeAccountingTransactionType::Expense;
                    $charge_accounting_record->transaction_category = ChargeAccountingTransactionCategory::KioskOctopusPreAuthorizationRefund;
                    $charge_accounting_record->source_transaction_number = $charge_pre_authorization_refund_record->charge_pre_authorization_refund_record_number;
                    $charge_accounting_record->amount = $charge_pre_authorization_refund_record->actual_refund_amount;
                    $charge_accounting_record->gmt_deduct = $charge_pre_authorization_refund_record->gmt_octopus_refund;
                    $charge_accounting_record->octopus_card_number = $charge_pre_authorization_refund_record->octopus_card_number;
                    $charge_accounting_record->saveOrFail();
                } catch (Exception $e) {
                    logger()->error('octopusPreAuthorizationRefund error', ['error' => $e->getMessage()]);
                    logger('API: /api/kiosk/octopusPreAuthorizationRefund; charge_pre_authorization_record/charge_pre_authorization_refund_record/charge_accounting_record save failed', [
                        'charge_pre_authorization_record' => $charge_pre_authorization_record,
                        'charge_pre_authorization_refund_record' => $charge_pre_authorization_refund_record,
                    ]);
                    throw new \Exception('Internal Server Error', 500);
                }
            });
        }

        $this->data = $charge_pre_authorization_refund_record;
        return $this->returnJson();
    }

    /**
     * 后付扣款APP
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-16
     */
    public function posUserChargeRecordBillDeduct(Request $request): JsonResponse
    {
        $total_amount = $request->total_amount;
        $language_code = self::getLanguageCode($request);
        $user_charge_record_bill_item_number_list = $request->user_charge_record_bill_item_number_list; // 充电记录ID
        $user_charge_record_bill_item_number_list = json_decode($user_charge_record_bill_item_number_list, true) ?: [];
        $payment_device = PaymentDeviceEnum::Pos; // 支付设备
        $payment_method = $request->input('payment_method'); // 支付方式
        $payment_status = $request->input('payment_status', PaymentStatusEnum::Completed); // 支付状态
        $pos_vendor = $request->input('pos_vendor'); // POS供应商
        $pos_receipt_number = $request->input('pos_receipt_number'); // POS收据编号
        $gmt_pos_deduct = $request->input('gmt_pos_deduct'); // POS扣款时间
        $pos_transaction_id = $request->input('pos_transaction_id'); // POS交易ID
        $pos_payment_method_name = $request->input('pos_payment_method_name'); // POS支付方式名称
        $pos_card_number = $request->input('pos_card_number'); // POS卡号
        $pos_trace_no = $request->input('pos_trace_no'); // POS追踪编号
        $pos_reference_id = $request->input('pos_reference_id'); // POS参考编号
        $pos_response_json = $request->input('pos_response_json'); // POS响应JSON

        $gmt_payment_status = $gmt_pos_deduct; // 支付状态时间

        $this->data = null;
        $user_charge_record_bill_item_list = [];
        if (blank($payment_method) || !PaymentMethod::hasValue($payment_method)) {
            $this->missingField('payment_method');
            return $this->returnJson();
        }
        if (blank($user_charge_record_bill_item_number_list)) {
            $this->missingField('user_charge_record_bill_item_number_list');
            return $this->returnJson();
        }
        $bill_condition = function ($query) use ($user_charge_record_bill_item_number_list) {
            $query->whereIn('charge_payment_record_number', $user_charge_record_bill_item_number_list);
        };
        // 查询同商户下所有未付款的充电记录
        $charge_record_list = ChargeRecord::with(['chargePaymentRecord' => $bill_condition])
            ->whereHas('chargePaymentRecord', $bill_condition)
            ->get();
        foreach ($charge_record_list as $charge_record) {
            foreach ($charge_record->chargePaymentRecord as $charge_payment_record) {
                $charge_record_number_array = explode('-', $charge_record->charge_record_number);
                $user_charge_record_bill_item_list[] = [
                    'user_charge_record_bill_item_number' => $charge_payment_record->charge_payment_record_number,
                    'bill_number' => end($charge_record_number_array) ?? null,
                    'gmt_start' => $charge_record->gmt_power_on ?? $charge_record->gmt_start,
                    'amount' => $charge_payment_record->total_amount,
                ];
                if ($charge_payment_record->payment_status !== PaymentStatusEnum::Pending) continue;
                $result = DB::transaction(function () use (
                    &$charge_record,
                    $charge_payment_record,
                    $payment_device,
                    $payment_method,
                    $payment_status,
                    $gmt_payment_status,
                    $pos_vendor,
                    $pos_receipt_number,
                    $gmt_pos_deduct,
                    $pos_transaction_id,
                    $pos_payment_method_name,
                    $pos_card_number,
                    $pos_trace_no,
                    $pos_reference_id,
                    $pos_response_json,
                    &$total_amount
                ) {
                    $charge_payment_record->payment_device = $payment_device; // 支付设备
                    $charge_payment_record->payment_method = $payment_method; // 支付方式
                    $charge_payment_record->payment_status = $payment_status; // 支付状态
                    $charge_payment_record->gmt_payment_status = $gmt_payment_status; // 支付状态时间
                    $charge_payment_record->pos_vendor = $pos_vendor; // POS供应商
                    $charge_payment_record->pos_receipt_number = $pos_receipt_number; // POS收据编号
                    $charge_payment_record->gmt_pos_deduct = $gmt_pos_deduct; // POS扣款时间
                    $charge_payment_record->pos_transaction_id = $pos_transaction_id; // POS交易ID
                    $charge_payment_record->pos_payment_method_name = $pos_payment_method_name; // POS支付方式名称
                    $charge_payment_record->pos_card_number = $pos_card_number; // POS卡号
                    $charge_payment_record->pos_trace_no = $pos_trace_no; // POS追踪编号
                    $charge_payment_record->pos_reference_id = $pos_reference_id; // POS参考编号
                    $charge_payment_record->pos_response_json = $pos_response_json; // POS响应JSON
                    // 计算付款金额
                    $charge_payment_record->use_points = 0; // 使用的积分
                    $charge_payment_record->actual_payment_amount = 0; // 实际支付金额
                    if ($total_amount > 0) {
                        // 实际支付金额。总金额如果大于实际支付金额，抵扣所有实际支付金额；否则抵扣总金额等量的实际支付金额
                        // $charge_payment_record->actual_payment_amount = $total_amount > $total_amount ? $total_amount : $total_amount; // 实际支付金额
                        $charge_payment_record->actual_payment_amount = $total_amount > $charge_payment_record->actual_payment_amount ? $charge_payment_record->total_amount : $total_amount; // 实际支付金额
                        // 扣除对应实际支付金额
                        $total_amount = bcsub($total_amount, $charge_payment_record->actual_payment_amount, 0);
                    }
                    $charge_record->actual_payment_amount += $charge_payment_record->actual_payment_amount; // 实际支付金额
                    $charge_payment_record->save();

                    return true;
                });
            }
            $charge_record->save();

            try {
                // 通知Kiosk充电记录
                $charge_record = $charge_record->first(); // Retrieve the actual ChargeRecord object
                $this->requestNotifyKioskChargeRecord($charge_record);
            } catch (Exception $e) {
            }
        }

        $this->data = [
            'user_charge_record_bill_item_number_list' => $user_charge_record_bill_item_number_list,
            'user_charge_record_bill_item_list' => $user_charge_record_bill_item_list,
            'payment_device' => $payment_device,
            'payment_method' => $payment_method,
            'payment_status' => $payment_status,
            'total_amount' => (int)$request->total_amount,
            'pos_vendor' => $pos_vendor,
            'gmt_pos_deduct' => $gmt_pos_deduct,
            'pos_receipt_number' => $pos_receipt_number,
            'pos_transaction_id' => $pos_transaction_id,
            'pos_payment_method_name' => $pos_payment_method_name,
            'pos_card_number' => $pos_card_number,
            'pos_trace_no' => $pos_trace_no,
            'pos_reference_id' => $pos_reference_id,
            'pos_response_json' => $pos_response_json,
        ];

        return $this->returnJson();
    }

    /**
     * 获取剩余充电量记录
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-24
     */
    public function getAvailableRemainChargeValueRecord(Request $request): JsonResponse
    {
        $charge_value_type = $request->input('charge_value_type'); // 充电量类型
        $identity_type = $request->input('identity_type'); // 身份类型
        $identity_number = $request->input('identity_number'); // 身份号码
        $connector_number = $request->input('connector_number'); // 充电枪编号

        $this->data = null;
        if (filled($identity_number) && IdentityType::hasValue($identity_type) && ChargeValueType::hasValue($charge_value_type) && filled($connector_number)) {
            $octopus_card_list = explode(',', $identity_number ?? '');
            $connector = Connector::firstWhere('connector_number', $connector_number);
            $connector_setting_number = $connector?->connector_setting_number ?? null; // 传入的充电枪编号对应的充电枪设置
            $remain_charge_value_record_list = RemainChargeValueRecord::where('charge_value_type', $charge_value_type)
                ->where('connector_setting_number', $connector_setting_number)
                ->where('identity_type', $identity_type)
                // // 如果identity_type是Octopus，需要拆分新旧卡号查询；如果是解锁码，直接判断
                ->when($identity_type === IdentityType::Octopus, function ($query) use ($identity_number, $octopus_card_list) {
                    $query->whereIn('identity_number', $octopus_card_list);
                }, function ($query) use ($identity_number){
                    $query->where('identity_number', $identity_number);
                })
                ->where('gmt_expired', '>', date('Y-m-d H:i:s'))
                ->whereNull('gmt_used')
                ->whereNull('gmt_invalid')
                ->orderBy('gmt_create', 'desc')
                ->get();
            switch ($identity_type) {
                case IdentityType::Octopus:
                    $card_b = $octopus_card_list[1] ?? null;
                    if (filled($card_b)) {
                        $remain_charge_value_record = $remain_charge_value_record_list->where('identity_number', $card_b)
                            ->first();
                    }
                    if (isset($remain_charge_value_record) && filled($remain_charge_value_record)) {
                        $this->data = $remain_charge_value_record;
                    } else {
                        $card_a = $octopus_card_list[0] ?? null;
                        $remain_charge_value_record = $remain_charge_value_record_list->where('identity_number', $card_a)
                            ->first();
                        if (filled($remain_charge_value_record)) {
                            $this->data = $remain_charge_value_record;
                        }
                    }
                    break;
                default:
                    $remain_charge_value_record = $remain_charge_value_record_list->first();
                    break;
            }
            if (isset($remain_charge_value_record)) {
                $this->data = $this->getArray($remain_charge_value_record);
            }
        } else {
            $this->missingField('charge_value_type|identity_type|identity_number|connector_number');
        }
        return $this->returnJson();
    }

    /**
     * 上传八达通文件
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-24
     */
    public function uploadOctopusFile(Request $request): JsonResponse
    {
        $kiosk_number = $request->input('kiosk_number'); // Kiosk编号
        $octopus_device_number = $request->input('octopus_device_number'); // 八达通设备编号
        $octopus_data_exchange_mode = $request->input('octopus_data_exchange_mode'); // 八达通数据交换模式
        $octopus_data_exchange_operation_mode = $request->input('octopus_data_exchange_operation_mode'); // 八达通数据交换操作模式

        $this->data = false;
        if (blank($kiosk_number)) {
            $this->missingField('kiosk_number');
            return $this->returnJson();
        }
        if (blank($octopus_device_number)) {
            $this->missingField('octopus_device_number');
            return $this->returnJson();
        }
        if (blank($octopus_data_exchange_mode)) {
            $this->missingField('octopus_data_exchange_mode');
            return $this->returnJson();
        }
        if (blank($octopus_data_exchange_operation_mode)) {
            $this->missingField('octopus_data_exchange_operation_mode');
            return $this->returnJson();
        }
        if (!$request->hasFile('file') || !$request->file('file')->isValid()) {
            $this->missingField('file');
            return $this->returnJson();
        }
        $kiosk = Kiosk::firstWhere('kiosk_number', $kiosk_number);
        if (blank($kiosk)) {
            $this->notFoundData('kiosk_number');
            return $this->returnJson();
        }

        $full_path = '/octopus_upload_file/';
        $full_path .= date('YmdHis') . '/'; // // 创建 年月日时分秒 文件夹

        $file_name = $request->file('file')->getClientOriginalName(); // 获取文件的原始名称
        $request->file('file')->storeAs($full_path, $file_name);
        // 如果$octopus_data_exchange_mode是CMS，复制一份文件到rwl/upload
        if ($octopus_data_exchange_mode === OctopusDataExchangeMode::Cms) {
            $full_path_rwl = '/rwl/upload/';
            $request->file('file')->storeAs($full_path_rwl, $file_name);
        }

        $octopus_upload_file_record = new OctopusUploadFileRecord;
        $octopus_upload_file_record->kiosk_number = $kiosk->kiosk_number; // Kiosk编号
        $octopus_upload_file_record->kiosk_name = $kiosk->name; // Kiosk名称
        $octopus_upload_file_record->octopus_device_number = $octopus_device_number; // 八达通设备编号
        $octopus_upload_file_record->octopus_data_exchange_mode = $octopus_data_exchange_mode; // 八达通数据交换模式
        $octopus_upload_file_record->octopus_data_exchange_operation_mode = $octopus_data_exchange_operation_mode; // 八达通数据交换操作模式
        $octopus_upload_file_record->file_url = $full_path . $file_name; // 文件路径
        $this->data = $octopus_upload_file_record->save();
        if ($this->data) {
            $kiosk->gmt_octopus_last_upload = now();
            $kiosk->save();
        }

        return $this->returnJson();
    }

    /**
     * 下载八达通文件
     *
     * @param  Request $request
     * @return JsonResponse
     * @Description
     * @example
     * @date 2024-05-23
     */
    public function downloadedOctopusFile(Request $request): JsonResponse
    {
        $kiosk_number = $request->input('kiosk_number'); // Kiosk编号
        $octopus_device_number = $request->input('octopus_device_number'); // 八达通设备编号
        $download_file_list = $request->input('download_file_list'); // 下载文件列表
        $downloaded_file_list = $request->input('downloaded_file_list'); // 已下载文件列表
        $octopus_data_exchange_mode = $request->input('octopus_data_exchange_mode'); // 八达通数据交换模式
        $octopus_data_exchange_operation_mode = $request->input('octopus_data_exchange_operation_mode'); // 八达通数据交换操作模式

        $this->data = false;
        if (blank($kiosk_number)) {
            $this->missingField('kiosk_number');
            return $this->returnJson();
        }
        if (blank($octopus_device_number)) {
            $this->missingField('octopus_device_number');
            return $this->returnJson();
        }
        if (blank($octopus_data_exchange_mode)) {
            $this->missingField('octopus_data_exchange_mode');
            return $this->returnJson();
        }
        if (blank($octopus_data_exchange_operation_mode)) {
            $this->missingField('octopus_data_exchange_operation_mode');
            return $this->returnJson();
        }

        $kiosk = Kiosk::firstWhere('kiosk_number', $kiosk_number);
        if (blank($kiosk)) {
            $this->notFoundData('kiosk_number');
            return $this->returnJson();
        }

        $octopus_download_file_record = new OctopusDownloadFileRecord;
        $octopus_download_file_record->kiosk_number = $kiosk->kiosk_number; // Kiosk编号
        $octopus_download_file_record->kiosk_name = $kiosk->name; // Kiosk名称
        $octopus_download_file_record->octopus_device_number = $octopus_device_number; // 八达通设备编号
        $octopus_download_file_record->octopus_data_exchange_mode = $octopus_data_exchange_mode; // 八达通数据交换模式
        $octopus_download_file_record->octopus_data_exchange_operation_mode = $octopus_data_exchange_operation_mode; // 八达通数据交换操作模式
        $octopus_download_file_record->download_file_list = $download_file_list; // 下载文件列表
        $octopus_download_file_record->downloaded_file_list = $downloaded_file_list; // 已下载文件列表
        $this->data = $octopus_download_file_record->save();

        if ($this->data) {
            $kiosk->gmt_octopus_last_download = now();
            $kiosk->save();
        }

        return $this->returnJson();
    }

    /**
     * 创建八达通卡更换记录
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     */
    public function createOctopusCardReplacementRecord(Request $request): JsonResponse
    {
        $kiosk_number = $request->input('kiosk_number'); // Kiosk编号
        $octopus_device_number = $request->input('octopus_device_number'); // 八达通设备编号
        $old_card_number = $request->input('old_card_number'); // 旧卡号
        $new_card_number = $request->input('new_card_number'); // 新卡号
        $description = $request->input('description'); // 描述

        if (!empty($kiosk_number) && !empty($octopus_device_number) && !empty($old_card_number) && !empty($new_card_number) && filled($kiosk = Kiosk::firstWhere('kiosk_number', $kiosk_number))) {

            $octopus_card_replacement_record = new OctopusCardReplacementRecord;
            $octopus_card_replacement_record->kiosk_number = $kiosk->kiosk_number; // Kiosk编号
            $octopus_card_replacement_record->kiosk_name = $kiosk->name; // Kiosk名称
            $octopus_card_replacement_record->octopus_device_number = $octopus_device_number; // 八达通设备编号
            $octopus_card_replacement_record->old_card_number = $old_card_number; // 旧卡
            $octopus_card_replacement_record->new_card_number = $new_card_number; // 新卡
            $octopus_card_replacement_record->description = $description; // 描述
            $this->data = $octopus_card_replacement_record->save();
        } else {
            $this->data = false;
            $this->missingField('kiosk_number|octopus_device_number|old_card_number|new_card_number');
        }
        return $this->returnJson();
    }

    /**
     * 创建Kiosk屏幕操作记录
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-24
     */
    public function createKioskScreenOperationRecord(Request $request): JsonResponse
    {
        $kiosk_number = $request->input('kiosk_number'); // Kiosk编号
        $from_screen = $request->input('from_screen'); // 起始屏幕
        $to_screen = $request->input('to_screen'); // 到达屏幕
        $detail = $request->input('detail'); // 详情

        if (!empty($kiosk_number) && !empty($from_screen) && !empty($to_screen) && filled($kiosk = Kiosk::firstWhere('kiosk_number', $kiosk_number))) {
            $kiosk_screen_operation_record = new KioskScreenOperationRecord;
            $kiosk_screen_operation_record->kiosk_number = $kiosk->kiosk_number; // Kiosk编号
            $kiosk_screen_operation_record->kiosk_name = $kiosk->name; // Kiosk名称
            $kiosk_screen_operation_record->from_screen = $from_screen; // 起始屏幕
            $kiosk_screen_operation_record->to_screen = $to_screen; // 到达屏幕
            $kiosk_screen_operation_record->detail = $detail; // 详情
            $this->data = $kiosk_screen_operation_record->save();

            $kiosk->current_display_screen = $to_screen; // 当前显示屏幕
            $kiosk->save();
        } else {
            $this->data = false;
            $this->missingField('kiosk_number|from_screen|to_screen');
        }

        return $this->returnJson();
    }

    /**
     * 获取充电队列信息
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-24
     */
    public function getChargeQueueInformation(Request $request): JsonResponse
    {
        $connector_number = $request->input('connector_number'); // 充电枪编号
        if (!empty($connector_number)) {
            $connector = Connector::firstWhere('connector_number', $connector_number);
            if (filled($connector) && filled($connector->chargePoint)) {
                // 当前最大队列位置
                $maximum_queue_position = null;
                // 最小排队时间
                $gmt_minimum_queue = null;
                if (filled($connector->chargePoint->site)) {
                    if ($connector->chargePoint->site->lms_mode === LmsMode::MaximumOutput) {
                        // 获取场地下所有的充电机ID
                        $site_charge_point_number_list = ChargePoint::where('site_number', $connector->site_number)
                            ->pluck('charge_point_number');
                        // 场地下充电机最大的排队位置
                        $maximum_queue_position = Connector::whereIn('charge_point_number', $site_charge_point_number_list)->max('queue_position');
                        if ($maximum_queue_position === 1) {
                            // 如果队列位置是1，则需要计算队列时间
                            $charge_record_number_list = array(); // 正在充电的充电记录ID列表
                            foreach ($connector->chargePoint->site->chargePoint as $charge_point) {
                                $current_charge_record_number_list = $charge_point->connector->whereNotNull('current_charge_record_number')->pluck('current_charge_record_number');

                                if (filled($current_charge_record_number_list)) {
                                    $charge_record_number_list = array_merge($charge_record_number_list, $current_charge_record_number_list->toArray());
                                }
                            }

                            $charge_record_list = ChargeRecord::whereIn('charge_record_number', $charge_record_number_list)->get();

                            $queue_time_list = array(); // 队列时间
                            foreach ($charge_record_list as $charge_record) {
                                $queue_time_list[] = strtotime($charge_record->gmt_start) + $charge_record->post_paid_maximum_charge_time;
                            }

                            if (filled($queue_time_list)) {
                                $gmt_minimum_queue = date('Y-m-d H:i:s', min($queue_time_list));
                            }
                        }
                    }
                }
                $this->data['maximum_queue_position'] = $maximum_queue_position;
                $this->data['gmt_minimum_queue'] = $gmt_minimum_queue;
            } else {
                $this->missingField('connector_number');
            }
        } else {
            $this->missingField('connector_number');
        }

        return $this->returnJson();
    }

    /**
     * 创建事件记录
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-24
     */
    public function createEventLog(Request $request): JsonResponse
    {
        $merchant_number = $request->input('merchant_number'); // 商户编号
        $merchant_name = $request->input('merchant_name'); // 商户名称
        $site_number = $request->input('site_number'); // 场地编号
        $site_name = $request->input('site_name'); // 场地名称
        $target = $request->input('target'); // 目标
        $target_number = $request->input('target_number'); // 目标编号
        $target_name = $request->input('target_name'); // 目标名称
        $event_type = $request->input('event_type'); // 事件类型
        $event_code = $request->input('event_code'); // 事件编码
        $description = $request->input('description'); // 描述
        $exception = $request->input('exception'); //异常
        $charge_record_number = $request->input('charge_record_number'); // 充电记录编号
        $remark = $request->input('remark'); // 备注

        if (!empty($target) && !empty($target_number) && !empty($target_name) && !empty($event_type)) {
            $sub_query = EventLog::where('merchant_number', $merchant_number)
                ->where('site_number', $site_number)
                ->where('target', $target)
                ->where('target_number', $target_number)
                ->orderBy('event_log_id', 'desc')
                ->limit(env('KIOSK_API_EVENT_LOG_LIMIT', 10));
            $event_log_model = EventLog::fromSub($sub_query, 'sub_query')
                ->where('event_type', $event_type)
                ->where('description', $description)
                ->where('exception', $exception)
                ->where('charge_record_number', $charge_record_number)
                ->where('remark', $remark)
                ->orderBy('event_log_id', 'desc')
                ->first();
            if (filled($event_log_model)) {
                // 仅修改修改时间
                $event_log_model->{EventLog::UPDATED_AT} = now();
                $event_log_model->save();
            } else {
                $event_log_model = new EventLog;
                $event_log_model->merchant_number = $merchant_number; // 商户编号
                $event_log_model->merchant_name = $merchant_name; // 商户名称
                $event_log_model->site_number = $site_number; // 场地编号
                $event_log_model->site_name = $site_name; // 场地名称
                $event_log_model->target = $target; //
                $event_log_model->target_number = $target_number; //
                $event_log_model->target_name = $target_name;
                $event_log_model->event_type = $event_type; //
                $event_log_model->event_code = $event_code;
                $event_log_model->description = $description; //
                $event_log_model->exception = $exception; //
                $event_log_model->charge_record_number = $charge_record_number;
                $event_log_model->remark = $remark;

                // 保存日志，根据规则保存对应日志
                $this->saveEventLogOrRules($event_log_model, true);
            }


            $this->data = true;

            // 并发送websocket通知
            self::sendInitPushWeb();
        } else {
            $this->data = false;
            $this->missingField('target|target_number|target_name|event_type');
        }

        return $this->returnJson();
    }

    /**
     * Kiosk创建事件记录
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2023-01-13
     */
    public function updateChargePointCSBypassMode(Request $request): JsonResponse
    {
        $kiosk_number = $request->input('kiosk_number');                      // kiosk_number
        $is_bypass_mode = $request->input('is_bypass_mode');          // 请求url

        if (empty($kiosk_number) || !filled($kiosk_model = Kiosk::firstWhere('kiosk_number', $kiosk_number)) || empty($is_bypass_mode)) {
            $this->data = false;
            $this->missingField('kiosk_number|is_bypass_mode');
        }

        // 获取结果
        $charge_point_result_list = ChargePoint::select('charge_point_cs_number')
            ->whereNotNull('charge_point_cs_number')
            ->groupBy('charge_point_cs_number')
            ->pluck('charge_point_cs_number');

        foreach ($charge_point_result_list as $charge_point_cs_number) {
            // 获取charge_point_cs
            if (
                filled($charge_point_cs_model = ChargePointCs::firstWhere('charge_point_cs_number', $charge_point_cs_number)) &&
                filled($charge_point_cs_model->api_url) &&
                filled($charge_point_cs_model->api_token)
            ) {
                try {
                    $params = array(
                        'key' => $charge_point_cs_model->api_token,
                        'mode' => $is_bypass_mode === 1 ? 'on' : 'off',
                    );

                    // 获取返回信息
                    $response = self::curlPostJson(
                        $charge_point_cs_model->api_url . '/command/setbypassmode',
                        json_encode($params),
                        array('Content-Type: application/json; charset=utf-8'),
                    );

                    $format_result = json_decode(htmlspecialchars_decode($response), true);

                    // 失败时保存EventLog
                    if (
                        is_null($format_result) || (isset($format_result['status']) && $format_result['status'] != 200) ||
                        (isset($format_result['response']) && strtolower($format_result['response']) != 'success')
                    ) {
                        $event_log_model = new EventLog;
                        $event_log_model->target = 'KIOSK';
                        $event_log_model->target_number = $kiosk_model->kiosk_number;
                        $event_log_model->target_name = $kiosk_model->name;
                        $event_log_model->event_type = EventTypeEnum::PostChargePointCSBypassFailure;
                        $event_log_model->description = 'Kiosk Change Bypass Mode Error: ' . $response;

                        self::saveEventLogOrRules($event_log_model, true);
                    }
                } catch (Exception $e) {
                    // 失败时保存EventLog
                    $event_log_model = new EventLog;
                    $event_log_model->target = 'KIOSK';
                    $event_log_model->target_number = $kiosk_model->kiosk_number;
                    $event_log_model->target_name = $kiosk_model->name;
                    $event_log_model->event_type = EventTypeEnum::PostChargePointCSBypassFailure;
                    $event_log_model->description = 'Kiosk Change Bypass Mode Error: ' . $e->getMessage();

                    self::saveEventLogOrRules($event_log_model, true);
                }
            }
        }

        return $this->returnJson();
    }

    /**
     * 获取可用维修记录
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-24
     */
    public function getActivationMaintenanceRecord(Request $request): JsonResponse
    {
        $kiosk_number = $request->input('kiosk_number');

        if (!empty($kiosk_number) && filled($kiosk = Kiosk::firstWhere('kiosk_number', $kiosk_number))) {
            $maintenance_record = MaintenanceRecord::orWhere(function ($query) use ($kiosk) {
                $query->orWhere(function ($in_query) {
                    $in_query->whereNull('kiosk_number_list')->whereNull('site_number_list');
                })
                    ->orWhereRaw('find_in_set("' . $kiosk->kiosk_number . '", kiosk_number_list)')
                    ->when(filled($kiosk->site_number), function ($in_query) use ($kiosk) {
                        $in_query->orWhereRaw('find_in_set("' . $kiosk->site_number . '", site_number_list)');
                    });
            })
                ->where(function ($query) {
                    $query->whereNull('gmt_end')
                        ->orWhere('gmt_end', '>', date('Y-m-d H:i:s'));
                })
                ->latest()
                ->first();

            $this->data = $this->getArray($maintenance_record);
        } else {
            $this->data = false;
            $this->missingField('kiosk_number');
        }

        return $this->returnJson();
    }

    /**
     * 发送充电记录接收
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-24
     */
    public function sendChargeRecordReminder(Request $request): JsonResponse
    {
        $charge_record_number = $request->input('charge_record_number');

        if (!empty($charge_record_number) && filled($charge_record = ChargeRecord::firstWhere('charge_record_number', $charge_record_number))) {
            try {
                // 通知Kiosk充电记录
                $this->requestNotifyKioskChargeRecord($charge_record);
                // 发送邮件及短信
                $this->sendMailAndSMS($charge_record);
            } catch (Exception $e) {
                logger('sendChargeRecordReminder')->error($e->getMessage());
            }
        } else {
            $this->missingField('charge_record_number');
        }

        return $this->returnJson();
    }

    public function sendChargeRecordLMSClosedReminder(Request $request): JsonResponse
    {
        $charge_record_number = $request->input('charge_record_number');

        if (!empty($charge_record_number) && filled($charge_record = ChargeRecord::firstWhere('charge_record_number', $charge_record_number))) {
            try {
                // 发送邮件及短信
                $this->sendChargeRecordLMSClosedMailAndSMS($charge_record);
            } catch (Exception $e) {
                logger('sendChargeRecordLMSClosedReminder')->error($e->getMessage());
            }
        } else {
            $this->missingField('charge_record_number');
        }

        return $this->returnJson();
    }

    /**
     * 心跳
     *
     * @return JsonResponse
     */
    public function heartbeat(): JsonResponse
    {
        $this->data = date('Y-m-d H:i:s');
        return $this->returnJson();
    }

    /**
     * 校验是否免费八达通
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function verifyFreeOctopusCard(Request $request): JsonResponse
    {
        $connector_number = $request->input('connector_number');
        $octopus_card_number = $request->input('octopus_card_number');

        // 默认返回false
        $this->data = false;

        // 校验connector
        if (empty($connector_number) || blank($connector_model = Connector::firstWhere('connector_number', $connector_number))) {
            $this->missingField('Connector');
            return $this->returnJson();
        }
        // 校验octopus_card_number
        if (empty($octopus_card_number)) {
            $this->missingField('Octopus Card Number');
            return $this->returnJson();
        }

        $octopus_card_list = explode(',', $octopus_card_number);

        $is_exist_free_octopus_card = FreeOctopusCard::where('merchant_number', $connector_model->merchant_number)
            ->where('site_number', $connector_model->site_number)
            ->whereIn('octopus_card_number', $octopus_card_list)
            ->where('is_enable', 1)
            ->where('gmt_activate', '<=', now())
            // 过期时间未设置或者未到
            ->where(function ($query) {
                $query->whereNull('gmt_expiry')
                    ->orWhere('gmt_expiry', '>=', now());
            })
            ->exists();

        $this->data = $is_exist_free_octopus_card;

        return $this->returnJson();
    }

    /**
     * 校验充电枪设置token
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function verifyConnectorSettingToken(Request $request): JsonResponse
    {
        $connector_number = $request->input('connector_number');
        $setting_token = $request->input('setting_token');

        // 校验connector
        if (empty($connector_number) || !filled($connector_model = Connector::firstWhere('connector_number', $connector_number))) {
            $this->missingField('Connector');
            return $this->returnJson();
        }

        // 校验充电枪设置token
        $this->data = $setting_token == $connector_model->setting_token;

        return $this->returnJson();
    }

    public function getOctopusDataExchangeDownloadList(Request $request): JsonResponse
    {
        // 返回rwl磁盘下download文件夹下所有文件，给所有文件生成地址， 返回地址字符串，以 ',' 分隔
        $file_list = Storage::disk('rwl')->files('download');
        $file_url_list = [];
        foreach ($file_list as $file) {
            $file_url_list[] = Storage::disk('rwl')->url($file);
        }

        $this->data = implode(',', $file_url_list);

        return $this->returnJson();
    }

    public function getUserChargeRecordBill(Request $request): JsonResponse
    {
        $kiosk_number = $request->kiosk_number;
        $user_id = $request->user_id;
        $selected_user_charge_record_bill_item_number_list = $request->selected_user_charge_record_bill_item_number_list;
        if (filled($selected_user_charge_record_bill_item_number_list) && is_string($selected_user_charge_record_bill_item_number_list)) {
            $selected_user_charge_record_bill_item_number_list = json_decode($selected_user_charge_record_bill_item_number_list, true) ?: [];
            $is_auto_selected = false;
        } else {
            $is_auto_selected = true;
        }

        if (blank($kiosk_number) || blank($kiosk = Kiosk::firstWhere('kiosk_number', $kiosk_number))) {
            $this->missingField('kiosk_number');
            return $this->returnJson();
        }
        if (blank($user_id) || blank($user = AppUser::find($user_id))) {
            $this->missingField('user_id');
            return $this->returnJson();
        }

        $this->data['total_amount'] = 0;
        $this->data['user_charge_record_bill_item_list'] = [];
        $this->data['selected_user_charge_record_bill_item_number_list'] = [];
        $this->data['user'] = [
            'user_id' => $user->user_id,
            'nickname' => $user->nickname,
            'avatar_url' => existsImage('avatar', $user->avatar_url),
        ];

        $charge_payment_record_number_list = [];

        $merchant_number = $kiosk->site?->merchant_number;
        $bill_condition = function ($query) {
            $query->select('charge_payment_record_number', 'charge_record_number', 'total_amount')
                ->where('payment_status', PaymentStatusEnum::Pending);
        };
        // 查询同商户下所有未付款的充电记录
        $charge_record_list = ChargeRecord::with(['chargePaymentRecord' => $bill_condition])
            ->whereHas('chargePaymentRecord', $bill_condition)
            ->where('merchant_number', $merchant_number)
            ->where('user_id', $user_id)
            ->get();
        foreach ($charge_record_list as $charge_record) {
            foreach ($charge_record->chargePaymentRecord as $charge_payment_record) {
                // 筛选已选中的充电记录
                if ($is_auto_selected || (!$is_auto_selected && in_array($charge_payment_record->charge_payment_record_number, $selected_user_charge_record_bill_item_number_list))) {
                    $charge_payment_record_number_list[] = $charge_payment_record->charge_payment_record_number;
                    $this->data['total_amount'] += $charge_payment_record->total_amount;
                }

                $charge_record_number_array = explode('-', $charge_record->charge_record_number);
                $this->data['user_charge_record_bill_item_list'][] = [
                    'user_charge_record_bill_item_number' => $charge_payment_record->charge_payment_record_number,
                    'bill_number' => end($charge_record_number_array) ?? null,
                    'gmt_start' => $charge_record->gmt_power_on ?? $charge_record->gmt_start,
                    'amount' => $charge_payment_record->total_amount,
                ];
            }
        }
        $this->data['selected_user_charge_record_bill_item_number_list'] = $charge_payment_record_number_list;
        $this->data['is_enable_cms_calculate_user_charge_record_bill_total_amount'] = $kiosk->kioskSetting?->is_enable_cms_calculate_user_charge_record_bill_total_amount ?? false;

        return $this->returnJson();
    }

    public function getUser(Request $request): JsonResponse
    {
        $user_id = $request->user_id;
        $telephone = $request->telephone;

        if (blank($user_id) && blank($telephone)) {
            $this->missingField('user_id|telephone');
            return $this->returnJson();
        }

        $user = AppUser::when(filled($user_id), function ($query) use ($user_id) {
            $query->where('user_id', $user_id);
        })
            ->when(filled($telephone), function ($query) use ($telephone) {
                $query->where('telephone', $telephone);
            })
            ->first();

        if (blank($user)) {
            $this->data = null;
            return $this->returnJson();
        }

        $this->data = [
            'user_id' => $user->user_id,
            'nickname' => $user->nickname,
            'avatar_url' => existsImage('avatar', $user->avatar_url),
        ];

        return $this->returnJson();
    }

    public function bindChargeRecordMemberCardGroupWithOctopusCard(Request $request): JsonResponse
    {
        $octopus_card_number = $request->octopus_card_number;
        $charge_record_number = $request->charge_record_number;

        $charge_record = ChargeRecord::firstWhere('charge_record_number', $charge_record_number);
        if (blank($charge_record)) {
            $this->notFoundData('charge_record_number');
            return $this->returnJson();
        }
        // 只有预付才需要获取会员卡回填
        if ($charge_record->charge_tariff_scheme !== ChargeTariffScheme::PrePaid) {
            return $this->returnJson();
        }

        $member_card = self::getMemberCardByOctopusCardNumber($octopus_card_number, $charge_record->site_number, $octopus_card_number);
        if (blank($member_card)) {
            $this->data = null;
            return $this->returnJson();
        }

        $charge_record->member_card_id = $member_card->member_card_id;
        $charge_record->member_name = $member_card->member_name;
        if (blank($charge_record->user_id)) {
            $charge_record->user_id = $member_card->user_id;
        }
        $member_card_group = null;
        if (filled($member_card->member_card_group_id)) {
            $member_card_group = MemberCardGroup::find($member_card->member_card_group_id);
            if (filled($member_card_group)) {
                $charge_record->member_card_group_id = $member_card_group->member_card_group_id;
                $charge_record->member_card_group_name = $member_card_group->name_json;
                $member_card_group->name_json = json($member_card_group->name_json);
            }
        }
        $charge_record->save();

        $this->data =  $member_card_group;

        return $this->returnJson();
    }

    public function bindChargeRecordUserGroupByUserCredential(Request $request): JsonResponse
    {
        $charge_record_number = $request->charge_record_number; // 充电记录编号
        $user_credential_type = $request->user_credential_type; // 用户凭证类型
        $user_credential_number = $request->user_credential_number; // 用户凭证编号

        if (blank($user_credential_number)) {
            $this->missingField('user_credential_number');
            return $this->returnJson();
        }
        $charge_record = ChargeRecord::firstWhere('charge_record_number', $charge_record_number);
        if (blank($charge_record)) {
            $this->notFoundData('charge_record_number');
            return $this->returnJson();
        }
        // 只有预付才需要获取用户凭证回填
        if ($charge_record->charge_tariff_scheme !== ChargeTariffScheme::PrePaid) {
            return $this->returnJson();
        }

        // 查询用户凭证
        $user_credential = UserCredential::where('credential_type', $user_credential_type)
            // 不同凭证类型分情况查询
            ->when(UserCredentialType::Octopus, function ($query) use ($user_credential_number) {
                // 八达通拆分AB卡查询
                $user_credential_number_list = explode(',', $user_credential_number);
                $query->whereIn('credential_number', $user_credential_number_list);
            }, function ($query) use ($user_credential_number) {
                // 默认直接查询
                $query->where('credential_number', $user_credential_number);
            })
            ->where('merchant_number', $charge_record->merchant_number)
            ->where('is_enable', 1)
            ->first();
        // 如果用户凭证不存在，则返回null
        if (blank($user_credential)) {
            return $this->returnJson();
        }

        // 回填充电记录的用户凭证
        $charge_record->user_id = $user_credential->user_id;
        $charge_record->user_credential_type = $user_credential->credential_type;
        $charge_record->user_credential_number = $user_credential->credential_number;

        // 查询用户组
        $user_group = null;
        // 查询用户
        $user = AppUser::find($user_credential->user_id);
        // 如果用户组存在且启用，则回填充电记录的用户组
        if (filled($user?->userGroup) && $user->userGroup->is_enable) {
            $charge_record->user_group_id = $user->userGroup->user_group_id;
            $charge_record->user_group_name = $user->userGroup->name_json;
            $user->userGroup->name_json = json($user->userGroup->name_json);
            $user_group = $user->userGroup;
        }
        $charge_record->save();

        $this->data = $user_group;

        return $this->returnJson();
    }

    public function updateKioskOctopusDevice(Request $request): JsonResponse
    {
        $kiosk_number = $request->kiosk_number;
        $octopus_device_number = $request->octopus_device_number;

        $this->data = false;
        if (blank($kiosk_number) || blank($kiosk = Kiosk::firstWhere('kiosk_number', $kiosk_number))) {
            $this->missingField('kiosk_number');
            return $this->returnJson();
        }
        $kiosk->octopus_device_number = $octopus_device_number;
        $this->data = $kiosk->save();

        return $this->returnJson();
    }

    /**
     * 获取打印历史收据充电记录列表
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function listPrintHistoryReceiptChargeRecord(Request $request): JsonResponse
    {
        $kiosk_number = $request->kiosk_number;
        $identity_type = $request->identity_type;
        $identity_number = $request->identity_number;

        if (blank($kiosk_number)) {
            $this->missingField('kiosk_number');
            return $this->returnJson();
        }
        if (blank($identity_type)) {
            $this->missingField('identity_type');
            return $this->returnJson();
        }
        if (blank($identity_number)) {
            $this->missingField('identity_number');
            return $this->returnJson();
        }
        $kiosk = Kiosk::firstWhere('kiosk_number', $kiosk_number);
        if (blank($kiosk)) {
            $this->notFoundData('kiosk_number');
            return $this->returnJson();
        }
        $kiosk_setting = $kiosk->kioskSetting;
        if (blank($kiosk_setting)) {
            $this->notFoundData('kiosk_setting');
            return $this->returnJson();
        }
        $site_number = $kiosk->site_number;
        $print_history_receipt_query_limit = $kiosk_setting->print_history_receipt_query_limit ?: 0; // 查询条数
        $print_history_receipt_query_validity_period = $kiosk_setting->print_history_receipt_query_validity_period ?: 0; // 打印历史收据查询有效期（秒）
        // 如果查询条数为0，则返回null
        if ($print_history_receipt_query_limit <= 0) {
            return $this->returnJson();
        }
        // 查询同场地下的支付记录的print_history_receipt_query_limit条
        $charge_payment_record_list = ChargePaymentRecord::with(relations: ['chargeRecord'])
            ->whereHas('chargeRecord', function ($query) use ($site_number, $identity_type, $identity_number) {
                $query->where('site_number', $site_number)
                    ->where('identity_type', $identity_type)
                    ->when($identity_type === IdentityType::Octopus || $identity_type === IdentityType::OctopusPreAuthorization, function ($query) use ($identity_number) {
                        $octopus_card_list = explode(',', $identity_number);
                        $query->whereIn('identity_number', $octopus_card_list);
                    }, function ($query) use ($identity_number) {
                        $query->where('identity_number', $identity_number);
                    })
                    ->whereNotNull('gmt_unlocked');
            })
            ->where('payment_status', PaymentStatusEnum::Completed)
            ->when($print_history_receipt_query_validity_period > 0, function ($query) use ($print_history_receipt_query_validity_period) {
                $query->where('gmt_create', '>=', now()->subSeconds($print_history_receipt_query_validity_period));
            })
            ->orderBy('gmt_create', 'desc')
            ->limit($print_history_receipt_query_limit)
            ->get();
        foreach ($charge_payment_record_list as $charge_payment_record) {
            $charge_record = $charge_payment_record->chargeRecord->toArray();
            $charge_payment_record = $charge_payment_record->toArray();
            unset($charge_payment_record['charge_record']);
            $charge_record['charge_payment_record_list'][] = $charge_payment_record;
            $charge_record['merchant_name'] = json($charge_record['merchant_name']);
            $charge_record['member_card_group_name'] = json($charge_record['member_card_group_name']);
            $charge_record['site_name'] = json($charge_record['site_name']);
            $charge_record['zone_name'] = json($charge_record['zone_name']);
            $this->data[] = $charge_record;
        }

        return $this->returnJson();
    }

    /**
     * 计数充电支付记录打印收据
     *
     * @param Request $request
     * @return JsonResponse
     * @Description
     * @example
     * @date 2025-03-25
     */
    public function countChargePaymentRecordPrintReceipt(Request $request): JsonResponse
    {
        $charge_payment_record_number = $request->charge_payment_record_number;
        $this->data = false;
        if (blank($charge_payment_record_number)) {
            $this->missingField('charge_payment_record_number');
            return $this->returnJson();
        }
        $charge_payment_record = ChargePaymentRecord::firstWhere('charge_payment_record_number', $charge_payment_record_number);
        if (blank($charge_payment_record)) {
            $this->notFoundData('charge_payment_record_number');
            return $this->returnJson();
        }
        $kiosk = $charge_payment_record->kiosk;
        if (blank($kiosk)) {
            $this->notFoundData('kiosk');
            return $this->returnJson();
        }
        $kiosk_setting = $kiosk->kioskSetting;
        if (blank($kiosk_setting)) {
            $this->notFoundData('kiosk_setting');
            return $this->returnJson();
        }
        $print_history_receipt_print_count_limit = $kiosk_setting->print_history_receipt_print_count_limit; // 打印次数限制
        if (!is_null($print_history_receipt_print_count_limit) && $print_history_receipt_print_count_limit > 0) {
            if ($charge_payment_record->print_receipt_count >= $print_history_receipt_print_count_limit) {
                $this->code = 30001;
                $this->message = __('api.error_print_history_receipt_print_count_limit_reached');
                return $this->returnJson();
            }
        }
        $charge_payment_record->print_receipt_count++;
        $charge_payment_record->gmt_last_print_receipt = now();
        $this->data = $charge_payment_record->save();

        return $this->returnJson();
    }

    /**
     * 请求通知kiosk充电记录
     *
     * @param ChargeRecord $charge_record
     * @return void
     */
    protected function requestNotifyKioskChargeRecord(ChargeRecord $charge_record): void
    {
        // 通知Kiosk充电记录
        if (isset($charge_record->connector->chargePoint->comServer->api_url) && filled($charge_record->connector->chargePoint->comServer->api_url)) {
            RequestNotifyKioskChargeRecordJob::dispatch($charge_record);
        }
    }

    /**
     * 发送邮件及短信
     *
     * @param ChargeRecord $charge_record
     * @return void
     */
    protected function sendMailAndSMS(ChargeRecord $charge_record): void
    {
        // 存在邮箱发送邮件
        if (!empty($charge_record->reminder_email)) {
            $send_content = $this->formatChargeRecordReminderSendMessage($charge_record, '<br>');
            $send_title = 'EVPro Charge';

            // 将发送邮件任务分发给队列
            SendEmailJob::dispatch($charge_record->reminder_email, $send_title, $send_content, $charge_record);
        }

        // 存在电话发送短信
        if (!empty($charge_record->reminder_telephone)) {
            // 发送短信，如果发送失败保存log
            $send_content = $this->formatChargeRecordReminderSendMessage($charge_record, PHP_EOL);

            // 将发送短信任务分发给队列
            SendSMSJob::dispatch($charge_record->reminder_telephone, $send_content, $charge_record);
        }
    }

    /**
     * 发送邮件及短信
     *
     * @param ChargeRecord $charge_record
     * @return void
     */
    protected function sendChargeRecordLMSClosedMailAndSMS(ChargeRecord $charge_record): void
    {
        // 存在邮箱发送邮件
        if (!empty($charge_record->reminder_email)) {
            $send_content = $this->formatChargeRecordLMSMailReminderSendMessage($charge_record, '<br>');
            $send_title = 'EVPro Charge';

            // 将发送邮件任务分发给队列
            SendEmailJob::dispatch($charge_record->reminder_email, $send_title, $send_content, $charge_record);
        }

        // 存在电话发送短信
        if (!empty($charge_record->reminder_telephone)) {
            // 发送短信，如果发送失败保存log
            $send_content = $this->formatChargeRecordLMSMailReminderSendMessage($charge_record, PHP_EOL);

            // 将发送短信任务分发给队列
            SendSMSJob::dispatch($charge_record->reminder_telephone, $send_content, $charge_record);
        }
    }

    /**
     * 格式化信息发送内容
     *
     * @param ChargeRecord $charge_record
     * @param $CRLF
     * @return string
     */
    protected function formatChargeRecordReminderSendMessage(ChargeRecord $charge_record, $CRLF): string
    {
        // 充电记录
        // 解锁码
        $identity_number = '';
        // 隊列位置
        $queue_position = '';

        // 支付
        // 收據號碼 = 扣除金額 = 要求充电数量 = 充电完成时间 = 付款方式 = 付款信息
        $receipt_number = $amount_deducted = $charging_value_requested = $charging_completed_time = $payment_method = $payment_info = '';

        // 如果身份类型为解锁码且解锁码不为空
        if ($charge_record->identity_type == IdentityType::UnplugCode && filled($charge_record->identity_number)) {
            $identity_number = $CRLF . 'Unlock Code 解鎖密碼: ' . ($charge_record->identity_number ?? '') .
                $CRLF . '請記住解鎖密碼，用於充電完成後拔除充需電纜' .
                $CRLF . 'Please keep the unlock code for unplug charging cable after charging completed';
        }

        // 如果隊列位置不為空則顯示隊列信息
        if (filled($connector = $charge_record->connector) && filled($connector->queue_position)) {
            $queue_position = $CRLF . 'Queue Position 隊列位置: ' . ($connector->queue_position ?? '');
        }

        if (filled($charge_payment_record = $charge_record->chargePaymentRecord()->first())) {
            // 收據號碼
            $receipt_number = $CRLF . 'Receipt no. 收據號碼: ';
            switch ($charge_payment_record->payment_method) {
                case PaymentMethod::Octopus:
                    $receipt_number .= $charge_payment_record->octopus_receipt_number;
                    break;
                default:
                    $receipt_number .= $charge_payment_record->pos_receipt_number;
                    break;
            }

            // 扣除金額
            $amount_deducted = $CRLF . 'Amount deducted 扣除金額: HK$' . (($charge_payment_record->charge_value_amount ?? 0) / 100);

            // 是否有闲置罚款
            if ($charge_payment_record->idling_penalty_amount > 0) {
                $amount_deducted = $CRLF . 'Idling penalty 閒置罰款: HK$' . ($charge_payment_record->idling_penalty_amount / 100) .
                    $CRLF . 'Total Amount 合共金額: HK$' . ((($charge_payment_record->idling_penalty_amount ?? 0) / 100) + (($charge_payment_record->charge_value_amount ?? 0) / 100));
            }

            // 付款方式
            $payment_method = $CRLF . 'Payment Method 付款方式: ' . (PaymentMethod::getDescription($charge_payment_record->payment_method) ?? '');

            // 判断付款方式
            switch ($charge_payment_record->payment_method) {
                case PaymentMethod::Octopus:
                    $payment_info = $CRLF . 'Octopus no. 八達通卡號: ' . ($charge_payment_record->octopus_card_number ?? '') .
                        $CRLF . 'Device no. 八達通機號: ' . ($charge_payment_record->octopus_device_number ?? '');
                    if ($charge_payment_record->octopus_card_type != '1') {
                        $payment_info .= $CRLF . 'Remaining Value 餘額: HK$' . number_format(($charge_payment_record->octopus_balance ?? 0) / 100, 2);
                    }
                    break;
                case PaymentMethod::Alipay:
                    $payment_info = $CRLF . 'AliPay Acc. 支付寶帳號: ' . ($charge_payment_record->pos_card_number ?? '') .
                        $CRLF . 'Trans. ID 交易編號: ' . ($charge_payment_record->pos_transaction_id ?? '') .
                        $CRLF . 'Ref no. 參考編號: ' . ($charge_payment_record->pos_reference_id ?? '');
                    break;
                case PaymentMethod::MasterCard:
                case PaymentMethod::Visa:
                    $payment_info = $CRLF . 'Credit Card no. 信用卡號: ' . ($charge_payment_record->pos_card_number ?? '') .
                        $CRLF . 'Trace no. 追蹤編號: ' . ($charge_payment_record->pos_trace_no ?? '') .
                        $CRLF . 'Trans. ID 交易編號: ' . ($charge_payment_record->pos_transaction_id ?? '') .
                        $CRLF . 'Ref no. 參考編號: ' . ($charge_payment_record->pos_reference_id ?? '');
                    break;
                default:
                    break;
            }
        }

        // 收费方案
        switch ($charge_record->charge_tariff_scheme) {
            case ChargeTariffScheme::PrePaid:
                // 判断充电类型
                switch ($charge_record->charge_value_type) {
                    case ChargeValueType::Time:
                        $charging_value_requested = $CRLF . 'Charging time requested 要求充電時間: ' . (round(($charge_record->pre_paid_charge_value ?? 0) / 60)) . '分';
                        $charging_completed_time = $CRLF . 'Charging completed time 充電完成時間: ' . date('Y/m/d H:i', strtotime(($charge_record->gmt_power_on ?? $charge_record->gmt_start)) + $charge_record->pre_paid_charge_value);
                        break;
                    case ChargeValueType::Energy:
                        $charging_value_requested = $CRLF . 'Charging energy requested 要求充電電量: ' . (round(($charge_record->pre_paid_charge_value ?? 0) / 1000, 3)) . '千瓦時';
                        break;
                }
                break;
            case ChargeTariffScheme::PostPaid:
                // 判断充电类型
                switch ($charge_record->charge_value_type) {
                    case ChargeValueType::Time:
                        $charging_value_requested = $CRLF . 'Charged time 充電時間: ' . (round(($charge_record->charged_time ?? 0) / 60)) . '分';
                        break;
                    case ChargeValueType::Energy:
                        $charging_value_requested = $CRLF . 'Charged energy 充電電量: ' . (round(($charge_record->charged_energy ?? 0) / 1000, 3)) . '千瓦時';
                        break;
                }
        }

        return 'Date/Time 日期/時間: ' . date('Y/m/d H:i', strtotime($charge_record->gmt_power_on ?? $charge_record->gmt_start)) .
            $CRLF . 'EV Charger no. 電動車充電編號: ' . ($charge_record->charge_point_name ?? '') .
            $charging_value_requested .
            $charging_completed_time .
            $queue_position .
            $receipt_number .
            $amount_deducted .
            $payment_method .
            $payment_info .
            $identity_number;
    }

    /**
     * 格式化信息发送内容
     *
     * @param ChargeRecord $charge_record
     * @param $CRLF
     * @return string
     */
    protected function formatChargeRecordLMSClosedMailReminderSendMessage(ChargeRecord $charge_record, $CRLF): string
    {
        return '充電服務意外中斷。如有查詢，請致電 XXXX XXXX 聯絡技術支援。' . $CRLF . ' Charging service suspended unexpectedly. Please contact technical support at XXXX XXXX for enquiry.';
    }

    /**
     * 序列化充电记录中的json字段
     *
     * @param ChargeRecord $model
     * @return ChargeRecord
     */
    protected function chargeRecordArrayToJson(ChargeRecord $model): ChargeRecord
    {
        $model->merchant_name = self::getValueFromLanguageArray($model->merchant_name);
        $model->member_card_group_name = self::getValueFromLanguageArray($model->member_card_group_name);
        // $model->site_name = self::getValueFromLanguageArray($model->site_name);
        $model->site_name = json($model->site_name);
        $model->zone_name = self::getValueFromLanguageArray($model->zone_name);

        return $model;
    }

    /**
     * 序列化充电支付记录中的json字段
     *
     * @param ChargePaymentRecord $model
     * @return ChargePaymentRecord
     */
    protected function chargePaymentRecordArrayToJson(ChargePaymentRecord $model): ChargePaymentRecord
    {
        if ($model->relationLoaded('chargePaymentRecordCalculation') && filled($charge_payment_record_calculation = $model->chargePaymentRecordCalculation)) {
            $model->charge_value_calculation_json = filled($charge_payment_record_calculation->charge_value_calculation_json) ? json_encode($charge_payment_record_calculation->charge_value_calculation_json) : '';
            $model->idling_penalty_calculation_json = filled($charge_payment_record_calculation->idling_penalty_calculation_json) ? json_encode($charge_payment_record_calculation->idling_penalty_calculation_json) : '';
        }

        return $model;
    }

    /**
     * 序列号充电记录收费规则的json字段
     *
     * @param ChargeRecordTariffRule $model
     * @return ChargeRecordTariffRule
     */
    protected function chargeRecordTariffRuleArrayToJson(ChargeRecordTariffRule $model): ChargeRecordTariffRule
    {

        $model->simple_tariff_table_json = filled($model->simple_tariff_table_json) ? json_encode($model->simple_tariff_table_json) : '';
        $model->member_card_group_simple_tariff_table_item_json = filled($model->member_card_group_simple_tariff_table_item_json) ? json_encode($model->member_card_group_simple_tariff_table_item_json) : '';
        $model->user_group_simple_tariff_table_item_json = filled($model->user_group_simple_tariff_table_item_json) ? json_encode($model->user_group_simple_tariff_table_item_json) : '';
        $model->time_tariff_table_item_json = filled($model->time_tariff_table_item_json) ? json_encode($model->time_tariff_table_item_json) : '';
        $model->member_card_group_time_tariff_table_item_json = filled($model->member_card_group_time_tariff_table_item_json) ? json_encode($model->member_card_group_time_tariff_table_item_json) : '';
        $model->user_group_time_tariff_table_item_json = filled($model->user_group_time_tariff_table_item_json) ? json_encode($model->user_group_time_tariff_table_item_json) : '';
        $model->energy_tariff_table_item_json = filled($model->energy_tariff_table_item_json) ? json_encode($model->energy_tariff_table_item_json) : '';
        $model->member_card_group_energy_tariff_table_item_json = filled($model->member_card_group_energy_tariff_table_item_json) ? json_encode($model->member_card_group_energy_tariff_table_item_json) : '';
        $model->user_group_energy_tariff_table_item_json = filled($model->user_group_energy_tariff_table_item_json) ? json_encode($model->user_group_energy_tariff_table_item_json) : '';
        $model->idling_penalty_tariff_table_item_json = filled($model->idling_penalty_tariff_table_item_json) ? json_encode($model->idling_penalty_tariff_table_item_json) : '';
        $model->member_card_group_idling_penalty_tariff_table_item_json = filled($model->member_card_group_idling_penalty_tariff_table_item_json) ? json_encode($model->member_card_group_idling_penalty_tariff_table_item_json) : '';
        $model->user_group_idling_penalty_tariff_table_item_json = filled($model->user_group_idling_penalty_tariff_table_item_json) ? json_encode($model->user_group_idling_penalty_tariff_table_item_json) : '';
        $model->peak_time_table_item_json = filled($model->peak_time_table_item_json) ? json_encode($model->peak_time_table_item_json) : '';
        $model->public_holiday_json = filled($model->public_holiday_json) ? json_encode($model->public_holiday_json) : '';

        return $model;
    }

    protected static function getMemberCardByOctopusCardNumber(string $octopus_card_number, string $site_number, string $octopus_raw_card_number = ''): ?MemberCard
    {
        $member_card = MemberCard::where('site_number', $site_number)
            ->where('octopus_card_number', $octopus_card_number)
            ->where('is_enable', 1)
            ->first();
        if (blank($member_card)) {
            $octopus_card_number_array = explode(',', $octopus_raw_card_number); // 拆分八达通原始卡号
            if (count($octopus_card_number_array) > 1) {
                $card_a = $octopus_card_number_array[0] ?? null;
                $card_b = $octopus_card_number_array[1] ?? null;
                // 先查找B卡
                if ($octopus_card_number !== $card_b) {
                    $member_card_model_b = MemberCard::where('site_number', $site_number)
                        ->where('octopus_card_number', $card_b)
                        ->where('is_enable', 1)
                        ->first();
                    $member_card = $member_card_model_b;
                }
                // 再查找A卡
                if ((!isset($member_card_model_b) || blank($member_card_model_b)) && $octopus_card_number !== $card_a) {
                    $member_card_model_a = MemberCard::where('site_number', $site_number)
                        ->where('octopus_card_number', $card_a)
                        ->where('is_enable', 1)
                        ->first();
                    $member_card = $member_card_model_a;
                }
            }
        }

        return $member_card;
    }
}

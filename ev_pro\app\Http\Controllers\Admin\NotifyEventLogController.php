<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Foundation\Application;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Admin\Traits\{
    Add,
    Edit,
    Delete
};
use App\Models\Modules\{
    NotifyEventLog,
};


class NotifyEventLogController extends CommonController
{
    // use Add, Edit, Delete;

    protected static string $module_name = 'notifyEventLog'; // 模块名称
    protected NotifyEventLog $model;

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new NotifyEventLog;
    }

    public function showPage(Request $request): View|Application|Factory
    {

        $data = array(
            'module_name' => self::$module_name,
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'event_log_search' => $request->input('event_log_search'),
            'buttonShow' => NotifyEventLog::exists(),
        );

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $length = (int)$request->input('length', 10);
        $event_log_search = $request->input('event_log_search');


        $where = array();

        if ($order == 'event_log_id') {
            $order = 'event_log.target_name';
        }

        if (filled($event_log_search)) {
            $where[] = ['event_log.target_name', 'like', "%$event_log_search%"];
        }

        $data_list = NotifyEventLog::select('notify_event_log.*', 'event_log.target_name as event_log_target_name_init')
            ->leftJoin('event_log', 'notify_event_log.event_log_id', '=', 'event_log.event_log_id')
            ->orderBy($order, $sort)
            ->latest('gmt_create')
            ->where($where)
            ->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $result[] = array(
                'notify_event_log_id' => $data->notify_event_log_id, // 通知事件记录ID
                'event_log_id' => $data->event_log_target_name_init ?? '—/—', // 事件记录ID 的目标名称
                'title' => $data->title ?? '—/—', // 标题
                'description' => $data->description ?? '—/—', // 描述
                'gmt_read' => $data->gmt_read ?? '—/—', // 已读时间
                'read_name' => $data->read_name ?? '—/—', // 已读名称
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
            );
        }
        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result
        );
        return response()->json($json);
    }

    // 清空NotifyEventLog表所有数据
    public function deleteAllData(Request $request): JsonResponse
    {
        // 先清除原错误规则
        NotifyEventLog::truncate();
        return $this->returnJson();
    }

    /**
     * 返回地址栏参数
     *
     * @param Request $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(Request $request): array
    {
        return array(
            'is_page' => 'true',
            'event_log_search' => $request->get('event_log_search'),
        );
    }
}

<?php

namespace App\Models\Modules;

use Illuminate\Database\Eloquent\Model;

class ConnectorSetting extends Model
{
    protected $table = 'connector_setting'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'connector_setting_id'; //定义主键，默认为id
    // 表的唯一number
    public static $table_number = 'connector_setting_number';
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';
    // protected $fillable = [];

    /**
     * 默认值
     *
     * @var array
     */
    protected $attributes = [
        'is_record_charge_record_meter_value' => false, // 是否记录充电记录仪表值
        'is_remote_stop_when_no_charge_record' => false, // 是否在没有充电记录时远程停止充电
        'is_enable_estimate_amount' => false, // 是否启用预估金额
        'is_enable_verify_setting_token' => false, // 是否启用校验设置Token
        'is_enable_member_card_group_tariff_table' => false, // 是否启用会员卡组收费表
        'sort_order' => 0, // 排序
    ];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'gmt_create',
        'gmt_modified',
    ];

    /**
     * 类型转化器
     */
    protected $casts = [
        'is_record_charge_record_meter_value' => 'bool',
        'is_remote_stop_when_no_charge_record' => 'bool',
        'is_display_menu_extra_information' => 'bool',
        'is_display_charging_extra_information' => 'bool',
        'is_enable_estimate_amount' => 'bool',
        'is_enable_verify_setting_token' => 'bool',
        'is_enable_charge_arrears' => 'bool',
        'is_enable_verify_park_sensor' => 'bool',
        'is_enable_member_card_group_tariff_table' => 'bool',
        'is_enable_user_group_tariff_table' => 'bool',
        'is_enable_push_notification' => 'bool',
        'is_allow_points_overdraft' => 'bool',
    ];

    /**
     * 不能被批量赋值的属性
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * 一对一关联 Simple tariff table
     */
    public function simpleTariffTable()
    {
        return $this->hasOne(SimpleTariffTable::class, 'simple_tariff_table_number', 'simple_tariff_table_number');
    }

    /**
     * 一对一关联 Time Tariff Table
     */
    public function timeTariffTable()
    {
        return $this->hasOne(TimeTariffTable::class, 'time_tariff_table_number', 'time_tariff_table_number');
    }

    /**
     * 一对一关联 Energy Tariff Table
     */
    public function energyTariffTable()
    {
        return $this->hasOne(EnergyTariffTable::class, 'energy_tariff_table_number', 'energy_tariff_table_number');
    }

    /**
     * 一对一关联 Idling Penalty Tariff Table
     */
    public function idlingPenaltyTariffTable()
    {
        return $this->hasOne(IdlingPenaltyTariffTable::class, 'idling_penalty_tariff_table_number', 'idling_penalty_tariff_table_number');
    }

    /**
     * 一对一关联 Peak Time Table
     */
    public function peakTimeTable()
    {
        return $this->hasOne(PeakTimeTable::class, 'peak_time_table_number', 'peak_time_table_number');
    }

    /**
     * 一对多关联 Trial Charge Rule
     */
    public function trialChargeRule()
    {
        return $this->hasMany(ConnectorSettingTrialChargeRule::class, 'connector_setting_number', 'connector_setting_number');
    }

    /**
     * 一对多关联 connector
     */
    public function connector()
    {
        return $this->hasMany(Connector::class, 'connector_setting_number', 'connector_setting_number');
    }

    /**
     * 一对多关联description
     */
    public function description()
    {
        return $this->hasMany(ConnectorSettingDescription::class, 'connector_setting_number', 'connector_setting_number');
    }
}

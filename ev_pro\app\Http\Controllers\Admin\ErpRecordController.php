<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use App\Models\Modules\ErpRecord;
use App\Services\ErpService;
use Exception;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Foundation\Application;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ErpRecordController extends CommonController
{
    protected static string $module_name = 'erpRecord'; // 模块名称

    /**
     * 初始页
     *
     * @param Request $request
     * @return View|Application|Factory
     */
    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'gmt_data_search' => $request->get('gmt_data_search'),
        );


        return view("pages.{$data['module_name']}.list", $data);
    }

    /**
     * 根据分隔符获取最后一个字符串
     * @param $delimiter
     * @param $str
     * @return string|null
     */
    function getLastStr($delimiter, $str): ?string
    {
        // 分隔符为空或者为空字符串，直接返回null
        if (empty($delimiter) || empty($str)) return null;
        $strArray = explode($delimiter, $str);
        return array_pop($strArray);
    }

    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'sort_order');
        $sort = $request->input('sort', 'asc');
        $length = (int)$request->input('length', 10);
        $gmt_data_search = $request->input('gmt_data_search');
        $where = array();
        if (filled($gmt_data_search)) {
            $gmt_data_search_array = explode(' - ', $gmt_data_search);
            // 判断是否是时间类型且为开始时间结束时间类型
            if (count($gmt_data_search_array) == 2 && strtotime($gmt_data_search_array[0]) && strtotime($gmt_data_search_array[1]) && (strtotime($gmt_data_search_array[0]) <= strtotime($gmt_data_search_array[1]))) {
                $where[] = ['gmt_data', '>=', "$gmt_data_search_array[0]"];
                $where[] = ['gmt_data', '<=', "$gmt_data_search_array[1]"];
            }
        }


        $data_list = ErpRecord::where($where)
            ->orderBy($order, $sort)
            ->latest('gmt_modified')
            ->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $result[] = array(
                'erp_record_id' => $data->erp_record_id, // 设置ID
                'data_json' => $data->data_json ?? '—/—', // 數據Json
                'gmt_data' => $data->gmt_data ?? '—/—', // 數據時間
                'gmt_request' => $data->gmt_request ?? '—/—', // 請求時間
                'response_json' => $data->response_json ?? '—/—', // 響應Json
                'exception' => $data->exception ?? '—/—', // 異常
                'is_request_success' => $data->is_request_success, // 是否請求成功
                'gmt_next_retry_request' => $data->gmt_next_retry_request ?? '—/—', // 下次重试请求时间
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 創建時間
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改時間
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result
        );
        return response()->json($json);
    }

    /**
     * 返回地址栏参数
     *
     * @param Request|null $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => 'true',
            'gmt_data_search' => $request->get('gmt_data_search'),
        );
    }

    public function manualUploadDailyTransactionRecord(Request $request): JsonResponse
    {
        // 临时修改配置，防止PHP脚本超时
        set_time_limit(0);
        $request->validate(self::rules($request), [], self::attributes());
        // 获取开始时间和结束时间
        $gmt_data_request = $request->gmt_data;
        $gmt_data_array = $this->getRangeDateTimeArray($gmt_data_request);
        $start_time = $gmt_data_array[0];
        $end_time = $gmt_data_array[1];

        // 开始和结束不能为空
        if (blank($start_time) || blank($end_time)) {
            $this->code = 40001;
            Log::channel('erpRecord')->info("Manual Generate And Post ERP Record: Start Time Or End Time Is Empty");
            return $this->returnJson();
        }
        // 开始时间不能大于结束时间
        if (strtotime($start_time) > strtotime($end_time)) {
            $this->code = 40001;
            Log::channel('erpRecord')->info("Manual Generate And Post ERP Record: Start Time Is Greater Than End Time");
            return $this->returnJson();
        }


        // 请求链接
        $erp_base_url = env('ERP_BASE_URL');
        // 请求token client id
        $erp_client_id = env('ERP_CLIENT_ID');
        // 请求token client secret
        $erp_client_secret = env('ERP_CLIENT_SECRET');
        // 场地编号
        $erp_site_code = env('ERP_SITE_CODE');
        // venue
        $erp_venue = env('ERP_VENUE');
        // event code
        $erp_event_code = env('ERP_EVENT_CODE');
        // 如果没有配置请求链接，不执行直接返回
        if (blank($erp_base_url) || blank($erp_client_id) || blank($erp_client_secret) || blank($erp_site_code) || blank($erp_venue) || blank($erp_event_code)) {
            $this->code = 40001;
            Log::channel('erpRecord')->info("Manual Generate And Post ERP Record: 配置参数错误
            ERP_BASE_URL: $erp_base_url; ERP_CLIENT_ID: $erp_client_id; ERP_CLIENT_SECRET: $erp_client_secret; ERP_SITE_CODE: $erp_site_code; ERP_VENUE: $erp_venue; ERP_EVENT_CODE: $erp_event_code");
            return $this->returnJson();
        }

        $success_count = 0;
        $error_count = 0;
        // 循环开始到结束时间，按照每天拼接23:59:59进行请求，获取json数据
        for ($i = strtotime($start_time); $i <= strtotime($end_time); $i += 86400) {
            $gmt_data = date('Y-m-d 23:59:59', $i);
            // 转换成秒
            $data_seconds = strtotime($gmt_data);
            $result = $this->getErpDataJson($gmt_data, $data_seconds);
            if ($result) {
                $success_count++;
            } else {
                $error_count++;
            }
        }

        $this->data = array(
            'success_count' => $success_count,
            'error_count' => $error_count,
        );
        return $this->returnJson();
    }

    protected function getErpDataJson($gmt_data, $data_seconds) {
        /*// 获取指定日期最新一条ERP记录, 如果没有则创建一条新的记录
        $erp_record = ErpRecord::whereDate('gmt_data', $gmt_data)
            ->orderby('gmt_data', 'desc')
            ->firstOrNew();*/
            $erp_record = new ErpRecord();
            // 请求json数据
            $data_json = null;
            // 异常信息
            $exception = null;

            $erp_service = new ErpService();
            try {
                // 获取json数据
                $data_json = $erp_service->getErpDataJson($data_seconds);
            } catch (Exception $e) {
                $exception = $e->getMessage();
            }
            // 获取请求数据的结果
            $curl_result = $erp_service->sendDataWithRetry($data_json, $exception);

            $erp_record->fill([
                'gmt_data' => $gmt_data,
                'data_json' => json_encode($data_json, JSON_PRESERVE_ZERO_FRACTION),
                'gmt_request' => $curl_result['gmt_request'],
                'response_json' => $curl_result['response_json'],
                'exception' => $curl_result['exception'],
                'is_request_success' => $curl_result['is_request_success'],
                'gmt_next_retry_request' => $curl_result['gmt_next_retry_request'],
            ]);
            return $erp_record->save();
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @return array
     */
    protected static function rules(?Request $request): array
    {
        $module_name = self::$module_name;
        $rules = array(
            'gmt_data' => 'required',
        );
        return $rules;
    }

    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'gmt_data' => __("$module_name.gmt_data"),
        ];
    }
}

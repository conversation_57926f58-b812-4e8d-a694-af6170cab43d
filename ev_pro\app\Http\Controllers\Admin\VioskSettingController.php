<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Contracts\View\{
    Factory,
    View
};
use Illuminate\Database\Eloquent\Model;
use Illuminate\Validation\Rule;
use Illuminate\Foundation\Application;
use Illuminate\Http\{
    JsonResponse,
    RedirectResponse
};
use Illuminate\Http\Request;
use App\Http\Controllers\Admin\Traits\{
    Edit,
};
use App\Models\Modules\{
    VioskSetting,
    VioskSettingDescription,
    VioskSettingScreenTime,
    Site,
};
use App\Enums\{
    VioskSettingScreenTimeKeyEnum,
};

class VioskSettingController extends CommonController
{
    use Edit;

    protected static string $module_name = 'vioskSetting'; // 模块名称
    protected static bool $module_check_site = true; // 标记该模块校验场地

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new VioskSetting;
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'add_url' => action([self::class, 'add']),
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'site_search' => $request->get('site_search'),
            'name_search' => $request->get('name_search'),
            'default_language_code_search' => $request->get('default_language_code_search'),
            'viosk_setting_number_search' => $request->get('viosk_setting_number_search'),
            'site_list' => $this->getSiteOptionList(),
        );
        $config_languages = config('languages');
        $config_languages['en_US_zh_HK'] = __('common.en_US_zh_HK');
        $data['config_languages'] = $config_languages;
        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        // dataTable字段
        $draw = (int)$request->input('draw', 1);
        $order = $request->input('order', 'sort_order');
        $sort = $request->input('sort', 'asc');
        $length = (int)$request->input('length', 10);
        $name_search = $request->input('name_search');
        $site_search = $request->input('site_search');
        $default_language_code_search = $request->input('default_language_code_search');
        $viosk_setting_number_search = $request->input('viosk_setting_number_search');

        if ($order == 'site_name') $order = 'site.name_json';
        $data_list = VioskSetting::query()
            ->select('viosk_setting.*', 'site.name_json as site_name_json_init')
            ->leftJoin('site', 'viosk_setting.site_number', '=', 'site.site_number')
            ->when(filled($name_search), fn($query) => $query->where('viosk_setting.name', 'like', "%$name_search%"))
            ->when(filled($site_search), fn($query) => $query->where('viosk_setting.site_number', $site_search))
            ->when(filled($default_language_code_search), fn($query) => $query->where('default_language_code', '=', $default_language_code_search))
            ->when(filled($viosk_setting_number_search), fn($query) => $query->where('viosk_setting.viosk_setting_number', 'like', "%$viosk_setting_number_search%"))
            ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('viosk_setting.site_number', auth()->user()->site_number_list))
            ->orderBy($order, $sort)
            ->orderBy('gmt_modified', 'desc')
            ->paginate($length, ['*'], 'start');

        // 单独加入双语语言，只有viosk需要显示双语，cms不需要双语
        $config_languages = config('languages');
        $config_languages['en_US_zh_HK'] = __('common.en_US_zh_HK');
        $result = array();
        foreach ($data_list as $data) {
            $site_name = $this->getValueFromLanguageArray($data->site_name_json_init) ?? '—/—';
            $result[] = array(
                'viosk_setting_id' => $data->viosk_setting_id, // Viosk设置ID
                'viosk_setting_number' => $data->viosk_setting_number, // Viosk设置编号
                'site_name' => $site_name, // 场地名称
                'name' => $data->name, // 名称
                'default_language_code' => $config_languages[$data->default_language_code] ?? '—/—', // 默认语言
                'is_display_idle_remain_time' => $data->is_display_idle_remain_time, // 是否显示闲置剩余时间
                'heartbeat_interval' => $data->heartbeat_interval . __('common.unit_s'), // 心跳间隔
                'self_check_interval' => $data->self_check_interval . __('common.unit_s'), // 自检间隔
                'charge_point_heartbeat_interval' => $data->charge_point_heartbeat_interval . __('common.unit_ms'), // 自检间隔
                'pos_check_health_interval' => $data->pos_check_health_interval . __('common.unit_s'), // POS检测健康间隔
                'sync_to_cloud_interval' => $data->sync_to_cloud_interval ? $data->sync_to_cloud_interval . __('common.unit_s') : '—/—', // 同步至云端间隔
                'check_charge_pre_authorization_interval' => $data->check_charge_pre_authorization_interval ? $data->check_charge_pre_authorization_interval . __('common.unit_s') : '—/—', // 检查充电预授权间隔
                'charge_pre_authorization_retry_count' => $data->charge_pre_authorization_retry_count ?? '—/—', // 充电预授权重试次数
                'is_enable_octopus_cancel' => $data->is_enable_octopus_cancel, // 是否启用八达通取消
                'sort_order' => $data->sort_order, // 排序
                'remark' => $data->remark ?? '—/—', // 备注
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );
        return response()->json($json);
    }

    protected function getForm(Request $request, array $data): View|Application|Factory
    {
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'], self::getUrlParams($request)
        );

        if (blank($data['model']->viosk_setting_id)) $data['model']->viosk_setting_number = $request->old('viosk_setting_number', $data['model']->viosk_setting_number); // Viosk设置编码

        $site_number = $data['model']->site_number;
        if ((isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) && blank($data['model']?->viosk_setting_id)) {
            // 如果是新增，并且是超级管理员或者管理员的商户大于1的情况下就直接获取页面传入的商户编号
            $site_number = $request->old('site_number', $data['model']->site_number);
        } else if (blank($data['model']?->viosk_setting_id) && auth()->user()->site_number_list->count() === 1) {
            // 如果是新增并且管理员商户只有一个就直接赋值
            $site_number = auth()->user()->site_number_list->first();
        }

        $data['model']->site_number = $site_number; // 场地编号
        $site_name = $this->getValueFromLanguageArray(
            Site::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))
                ->firstWhere('site_number', $site_number)?->name_json
        );
        $data['model']->site_name = $site_name; // 场地名称
        $data['model']->name = $request->old('name', $data['model']->name); // 名称
        $data['model']->default_language_code = $request->old('default_language_code', $data['model']->default_language_code); // 默认语言
        $data['model']->is_display_idle_remain_time = $request->old('is_display_idle_remain_time', $data['model']->is_display_idle_remain_time); // 是否显示闲置剩余时间
        $data['model']->heartbeat_interval = $request->old('heartbeat_interval', $data['model']->heartbeat_interval); // 心跳间隔
        $data['model']->self_check_interval = $request->old('self_check_interval', $data['model']->self_check_interval); // 自检间隔
        $data['model']->charge_point_heartbeat_interval = $request->old('charge_point_heartbeat_interval', $data['model']->charge_point_heartbeat_interval); // 充电机心跳间隔
        $data['model']->pos_check_health_interval = $request->old('pos_check_health_interval', $data['model']->pos_check_health_interval); // POS检测健康间隔
        $data['model']->sync_to_cloud_interval = $request->old('sync_to_cloud_interval', $data['model']->sync_to_cloud_interval); // 同步至云端间隔
        $data['model']->check_charge_pre_authorization_interval = $request->old('check_charge_pre_authorization_interval', $data['model']->check_charge_pre_authorization_interval); // 检查充电预授权间隔
        $data['model']->charge_pre_authorization_retry_count = $request->old('charge_pre_authorization_retry_count', $data['model']->charge_pre_authorization_retry_count); // 充电预授权重试次数
        $data['model']->is_enable_octopus_cancel = $request->old('is_enable_octopus_cancel', $data['model']->is_enable_octopus_cancel); // 是否启用八达通取消
        $data['model']->sort_order = $request->old('sort_order', $data['model']->sort_order); // 排序
        $data['model']->remark = $request->old('remark', $data['model']->remark); // 备注

        // 读取系统语言并查询出对应VioskSettingDescription数据
        $data['item'] = array();
        $config_languages = config('languages');
        // 单独加入双语语言，只有viosk需要显示双语，cms不需要双语
        $config_languages['en_US_zh_HK'] = __('common.en_US_zh_HK');
        foreach ($config_languages as $language_code => $language_name) {
            // 有旧数据
            $viosk_setting_description_old_list = $request->old('item');
            if ($viosk_setting_description_old_list && filled($viosk_setting_description_old_list)) {
                $data['item'][$language_code] = array(
                    'language_name' => $language_name,
                    'language_code' => $language_code,
                    'viosk_setting_description_id' => $viosk_setting_description_old_list[$language_code]['viosk_setting_description_id'] ?? null, // ID
                    'main_logo_image_url' => $viosk_setting_description_old_list[$language_code]['main_logo_image_url'] ?? null,
                    'home_page_image_url' => $viosk_setting_description_old_list[$language_code]['home_page_image_url'] ?? null,
                    'main_header_message' => $viosk_setting_description_old_list[$language_code]['main_header_message'] ?? null,
                );
            } else {
                $viosk_setting_description_result = $data['model']->description->where('language_code', $language_code)->first();
                $data['item'][$language_code] = array(
                    'language_name' => $language_name,
                    'language_code' => $language_code,
                    'viosk_setting_description_id' => $viosk_setting_description_result->viosk_setting_description_id ?? '', // ID
                    'main_logo_image_url' => $viosk_setting_description_result->main_logo_image_url ?? null, // 主图标图片URL
                    'home_page_image_url' => $viosk_setting_description_result->home_page_image_url ?? null, // 首页图片URL
                    'main_header_message' => $viosk_setting_description_result->main_header_message ?? '', // 主页头部消息
                );
            }
        }

        // 是否更改屏幕时间
        $is_modified_screen_time = $data['model']->is_modified_screen_time;
        /* 获取当前屏幕时间数据 根据是否更改屏幕时间来判断需要旧数据还是模型里的数据，
        防止编辑时屏幕时间原本有数据，然后清空屏幕时间并且有些字段没过验证的情况下回填数据时把屏幕时间模型里的数据回填了 */
        if (filled($data['model']->viosk_setting_id) || filled($request->input('_viosk_setting_number'))) {
            // 编辑的情况下 和 复制到新增的情况下
            $screen_time_list = $is_modified_screen_time ? $request->old('screen', []) : $data['model']->screenTime;
        } else {
            // 新增的情况下
            $screen_time_list = $request->old('screen', []);
        }
        // 防止在复制到新增的时候因为is_modified_screen_time第一次进去默认为false导致数据无法新增进去，所以如果是复制到新增的话就直接为true，为true就是已修改
        if (filled($request->input('_viosk_setting_number'))) $data['model']->is_modified_screen_time = 1;

        // 获取当前screen time数据
        $data['screen'] = array();

        foreach ($screen_time_list as $screen_time) {
            $name = VioskSettingScreenTimeKeyEnum::getDescription($screen_time['key']);
            if ($request->old('screen')) {
                $data['screen'][] = array(
                    'key' => $screen_time['key'],
                    'name' => $name,
                    'time' => $screen_time['time'],
                );
            } else {
                $data['screen'][] = array(
                    'viosk_setting_screen_time_id' => $screen_time->viosk_setting_screen_time_id,
                    'key' => $screen_time->key,
                    'name' => $name,
                    'time' => $screen_time->time,
                    'gmt_create' => $screen_time->gmt_create,
                    'gmt_modified' => $screen_time->gmt_modified,
                );
            }
        }

        // 屏幕时间枚举
        $data['screen_time_list'] = array();
        foreach (VioskSettingScreenTimeKeyEnum::asSelectArray() as $value => $description) {
            $data['screen_time_list'][] = array(
                'name' => $description,
                'value' => $value,
            );
        }

        $data['image_not_select_path'] = existsImage('icon', 'not_select_image.png');

        $data['language_code_list'] = $data['language_code_value'] = array();
        foreach ($config_languages as $language_code => $language_name) {
            $data['language_code_list'][$language_code] = $language_name;
            $data['language_code_value'][] = $language_code;
        }
        return view("pages.{$data['module_name']}.form", $data);
    }

    public function add(Request $request): Application|View|Factory|RedirectResponse
    {
        $data = array();

        $model = $this->model;

        if ($request->isMethod('post')) {
            return $this->modelValidateAndSave($request, $model);
        }

        // 如果有viosk_setting_number的存在就是复制除了viosk_setting_number和name以外的收费表数据输出到新增页面
        $viosk_setting_number = $request->input('_viosk_setting_number');
        if (filled($viosk_setting_number)) {
            $old_data_model = $model->with(['description', 'screenTime'])
                ->when(!isSuperAdministrator(), fn($query) => $query->whereIn('viosk_setting.site_number', auth()->user()->site_number_list))
                ->where('viosk_setting_number', $viosk_setting_number)
                ->firstOrFail();
            $model = $old_data_model->replicate(['name', 'viosk_setting_number']);

            foreach ($model->description as $description) {
                $description->viosk_setting_description_id = $description->viosk_setting_number = null;
            }
            foreach ($model->screenTime as $screen_time) {
                $screen_time->viosk_setting_screen_time_id = $screen_time->viosk_setting_number = null;
            }
        }

        $data['model'] = $model;

        return $this->getForm($request, $data);
    }

    public function delete(Request $request): JsonResponse
    {
        $module_name = self::$module_name;
        $viosk_setting_number = $request->post('viosk_setting_number');

        $viosk_setting = VioskSetting::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('viosk_setting_number', $viosk_setting_number);
        if (!empty($viosk_setting_number) && filled($viosk_setting)) {
            if (blank($viosk_setting_list = $viosk_setting->viosk()->get())) {
                // 并且删除关联的
                $viosk_setting->description()->delete();
                $viosk_setting->screenTime()->delete();

                // 删除viosk setting
                $viosk_setting->delete();
            } else {
                $viosk_str = '';
                foreach ($viosk_setting_list as $viosk) {
                    $viosk_str .= '<li>' . $viosk->name . '</li>';
                }
                $this->code = 201;
                $this->message = __('common.error_has_binding_unable_to_delete_viosk_setting', [
                    'viosk' => $viosk_str
                ]);
            }
        } else {
            $this->code = 404;
            $this->message = __('common.text_not_found', ['field' => __("$module_name.viosk_setting_number")]);
        }

        return $this->returnJson();
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @param VioskSetting $model
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    protected function modelValidateAndSave(Request $request, VioskSetting $model): RedirectResponse
    {
        $request->validate(self::rules($request, $model), [], self::attributes());
        // 判断是否是新增
        if (blank($model->viosk_setting_id)) {
            $model = $this->model;
            $model->viosk_setting_number = $request->input('viosk_setting_number');
            $site_number = $model->site_number;

            if (isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) {
                // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下就直接获取页面传入的场地编号
                $site_number = $request->input('site_number');
            } else if (auth()->user()->site_number_list->count() === 1) {
                // 如果是新增并且管理员场地只有一个就直接赋值
                $site_number = auth()->user()->site_number_list->first();
            }
            $model->site_number = $site_number;
            $model->merchant_number = Site::firstWhere('site_number', $model->site_number)?->merchant_number;
        }
        $model->name = $request->input('name');
        $model->default_language_code = $request->input('default_language_code');
        $model->is_display_idle_remain_time = $request->input('is_display_idle_remain_time', 0);
        $model->heartbeat_interval = $request->input('heartbeat_interval', 0);
        $model->self_check_interval = $request->input('self_check_interval', 0);
        $model->charge_point_heartbeat_interval = $request->input('charge_point_heartbeat_interval', 0);
        $model->pos_check_health_interval = $request->input('pos_check_health_interval', 0);
        $model->sync_to_cloud_interval = $request->input('sync_to_cloud_interval', 0);
        $model->check_charge_pre_authorization_interval = $request->input('check_charge_pre_authorization_interval', 0);
        $model->charge_pre_authorization_retry_count = $request->input('charge_pre_authorization_retry_count', 0);
        $model->is_enable_octopus_cancel = $request->input('is_enable_octopus_cancel', 0);
        $model->sort_order = $request->input('sort_order', 0);
        $model->remark = $request->input('remark');

        $model->save();

        // 是否更改数据
        $is_modified_screen_time = $request->input('is_modified_screen_time', 0);
        // 获取请求充电枪数据
        $viosk_setting_description_list = $request->input('item', array());
        // 获取请求屏幕时间数据
        $viosk_setting_screen_time_list = $request->input('screen', array());

        // 保存Viosk设置后再保存并关联Viosk设置
        foreach ($viosk_setting_description_list as $viosk_setting_description) {
            if (!isset($viosk_setting_description['viosk_setting_description_id']) ||
                blank($viosk_setting_description_model = VioskSettingDescription::find($viosk_setting_description['viosk_setting_description_id']))) {
                $viosk_setting_description_model = new VioskSettingDescription;
            }
            $viosk_setting_description_model->viosk_setting_number = $model->viosk_setting_number;
            $viosk_setting_description_model->language_code = $viosk_setting_description['language_code'];
            $viosk_setting_description_model->main_header_message = $viosk_setting_description['main_header_message'] ?? null;
            $viosk_setting_description_model->main_logo_image_url = $viosk_setting_description['main_logo_image_url'] ?? null;
            $viosk_setting_description_model->home_page_image_url = $viosk_setting_description['home_page_image_url'] ?? null;

            $viosk_setting_description_model->save();
        }

        // 是否更改数据
        if ($is_modified_screen_time == 1) {
            // 移除该viosk设置下的屏幕时间数据
            $model->screenTime()->delete();
            foreach ($viosk_setting_screen_time_list as $screen_time) {
                // 再次校验key是否为枚举值
                if (isset($screen_time['key']) && VioskSettingScreenTimeKeyEnum::hasValue($screen_time['key'], false)) {
                    $viosk_setting_screen_time_model = new VioskSettingScreenTime;
                    $viosk_setting_screen_time_model->viosk_setting_number = $model->viosk_setting_number;
                    $viosk_setting_screen_time_model->key = $screen_time['key'];
                    $viosk_setting_screen_time_model->time = $screen_time['time'] ?? 0;

                    $viosk_setting_screen_time_model->save();
                }
            }
        }

        return redirect()->action(
            [self::class, 'showPage'], self::getUrlParams($request)
        );
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function rules(?Request $request, ?Model $model): array
    {
        $module_name = self::$module_name;

        $config_languages = config('languages');
        // 单独加入双语语言，只有viosk需要显示双语，cms不需要双语
        $config_languages['en_US_zh_HK'] = __('common.en_US_zh_HK');

        $config_languages_keys = array_keys($config_languages);
        $rules = array(
            'name' => 'required|string|max:45',
            'default_language_code' => 'required|string|max:20|in:' . implode(",", $config_languages_keys),
            'is_display_idle_remain_time' => 'bool',
            'heartbeat_interval' => 'required|integer|min:0|max:999999',
            'self_check_interval' => 'required|integer|min:0|max:999999',
            'charge_point_heartbeat_interval' => 'required|integer|min:0|max:999999',
            'pos_check_health_interval' => 'required|integer|min:0|max:999999',
            'sync_to_cloud_interval' => 'nullable|integer|min:0|max:999999',
            'check_charge_pre_authorization_interval' => 'nullable|integer|min:0|max:999999',
            'charge_pre_authorization_retry_count' => 'nullable|integer|min:0|max:999999',
            'is_enable_octopus_cancel' => 'bool',
            'sort_order' => 'integer|min:0',
            'remark' => 'nullable|string',
        );

        $language_codes = array_keys($config_languages);
        $rules['item.*.language_code'] = Rule::in($language_codes);

        $rules['item.*.main_header_message'] = 'nullable|max:1000';

        // 验证枚举值且唯一
        $rules['screen.*.key'] = 'required|distinct|enum_value:' . VioskSettingScreenTimeKeyEnum::class;
        $rules['screen.*.time'] = 'required|integer|min:0|max:999';

        // 如果是新增，并且是超级管理员或者管理员的场地大于1的情况下才需要判断场地编号是否存在
        if (blank($model->viosk_setting_id)) {
            $rules['viosk_setting_number'] = [
                'required',
                'unique:App\Models\Modules\VioskSetting,viosk_setting_number',
                'max:30',
                'regex:/^[a-zA-Z0-9]+$/', // 只能填写字母和数字
            ];
            if (isSuperAdministrator() || auth()->user()->site_number_list->count() > 1) {
                $rules['site_number'] = [
                    'required',
                    'exists:App\Models\Modules\Site,site_number',
                    function ($attr, $value, $fail) use ($module_name) {
                        // 判断选择的site是否包含在当前角色的场地内
                        $site = Site::when(!isSuperAdministrator(), fn($query) => $query->whereIn('site_number', auth()->user()->site_number_list))->firstWhere('site_number', $value);
                        if (blank($site)) $fail(__('common.text_not_found', ['field' => __("$module_name.site")]));
                    }
                ];
            }
        }

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        $errors = [
            'site_number' => __("$module_name.site"),
            'viosk_setting_number' => __("$module_name.viosk_setting_number"),
            'name' => __("$module_name.name"),
            'default_language_code' => __("$module_name.default_language_code"),
            'is_display_idle_remain_time' => __("$module_name.is_display_idle_remain_time"),
            'heartbeat_interval' => __("$module_name.heartbeat_interval"),
            'self_check_interval' => __("$module_name.self_check_interval"),
            'charge_point_heartbeat_interval' => __("$module_name.charge_point_heartbeat_interval"),
            'pos_check_health_interval' => __("$module_name.pos_check_health_interval"),
            'sync_to_cloud_interval' => __("$module_name.sync_to_cloud_interval"),
            'check_charge_pre_authorization_interval' => __("$module_name.check_charge_pre_authorization_interval"),
            'charge_pre_authorization_retry_count' => __("$module_name.charge_pre_authorization_retry_count"),
            'is_enable_octopus_cancel' => __("$module_name.is_enable_octopus_cancel"),
            'sort_order' => __("$module_name.sort_order"),
            'remark' => __("$module_name.remark"),

            'item.*.main_header_message' => __("$module_name.main_header_message"),
            'item.*.language_code' => __("$module_name.language_code"),

            'screen.*.key' => __("$module_name.screen_time_key"),
            'screen.*.time' => __("$module_name.screen_time_time"),
        ];
        $config_languages = config('languages');
        // 单独加入双语语言，只有viosk需要显示双语，cms不需要双语
        $config_languages['en_US_zh_HK'] = __('common.en_US_zh_HK');
        foreach ($config_languages as $language_code => $language_name) {
            $errors['screensaver_list.' . $language_code] = $language_name . __("$module_name.follow_field");
        }

        return $errors;
    }

    /**
     * 返回地址栏参数
     *
     * @param Request $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => 'true',
            'site_search' => $request->get('site_search'),
            'name_search' => $request->get('name_search'),
            'default_language_code_search' => $request->get('default_language_code_search'),
            'viosk_setting_number_search' => $request->get('viosk_setting_number_search'),
        );
    }

    /**
     * 去除数组中的null或''
     * @param array $array
     * @return array
     * <AUTHOR>
     * @date 2024-05-20
     */
    protected static function arrayFilterNullOrEmpty(array $array): array
    {
        return array_filter($array, function ($value) {
            return !is_null($value) && $value !== '';
        });
    }

    /**
     * 将时间转换为秒
     * @param String $time
     * @return int
     * <AUTHOR>
     * @date 2024-05-20
     */
    protected static function timeToSeconds(string $time): int
    {
        // 判断传入的值是否为空
        // 使用正则表达式验证时间格式是否为 HH:mm
        if (is_null($time) || '' == trim($time) || !preg_match('/^([01]\d|2[0-3]):([0-5]\d)$/', $time)) return -1;
        list($hours, $minutes) = explode(':', $time);
        return $hours * 3600 + $minutes * 60;
    }

    /**
     * 将秒数转换为HH:mm格式的时间字符串
     * @param int $seconds
     * @return string
     */
    protected static function secondsToTime(int $seconds): string
    {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        return sprintf('%02d:%02d', $hours, $minutes);
    }

    /**
     * 将时间字符串数组转成秒数组,并返回指定分隔符分隔的秒数组字符串
     * @param Array $timeArray
     * @return String
     * <AUTHOR>
     * @date 2024-05-20
     */
    protected static function timeArrayStrToSecondsArrayStr(array $time_array)
    {
        if (blank($time_array)) return '';
        // 去除重复值
        $unique_time_array = array_unique($time_array);
        // 过滤掉空字符串和 null 值
        $filtered_time_array = self::arrayFilterNullOrEmpty($unique_time_array);
        // 检查过滤后的数组是否为空，如果为空直接返回空字符串
        if (blank($filtered_time_array)) return '';
        // 将过滤后的时间数组转换为秒的数组
        $seconds_array = array_map([self::class, 'timeToSeconds'], $filtered_time_array);
        // 过滤掉转换结果为 -1 的元素
        $filter_second_array = array_filter($seconds_array, function ($seconds) {
            return $seconds >= 0;
        });
        // 对秒的数组进行排序
        sort($filter_second_array);
        // 将排序后的秒数组转换为以逗号分隔的字符串
        $seconds_array_str = implode(',', $filter_second_array);
        return $seconds_array_str;
    }

    /**
     * 将以逗号分隔的秒数字符串转换为HH:mm时间数组
     * @param string $secondsString
     * @return array
     */
    protected static function secondsStringToTimeArray(?string $secondsString): array
    {
        if (blank($secondsString)) {
            return [];
        }

        // 将逗号分隔的字符串转换为数组
        $secondsArray = explode(',', $secondsString);

        // 去除数组中的null或空字符串
        // $filteredSecondsArray = self::arrayFilterNullOrEmpty($secondsArray);

        // 将每个秒数转换为HH:mm格式的时间
        $timeArray = array_map(function ($seconds) {
            return self::secondsToTime((int)$seconds);
        }, $secondsArray);
        // }, $filteredSecondsArray);
        return $timeArray;
    }

}

<?php

namespace App\Models;

use App\Models\Traits\Emoji;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasFactory, Notifiable, HasRoles, emoji;

    protected $table = 'administrator'; //默认是小写的模型类名复数格式，此处为自定义表名
    protected $primaryKey = 'administrator_id'; //定义主键，默认为id
    const CREATED_AT = 'gmt_create';
    const UPDATED_AT = 'gmt_modified';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'gmt_email_verified' => 'datetime',
    ];

    /**
     * 返回site_number列表
     *
     * @return void
     * @Description
     * @example
     * @date 2024-03-14
     */
    public function getSiteNumberListAttribute()
    {
        $this->roles->loadMissing('site.merchant');
        // 从user关联的role中，通过role获取role_to_site中间表的site_number
        return $this->roles->flatMap(function ($role) {
            return $role->site->pluck('site_number');
        })->unique();
    }

    public function getMerchantNumberListAttribute()
    {
        $this->roles->loadMissing('site');
        // 从user关联的role中，通过role获取role_to_site中间表的site_number，再通过site表获取merchant_number
        return $this->roles->flatMap(function ($role) {
            return $role->site->pluck('merchant_number');
        })->unique();
    }
}

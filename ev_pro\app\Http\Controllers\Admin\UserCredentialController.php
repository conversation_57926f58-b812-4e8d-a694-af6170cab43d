<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Common\CommonController;
use Illuminate\Contracts\View\{
    Factory,
    View,
};
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Application;
use Illuminate\Validation\Rule;
use Illuminate\Http\{
    Request,
    JsonResponse,
    RedirectResponse,
};
use App\Http\Controllers\Admin\Traits\{
    Add,
    Edit,
};
use App\Models\Modules\{
    UserCredential,
    AppUser,
};
use App\Enums\{
    UserCredentialType,
};

class UserCredentialController extends CommonController
{
    use Add, Edit;

    protected static string $module_name = 'userCredential'; // 模块名称
    protected static bool $module_check_merchant = true; // 标记该模块校验商户

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->model = new UserCredential;
    }

    public function showPage(Request $request): View|Application|Factory
    {
        $data = array(
            'module_name' => self::$module_name,
            'add_url' => action([self::class, 'add']),
            'list_url' => action([self::class, 'list']),
            'show_page_url' => action([self::class, 'showPage']),
            'merchant_search' => $request->input('merchant_search'),
            'credential_type_search' => $request->input('credential_type_search'),
            'credential_number_search' => $request->input('credential_number_search'),
        );

        // 商户下拉列表
        $data['merchant_list'] = $this->getMerchantOptionList();

        // 凭证类型下拉列表
        $data['credential_type_list'] = [];
        foreach (UserCredentialType::asSelectArray() as $value => $name) {
            $data['credential_type_list'][] = [
                'name' => $name,
                'value' => $value,
            ];
        }

        return view("pages.{$data['module_name']}.list", $data);
    }

    public function list(Request $request): JsonResponse
    {
        $draw = (int)$request->input('draw', 1);
        $length = (int)$request->input('length', 10);
        $search = $this->_searchList($request);
        $data_list = $search->paginate($length, ['*'], 'start');

        $result = array();
        foreach ($data_list as $data) {
            $merchant_name = $this->getValueFromLanguageArray($data->merchant?->name_json) ?? '—/—';
            $result[] = array(
                'user_credential_id' => $data->user_credential_id, // 用户凭证ID
                'merchant_name' => $merchant_name, // 商户名称
                'user_id' => $data->user_id, // 用户ID
                'user_name' => $data->user?->email ?? ($data->user?->nickname ?? $data->user_id), // 用户名称
                'avatar' => existsImage('avatar', $data->user?->avatar_url) ?: existsImage('icon', 'not_select_image.png'), // 用户头像
                'credential_type' => UserCredentialType::getDescription($data->credential_type) ?? '—/—', // 凭证类型
                'credential_number' => $data->credential_number, // 凭证号码
                'name' => $data->name ?? '—/—', // 名称
                'email' => $data->email ?? '—/—', // 邮箱
                'telephone' => $data->telephone ?? '—/—', // 电话
                'is_enable' => $data->is_enable, // 是否启用
                'sort_order' => $data->sort_order, // 排序
                'remark' => $data->remark ?? '—/—', // 备注
                'gmt_create' => $data->gmt_create->toDateTimeString(), // 创建时间
                'gmt_modified' => $data->gmt_modified->toDateTimeString(), // 修改时间
            );
        }

        $json = array(
            'draw' => $draw,
            'recordsTotal' => $data_list->total(),
            'recordsFiltered' => $data_list->total(),
            "data" => $result,
        );
        return response()->json($json);
    }

    protected function getForm(Request $request, array $data): View|Application|Factory
    {
        $data['module_name'] = self::$module_name;
        $data['cancel_url'] = action(
            [self::class, 'showPage'],
            self::getUrlParams($request)
        );

        // 当前存在以get窗口的方式打开form，判断是否有user_search，否则读取旧数据等等
        $user_id = $request->get('user_search');
        if (blank($user_id)) {
            $user_id = $request->old('user_id', $data['model']->user_id);
        }
        $user = AppUser::find($user_id);
        $data['model']->user_id = $user_id;
        $data['model']->user_name = $user?->email ?? ($user?->nickname ?? $user_id);

        $data['model']->credential_type = $request->old('credential_type', $data['model']->credential_type);
        $data['model']->credential_number = $request->old('credential_number', $data['model']->credential_number);
        $data['model']->name = $request->old('name', $data['model']->name);
        $data['model']->email = $request->old('email', $data['model']->email);
        $data['model']->telephone = $request->old('telephone', $data['model']->telephone);
        $data['model']->is_enable = $request->old('is_enable', $data['model']->is_enable);
        $data['model']->sort_order = $request->old('sort_order', $data['model']->sort_order);
        $data['model']->remark = $request->old('remark', $data['model']->remark);

        $data['credential_type_list'] = [];
        foreach (UserCredentialType::asSelectArray() as $value => $name) {
            $data['credential_type_list'][] = [
                'name' => $name,
                'value' => $value,
            ];
        }

        return view("pages.{$data['module_name']}.form", $data);
    }

    /**
     * 验证模型并存入数据库
     *
     * @param Request $request
     * @param UserCredential $model
     * @return RedirectResponse
     * @Description 验证模型规则，通过之后插入或者更新数据库
     * @example
     * <AUTHOR>
     * @date 2022-05-14
     */
    protected function modelValidateAndSave(Request $request, UserCredential $model): RedirectResponse
    {
        $module_name = self::$module_name;
        $is_add = blank($model?->user_credential_id);
        $is_edit = !$is_add;


        // 验证规则
        $request->validate(self::rules($request, $model), [], self::attributes());

        $user = AppUser::find($request->input('user_id'));
        $model->merchant_number = $user?->merchant_number;
        $model->user_id = $request->input('user_id');
        $model->credential_type = $request->input('credential_type');
        $model->credential_number = $request->input('credential_number');
        $model->name = $request->input('name');
        $model->email = $request->input('email');
        $model->telephone = $request->input('telephone');
        $model->sort_order = $request->input('sort_order');
        $model->is_enable = $request->input('is_enable', 0);
        $model->remark = $request->input('remark');
        $model->save();

        return redirect()->action(
            [self::class, 'showPage'],
            self::getUrlParams($request)
        );
    }

    public function delete(Request $request): JsonResponse
    {
        $id = $request->post('id');

        if (blank($id)) {
            $this->notFoundData('ID');
            return $this->returnJson();
        }

        $model = $this->model::when(!isSuperAdministrator(), fn ($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))->find($id);

        if (blank($model)) {
            $this->notFoundData('ID');
            return $this->returnJson();
        }

        // 删除关联数据
        $model->delete();

        return $this->returnJson();
    }

    /**
     * 获取应用于该请求的验证规则。
     *
     * @param Request|null $request
     * @param Model|null $model
     * @return array
     */
    protected static function rules(?Request $request, ?Model $model): array
    {
        $module_name = self::$module_name;

        $user = AppUser::find($request->input('user_id'));
        $merchant_number = $user?->merchant_number;

        $rules = array(
            'user_id' => [
                'required',
                'exists:App\Models\Modules\AppUser,user_id',
                function ($attr, $value, $fail) use ($module_name, $model, $merchant_number) {
                    // 判断选择的user的商户是否为当前管理员权限下的
                    if (!isSuperAdministrator() && !in_array($merchant_number, auth()->user()->merchant_number_list->toArray())) $fail(__('common.text_not_found', ['field' => __("$module_name.merchant")]));
                },
            ],
            'credential_type' => 'required|enum_value:' . UserCredentialType::class,
            'credential_number' => [
                'required',
                'max:255',
                // 同商户同类型下唯一
                Rule::unique('App\Models\Modules\UserCredential', 'credential_number')
                    ->where(fn ($query) => $query->where('merchant_number', $merchant_number))
                    ->where(fn ($query) => $query->where('credential_type', $request->input('credential_type')))
                    ->where(fn ($query) => $query->where('user_credential_id', '!=', $model?->user_credential_id)),
            ],
            'name' => 'nullable|max:45',
            'email' => 'nullable|max:255|email:rfc',
            'telephone' => 'nullable|max:45|regex:/^\+[0-9]{1,3}[\s.-]?[0-9]{4,11}$/',
            'is_enable' => 'boolean',
            'sort_order' => 'integer|min:0',
            'remark' => 'nullable|max:1000',
        );

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array
     */
    protected static function attributes(): array
    {
        $module_name = self::$module_name;
        return [
            'user_id' => __("$module_name.user_id"),
            'credential_type' => __("$module_name.credential_type"),
            'credential_number' => __("$module_name.credential_number"),
            'name' => __("$module_name.name"),
            'email' => __("$module_name.email"),
            'telephone' => __("$module_name.telephone"),
            'is_enable' => __("$module_name.is_enable"),
            'sort_order' => __("$module_name.sort_order"),
            'remark' => __("$module_name.remark"),
        ];
    }

    /**
     * 返回地址栏参数
     *
     * @param Request|null $request
     * @return array
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-05-13
     */
    protected static function getUrlParams(?Request $request): array
    {
        return array(
            'is_page' => 'true',
            'merchant_search' => $request->get('merchant_search'),
            'credential_type_search' => $request->get('credential_type_search'),
            'credential_number_search' => $request->get('credential_number_search'),
            'user_search' => $request->get('user_search'),
        );
    }

    /**
     * 获取搜索数据
     *
     * @param Request $request
     * @return mixed
     * @Description
     * @example
     * <AUTHOR>
     * @date 2022-11-08
     */
    protected function _searchList(Request $request): mixed
    {
        // dataTable字段
        $order = $request->input('order', 'gmt_create');
        $sort = $request->input('sort', 'desc');
        $merchant_search = $request->input('merchant_search');
        $credential_type_search = $request->input('credential_type_search');
        $credential_number_search = $request->input('credential_number_search');
        $user_search = $request->input('user_search');

        return UserCredential::with(['merchant', 'user'])
            ->when(!isSuperAdministrator(), fn ($query) => $query->whereIn('merchant_number', auth()->user()->merchant_number_list))
            ->when(filled($user_search), fn ($query) => $query->where('user_id', $user_search))
            ->when(filled($merchant_search), fn ($query) => $query->where('merchant_number', $merchant_search))
            ->when(filled($credential_type_search), fn ($query) => $query->where('credential_type', $credential_type_search))
            ->when(filled($credential_number_search), fn ($query) => $query->where('credential_number', 'like', "%$credential_number_search%"))
            ->orderBy($order, $sort)
            ->orderBy('gmt_modified', 'desc');
    }
}
